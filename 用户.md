
# 用户
0.0.1. 用户的基本属性
用户拥有如下四个属性，userId、name、displayName、mobile。

userId 用户在系统中的唯一ID，一般是一个字符串，具有系统中唯一性，用户在使用过程中无法看到，不可以修改，一般是系统生成。野火IM中用户ID最大长度是64字节。
name 有人也称为loginName或accountName，一般是用来登录时填写的用户名，具有系统中的唯一性，有些系统是可以修改，比如微信。一般是用户自主填写的，可以根据这个字段来搜索用户。野火IM中name最大长度也是64字节
displayName 有人也称为nickName，一般情况下用户可以随意修改，不要求唯一性。displayName最大长度也是64字节。
mobile 电话号码，这个属性理论上可以不唯一，但现实生活中往往是以电话号码来登录的。比如我们demo就是用电话号码登录的。
其它的一些属性，比如头像，号码，地址等，不容易引起误解这里就不做解释了。另外用户提供有extra字段，客户可以自定义使用。

0.0.2. 登录与连接
登录与连接是两个不同的概念。登录是指应用认证用户的一个过程，登录成功后，应用可以识别当前用户，并授权用户对应的权限。连接这里指的是IM的长链接建立，是发生在登录之后，需要保持与IM服务器长链接，以便发送消息或者接收消息推送。

0.0.3. 用户Token
用户Token，在不引起歧义的情况下称为token。token是用户身份验证的凭证，在IM连接之前，需要先换取token，一般是在登录成功时，由应用服务器返回token，使用token连接IM服务器，IM服务器会对token的有效性进行检查。token作为客户的连接凭证，需要严格保密。

0.0.4. 连接
SDK仅需要用户ID和token进行连接，在IM系统核心处理部分，只用到用户ID，不需要系统中一定有这个用户，这样客户就可以选择是否托管用户信息。


# 用户设置
野火系统中，有一种数据结构叫用户设置，系统中大部分用户相关的数据都是存储在这个数据结构中。因此这个数据是个通用的数据结构，在操作这个数据时需要格外小心，避免不同端直接因为格式不一致导致错误，甚至崩溃。

1. 用户设置的格式
   用户设置包含4部分内容：

用户ID：是属于那个用户的数据。字段名为userId。
数据类型：是那一类数据。字段名为scope。
数据的Key：数据的Key值。字段名为key。
数据的Value：数据的value。字段名为value。 在客户端因为只能同步当前用户的设置，所以就不包含用户ID字段。
2. 用户设置的同步
   用户设置会自动实时同步。当修改设置时，会自动同步到所有端及IM服务上去。

3. 内置的数据类型。
   每个客户端和服务器都有枚举或者定义UserSettingScope，定义了系统内置的设置。包括如下：

//会话静音设置
UserSettingScope_Conversation_Silent = 1,
//全局静音设置
UserSettingScope_Global_Silent = 2,
//会话置顶设置
UserSettingScope_Conversation_Top = 3,
//隐藏通知详情设置
UserSettingScope_Hidden_Notification_Detail = 4,
//群组隐藏昵称设置
UserSettingScope_Group_Hide_Nickname = 5,
//收藏群组设置
UserSettingScope_Favourite_Group = 6,
//同步会话阅读状态设置
UserSettingScope_Conversation_Sync = 7,
//拥有的频道设置
UserSettingScope_My_Channel = 8,
//订阅的频道设置
UserSettingScope_Listened_Channel = 9,
//PC在线状态设置
UserSettingScope_PC_Online = 10,
//同步会话已读状态设置
UserSettingScope_Conversation_Readed = 11,
//web在线状态设置
UserSettingScope_WebOnline = 12,
//禁止草稿同步设置
UserSettingScope_DisableRecipt = 13,
//收藏圈子设置
UserSettingScope_Favourite_User = 14,
//不能直接使用
UserSettingScope_Mute_When_PC_Online = 15,
//不能直接使用
UserSettingScope_Lines_Readed = 16,
//不能直接使用
UserSettingScope_No_Disturbing = 17,
//不能直接使用，协议栈内会使用此值
UserSettingScope_Conversation_Clear_Message = 18,
//不能直接使用，协议栈内会使用此值
UserSettingScope_Conversation_Draft = 19,
//不能直接使用，协议栈内会使用此值
UserSettingScope_Disable_Sync_Draft = 20,
//不能直接使用，协议栈内会使用此值
UserSettingScope_Voip_Silent = 21,
//不能直接使用，协议栈内会使用此值
UserSettingScope_PTT_Reserved = 22,
//不能直接使用，协议栈内会使用此值
UserSettingScope_Custom_State = 23,
//不能直接使用，协议栈内会使用此值
UserSettingScope_Disable_Secret_Chat = 24,
//不能直接使用，协议栈内会使用此值
UserSettingScope_Ptt_Silent = 25,
//不能直接使用，协议栈内会使用此值
UserSettingScope_Group_Remark = 26,
//不能直接使用，协议栈内会使用此值
UserSettingScope_Privacy_Searchable = 27,
//不能直接使用，协议栈内会使用此值
UserSettingScope_AddFriend_NoVerify = 28,

//自定义用户设置，请使用1000以上的key
UserSettingScope_Custom_Begin = 1000
上述内置设置在所有端和服务器都有使用，因此内置设置不能更改。如果第三方服务设置修改需要严格按照格式处理，否则有可能出严重问题，甚至崩溃。

客户可以二开使用少量设置，scope的值要大于1000，避免与野火未来设置冲突。
