<script>
import {getItem, setItem, removeItem} from './pages/util/storageHelper'
import conferenceManager from './pages/voip/conference/conferenceManager'
import ConferenceInviteMessageContent from './wfc/av/messages/conferenceInviteMessageContent'
import Message from './wfc/messages/message'
import ForwardType from './pages/conversation/message/forward/ForwardType'
import avengineKit from "@/wfc/av/engine/avengineKit";
import wfc from "@/wfc/client/wfc";

export default {
  data() {
    return {
      wfc: null,
      avengineKit: null,
      conferenceManager: null,
      currentVersion: '23',
      youthModeEnabled: false,
      isSelectingMedia: false
    }
  },
  methods: {
    appGo(){
      let userId = getItem('userId')
      let im_token = getItem('im_token')
      if (userId && im_token) {
        this.go2ConversationList()
      } else {
        this.go2Login()
      }
    },
    go2ConversationList() {
      uni.switchTab({
        url: '/pages/conversationList/ConversationListPage',
        success: () => {
          console.log('to conversation list success')
        },
        fail: (e) => {
          console.log('to conversation list error', e)
        },
        complete: () => {
          console.log('switch tab complete')
        },
      })
    },

    go2Login() {
      uni.reLaunch({
        url: '/pages/login/LoginPage'
      })
    },

    // 设置全局导航拦截器
    setupNavigationInterceptor() {
      // 拦截页面跳转
      const interceptNavigationMethods = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']

      interceptNavigationMethods.forEach(method => {
        const originalMethod = uni[method]

        uni[method] = (options) => {
          const youthModeEnabled = getItem('youth_mode_status') === 'enabled'

          if (youthModeEnabled) {
            // 检查是否允许访问
            if (!this.checkYouthModeRestriction(options.url)) {
              console.log('青少年模式下不允许访问:', options.url)

              // 提示用户
              uni.showToast({
                title: '青少年模式下不可用',
                icon: 'none'
              })

              // 如果提供了失败回调，则调用
              if (typeof options.fail === 'function') {
                options.fail({
                  errMsg: '青少年模式下不允许访问该页面'
                })
              }

              return false
            }
          }

          // 允许访问，调用原始方法
          return originalMethod.call(uni, options)
        }
      })
    },

    // 检查是否在青少年模式下允许访问页面
    checkYouthModeRestriction(url) {
      const youthModeEnabled = getItem('youth_mode_status') === 'enabled'

      if (!youthModeEnabled) {
        return true // 未开启青少年模式，允许访问所有页面
      }

      // 白名单：允许访问的页面
      const allowedPages = [
        // 基础页面
        '/pages/conversationList/',
        '/pages/me/',
        '/pages/SplashPage',
        '/pages/login/',

        // 设置相关页面
        '/pages/settings/YouthMode',
        '/pages/settings/Settings',

        // 必要功能页面
        '/pages/conversation/ConversationPage', // 聊天详情页
        '/pages/user/UserDetailPage',          // 用户详情
        '/pages/me/UserInfo',                  // 个人信息
        '/pages/me/EditDisplayName',          // 修改昵称
        '/pages/me/AvatarUpload',             // 上传头像
        '/pages/me/EditSignature',            // 修改签名

        // 各类必要的预览/功能页面
        '/pages/conversation/message/',        // 消息相关功能
        '/pages/misc/WebViewPage',            // 网页浏览
        '/pages/misc/PreviewVideoPage'        // 视频预览
      ]

      // 检查URL是否属于允许访问的页面
      return allowedPages.some(page => url.startsWith(page))
    },

    forwardConferenceInviteMessage(
        callId,
        host,
        title,
        desc,
        startTime,
        audioOnly,
        defaultAudience,
        advance,
        pin
    ) {
      let inviteMessageContent = new ConferenceInviteMessageContent(
          callId,
          host,
          title,
          desc,
          startTime,
          audioOnly,
          defaultAudience,
          advance,
          pin
      )
      console.log('invite', inviteMessageContent)
      let message = new Message(null, inviteMessageContent)
      this.$forward({
        forwardType: ForwardType.NORMAL,
        messages: [message],
      })
    },
  },

  onHide: function () {
    console.log('App Hide')
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    // 检查所有可能涉及到媒体选择或编辑的页面
    const editPages = [
      'EditCard',
      'EditField',
      'EditMoreSocial',
      'AvatarUpload',
      'UserInfo',
      'card/EditField',
      'card/EditData'
    ]
    
    if(currentPage && currentPage.route) {
      const isEditPage = editPages.some(page => currentPage.route.includes(page))
      if(isEditPage) {
        this.isSelectingMedia = true
        console.log('设置选择媒体标记,当前页面:', currentPage.route)
      }
    }
  },

  onLaunch: function () {
    console.log('App Launch')
    
    // 设置应用启动标志，用于LoginPage判断是否为新会话
    setItem('app_just_launched', 'true')
	  setItem('app_just_launched_temp', 'true')
    console.log('设置应用启动标志')
    
    // 检查青少年模式是否开启
    this.youthModeEnabled = getItem('youth_mode_status') === 'enabled'

    // 添加全局导航拦截器
    this.setupNavigationInterceptor()
    this.wfc = wfc
    this.avengineKit = avengineKit
    // this.store = store
    this.conferenceManager = conferenceManager


  },
  onLoad: async function () {
    console.log('App onLoad')
    this.setupNavigationInterceptor()
    // 初始化 conferenceManager
    this.conferenceManager.init();

  },
  onShow: async function () {
    console.log('App Show')
    this.appGo()
  },
}
</script>

<style lang="css">
/*每个页面公共css */
@import './global.css';
@import './wfc.css';
/* #ifndef APP-NVUE */
@import './static/iconfonts/customicons.css';
@import './static/iconfonts/icomoon/style.css';
/* #endif */

:root {
  --uni-tabbar-height: 60px;

  /*app-plus header 和 tabbar 是原生的*/

  /* #ifdef APP-PLUS */
  --uni-page-header-height: 0;
  --page-full-height-without-header-and-tabbar: 100vh;
  --page-full-height-without-header: 100vh;
  /* #endif */

  /* #ifdef H5 */
  --uni-page-header-height: 44px;
  --page-full-height-without-header-and-tabbar: calc(100vh - 44px - 50px);
  --page-full-height-without-header: calc(100vh - 44px);
  /* #endif */
}

.conversation-rq {
  width: calc(100% - 20px);
  padding: 2px;
  margin: 10px;
  border-radius: 10px;
  background-color: #ffffff;
  font-size: 16px;
}

.rq-hine {
  width: 100%;
  height: 2px;
  position: relative;
}

.rq-hine2 {
  width: 100%;
  height: 2px;
  position: relative;
}

.rq-hine::after {
  content: '';
  position: absolute;
  left: 65px;
  right: 0;
  bottom: 0;
  border-bottom: 1px solid #f4f4f4;
}

.rq-hine2::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  border-bottom: 1px solid #f4f4f4;
}

.rq-tc .tz {
  width: 100%;
  height: 20vh;
  background: rgba(56, 107, 246, 0);
  position: absolute;
  top: 0;
  left: 0;
}

.rq-tc .tc {
  width: 100%;
  height: 80vh;
  background: #fff;
  position: absolute;
  bottom: 0;
  left: 0;
  border-radius: 20px 20px 0px 0px;
  padding: 15px 20px;
  overflow-y: auto;
}

.rq-tc .tc .tch {
  margin-top: 60px;
  width: 100%;
  height: 46px;
  border-radius: 10px;
  background: #386bf6;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-family: MiSans;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.rq-tc .tc .tcg {
  text-align: center;
  margin-top: 18px;
  width: 100%;
  color: #999;
  font-family: MiSans;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.rq-tc .tc .tcf {
  text-align: center;
  margin-top: 28px;
  width: 100%;
}

.rq-tc .tc .tcf .tcf1 {
  width: 160px;
  height: 160px;
}

.rq-tc .tc .tce {
  text-align: center;
  margin-top: 1px;
  width: 100%;
  color: #bcbdbd;
  font-family: MiSans;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: -0.72px;
}

.rq-tc .tc .tcd {
  text-align: center;
  margin-top: 5px;
  width: 100%;
  color: #21252a;
  font-family: MiSans;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.rq-tc .tc .tcc {
  text-align: center;
  margin-top: 28px;
  width: 100%;
}

.rq-tc .tc .tcc .tcc1 {
  width: 75px;
  height: 75px;
  border-radius: 50%;
  margin: 0 auto;
}

.rq-tc .tc .tcb {
  margin-top: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rq-tc .tc .tcb .tcb2 {
  width: 22px;
  height: 22px;
}

.rq-tc .tc .tcb .tcb1 {
  color: #21252a;
  text-align: center;
  font-family: MiSans;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.rq-tc .tc .tca {
  width: 38px;
  height: 4px;
  border-radius: 10px;
  background: #e7e7e7;
  margin: 0 auto;
}

.rq-tc {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
}
</style>