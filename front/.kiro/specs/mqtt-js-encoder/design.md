# JavaScript MQTT消息编码器设计文档

## 概述

基于后端Java MqttDecoder的解析逻辑，设计并实现JavaScript版本的MQTT消息编码器。该编码器将在前端构造符合MQTT协议规范的二进制数据流，确保后端Java解码器能够正确解析这些消息。编码器将与现有的protobuf序列化系统和WebSocket客户端无缝集成。

## 架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    前端应用层                                │
├─────────────────────────────────────────────────────────────┤
│  WebSocket Client  │  Message Factory  │  Application Logic │
├─────────────────────────────────────────────────────────────┤
│                  MQTT消息编码器层                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ MqttEncoder │  │ MqttMessage │  │ MqttMessageBuilder  │  │
│  │             │  │ Factory     │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                   协议处理层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ Fixed       │  │ Variable    │  │ Payload             │  │
│  │ Header      │  │ Header      │  │ Encoder             │  │
│  │ Encoder     │  │ Encoder     │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                   数据编码层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ Binary      │  │ String      │  │ Integer             │  │
│  │ Encoder     │  │ Encoder     │  │ Encoder             │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                 集成适配层                                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ Protobuf    │  │ AES         │  │ WebSocket           │  │
│  │ Adapter     │  │ Adapter     │  │ Adapter             │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. MqttEncoder（主编码器）
- 负责协调整个编码过程
- 管理编码状态和缓冲区
- 提供统一的编码接口

#### 2. MqttMessageFactory（消息工厂）
- 创建各种类型的MQTT消息
- 验证消息参数的有效性
- 提供便捷的消息构造方法

#### 3. MqttMessageBuilder（消息构建器）
- 提供链式调用的消息构建接口
- 支持逐步构建复杂消息
- 内置验证和错误处理

## 组件和接口设计

### 核心类设计

#### MqttEncoder类

```javascript
class MqttEncoder {
    constructor(options = {}) {
        this.maxBytesInMessage = options.maxBytesInMessage || 268435455; // 256MB
        this.maxClientIdLength = options.maxClientIdLength || 65535;
        this.version = options.version || MqttVersion.MQTT_3_1_1;
        this.buffer = new ByteBuffer();
    }
    
    // 主要编码方法
    encode(message) { /* 编码MQTT消息 */ }
    encodeFixedHeader(messageType, flags, remainingLength) { /* 编码固定头部 */ }
    encodeVariableHeader(messageType, variableHeader) { /* 编码可变头部 */ }
    encodePayload(messageType, payload) { /* 编码有效载荷 */ }
    
    // 辅助方法
    calculateRemainingLength(variableHeader, payload) { /* 计算剩余长度 */ }
    validateMessage(message) { /* 验证消息 */ }
    reset() { /* 重置编码器状态 */ }
}
```

#### MqttMessage类

```javascript
class MqttMessage {
    constructor(fixedHeader, variableHeader = null, payload = null) {
        this.fixedHeader = fixedHeader;
        this.variableHeader = variableHeader;
        this.payload = payload;
    }
    
    // 验证方法
    validate() { /* 验证消息完整性 */ }
    getMessageType() { /* 获取消息类型 */ }
    getRemainingLength() { /* 获取剩余长度 */ }
}
```

#### MqttFixedHeader类

```javascript
class MqttFixedHeader {
    constructor(messageType, dupFlag, qosLevel, retain, remainingLength) {
        this.messageType = messageType;
        this.dupFlag = dupFlag;
        this.qosLevel = qosLevel;
        this.retain = retain;
        this.remainingLength = remainingLength;
    }
    
    // 编码方法
    encode() { /* 编码为字节数组 */ }
    
    // 验证方法
    validate() { /* 验证头部有效性 */ }
}
```

### 数据模型设计

#### MQTT消息类型枚举

```javascript
const MqttMessageType = {
    RESERVED_0: 0,
    CONNECT: 1,
    CONNACK: 2,
    PUBLISH: 3,
    PUBACK: 4,
    PUBREC: 5,
    PUBREL: 6,
    PUBCOMP: 7,
    SUBSCRIBE: 8,
    SUBACK: 9,
    UNSUBSCRIBE: 10,
    UNSUBACK: 11,
    PINGREQ: 12,
    PINGRESP: 13,
    DISCONNECT: 14,
    AUTH: 15 // MQTT 5.0
};
```

#### QoS级别枚举

```javascript
const MqttQoS = {
    AT_MOST_ONCE: 0,    // 最多一次
    AT_LEAST_ONCE: 1,   // 至少一次
    EXACTLY_ONCE: 2,    // 恰好一次
    RESERVED: 3         // 保留（无效）
};
```

#### MQTT版本枚举

```javascript
const MqttVersion = {
    MQTT_3_1: {
        protocolName: 'MQIsdp',
        protocolLevel: 3
    },
    MQTT_3_1_1: {
        protocolName: 'MQTT',
        protocolLevel: 4
    },
    MQTT_5: {
        protocolName: 'MQTT',
        protocolLevel: 5
    }
};
```

## 编码算法设计

### 固定头部编码算法

```javascript
function encodeFixedHeader(messageType, dupFlag, qosLevel, retain, remainingLength) {
    // 1. 构造第一个字节：消息类型(4位) + 标志位(4位)
    let firstByte = (messageType << 4) & 0xF0;
    if (dupFlag) firstByte |= 0x08;
    firstByte |= (qosLevel << 1) & 0x06;
    if (retain) firstByte |= 0x01;
    
    // 2. 编码剩余长度（可变字节整数）
    const remainingLengthBytes = encodeVariableByteInteger(remainingLength);
    
    // 3. 组合结果
    const result = new Uint8Array(1 + remainingLengthBytes.length);
    result[0] = firstByte;
    result.set(remainingLengthBytes, 1);
    
    return result;
}
```

### 可变字节整数编码算法

```javascript
function encodeVariableByteInteger(value) {
    if (value < 0 || value > 268435455) { // 2^28 - 1
        throw new Error('Variable byte integer out of range: ' + value);
    }
    
    const bytes = [];
    do {
        let encodedByte = value % 128;
        value = Math.floor(value / 128);
        if (value > 0) {
            encodedByte |= 128; // 设置继续位
        }
        bytes.push(encodedByte);
    } while (value > 0);
    
    return new Uint8Array(bytes);
}
```

### UTF-8字符串编码算法

```javascript
function encodeString(str) {
    if (!str) str = '';
    
    // 1. 将字符串转换为UTF-8字节数组
    const utf8Bytes = new TextEncoder().encode(str);
    
    // 2. 检查长度限制
    if (utf8Bytes.length > 65535) {
        throw new Error('String too long: ' + utf8Bytes.length);
    }
    
    // 3. 构造结果：长度(2字节) + UTF-8数据
    const result = new Uint8Array(2 + utf8Bytes.length);
    const view = new DataView(result.buffer);
    
    // 写入长度（大端序）
    view.setUint16(0, utf8Bytes.length, false);
    
    // 写入UTF-8数据
    result.set(utf8Bytes, 2);
    
    return result;
}
```

### 二进制数据编码算法

```javascript
function encodeBinaryData(data) {
    if (!data) data = new Uint8Array(0);
    
    // 检查长度限制
    if (data.length > 65535) {
        throw new Error('Binary data too long: ' + data.length);
    }
    
    // 构造结果：长度(2字节) + 二进制数据
    const result = new Uint8Array(2 + data.length);
    const view = new DataView(result.buffer);
    
    // 写入长度（大端序）
    view.setUint16(0, data.length, false);
    
    // 写入二进制数据
    result.set(data, 2);
    
    return result;
}
```

## 消息类型特定编码

### CONNECT消息编码

```javascript
function encodeConnectMessage(connectRequest) {
    // 1. 编码可变头部
    const protocolName = encodeString(connectRequest.protocolName);
    const protocolLevel = new Uint8Array([connectRequest.protocolLevel]);
    const connectFlags = encodeConnectFlags(connectRequest);
    const keepAlive = encodeMsbLsb(connectRequest.keepAlive);
    
    let variableHeader = concatBytes([protocolName, protocolLevel, connectFlags, keepAlive]);
    
    // MQTT 5.0属性支持
    if (connectRequest.version === MqttVersion.MQTT_5) {
        const properties = encodeProperties(connectRequest.properties);
        variableHeader = concatBytes([variableHeader, properties]);
    }
    
    // 2. 编码有效载荷
    const payload = encodeConnectPayload(connectRequest);
    
    // 3. 计算剩余长度
    const remainingLength = variableHeader.length + payload.length;
    
    // 4. 编码固定头部
    const fixedHeader = encodeFixedHeader(
        MqttMessageType.CONNECT,
        false, // DUP
        MqttQoS.AT_MOST_ONCE, // QoS
        false, // RETAIN
        remainingLength
    );
    
    // 5. 组合最终消息
    return concatBytes([fixedHeader, variableHeader, payload]);
}
```

### PUBLISH消息编码

```javascript
function encodePublishMessage(publishRequest) {
    // 1. 编码可变头部
    const topicName = encodeString(publishRequest.topicName);
    let variableHeader = topicName;
    
    // QoS > 0时需要消息ID
    if (publishRequest.qosLevel > MqttQoS.AT_MOST_ONCE) {
        const messageId = encodeMsbLsb(publishRequest.messageId);
        variableHeader = concatBytes([variableHeader, messageId]);
    }
    
    // MQTT 5.0属性支持
    if (publishRequest.version === MqttVersion.MQTT_5) {
        const properties = encodeProperties(publishRequest.properties);
        variableHeader = concatBytes([variableHeader, properties]);
    }
    
    // 2. 有效载荷就是消息内容
    const payload = publishRequest.payload || new Uint8Array(0);
    
    // 3. 计算剩余长度
    const remainingLength = variableHeader.length + payload.length;
    
    // 4. 编码固定头部
    const fixedHeader = encodeFixedHeader(
        MqttMessageType.PUBLISH,
        publishRequest.dupFlag,
        publishRequest.qosLevel,
        publishRequest.retain,
        remainingLength
    );
    
    // 5. 组合最终消息
    return concatBytes([fixedHeader, variableHeader, payload]);
}
```

## 错误处理策略

### 异常类型定义

```javascript
class MqttEncodingException extends Error {
    constructor(message, code) {
        super(message);
        this.name = 'MqttEncodingException';
        this.code = code;
    }
}

class MqttValidationException extends MqttEncodingException {
    constructor(message, field) {
        super(message, 'VALIDATION_ERROR');
        this.field = field;
    }
}

class MqttTooLongFrameException extends MqttEncodingException {
    constructor(message, actualLength, maxLength) {
        super(message, 'TOO_LONG_FRAME');
        this.actualLength = actualLength;
        this.maxLength = maxLength;
    }
}
```

### 验证策略

```javascript
class MqttMessageValidator {
    static validateConnectMessage(message) {
        const errors = [];
        
        // 验证客户端ID
        if (!this.isValidClientId(message.clientId, message.version)) {
            errors.push('Invalid client ID: ' + message.clientId);
        }
        
        // 验证协议版本
        if (!this.isValidProtocolVersion(message.protocolName, message.protocolLevel)) {
            errors.push('Invalid protocol version');
        }
        
        // 验证保持连接时间
        if (message.keepAlive < 0 || message.keepAlive > 65535) {
            errors.push('Invalid keep alive: ' + message.keepAlive);
        }
        
        return errors;
    }
    
    static validatePublishMessage(message) {
        const errors = [];
        
        // 验证主题名称
        if (!this.isValidPublishTopicName(message.topicName)) {
            errors.push('Invalid topic name: ' + message.topicName);
        }
        
        // 验证QoS级别
        if (message.qosLevel === MqttQoS.RESERVED) {
            errors.push('Invalid QoS level: 3');
        }
        
        // 验证消息ID（QoS > 0时必需）
        if (message.qosLevel > MqttQoS.AT_MOST_ONCE) {
            if (!this.isValidMessageId(message.messageId)) {
                errors.push('Invalid message ID: ' + message.messageId);
            }
        }
        
        return errors;
    }
}
```

## 测试策略

### 单元测试覆盖

1. **编码算法测试**
   - 固定头部编码正确性
   - 可变字节整数编码边界情况
   - UTF-8字符串编码特殊字符
   - 二进制数据编码大小限制

2. **消息类型测试**
   - CONNECT消息各种参数组合
   - PUBLISH消息不同QoS级别
   - SUBSCRIBE/UNSUBSCRIBE消息
   - 心跳和断开连接消息

3. **错误处理测试**
   - 无效参数验证
   - 长度超限异常
   - 协议版本不匹配
   - 消息格式错误

### 集成测试策略

1. **与Java解码器兼容性测试**
   - 构造各种消息类型发送给Java解码器
   - 验证解码结果与预期一致
   - 测试边界情况和异常场景

2. **与现有系统集成测试**
   - 与protobuf序列化器集成
   - 与WebSocket客户端集成
   - 与AES加密模块集成

3. **性能测试**
   - 大量消息编码性能
   - 内存使用情况监控
   - 并发编码场景测试

## 性能优化

### 内存优化策略

1. **缓冲区复用**
   - 使用对象池管理ByteBuffer
   - 避免频繁创建临时数组
   - 实现缓冲区自动扩容机制

2. **字符串处理优化**
   - 缓存常用字符串的UTF-8编码
   - 使用TextEncoder的流式API
   - 避免不必要的字符串转换

### 计算优化策略

1. **长度计算优化**
   - 预计算固定长度字段
   - 使用位运算替代除法运算
   - 缓存重复计算结果

2. **编码路径优化**
   - 针对常用消息类型优化编码路径
   - 减少条件分支判断
   - 使用查找表替代复杂计算

## 与现有系统集成

### Protobuf集成适配器

```javascript
class MqttProtobufAdapter {
    constructor(mqttEncoder) {
        this.mqttEncoder = mqttEncoder;
    }
    
    // 将WildfireMessage转换为MQTT消息
    convertToMqttMessage(wildfireMessage) {
        switch (wildfireMessage.type) {
            case MessageType.CONNECT:
                return this.createConnectMessage(wildfireMessage);
            case MessageType.PUBLISH:
                return this.createPublishMessage(wildfireMessage);
            // ... 其他消息类型
        }
    }
    
    // 编码并集成到现有序列化流程
    async encodeAndSerialize(wildfireMessage, encrypt, encryptKey) {
        const mqttMessage = this.convertToMqttMessage(wildfireMessage);
        const mqttBytes = this.mqttEncoder.encode(mqttMessage);
        
        // 包装为protobuf格式
        return await this.wrapInProtobufFormat(mqttBytes, encrypt, encryptKey);
    }
}
```

### WebSocket集成适配器

```javascript
class MqttWebSocketAdapter {
    constructor(webSocketClient, mqttEncoder) {
        this.webSocketClient = webSocketClient;
        this.mqttEncoder = mqttEncoder;
    }
    
    // 重写发送消息方法
    async sendMqttMessage(message) {
        try {
            // 使用MQTT编码器编码消息
            const mqttBytes = this.mqttEncoder.encode(message);
            
            // 通过WebSocket发送
            this.webSocketClient.ws.send(mqttBytes);
            
            return true;
        } catch (error) {
            console.error('发送MQTT消息失败:', error);
            return false;
        }
    }
}
```

这个设计确保了JavaScript MQTT编码器能够生成与Java MqttDecoder完全兼容的二进制数据，同时与现有的前端架构无缝集成。