/**
 * MQTT编码器自定义异常类型
 * 实现详细的错误信息和调试支持
 */

/**
 * MQTT编码异常基类
 * 所有MQTT相关异常的基类
 */
class MqttEncodingException extends Error {
    /**
     * 构造函数
     * @param {string} message - 错误消息
     * @param {string} code - 错误代码
     * @param {Object} details - 错误详细信息
     */
    constructor(message, code = 'MQTT_ENCODING_ERROR', details = {}) {
        super(message);
        this.name = 'MqttEncodingException';
        this.code = code;
        this.details = details;
        this.timestamp = new Date().toISOString();
        
        // 确保堆栈跟踪正确显示
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, MqttEncodingException);
        }
    }

    /**
     * 获取格式化的错误信息
     * @returns {string} 格式化的错误信息
     */
    getFormattedMessage() {
        let formatted = `[${this.code}] ${this.message}`;
        
        if (Object.keys(this.details).length > 0) {
            formatted += `\nDetails: ${JSON.stringify(this.details, null, 2)}`;
        }
        
        formatted += `\nTimestamp: ${this.timestamp}`;
        
        return formatted;
    }

    /**
     * 转换为JSON对象
     * @returns {Object} JSON表示
     */
    toJSON() {
        return {
            name: this.name,
            message: this.message,
            code: this.code,
            details: this.details,
            timestamp: this.timestamp,
            stack: this.stack
        };
    }
}

/**
 * MQTT验证异常
 * 用于消息验证失败的情况
 */
class MqttValidationException extends MqttEncodingException {
    /**
     * 构造函数
     * @param {string} message - 错误消息
     * @param {string} field - 验证失败的字段名
     * @param {*} value - 验证失败的值
     * @param {string} rule - 违反的验证规则
     */
    constructor(message, field = null, value = null, rule = null) {
        const details = {};
        if (field !== null) details.field = field;
        if (value !== null) details.value = value;
        if (rule !== null) details.rule = rule;
        
        super(message, 'VALIDATION_ERROR', details);
        this.name = 'MqttValidationException';
        this.field = field;
        this.value = value;
        this.rule = rule;
    }
}

/**
 * MQTT消息过长异常
 * 用于消息或字段长度超过限制的情况
 */
class MqttTooLongFrameException extends MqttEncodingException {
    /**
     * 构造函数
     * @param {string} message - 错误消息
     * @param {number} actualLength - 实际长度
     * @param {number} maxLength - 最大允许长度
     * @param {string} field - 超长的字段名
     */
    constructor(message, actualLength, maxLength, field = null) {
        const details = {
            actualLength,
            maxLength,
            exceededBy: actualLength - maxLength
        };
        if (field !== null) details.field = field;
        
        super(message, 'TOO_LONG_FRAME', details);
        this.name = 'MqttTooLongFrameException';
        this.actualLength = actualLength;
        this.maxLength = maxLength;
        this.field = field;
    }
}

/**
 * MQTT标识符拒绝异常
 * 用于客户端ID或其他标识符被拒绝的情况
 */
class MqttIdentifierRejectedException extends MqttEncodingException {
    /**
     * 构造函数
     * @param {string} message - 错误消息
     * @param {string} identifier - 被拒绝的标识符
     * @param {string} reason - 拒绝原因
     */
    constructor(message, identifier = null, reason = null) {
        const details = {};
        if (identifier !== null) details.identifier = identifier;
        if (reason !== null) details.reason = reason;
        
        super(message, 'IDENTIFIER_REJECTED', details);
        this.name = 'MqttIdentifierRejectedException';
        this.identifier = identifier;
        this.reason = reason;
    }
}

/**
 * MQTT解码器异常
 * 用于解码过程中的错误（虽然这是编码器，但为了兼容性保留）
 */
class MqttDecoderException extends MqttEncodingException {
    /**
     * 构造函数
     * @param {string} message - 错误消息
     * @param {string} phase - 解码阶段
     * @param {Uint8Array} data - 导致错误的数据
     */
    constructor(message, phase = null, data = null) {
        const details = {};
        if (phase !== null) details.phase = phase;
        if (data !== null) {
            details.dataLength = data.length;
            details.dataPreview = Array.from(data.slice(0, 16)).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' ');
        }
        
        super(message, 'DECODER_ERROR', details);
        this.name = 'MqttDecoderException';
        this.phase = phase;
        this.data = data;
    }
}

/**
 * MQTT协议异常
 * 用于协议级别的错误
 */
class MqttProtocolException extends MqttEncodingException {
    /**
     * 构造函数
     * @param {string} message - 错误消息
     * @param {number} messageType - 消息类型
     * @param {string} protocolVersion - 协议版本
     */
    constructor(message, messageType = null, protocolVersion = null) {
        const details = {};
        if (messageType !== null) details.messageType = messageType;
        if (protocolVersion !== null) details.protocolVersion = protocolVersion;
        
        super(message, 'PROTOCOL_ERROR', details);
        this.name = 'MqttProtocolException';
        this.messageType = messageType;
        this.protocolVersion = protocolVersion;
    }
}

/**
 * MQTT缓冲区异常
 * 用于缓冲区相关的错误
 */
class MqttBufferException extends MqttEncodingException {
    /**
     * 构造函数
     * @param {string} message - 错误消息
     * @param {string} operation - 缓冲区操作类型
     * @param {number} requiredSize - 所需大小
     * @param {number} availableSize - 可用大小
     */
    constructor(message, operation = null, requiredSize = null, availableSize = null) {
        const details = {};
        if (operation !== null) details.operation = operation;
        if (requiredSize !== null) details.requiredSize = requiredSize;
        if (availableSize !== null) details.availableSize = availableSize;
        
        super(message, 'BUFFER_ERROR', details);
        this.name = 'MqttBufferException';
        this.operation = operation;
        this.requiredSize = requiredSize;
        this.availableSize = availableSize;
    }
}

/**
 * 异常工厂类
 * 提供便捷的异常创建方法
 */
class MqttExceptionFactory {
    /**
     * 创建验证异常
     * @param {string} field - 字段名
     * @param {*} value - 字段值
     * @param {string} rule - 验证规则
     * @param {string} customMessage - 自定义消息
     * @returns {MqttValidationException} 验证异常
     */
    static createValidationError(field, value, rule, customMessage = null) {
        const message = customMessage || `Validation failed for field '${field}': ${rule}`;
        return new MqttValidationException(message, field, value, rule);
    }

    /**
     * 创建长度超限异常
     * @param {string} field - 字段名
     * @param {number} actualLength - 实际长度
     * @param {number} maxLength - 最大长度
     * @returns {MqttTooLongFrameException} 长度超限异常
     */
    static createTooLongError(field, actualLength, maxLength) {
        const message = `${field} too long: ${actualLength} bytes (max: ${maxLength})`;
        return new MqttTooLongFrameException(message, actualLength, maxLength, field);
    }

    /**
     * 创建标识符拒绝异常
     * @param {string} identifier - 标识符
     * @param {string} reason - 拒绝原因
     * @returns {MqttIdentifierRejectedException} 标识符拒绝异常
     */
    static createIdentifierRejectedError(identifier, reason) {
        const message = `Identifier rejected: ${identifier} (${reason})`;
        return new MqttIdentifierRejectedException(message, identifier, reason);
    }

    /**
     * 创建协议异常
     * @param {string} message - 错误消息
     * @param {number} messageType - 消息类型
     * @param {string} protocolVersion - 协议版本
     * @returns {MqttProtocolException} 协议异常
     */
    static createProtocolError(message, messageType = null, protocolVersion = null) {
        return new MqttProtocolException(message, messageType, protocolVersion);
    }

    /**
     * 创建缓冲区异常
     * @param {string} operation - 操作类型
     * @param {number} requiredSize - 所需大小
     * @param {number} availableSize - 可用大小
     * @returns {MqttBufferException} 缓冲区异常
     */
    static createBufferError(operation, requiredSize, availableSize) {
        const message = `Buffer ${operation} failed: required ${requiredSize} bytes, available ${availableSize} bytes`;
        return new MqttBufferException(message, operation, requiredSize, availableSize);
    }

    /**
     * 从普通错误创建MQTT异常
     * @param {Error} error - 原始错误
     * @param {string} context - 错误上下文
     * @returns {MqttEncodingException} MQTT异常
     */
    static fromError(error, context = null) {
        const message = context ? `${context}: ${error.message}` : error.message;
        const exception = new MqttEncodingException(message, 'WRAPPED_ERROR', {
            originalError: error.name,
            originalMessage: error.message,
            context
        });
        
        // 保留原始堆栈跟踪
        if (error.stack) {
            exception.stack = error.stack;
        }
        
        return exception;
    }
}

/**
 * 错误处理工具类
 * 提供错误处理和调试的实用方法
 */
class MqttErrorHandler {
    /**
     * 格式化错误信息用于日志记录
     * @param {Error} error - 错误对象
     * @param {Object} context - 上下文信息
     * @returns {string} 格式化的错误信息
     */
    static formatErrorForLogging(error, context = {}) {
        let formatted = `[${new Date().toISOString()}] `;
        
        if (error instanceof MqttEncodingException) {
            formatted += error.getFormattedMessage();
        } else {
            formatted += `${error.name}: ${error.message}`;
        }
        
        if (Object.keys(context).length > 0) {
            formatted += `\nContext: ${JSON.stringify(context, null, 2)}`;
        }
        
        if (error.stack) {
            formatted += `\nStack Trace:\n${error.stack}`;
        }
        
        return formatted;
    }

    /**
     * 检查错误是否为可恢复的错误
     * @param {Error} error - 错误对象
     * @returns {boolean} 是否可恢复
     */
    static isRecoverableError(error) {
        if (!(error instanceof MqttEncodingException)) {
            return false;
        }
        
        // 验证错误和长度错误通常是可恢复的
        const recoverableCodes = ['VALIDATION_ERROR', 'TOO_LONG_FRAME', 'IDENTIFIER_REJECTED'];
        return recoverableCodes.includes(error.code);
    }

    /**
     * 获取错误的严重程度
     * @param {Error} error - 错误对象
     * @returns {string} 严重程度 ('low', 'medium', 'high', 'critical')
     */
    static getErrorSeverity(error) {
        if (!(error instanceof MqttEncodingException)) {
            return 'medium';
        }
        
        switch (error.code) {
            case 'VALIDATION_ERROR':
            case 'TOO_LONG_FRAME':
                return 'low';
            case 'IDENTIFIER_REJECTED':
            case 'PROTOCOL_ERROR':
                return 'medium';
            case 'BUFFER_ERROR':
            case 'DECODER_ERROR':
                return 'high';
            default:
                return 'medium';
        }
    }

    /**
     * 创建错误报告
     * @param {Error} error - 错误对象
     * @param {Object} context - 上下文信息
     * @returns {Object} 错误报告
     */
    static createErrorReport(error, context = {}) {
        const report = {
            timestamp: new Date().toISOString(),
            severity: this.getErrorSeverity(error),
            recoverable: this.isRecoverableError(error),
            error: {
                name: error.name,
                message: error.message,
                code: error.code || 'UNKNOWN',
                stack: error.stack
            },
            context
        };
        
        if (error instanceof MqttEncodingException) {
            report.error.details = error.details;
            report.error.formattedMessage = error.getFormattedMessage();
        }
        
        return report;
    }
}

// 导出异常类和工具
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        MqttEncodingException,
        MqttValidationException,
        MqttTooLongFrameException,
        MqttIdentifierRejectedException,
        MqttDecoderException,
        MqttProtocolException,
        MqttBufferException,
        MqttExceptionFactory,
        MqttErrorHandler
    };
} else if (typeof window !== 'undefined') {
    window.MqttEncodingException = MqttEncodingException;
    window.MqttValidationException = MqttValidationException;
    window.MqttTooLongFrameException = MqttTooLongFrameException;
    window.MqttIdentifierRejectedException = MqttIdentifierRejectedException;
    window.MqttDecoderException = MqttDecoderException;
    window.MqttProtocolException = MqttProtocolException;
    window.MqttBufferException = MqttBufferException;
    window.MqttExceptionFactory = MqttExceptionFactory;
    window.MqttErrorHandler = MqttErrorHandler;
}