/**
 * Protocol Buffers Message Definitions
 * 
 * JavaScript implementation of WildFire IM protobuf messages
 * Compatible with server-side protobuf definitions
 */

// Message Type Enumeration
const MessageType = {
    // Connection messages
    CONNECT: 1,
    CONNECT_RESPONSE: 2,
    
    // Message operations
    SEND_MESSAGE: 10,
    RECEIVE_MESSAGE: 11,
    GET_MESSAGES: 12,
    
    // User operations
    GET_USER_INFO: 20,
    SEARCH_USER: 21,
    
    // Conversation operations
    GET_CONVERSATION_LIST: 30,
    
    // Group operations
    CREATE_GROUP: 40,
    
    // Friend operations
    SEND_FRIEND_REQUEST: 50,
    
    // Settings
    SET_USER_SETTING: 60,
    
    // Media
    UPLOAD_MEDIA: 70,
    
    // System messages
    HEARTBEAT: 80,
    ACK: 90,
    ERROR: 99
};

// Conversation Type Enumeration
const ConversationType = {
    SINGLE: 0,
    GROUP: 1,
    CHANNEL: 2
};

// Content Type Enumeration
const ContentType = {
    TEXT: 0,
    IMAGE: 1,
    VOICE: 2,
    VIDEO: 3,
    FILE: 4,
    LOCATION: 5,
    LINK: 6,
    STICKER: 7,
    COMPOSITE_MESSAGE: 8
};

// Platform Type Enumeration
const PlatformType = {
    UNKNOWN: 0,
    APP: 1,
    H5_WEB: 2,
    MINI_PROGRAM: 3
};

/**
 * Base WildFire Message Class
 */
class WildfireMessage {
    constructor() {
        this.type = MessageType.HEARTBEAT;
        this.messageId = '';
        this.timestamp = 0;
        this.fromUser = '';
        this.payload = '';
    }
    
    /**
     * Check if message type is valid
     * @returns {boolean} True if valid
     */
    isValidType() {
        return Object.values(MessageType).includes(this.type);
    }
    
    /**
     * Convert to JSON string
     * @returns {string} JSON representation
     */
    toJSON() {
        return JSON.stringify({
            type: this.type,
            messageId: this.messageId,
            timestamp: this.timestamp,
            fromUser: this.fromUser,
            payload: this.payload
        });
    }
    
    /**
     * Create from JSON string
     * @param {string} jsonStr JSON string
     * @returns {WildfireMessage} Message instance
     */
    static fromJSON(jsonStr) {
        const data = JSON.parse(jsonStr);
        const message = new WildfireMessage();
        Object.assign(message, data);
        return message;
    }
}

/**
 * Connect Request Message
 */
class ConnectRequest {
    constructor() {
        this.userId = '';
        this.token = '';
        this.clientId = '';
        this.platform = 'H5';
        this.version = '1.0.0';
    }
}

/**
 * Connect Response Message
 */
class ConnectResponse {
    constructor() {
        this.success = false;
        this.sessionId = '';
        this.serverTime = 0;
        this.errorCode = 0;
        this.errorMessage = '';
    }
}

/**
 * Conversation Definition
 */
class Conversation {
    constructor() {
        this.type = ConversationType.SINGLE;
        this.target = '';
        this.line = 0;
    }
}

/**
 * Message Content Definition
 */
class MessageContent {
    constructor() {
        this.type = ContentType.TEXT;
        this.text = '';
        this.data = null;
        this.extra = {};
    }
}

/**
 * Send Message Request
 */
class SendMessageRequest {
    constructor() {
        this.conversation = new Conversation();
        this.content = new MessageContent();
        this.toUsers = [];
    }
}

/**
 * Get Messages Request
 */
class GetMessagesRequest {
    constructor() {
        this.conversation = new Conversation();
        this.count = 20;
        this.fromIndex = 0;
    }
}

/**
 * Get User Info Request
 */
class GetUserInfoRequest {
    constructor() {
        this.userId = '';
    }
}

/**
 * Get Conversation List Request
 */
class GetConversationListRequest {
    constructor() {
        this.types = [ConversationType.SINGLE, ConversationType.GROUP];
        this.lines = [0];
    }
}

/**
 * Create Group Request
 */
class CreateGroupRequest {
    constructor() {
        this.groupName = '';
        this.memberIds = [];
        this.groupPortrait = '';
        this.groupExtra = '';
    }
}

/**
 * Send Friend Request
 */
class SendFriendRequestRequest {
    constructor() {
        this.targetUserId = '';
        this.reason = '';
    }
}

/**
 * Set User Setting Request
 */
class SetUserSettingRequest {
    constructor() {
        this.key = '';
        this.value = '';
    }
}

/**
 * Search User Request
 */
class SearchUserRequest {
    constructor() {
        this.keyword = '';
        this.searchType = 0; // 0: fuzzy, 1: exact
    }
}

/**
 * Upload Media Request
 */
class UploadMediaRequest {
    constructor() {
        this.mediaType = ContentType.IMAGE;
        this.mediaData = null;
        this.fileName = '';
    }
}

/**
 * Heartbeat Request
 */
class HeartbeatRequest {
    constructor() {
        this.ping = Date.now();
    }
}

/**
 * ACK Request
 */
class AckRequest {
    constructor() {
        this.messageId = '';
        this.success = true;
    }
}

/**
 * Error Response
 */
class ErrorResponse {
    constructor() {
        this.errorCode = 0;
        this.errorMessage = '';
        this.details = {};
    }
}

// Export all classes and constants
if (typeof module !== 'undefined' && module.exports) {
    // Node.js environment
    module.exports = {
        MessageType,
        ConversationType,
        ContentType,
        PlatformType,
        WildfireMessage,
        ConnectRequest,
        ConnectResponse,
        Conversation,
        MessageContent,
        SendMessageRequest,
        GetMessagesRequest,
        GetUserInfoRequest,
        GetConversationListRequest,
        CreateGroupRequest,
        SendFriendRequestRequest,
        SetUserSettingRequest,
        SearchUserRequest,
        UploadMediaRequest,
        HeartbeatRequest,
        AckRequest,
        ErrorResponse
    };
} else {
    // Browser environment
    window.WildfireProtobuf = {
        MessageType,
        ConversationType,
        ContentType,
        PlatformType,
        WildfireMessage,
        ConnectRequest,
        ConnectResponse,
        Conversation,
        MessageContent,
        SendMessageRequest,
        GetMessagesRequest,
        GetUserInfoRequest,
        GetConversationListRequest,
        CreateGroupRequest,
        SendFriendRequestRequest,
        SetUserSettingRequest,
        SearchUserRequest,
        UploadMediaRequest,
        HeartbeatRequest,
        AckRequest,
        ErrorResponse
    };
}