/**
 * MQTT数据编码器
 * 
 * 提供MQTT协议数据的编码和解码功能
 * 与Java MqttDecoder完全兼容
 */

(function(global) {
'use strict';

// 检查是否已经加载过，避免重复声明
if (global.MqttDataEncoders) {
    return;
}

/**
 * MQTT数据编码器类
 */
class MqttDataEncoders {
    constructor() {
        this.maxMessageSize = 268435455; // MQTT协议最大消息大小
    }

    /**
     * 编码可变长度整数（MQTT协议标准）
     * @param {number} value - 要编码的整数值
     * @returns {Uint8Array} 编码后的字节数组
     */
    encodeVariableByteInteger(value) {
        if (value < 0 || value > this.maxMessageSize) {
            throw new Error(`值超出范围: ${value}`);
        }

        const bytes = [];
        do {
            let encodedByte = value % 128;
            value = Math.floor(value / 128);
            if (value > 0) {
                encodedByte |= 128; // 设置继续位
            }
            bytes.push(encodedByte);
        } while (value > 0);

        return new Uint8Array(bytes);
    }

    /**
     * 解码可变长度整数
     * @param {Uint8Array} buffer - 输入缓冲区
     * @param {number} offset - 起始偏移量
     * @returns {Object} {value: number, bytesRead: number}
     */
    decodeVariableByteInteger(buffer, offset = 0) {
        let value = 0;
        let multiplier = 1;
        let bytesRead = 0;
        let byte;

        do {
            if (offset + bytesRead >= buffer.length) {
                throw new Error('缓冲区长度不足');
            }
            
            byte = buffer[offset + bytesRead];
            bytesRead++;
            
            value += (byte & 127) * multiplier;
            multiplier *= 128;
            
            if (bytesRead > 4) {
                throw new Error('可变长度整数超过4字节');
            }
        } while ((byte & 128) !== 0);

        return { value, bytesRead };
    }

    /**
     * 编码UTF-8字符串（带长度前缀）
     * @param {string} str - 要编码的字符串
     * @returns {Uint8Array} 编码后的字节数组
     */
    encodeString(str) {
        if (typeof str !== 'string') {
            throw new Error('输入必须是字符串');
        }

        // 转换为UTF-8字节
        const utf8Bytes = new TextEncoder().encode(str);
        
        // 检查长度限制
        if (utf8Bytes.length > 65535) {
            throw new Error(`字符串过长: ${utf8Bytes.length} 字节`);
        }

        // 创建结果数组：2字节长度 + UTF-8字节
        const result = new Uint8Array(2 + utf8Bytes.length);
        
        // 写入长度（大端序）
        result[0] = (utf8Bytes.length >> 8) & 0xFF;
        result[1] = utf8Bytes.length & 0xFF;
        
        // 写入UTF-8字节
        result.set(utf8Bytes, 2);

        return result;
    }

    /**
     * 解码UTF-8字符串
     * @param {Uint8Array} buffer - 输入缓冲区
     * @param {number} offset - 起始偏移量
     * @returns {Object} {value: string, bytesRead: number}
     */
    decodeString(buffer, offset = 0) {
        if (buffer.length < offset + 2) {
            throw new Error('缓冲区长度不足以读取字符串长度');
        }

        // 读取字符串长度（大端序）
        const length = (buffer[offset] << 8) | buffer[offset + 1];
        
        if (buffer.length < offset + 2 + length) {
            throw new Error('缓冲区长度不足以读取完整字符串');
        }

        // 提取UTF-8字节并解码
        const utf8Bytes = buffer.slice(offset + 2, offset + 2 + length);
        const value = new TextDecoder().decode(utf8Bytes);

        return {
            value,
            bytesRead: 2 + length
        };
    }

    /**
     * 编码二进制数据（带长度前缀）
     * @param {Uint8Array} data - 要编码的二进制数据
     * @returns {Uint8Array} 编码后的字节数组
     */
    encodeBinaryData(data) {
        if (!(data instanceof Uint8Array)) {
            throw new Error('输入必须是Uint8Array');
        }

        if (data.length > 65535) {
            throw new Error(`二进制数据过长: ${data.length} 字节`);
        }

        // 创建结果数组：2字节长度 + 二进制数据
        const result = new Uint8Array(2 + data.length);
        
        // 写入长度（大端序）
        result[0] = (data.length >> 8) & 0xFF;
        result[1] = data.length & 0xFF;
        
        // 写入二进制数据
        result.set(data, 2);

        return result;
    }

    /**
     * 解码二进制数据
     * @param {Uint8Array} buffer - 输入缓冲区
     * @param {number} offset - 起始偏移量
     * @returns {Object} {value: Uint8Array, bytesRead: number}
     */
    decodeBinaryData(buffer, offset = 0) {
        if (buffer.length < offset + 2) {
            throw new Error('缓冲区长度不足以读取数据长度');
        }

        // 读取数据长度（大端序）
        const length = (buffer[offset] << 8) | buffer[offset + 1];
        
        if (buffer.length < offset + 2 + length) {
            throw new Error('缓冲区长度不足以读取完整数据');
        }

        // 提取二进制数据
        const value = buffer.slice(offset + 2, offset + 2 + length);

        return {
            value,
            bytesRead: 2 + length
        };
    }

    /**
     * 编码16位整数（大端序）
     * @param {number} value - 要编码的16位整数
     * @returns {Uint8Array} 编码后的2字节数组
     */
    encodeMsbLsb(value) {
        if (value < 0 || value > 65535) {
            throw new Error(`值超出范围: ${value}`);
        }

        return new Uint8Array([
            (value >> 8) & 0xFF,  // MSB (高字节)
            value & 0xFF          // LSB (低字节)
        ]);
    }

    /**
     * 解码16位整数（大端序）
     * @param {Uint8Array} buffer - 输入缓冲区
     * @param {number} offset - 起始偏移量
     * @returns {Object} {value: number, bytesRead: number}
     */
    decodeMsbLsb(buffer, offset = 0) {
        if (buffer.length < offset + 2) {
            throw new Error('缓冲区长度不足以读取16位整数');
        }

        const value = (buffer[offset] << 8) | buffer[offset + 1];
        
        return {
            value,
            bytesRead: 2
        };
    }

    /**
     * 组合多个字节数组
     * @param {Uint8Array[]} arrays - 要组合的字节数组列表
     * @returns {Uint8Array} 组合后的字节数组
     */
    combineByteArrays(arrays) {
        const totalLength = arrays.reduce((sum, arr) => sum + arr.length, 0);
        const result = new Uint8Array(totalLength);

        let offset = 0;
        for (const arr of arrays) {
            result.set(arr, offset);
            offset += arr.length;
        }

        return result;
    }

    /**
     * 将字节数组转换为十六进制字符串（用于调试）
     * @param {Uint8Array} bytes - 字节数组
     * @returns {string} 十六进制字符串
     */
    bytesToHex(bytes) {
        return Array.from(bytes)
            .map(b => b.toString(16).padStart(2, '0'))
            .join(' ');
    }

    /**
     * 将十六进制字符串转换为字节数组
     * @param {string} hex - 十六进制字符串
     * @returns {Uint8Array} 字节数组
     */
    hexToBytes(hex) {
        const cleanHex = hex.replace(/\s+/g, '');
        const bytes = [];
        
        for (let i = 0; i < cleanHex.length; i += 2) {
            bytes.push(parseInt(cleanHex.substr(i, 2), 16));
        }
        
        return new Uint8Array(bytes);
    }
}

// 创建全局实例
const mqttDataEncoders = new MqttDataEncoders();

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        MqttDataEncoders,
        mqttDataEncoders
    };
} else {
    global.MqttDataEncoders = MqttDataEncoders;
    global.mqttDataEncoders = mqttDataEncoders;
}

})(typeof window !== 'undefined' ? window : globalThis);