{
    "name" : "地球岛",
    "appid" : "__UNI__35DD0AA",
    "description" : "地球岛，让世界更美好",
    "versionName" : "********",
    "versionCode" : 10350,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "optimization" : {
            "minifyJS" : "terser", // 压缩 JS
            "minifyCSS" : true, // 压缩 CSS
            "removeConsole" : true // 去掉 console.*
        },
        "splashscreen" : {
            "alwaysShowBeforeRender" : false,
            "waiting" : false,
            "autoclose" : true,
            "delay" : 0,
            "backgroundColor" : "#0074f8", // 设置启动背景颜色（十六进制颜色值）
            "androidStyle" : "default",
            "android" : {
                "hdpi" : "static/image/login/1.png",
                "xhdpi" : "static/image/login/1.png",
                "xxhdpi" : "static/image/login/1.png"
            },
            "ios" : {
                "iphone" : {
                    "portrait-896h@3x" : "static/image/login/1500.png",
                    "portrait-896h@2x" : "static/image/login/1500.png",
                    "landscape-896h@3x" : "static/image/login/1500.png",
                    "landscape-896h@2x" : "static/image/login/1500.png",
                    "portrait-844h@3x" : "static/image/login/1500.png",
                    "portrait-812h@3x" : "static/image/login/1500.png",
                    "landscape-844h@3x" : "static/image/login/1500.png",
                    "landscape-812h@3x" : "static/image/login/1500.png"
                }
            }
        },
        /* 模块配置 */
        "modules" : {
            "VideoPlayer" : {},
            "Record" : {},
            "Camera" : {},
            "Share" : {},
            "Push" : {},
            "OAuth" : {},
            "Geolocation" : {},
            "Barcode" : {},
            "Contacts" : {},
            "Payment" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\"/>",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ],
                "targetSdkVersion" : 33,
                "minSdkVersion" : 21
            },
            /* ios打包配置 */
            "ios" : {
                "privacyDescription" : {
                    "NSCameraUsageDescription" : "视频通话",
                    "NSMicrophoneUsageDescription" : "音频通话及语音消息",
                    "NSPhotoLibraryUsageDescription" : "发送图片消息",
                    "NSLocationWhenInUseUsageDescription" : "获取您当前位置给您提供天气服务、动态定位、发现附近的朋友。"
                },
                "dSYMs" : false,
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [
                            "applinks:static-mp-2a4767c0-f2b1-4e3b-a548-9bdf9090f5cc.next.bspapp.com"
                        ]
                    }
                }
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "ad" : {},
                "push" : {
                    "unipush" : {
                        "offline" : true,
                        "hms" : {}
                    }
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wxebe732d88afb8986",
                        "UniversalLinks" : "https://static-mp-2a4767c0-f2b1-4e3b-a548-9bdf9090f5cc.next.bspapp.com/uni-universallinks/__UNI__35DD0AA/"
                    }
                },
                "oauth" : {
                    "weixin" : {
                        "appid" : "wxebe732d88afb8986",
                        "appsecret" : "640d08d3c2f503d3632a5901bb7a37b6",
                        "UniversalLinks" : "https://static-mp-2a4767c0-f2b1-4e3b-a548-9bdf9090f5cc.next.bspapp.com/uni-universallinks/__UNI__35DD0AA/"
                    }
                },
                "maps" : {
                    "amap" : {
                        "name" : "amap_17621722633BsfVvAg1z",
                        "appkey_ios" : "700b63d5b780fac0b95504289cac7e31",
                        "appkey_android" : "e98aea8f9bfd66d9494f5c62edea2871"
                    }
                },
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    },
                    "amap" : {
                        "name" : "amap_17621722633BsfVvAg1z",
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "700b63d5b780fac0b95504289cac7e31",
                        "appkey_android" : "e98aea8f9bfd66d9494f5c62edea2871"
                    }
                },
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "android" ],
                        "appid" : "wxebe732d88afb8986",
                        "UniversalLinks" : "https://static-mp-2a4767c0-f2b1-4e3b-a548-9bdf9090f5cc.next.bspapp.com/uni-universallinks/__UNI__35DD0AA/"
                    },
                    "alipay" : {
                        "__platform__" : [ "android" ]
                    },
                    "appleiap" : {}
                }
            },
            "icons" : {
                "android" : {
                    "xxxhdpi" : "unpackage/res/icons/192x192.png",
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024.png",
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120.png",
                        "app@3x" : "unpackage/res/icons/180.png",
                        "notification@2x" : "unpackage/res/icons/40.png",
                        "notification@3x" : "unpackage/res/icons/60.png",
                        "settings@2x" : "unpackage/res/icons/58.png",
                        "settings@3x" : "unpackage/res/icons/87.png",
                        "spotlight@2x" : "unpackage/res/icons/80.png",
                        "spotlight@3x" : "unpackage/res/icons/120.png"
                    },
                    "ipad" : {
                        "app" : "",
                        "app@2x" : "",
                        "notification" : "",
                        "notification@2x" : "",
                        "proapp@2x" : "",
                        "settings" : "",
                        "settings@2x" : "",
                        "spotlight" : "",
                        "spotlight@2x" : ""
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "default",
                "useOriginalMsgbox" : false,
                "android" : {
                    "hdpi" : "static/image/login/1500.png",
                    "xhdpi" : "static/image/login/1500.png",
                    "xxhdpi" : "static/image/login/1500.png"
                },
                "iosStyle" : "common"
            }
        },
        "nativePlugins" : {
            "AliCloud-NirvanaPns" : {
                "__plugin_info__" : {
                    "name" : "阿里云号码认证SDK",
                    "description" : "阿里云号码认证SDK",
                    "platforms" : "Android,iOS",
                    "url" : "",
                    "android_package_name" : "",
                    "ios_bundle_id" : "",
                    "isCloud" : false,
                    "bought" : -1,
                    "pid" : "",
                    "parameters" : {}
                }
            },
            "wf-uni-wfc-avclient" : {
                "__plugin_info__" : {
                    "name" : "【官方】野火实时音视频RTC插件",
                    "description" : "野火官方发布的移动端实时音视频插件，包括Android和iOS平台，拥有完善的Demo，服务器和移动端免费使用，支持多人音频和多人视频通话，让您一步拥有完美的即时通讯软件。",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=15619",
                    "android_package_name" : "",
                    "ios_bundle_id" : "com.yk.cl",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "15619",
                    "parameters" : {}
                }
            },
            "wf-uni-wfc-client" : {
                "__plugin_info__" : {
                    "name" : "【官方】野火即时通讯IM插件",
                    "description" : "野火官方发布移动端即时通讯插件，包括Android和iOS平台，拥有完善的Demo，支持单人、群组和聊天室会话，支持文字、语音、图片等常见消息，服务器和移动端免费使用，让您一步拥有完美的即时通讯软件。",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=7895",
                    "android_package_name" : "",
                    "ios_bundle_id" : "com.yk.cl",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "7895",
                    "parameters" : {}
                }
            },
            "ZS-Bugly" : {
                "__plugin_info__" : {
                    "name" : "bugly崩溃闪退日志收集",
                    "description" : "一键对接腾讯Bugly SDK 手机使用APP过程中的闪退 崩溃的日志信息等",
                    "platforms" : "Android",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=19115",
                    "android_package_name" : "",
                    "ios_bundle_id" : "",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "19115",
                    "parameters" : {}
                }
            }
        },
        "nvueLaunchMode" : "",
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于查找附近的人"
            }
        },
        "uniStatistics" : {
            "enable" : true
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx1b5f9aaca22d6163",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true,
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于查找附近的人"
            }
        },
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-baidu" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "uniStatistics" : {
        "enable" : true,
        "version" : "2"
    },
    "vueVersion" : "3",
    "_spaceID" : "mp-2a4767c0-f2b1-4e3b-a548-9bdf9090f5cc",
    "app-harmony" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "h5" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-harmony" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-jd" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-kuaishou" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-lark" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-qq" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-xhs" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "quickapp-webview-huawei" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "quickapp-webview-union" : {
        "uniStatistics" : {
            "enable" : true
        }
    }
}
