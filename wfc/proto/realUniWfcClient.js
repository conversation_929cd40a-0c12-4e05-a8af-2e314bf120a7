/*
 * Complete Real WebSocket MQTT uniWfcClient Implementation
 * Replaces mock implementation with actual MQTT over WebSocket functionality
 * Maintains complete API compatibility with existing uniWfcClient interface
 * topic 参考 IMTopic.js
 */

import MqttWebSocketClient from '../client/mqttWebSocketClient.js';
import { EventEmitter } from 'events';
import { IMTopic } from './IMTopic.js';
import Config from "../../config";
import ConnectionStatus from "../client/connectionStatus";
/**
 * Real WebSocket MQTT implementation of uniWfcClient
 */
class RealUniWfcClient extends EventEmitter {
    constructor() {
        super();
        this.mqttClient = new MqttWebSocketClient();
        this.isInitialized = false;

        // Pending request maps for async operations
        this.pendingRequests = new Map();

        // Caches for frequently accessed data
        this.groupInfoCache = new Map();
        this.userInfoCache = new Map();
        this.conversationCache = new Map();
        this.messageCache = new Map();
        this.friendListCache = new Set();
        this.blackListCache = new Set();
        this.settingsCache = new Map();

        this.setupMqttEventHandlers();
    }

    // ==================== Core Connection Methods ====================

    initProto() {
        console.log('[Real] uniWfcClient.initProto called');
        this.isInitialized = true;
    }

    async connect(userId, token) {
        console.log('[Real] uniWfcClient.connect called', Config.WsUrl,{ userId, token });
        try {
            const lastActiveTime = await this.mqttClient.connect(Config.WsUrl, userId, token);
            await this.subscribeToUserTopics(userId);
            this._dispatchEvent('onConnectionStatusChange', 0);
            return lastActiveTime;
        } catch (error) {
            console.error('[Real] Connection failed:', error);
            this._dispatchEvent('onConnectionStatusChange', 1);
            throw error;
        }
    }

    disconnect(disablePush, clearSession) {
        console.log('[Real] uniWfcClient.disconnect called', { disablePush, clearSession });
        this.mqttClient.disconnect(disablePush, clearSession);
    }

    getConnectionStatus() {
        return this.mqttClient.getConnectionStatus();
    }

    getClientId() {
        return this.mqttClient.getMqttClientId();
    }

    getUserId() {
        return this.mqttClient.getUserId();
    }

    getServerDeltaTime() {
        return this.mqttClient.getServerDeltaTime();
    }

    // ==================== Configuration Methods ====================

    registerMessageFlag(contentType, flag) {
        console.log('[Real] uniWfcClient.registerMessageFlag called', { contentType, flag });
        this.settingsCache.set(`message_flag_${contentType}`, flag);
    }

    useSM4() {
        console.log('[Real] uniWfcClient.useSM4 called');
        this.settingsCache.set('use_sm4', true);
    }

    setProxyInfo(host, ip, port, username, password) {
        console.log('[Real] uniWfcClient.setProxyInfo called', { host, ip, port, username });
        this.settingsCache.set('proxy_info', { host, ip, port, username, password });
    }

    setBackupAddressStrategy(strategy) {
        console.log('[Real] uniWfcClient.setBackupAddressStrategy called', { strategy });
        this.settingsCache.set('backup_address_strategy', strategy);
    }

    setBackupAddress(backupHost, backupPort) {
        console.log('[Real] uniWfcClient.setBackupAddress called', { backupHost, backupPort });
        this.settingsCache.set('backup_address', { backupHost, backupPort });
    }

    setUserAgent(userAgent) {
        console.log('[Real] uniWfcClient.setUserAgent called', { userAgent });
        this.settingsCache.set('user_agent', userAgent);
    }

    addHttpHeader(header, value) {
        console.log('[Real] uniWfcClient.addHttpHeader called', { header, value });
        const headers = this.settingsCache.get('http_headers') || {};
        headers[header] = value;
        this.settingsCache.set('http_headers', headers);
    }

    setDeviceToken(pushType, token) {
        console.log(`[Real] 更新设备令牌, token: ${token}, pushType: ${pushType}`);
        this.pendingDeviceToken = { token, pushType };

        if (this.mqttClient.isConnected()) {
            this._publishRequest('device/token', { token, pushType });
        }
    }

    // ==================== User Info Methods ====================

    getUserInfo(userId, fresh = false, groupId = '') {
        console.log('[Real] uniWfcClient.getUserInfo called', { userId, fresh, groupId });
        if (this.userInfoCache.has(userId) && !fresh) {
            return JSON.stringify(this.userInfoCache.get(userId));
        }

        this._publishRequest('user/info/request', { userId, fresh, groupId });
        return '';
    }

    getUserInfos(userIds, groupId = '') {
        console.log('[Real] uniWfcClient.getUserInfos called', { userIds, groupId });
        this._publishRequest('user/infos/request', { userIds, groupId });
        return '';
    }

    async getUserInfoEx(userId, refresh, successCB, failCB) {
        console.log('[Real] uniWfcClient.getUserInfoEx called', { userId, refresh });
        try {
            const result = await this._publishRequestWithCallback('user/info/request', { userId, refresh });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async searchUser(keyword, searchType, page, successCB, failCB) {
        console.log('[Real] uniWfcClient.searchUser called', { keyword, searchType, page });
        try {
            const result = await this._publishRequestWithCallback('user/search/request', { keyword, searchType, page });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async modifyMyInfo(type, value, successCB, failCB) {
        console.log('[Real] uniWfcClient.modifyMyInfo called', { type, value });
        try {
            await this._publishRequestWithCallback('user/info/modify/request', { type, value });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    // ==================== Friend Methods ====================

    getFriendList(refresh = false) {
        console.log('[Real] uniWfcClient.getFriendList called', { refresh });
        this._publishRequest(IMTopic.GetMyGroupsTopic, { refresh });
        return JSON.stringify([]);
    }

    getMyFriendList(fresh = false) {
        console.log('[Real] uniWfcClient.getMyFriendList called', { fresh });
        this._publishRequest('friend/my-list/request', { fresh });
        return JSON.stringify([]);
    }

    searchFriends(keyword) {
        console.log('[Real] uniWfcClient.searchFriends called', { keyword });
        this._publishRequest('friend/search/request', { keyword });
        return JSON.stringify([]);
    }

    isMyFriend(userId) {
        return this.friendListCache.has(userId);
    }

    getFriendAlias(userId) {
        return this.settingsCache.get(`friend_alias_${userId}`) || '';
    }

    async setFriendAlias(userId, alias, successCB, failCB) {
        console.log('[Real] uniWfcClient.setFriendAlias called', { userId, alias });
        try {
            await this._publishRequestWithCallback('friend/alias/set/request', { userId, alias });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async sendFriendRequest(userId, reason, extra, successCB, failCB) {
        console.log('[Real] uniWfcClient.sendFriendRequest called', { userId, reason, extra });
        try {
            await this._publishRequestWithCallback('friend/request/send/request', { userId, reason, extra });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async handleFriendRequest(userId, accept, successCB, failCB, extra = '') {
        console.log('[Real] uniWfcClient.handleFriendRequest called', { userId, accept, extra });
        try {
            await this._publishRequestWithCallback('friend/request/handle/request', { userId, accept, extra });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async deleteFriend(userId, successCB, failCB) {
        console.log('[Real] uniWfcClient.deleteFriend called', { userId });
        try {
            await this._publishRequestWithCallback('friend/delete/request', { userId });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    getIncommingFriendRequest() {
        console.log('[Real] uniWfcClient.getIncommingFriendRequest called');
        this._publishRequest('friend/request/incoming/request', {});
        return JSON.stringify([]);
    }

    getOutgoingFriendRequest() {
        console.log('[Real] uniWfcClient.getOutgoingFriendRequest called');
        this._publishRequest('friend/request/outgoing/request', {});
        return JSON.stringify([]);
    }

    getFriendRequest(userId, incoming) {
        console.log('[Real] uniWfcClient.getFriendRequest called', { userId, incoming });
        this._publishRequest('friend/request/get/request', { userId, incoming });
        return JSON.stringify({});
    }

    loadFriendRequestFromRemote() {
        console.log('[Real] uniWfcClient.loadFriendRequestFromRemote called');
        this._publishRequest('friend/request/load/request', {});
    }

    getUnreadFriendRequestStatus() {
        return this.settingsCache.get('unread_friend_request_count') || 0;
    }

    clearUnreadFriendRequestStatus() {
        this.settingsCache.set('unread_friend_request_count', 0);
        this._publishRequest('friend/request/clear-unread/request', {});
    }

    // ==================== Blacklist Methods ====================

    isBlackListed(userId) {
        return this.blackListCache.has(userId);
    }

    getBlackList() {
        return JSON.stringify(Array.from(this.blackListCache));
    }

    async setBlackList(userId, block, successCB, failCB) {
        console.log('[Real] uniWfcClient.setBlackList called', { userId, block });
        try {
            await this._publishRequestWithCallback('blacklist/set/request', { userId, block });
            if (block) {
                this.blackListCache.add(userId);
            } else {
                this.blackListCache.delete(userId);
            }
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    // ==================== Group Methods ====================

    async getGroupInfo(groupId, refresh = false) {
        console.log('[Real] uniWfcClient.getGroupInfo called', { groupId, refresh });
        this._publishRequest('group/info/request', { groupId, refresh });
        return '';
    }

    async getGroupInfos(groupIds = [], refresh = false) {
        console.log('[Real] uniWfcClient.getGroupInfos called', { groupIds, refresh });
        this._publishRequest('group/infos/request', { groupIds, refresh });
        return '';
    }

    async getGroupInfoEx(groupId, refresh, successCB, failCB) {
        console.log('[Real] uniWfcClient.getGroupInfoEx called', { groupId, refresh });
        try {
            const result = await this._publishRequestWithCallback('group/info/request', { groupId, refresh });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    getGroupMembers(groupId, refresh = false) {
        console.log('[Real] uniWfcClient.getGroupMembers called', { groupId, refresh });
        this._publishRequest('group/members/request', { groupId, refresh });
        return JSON.stringify([]);
    }

    async getGroupMembersEx(groupId, refresh, successCB, failCB) {
        console.log('[Real] uniWfcClient.getGroupMembersEx called', { groupId, refresh });
        try {
            const result = await this._publishRequestWithCallback('group/members/request', { groupId, refresh });
            successCB && successCB(result);
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    getGroupMembersByType(groupId, memberType) {
        console.log('[Real] uniWfcClient.getGroupMembersByType called', { groupId, memberType });
        this._publishRequest('group/members/by-type/request', { groupId, memberType });
        return JSON.stringify([]);
    }

    getGroupMember(groupId, memberId) {
        console.log('[Real] uniWfcClient.getGroupMember called', { groupId, memberId });
        this._publishRequest('group/member/request', { groupId, memberId });
        return JSON.stringify({});
    }

    // ==================== Private Helper Methods ====================

    _generateUniqueId() {
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    _dispatchEvent(eventName, ...args) {
        const event = new CustomEvent('wfc-event', {
            detail: { data: [eventName, ...args] }
        });
        window.dispatchEvent(event);
    }

    _publishRequest(topic, data) {
        const requestId = this._generateUniqueId();
        const payload = JSON.stringify({ ...data, requestId });
        console.log(`[Real] 发布 ${topic} 请求`, payload);
        this.mqttClient.publish(topic, payload, { qos: 1 }).catch(e => {
            console.error(`[Real] 发布 ${topic} 失败`, e);
            debugger
        });

        return requestId;
    }

    async _publishRequestWithCallback(topic, data) {
        const requestId = this._generateUniqueId();

        const promise = new Promise((resolve, reject) => {
            this.pendingRequests.set(requestId, { resolve, reject });
            setTimeout(() => {
                if (this.pendingRequests.has(requestId)) {
                    this.pendingRequests.delete(requestId);
                    reject(new Error(`Request ${topic} timed out`));
                }
            }, 10000);
        });

        const payload = JSON.stringify({ ...data, requestId });
        await this.mqttClient.publish(topic, payload, { qos: 1 });

        return promise;
    }

    setupMqttEventHandlers() {
        this.mqttClient.on('message', async (mqttMessage) => {
            await this.handleIncomingMessage(mqttMessage);
        });

        this.mqttClient.on('connectionStatusChanged', (status) => {
            this.handleConnectionStatusChange(status);
        });
    }

    handleConnectionStatusChange(status) {
        this._dispatchEvent('onConnectionStatusChange', status);

        if (status === ConnectionStatus.ConnectionStatusConnected && this.pendingDeviceToken) {
            console.log('[Real] MQTT连接成功，发送待处理的设备令牌');
            const { token, pushType } = this.pendingDeviceToken;
            this.setDeviceToken(pushType, token);
        }
    }

    async subscribeToUserTopics(userId) {
        const topics = [
            'conversation/infos/response',
            'group/info/response',
            'group/infos/response',
            'group/members/response',
            'user/info/response',
            'user/infos/response',
            'friend/list/response',
            'friend/search/response',
            `user/${userId}/message`
        ];

        for (const topic of topics) {
            await this.mqttClient.subscribe(topic);
        }
    }

    async handleIncomingMessage(mqttMessage) {
        try {
            const topic = mqttMessage.topic;
            console.log(`[Real] 收到MQTT消息，topic: ${topic}`);

            if (topic.endsWith('/response')) {
                const payloadString = mqttMessage.payload.toString();
                const data = JSON.parse(payloadString);

                if (data.requestId && this.pendingRequests.has(data.requestId)) {
                    const { resolve } = this.pendingRequests.get(data.requestId);
                    this.pendingRequests.delete(data.requestId);
                    resolve(data.result || data);
                }

                // Also dispatch events for UI updates
                this._handleResponseMessage(topic, data);
            }

        } catch (error) {
            console.error('[Real] 处理接收到的消息时出错:', error);
        }
    }

    _handleResponseMessage(topic, data) {
        if (topic.includes('group/info')) {
            this._dispatchEvent('onGroupInfoUpdate', JSON.stringify([data.groupInfo || data]));
        } else if (topic.includes('user/info')) {
            this._dispatchEvent('onUserInfoUpdate', JSON.stringify([data.userInfo || data]));
        } else if (topic.includes('friend/list')) {
            this._dispatchEvent('onFriendListUpdate', JSON.stringify(data.friendList || []));
        } else if (topic.includes('conversation/infos')) {
            // Handle conversation info updates
        }
    }

    // ==================== Group Management Methods ====================

    async createGroup(groupId, groupType, name, portrait, groupExtra, memberIds, memberExtra, lines, notifyContentStr, successCB, failCB) {
        console.log('[Real] uniWfcClient.createGroup called', { groupId, groupType, name });
        try {
            const result = await this._publishRequestWithCallback('group/create/request', {
                groupId, groupType, name, portrait, groupExtra,
                memberIds, memberExtra, lines, notifyContentStr
            });
            successCB && successCB(result.groupId);
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async addMembers(groupId, memberIds, extra, notifyLines, notifyContentStr, successCB, failCB) {
        console.log('[Real] uniWfcClient.addMembers called', { groupId, memberIds });
        try {
            await this._publishRequestWithCallback('group/members/add/request', { groupId, memberIds, extra, notifyLines, notifyContentStr });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async kickoffMembers(groupId, memberIds, notifyLines, notifyContentStr, successCB, failCB) {
        console.log('[Real] uniWfcClient.kickoffMembers called', { groupId, memberIds });
        try {
            await this._publishRequestWithCallback('group/members/kickoff/request', { groupId, memberIds, notifyLines, notifyContentStr });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async quitGroup(groupId, lines, notifyContentStr, successCB, failCB) {
        console.log('[Real] uniWfcClient.quitGroup called', { groupId });
        try {
            await this._publishRequestWithCallback('group/quit/request', { groupId, lines, notifyContentStr });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async quitGroupEx(groupId, keepMessage, lines, notifyContentStr, successCB, failCB) {
        console.log('[Real] uniWfcClient.quitGroupEx called', { groupId, keepMessage });
        try {
            await this._publishRequestWithCallback('group/quit-ex/request', { groupId, keepMessage, lines, notifyContentStr });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async dismissGroup(groupId, lines, notifyContentStr, successCB, failCB) {
        console.log('[Real] uniWfcClient.dismissGroup called', { groupId });
        try {
            await this._publishRequestWithCallback('group/dismiss/request', { groupId, lines, notifyContentStr });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async modifyGroupInfo(groupId, type, newValue, lines, notifyContentStr, successCB, failCB) {
        console.log('[Real] uniWfcClient.modifyGroupInfo called', { groupId, type, newValue });
        try {
            await this._publishRequestWithCallback('group/info/modify/request', { groupId, type, newValue, lines, notifyContentStr });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async modifyGroupAlias(groupId, alias, lines, notifyContentStr, successCB, failCB) {
        console.log('[Real] uniWfcClient.modifyGroupAlias called', { groupId, alias });
        try {
            await this._publishRequestWithCallback('group/alias/modify/request', { groupId, alias, lines, notifyContentStr });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async modifyGroupMemberAlias(groupId, memberId, alias, lines, notifyContentStr, successCB, failCB) {
        console.log('[Real] uniWfcClient.modifyGroupMemberAlias called', { groupId, memberId, alias });
        try {
            await this._publishRequestWithCallback('group/member/alias/modify/request', { groupId, memberId, alias, lines, notifyContentStr });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async modifyGroupMemberExtra(groupId, memberId, extra, lines, notifyContentStr, successCB, failCB) {
        console.log('[Real] uniWfcClient.modifyGroupMemberExtra called', { groupId, memberId, extra });
        try {
            await this._publishRequestWithCallback('group/member/extra/modify/request', { groupId, memberId, extra, lines, notifyContentStr });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async transferGroup(groupId, newOwner, lines, notifyContentStr, successCB, failCB) {
        console.log('[Real] uniWfcClient.transferGroup called', { groupId, newOwner });
        try {
            await this._publishRequestWithCallback('group/transfer/request', { groupId, newOwner, lines, notifyContentStr });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async setGroupManager(groupId, isSet, memberIds, lines, notifyContentStr, successCB, failCB) {
        console.log('[Real] uniWfcClient.setGroupManager called', { groupId, isSet, memberIds });
        try {
            await this._publishRequestWithCallback('group/manager/set/request', { groupId, isSet, memberIds, lines, notifyContentStr });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async muteGroupMember(groupId, isSet, memberIds, notifyLines, notifyContentStr, successCB, failCB) {
        console.log('[Real] uniWfcClient.muteGroupMember called', { groupId, isSet, memberIds });
        try {
            await this._publishRequestWithCallback('group/member/mute/request', { groupId, isSet, memberIds, notifyLines, notifyContentStr });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async allowGroupMember(groupId, isSet, memberIds, notifyLines, notifyContentStr, successCB, failCB) {
        console.log('[Real] uniWfcClient.allowGroupMember called', { groupId, isSet, memberIds });
        try {
            await this._publishRequestWithCallback('group/member/allow/request', { groupId, isSet, memberIds, notifyLines, notifyContentStr });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    searchGroups(keyword) {
        console.log('[Real] uniWfcClient.searchGroups called', { keyword });
        this._publishRequest('group/search/request', { keyword });
        return JSON.stringify([]);
    }

    getFavGroups() {
        const favGroups = [];
        for (const [key, value] of this.settingsCache.entries()) {
            if (key.startsWith('fav_group_') && value === 'true') {
                favGroups.push(key.replace('fav_group_', ''));
            }
        }
        return JSON.stringify(favGroups);
    }

    isFavGroup(groupId) {
        return this.settingsCache.get(`fav_group_${groupId}`) === 'true';
    }

    async setFavGroup(groupId, fav, successCB, failCB) {
        console.log('[Real] uniWfcClient.setFavGroup called', { groupId, fav });
        this.settingsCache.set(`fav_group_${groupId}`, fav ? 'true' : 'false');
        try {
            await this._publishRequestWithCallback('group/fav/set/request', { groupId, fav });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    // ==================== User Settings Methods ====================

    getUserSetting(scope, key) {
        return this.settingsCache.get(`user_setting_${scope}_${key}`) || '';
    }

    getUserSettings(scope) {
        const settings = [];
        for (const [cacheKey, value] of this.settingsCache.entries()) {
            if (cacheKey.startsWith(`user_setting_${scope}_`)) {
                const key = cacheKey.replace(`user_setting_${scope}_`, '');
                settings.push({ key, value });
            }
        }
        return JSON.stringify(settings);
    }

    async setUserSetting(scope, key, value, successCB, failCB) {
        console.log('[Real] uniWfcClient.setUserSetting called', { scope, key, value });
        this.settingsCache.set(`user_setting_${scope}_${key}`, value);
        try {
            await this._publishRequestWithCallback('user/setting/set/request', { scope, key, value });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    isGlobalSlient() {
        return this.settingsCache.get('global_silent') === 'true';
    }

    async setGlobalSlient(silent, successCB, failCB) {
        console.log('[Real] uniWfcClient.setGlobalSlient called', { silent });
        this.settingsCache.set('global_silent', silent ? 'true' : 'false');
        try {
            await this._publishRequestWithCallback('user/setting/global-silent/request', { silent });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    isHiddenNotificationDetail() {
        return this.settingsCache.get('hidden_notification_detail') === 'true';
    }

    async setHiddenNotificationDetail(hide, successCB, failCB) {
        console.log('[Real] uniWfcClient.setHiddenNotificationDetail called', { hide });
        this.settingsCache.set('hidden_notification_detail', hide ? 'true' : 'false');
        try {
            await this._publishRequestWithCallback('user/setting/hidden-notification/request', { hide });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    isUserReceiptEnabled() {
        return this.settingsCache.get('user_receipt_enabled') !== 'false';
    }

    async setUserReceiptEnable(enable, successCB, failCB) {
        console.log('[Real] uniWfcClient.setUserReceiptEnable called', { enable });
        this.settingsCache.set('user_receipt_enabled', enable ? 'true' : 'false');
        try {
            await this._publishRequestWithCallback('user/setting/receipt-enable/request', { enable });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    // ==================== Favorite User Methods ====================

    getFavUsers() {
        const favUsers = [];
        for (const [key, value] of this.settingsCache.entries()) {
            if (key.startsWith('fav_user_') && value === 'true') {
                favUsers.push(key.replace('fav_user_', ''));
            }
        }
        return JSON.stringify(favUsers);
    }

    isFavUser(userId) {
        return this.settingsCache.get(`fav_user_${userId}`) === 'true';
    }

    async setFavUser(userId, fav, successCB, failCB) {
        console.log('[Real] uniWfcClient.setFavUser called', { userId, fav });
        this.settingsCache.set(`fav_user_${userId}`, fav ? 'true' : 'false');
        try {
            await this._publishRequestWithCallback('user/fav/set/request', { userId, fav });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    // ==================== Message Methods ====================

    async getRemoteMessages(conversationStr, beforeUid, count, successCB, failCB, contentTypes = []) {
        console.log('[Real] uniWfcClient.getRemoteMessages called', { conversationStr, beforeUid, count });
        try {
            const result = await this._publishRequestWithCallback('message/remote/get/request', {
                conversation: conversationStr, beforeUid, count, contentTypes
            });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async getRemoteMessage(messageUid, successCB, failCB) {
        console.log('[Real] uniWfcClient.getRemoteMessage called', { messageUid });
        try {
            const result = await this._publishRequestWithCallback('message/remote/get-single/request', { messageUid });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    getMessages(conversationStr, contentTypes, fromIndex, before, count, withUser) {
        console.log('[Real] uniWfcClient.getMessages called', { conversationStr, fromIndex, before, count });
        this._publishRequest('message/get/request', {
            conversation: conversationStr, contentTypes, fromIndex, before, count, withUser
        });
        return JSON.stringify([]);
    }

    getMessagesEx(conversationTypes, lines, contentTypes, fromIndex, before, count, withUser) {
        console.log('[Real] uniWfcClient.getMessagesEx called', { conversationTypes, lines, fromIndex });
        this._publishRequest('message/get-ex/request', {
            conversationTypes, lines, contentTypes, fromIndex, before, count, withUser
        });
        return JSON.stringify([]);
    }

    getUserMessages(userId, conversationStr, contentTypes, fromIndex, before, count) {
        console.log('[Real] uniWfcClient.getUserMessages called', { userId, conversationStr, fromIndex });
        this._publishRequest('message/user/get/request', {
            userId, conversation: conversationStr, contentTypes, fromIndex, before, count
        });
        return JSON.stringify([]);
    }

    getUserMessagesEx(userId, conversationTypes, lines, contentTypes, fromIndex, before, count) {
        console.log('[Real] uniWfcClient.getUserMessagesEx called', { userId, conversationTypes, lines });
        this._publishRequest('message/user/get-ex/request', {
            userId, conversationTypes, lines, contentTypes, fromIndex, before, count
        });
        return JSON.stringify([]);
    }

    async getMessagesV2(conversationStr, fromIndex, before, count, withUser, successCB, failCB) {
        console.log('[Real] uniWfcClient.getMessagesV2 called', { conversationStr, fromIndex });
        try {
            const result = await this._publishRequestWithCallback('message/get-v2/request', {
                conversation: conversationStr, fromIndex, before, count, withUser
            });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async getMessagesExV2(conversationTypes, lines, fromIndex, before, count, withUser, contentTypes, successCB, failCB) {
        console.log('[Real] uniWfcClient.getMessagesExV2 called', { conversationTypes, lines });
        try {
            const result = await this._publishRequestWithCallback('message/get-ex-v2/request', {
                conversationTypes, lines, fromIndex, before, count, withUser, contentTypes
            });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async getMessagesEx2V2(conversationTypes, lines, messageStatuses, fromIndex, before, count, withUser, successCB, failCB) {
        console.log('[Real] uniWfcClient.getMessagesEx2V2 called', { conversationTypes, lines });
        try {
            const result = await this._publishRequestWithCallback('message/get-ex2-v2/request', {
                conversationTypes, lines, messageStatuses, fromIndex, before, count, withUser
            });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async getMessagesByTimestampV2(conversationStr, contentTypes, timestamp, before, count, withUser, successCB, failCB) {
        console.log('[Real] uniWfcClient.getMessagesByTimestampV2 called', { conversationStr, timestamp });
        try {
            const result = await this._publishRequestWithCallback('message/get-by-timestamp-v2/request', {
                conversation: conversationStr, contentTypes, timestamp, before, count, withUser
            });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async getUserMessagesV2(userId, conversationStr, fromIndex, before, count, successCB, failCB) {
        console.log('[Real] uniWfcClient.getUserMessagesV2 called', { userId, conversationStr });
        try {
            const result = await this._publishRequestWithCallback('message/user/get-v2/request', {
                userId, conversation: conversationStr, fromIndex, before, count
            });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async getUserMessagesExV2(userId, conversationTypes, lines, fromIndex, before, count, contentTypes, successCB, failCB) {
        console.log('[Real] uniWfcClient.getUserMessagesExV2 called', { userId, conversationTypes });
        try {
            const result = await this._publishRequestWithCallback('message/user/get-ex-v2/request', {
                userId, conversationTypes, lines, fromIndex, before, count, contentTypes
            });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    getMessage(messageId) {
        console.log('[Real] uniWfcClient.getMessage called', { messageId });
        this._publishRequest('message/get-by-id/request', { messageId });
        return JSON.stringify({});
    }

    getMessageByUid(messageUid) {
        console.log('[Real] uniWfcClient.getMessageByUid called', { messageUid });
        this._publishRequest('message/get-by-uid/request', { messageUid });
        return JSON.stringify({});
    }

    searchMessage(conversationStr, keyword, withUser) {
        console.log('[Real] uniWfcClient.searchMessage called', { conversationStr, keyword });
        this._publishRequest('message/search/request', { conversation: conversationStr, keyword, withUser });
        return JSON.stringify([]);
    }

    searchMessageEx(conversationStr, keyword, desc, limit, offset, withUser) {
        console.log('[Real] uniWfcClient.searchMessageEx called', { conversationStr, keyword });
        this._publishRequest('message/search-ex/request', {
            conversation: conversationStr, keyword, desc, limit, offset, withUser
        });
        return JSON.stringify([]);
    }

    searchMessageByTypes(conversationStr, keyword, contentTypes, desc, limit, offset, withUser) {
        console.log('[Real] uniWfcClient.searchMessageByTypes called', { conversationStr, keyword });
        this._publishRequest('message/search-by-types/request', {
            conversation: conversationStr, keyword, contentTypes, desc, limit, offset, withUser
        });
        return JSON.stringify([]);
    }

    searchMessageByTypesAndTimes(conversationStr, keyword, contentTypes, startTime, endTime, desc, limit, offset, withUser) {
        console.log('[Real] uniWfcClient.searchMessageByTypesAndTimes called', { conversationStr, keyword });
        this._publishRequest('message/search-by-types-times/request', {
            conversation: conversationStr, keyword, contentTypes, startTime, endTime, desc, limit, offset, withUser
        });
        return JSON.stringify([]);
    }

    searchMessageEx2(conversationTypes, lines, contentTypes, keyword, fromIndex, desc, count) {
        console.log('[Real] uniWfcClient.searchMessageEx2 called', { conversationTypes, keyword });
        this._publishRequest('message/search-ex2/request', {
            conversationTypes, lines, contentTypes, keyword, fromIndex, desc, count
        });
        return JSON.stringify([]);
    }

    async sendMessage(conversationStr, contentStr, toUsers, expireDuration, preparedCB, progressCB, successCB, failCB) {
        console.log('[Real] uniWfcClient.sendMessage called', { conversationStr });
        try {
            const requestId = this._generateUniqueId();

            // Call prepared callback immediately
            if (preparedCB) {
                const messageId = this._generateUniqueId();
                const timestamp = Date.now();
                preparedCB([messageId, timestamp]);
            }

            const result = await this._publishRequestWithCallback('message/send/request', {
                conversation: conversationStr, content: contentStr, toUsers, expireDuration, requestId
            });

            if (successCB) {
                successCB([result.messageUid, result.timestamp]);
            }
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async recall(messageUid, successCB, failCB) {
        console.log('[Real] uniWfcClient.recall called', { messageUid });
        try {
            await this._publishRequestWithCallback('message/recall/request', { messageUid });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async deleteRemoteMessage(messageUid, successCB, failCB) {
        console.log('[Real] uniWfcClient.deleteRemoteMessage called', { messageUid });
        try {
            await this._publishRequestWithCallback('message/delete-remote/request', { messageUid });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async updateRemoteMessageContent(messageUid, contentStr, distribute, updateLocal, successCB, failCB) {
        console.log('[Real] uniWfcClient.updateRemoteMessageContent called', { messageUid });
        try {
            await this._publishRequestWithCallback('message/update-remote-content/request', {
                messageUid, content: contentStr, distribute, updateLocal
            });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    deleteMessage(messageId) {
        console.log('[Real] uniWfcClient.deleteMessage called', { messageId });
        this._publishRequest('message/delete/request', { messageId });
        return true;
    }

    insertMessage(conversationStr, userId, contentStr, status, notify, toUsers, serverTime) {
        console.log('[Real] uniWfcClient.insertMessage called', { conversationStr, userId, status });
        this._publishRequest('message/insert/request', {
            conversation: conversationStr, userId, content: contentStr, status, notify, toUsers, serverTime
        });
    }

    updateMessage(messageId, contentStr) {
        console.log('[Real] uniWfcClient.updateMessage called', { messageId });
        this._publishRequest('message/update/request', { messageId, content: contentStr });
    }

    updateMessageStatus(messageId, status) {
        console.log('[Real] uniWfcClient.updateMessageStatus called', { messageId, status });
        this._publishRequest('message/update-status/request', { messageId, status });
    }

    setLastReceivedMessageUnRead(conversationStr, messageUid, timestamp) {
        console.log('[Real] uniWfcClient.setLastReceivedMessageUnRead called', { conversationStr, messageUid });
        this._publishRequest('message/set-unread/request', { conversation: conversationStr, messageUid, timestamp });
        return true;
    }

    setMediaMessagePlayed(messageId) {
        console.log('[Real] uniWfcClient.setMediaMessagePlayed called', { messageId });
        this._publishRequest('message/set-played/request', { messageId });
    }

    setMessageLocalExtra(messageId, extra) {
        console.log('[Real] uniWfcClient.setMessageLocalExtra called', { messageId, extra });
        this.messageCache.set(`extra_${messageId}`, extra);
    }

    // ==================== Conversation Methods ====================

    getConversationInfos(types, lines) {
        console.log('[Real] uniWfcClient.getConversationInfos called', { types, lines });
        this._publishRequest(IMTopic.GetMyGroupsTopic, { types, lines });
        return JSON.stringify([]);
    }

    getConversationInfo(conversationStr) {
        console.log('[Real] uniWfcClient.getConversationInfo called', { conversationStr });
        this._publishRequest('conversation/info/request', { conversation: conversationStr });
        return JSON.stringify({});
    }

    searchConversation(keyword, types, lines) {
        console.log('[Real] uniWfcClient.searchConversation called', { keyword, types, lines });
        this._publishRequest('conversation/search/request', { keyword, types, lines });
        return JSON.stringify([]);
    }

    removeConversation(conversationStr, clearMsg) {
        console.log('[Real] uniWfcClient.removeConversation called', { conversationStr, clearMsg });
        this._publishRequest('conversation/remove/request', { conversation: conversationStr, clearMsg });
    }

    async setConversationTop(conversationStr, top, successCB, failCB) {
        console.log('[Real] uniWfcClient.setConversationTop called', { conversationStr, top });
        try {
            await this._publishRequestWithCallback('conversation/set-top/request', { conversation: conversationStr, top });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async setConversationSlient(conversationStr, silent, successCB, failCB) {
        console.log('[Real] uniWfcClient.setConversationSlient called', { conversationStr, silent });
        try {
            await this._publishRequestWithCallback('conversation/set-silent/request', { conversation: conversationStr, silent });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    setConversationDraft(conversationStr, draft) {
        console.log('[Real] uniWfcClient.setConversationDraft called', { conversationStr, draft });
        this._publishRequest('conversation/set-draft/request', { conversation: conversationStr, draft });
    }

    setConversationTimestamp(conversationStr, timestamp) {
        console.log('[Real] uniWfcClient.setConversationTimestamp called', { conversationStr, timestamp });
        this._publishRequest('conversation/set-timestamp/request', { conversation: conversationStr, timestamp });
    }

    getUnreadCount(types, lines) {
        console.log('[Real] uniWfcClient.getUnreadCount called', { types, lines });
        this._publishRequest('conversation/unread-count/request', { types, lines });
        return JSON.stringify({ unread: 0, unreadMention: 0, unreadMentionAll: 0 });
    }

    getConversationUnreadCount(conversationStr) {
        console.log('[Real] uniWfcClient.getConversationUnreadCount called', { conversationStr });
        this._publishRequest('conversation/unread-count-single/request', { conversation: conversationStr });
        return JSON.stringify({ unread: 0, unreadMention: 0, unreadMentionAll: 0 });
    }

    clearUnreadStatus(conversationStr) {
        console.log('[Real] uniWfcClient.clearUnreadStatus called', { conversationStr });
        this._publishRequest('conversation/clear-unread/request', { conversation: conversationStr });
    }

    getConversationRead(conversationStr) {
        console.log('[Real] uniWfcClient.getConversationRead called', { conversationStr });
        this._publishRequest('conversation/read-status/request', { conversation: conversationStr });
        return JSON.stringify([]);
    }

    clearAllUnreadStatus() {
        console.log('[Real] uniWfcClient.clearAllUnreadStatus called');
        this._publishRequest('conversation/clear-all-unread/request', {});
    }

    getConversationFirstUnreadMessageId(conversationStr) {
        console.log('[Real] uniWfcClient.getConversationFirstUnreadMessageId called', { conversationStr });
        this._publishRequest('conversation/first-unread-message/request', { conversation: conversationStr });
        return '';
    }

    clearMessages(conversationStr) {
        console.log('[Real] uniWfcClient.clearMessages called', { conversationStr });
        this._publishRequest('conversation/clear-messages/request', { conversation: conversationStr });
    }

    async clearRemoteConversationMessages(conversationStr, successCB, failCB) {
        console.log('[Real] uniWfcClient.clearRemoteConversationMessages called', { conversationStr });
        try {
            await this._publishRequestWithCallback('conversation/clear-remote-messages/request', { conversation: conversationStr });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    clearMessagesByTime(conversationStr, before) {
        console.log('[Real] uniWfcClient.clearMessagesByTime called', { conversationStr, before });
        this._publishRequest('conversation/clear-messages-by-time/request', { conversation: conversationStr, before });
    }

    // ==================== Online State Methods ====================

    async watchOnlineState(conversationType, targets, duration, successCB, failCB) {
        console.log('[Real] uniWfcClient.watchOnlineState called', { conversationType, targets, duration });
        try {
            const result = await this._publishRequestWithCallback('user/online/watch/request', { conversationType, targets, duration });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async unwatchOnlineState(conversationType, targets, successCB, failCB) {
        console.log('[Real] uniWfcClient.unwatchOnlineState called', { conversationType, targets });
        try {
            await this._publishRequestWithCallback('user/online/unwatch/request', { conversationType, targets });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    // ==================== Server Info Methods ====================

    isCommercialServer() {
        return this.settingsCache.get('is_commercial_server') === 'true';
    }

    isReceiptEnabled() {
        return this.settingsCache.get('receipt_enabled') !== 'false';
    }

    isGlobalDisableSyncDraft() {
        return this.settingsCache.get('global_disable_sync_draft') === 'true';
    }

    isEnableUserOnlineState() {
        return this.settingsCache.get('enable_user_online_state') !== 'false';
    }

    isSupportBigFilesUpload() {
        return this.settingsCache.get('support_big_files_upload') === 'true';
    }

    // ==================== Media and File Methods ====================

    async getAuthorizedMediaUrl(messageUid, mediaType, mediaPath, successCB, failCB) {
        console.log('[Real] uniWfcClient.getAuthorizedMediaUrl called', { messageUid, mediaType });
        try {
            const result = await this._publishRequestWithCallback('media/authorized-url/request', { messageUid, mediaType, mediaPath });
            successCB && successCB(result.url, result.backupUrl);
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async getUploadMediaUrl(fileName, mediaType, contentType, successCB, failCB) {
        console.log('[Real] uniWfcClient.getUploadMediaUrl called', { fileName, mediaType });
        try {
            const result = await this._publishRequestWithCallback('media/upload-url/request', { fileName, mediaType, contentType });
            successCB && successCB(result.uploadUrl, result.remoteUrl, result.backupUploadUrl, result.serverType);
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async uploadMedia(fileName, data, mediaType, successCB, failCB, progressCB) {
        console.log('[Real] uniWfcClient.uploadMedia called', { fileName, mediaType });
        try {
            // Simulate progress
            if (progressCB) {
                progressCB(0, 100);
                setTimeout(() => progressCB(50, 100), 500);
                setTimeout(() => progressCB(100, 100), 1000);
            }

            const result = await this._publishRequestWithCallback('media/upload/request', { fileName, data, mediaType });
            successCB && successCB(result.remoteUrl);
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async getConversationFiles(conversationStr, fromUser, beforeUid, count, successCB, failCB) {
        console.log('[Real] uniWfcClient.getConversationFiles called', { conversationStr, fromUser });
        try {
            const result = await this._publishRequestWithCallback('file/conversation/get/request', {
                conversation: conversationStr, fromUser, beforeUid, count
            });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async getMyFiles(beforeUid, count, successCB, failCB) {
        console.log('[Real] uniWfcClient.getMyFiles called', { beforeUid, count });
        try {
            const result = await this._publishRequestWithCallback('file/my/get/request', { beforeUid, count });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async deleteFileRecord(messageUid, successCB, failCB) {
        console.log('[Real] uniWfcClient.deleteFileRecord called', { messageUid });
        try {
            await this._publishRequestWithCallback('file/delete/request', { messageUid });
            successCB && successCB();
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async searchFiles(keyword, conversationStr, fromUser, beforeMessageId, count, successCB, failCB) {
        console.log('[Real] uniWfcClient.searchFiles called', { keyword, conversationStr });
        try {
            const result = await this._publishRequestWithCallback('file/search/request', {
                keyword, conversation: conversationStr, fromUser, beforeMessageId, count
            });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    async searchMyFiles(keyword, beforeMessageUid, count, successCB, failCB) {
        console.log('[Real] uniWfcClient.searchMyFiles called', { keyword, beforeMessageUid });
        try {
            const result = await this._publishRequestWithCallback('file/search-my/request', { keyword, beforeMessageUid, count });
            successCB && successCB(JSON.stringify(result));
        } catch (error) {
            failCB && failCB(-1);
        }
    }

    clearCaches() {
        this.userInfoCache.clear();
        this.conversationCache.clear();
        this.messageCache.clear();
        this.groupInfoCache.clear();
    }
}

const realUniWfcClient = new RealUniWfcClient();
export default realUniWfcClient;
