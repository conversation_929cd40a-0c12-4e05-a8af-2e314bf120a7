/*
 * Copyright (c) 2020 WildFireChat. All rights reserved.
 */

// import proto from 'node-loader!../../../marswrapper.node';
import Message from '../messages/message';
import Conversation from '../model/conversation';
import ConversationInfo from '../model/conversationInfo';
import {
    EventEmitter
} from 'events';
import EventType from '../client/wfcEvent'
import UserInfo from '../model/userInfo';
import NullUserInfo from '../model/nullUserInfo';
import NullGroupInfo from '../model/nullGroupInfo';
import GroupInfo from '../model/groupInfo';
import GroupMember from '../model/groupMember';
import UserSettingScope from '../client/userSettingScope';
import AddGroupMemberNotification from '../messages/notification/addGroupMemberNotification';
import MessageConfig from '../client/messageConfig';
import UnreadCount from '../model/unreadCount';
import ConversationSearchResult from '../model/conversationSearchResult';
import MessageStatus from '../messages/messageStatus';
import GroupSearchResult from '../model/groupSearchResult';
import FriendRequest from '../model/friendRequest';
import ChatRoomMemberInfo from '../model/chatRoomMemberInfo';
import ChannelInfo from '../model/channelInfo';
import ConnectionStatus from '../client/connectionStatus';
import Long from 'long';
import Config from "../../config";
import ChatRoomInfo from "../model/chatRoomInfo";
import ReadEntry from "../model/readEntry";
import FileRecord from "../model/fileRecord";
import MediaMessageContent from "../messages/mediaMessageContent";
import MarkUnreadMessageContent from "../messages/markUnreadMessageContent";
import {
    gt, numberValue,
    stringValue
} from "../util/longUtil";
import NullChannelInfo from "../model/NullChannelInfo";
import UserOnlineState from "../model/userOnlineState";
import UserCustomState from "../model/userCustomState";
import UserClientState from "../model/userClientState";
import ConversationType from "../model/conversationType";

var proto = null;

var lastScreenShotTime = 0;

// var uniWfcClient = uni.requireNativePlugin("wf-uni-wfc-client");
import uniWfcClient from './uniWfcClient';
/**
 * java long 类型在 javascript 类型的映射说明:
 * 字段messageUid 对应 Long 对象
 * 其他 long 类型的变量 对应 number
 *
 * native 和 proto.min.js 之间 java long类型数据的传递
 * 双向传递时，都需 java long 类型的数据转换成字符串传递
 *
 */
class WfcImpl {
    connectionStatus = 0;
    userId = '';
    token = '';
    isLogined = false;
    firstSync = true;

    // TODO 移除吧，全都走EventEmitter
    // onReceiveMessageListeners = [];

    messageContentList = [];

    eventEmitter = null;

    needPreloadDefaultData = false;

    onConnectionChanged(status) {
        status = Number(status);
        if (!self.isLogined && status == ConnectionStatus.ConnectionStatusConnected) {
            self.isLogined = true;
        }
        if (status === ConnectionStatus.ConnectionStatusConnected) {
            if (self.needPreloadDefaultData) {
                // 预加载数据
                // 拉取会话相关用户、群信息
                // 自己的用户信息
                // 获取所有好友、好友请求的用户信息
                let estimatedTime = self._preloadDefaultData();
                console.log('to load default data', estimatedTime);
                setTimeout(() => {
                    self.connectionStatus = status;
                    self.eventEmitter.emit(EventType.ConnectionStatusChanged, status);
                }, estimatedTime)
            } else {
                self.connectionStatus = status;
                self.eventEmitter.emit(EventType.ConnectionStatusChanged, status);
            }
        } else {
            self.connectionStatus = status;
            self.eventEmitter.emit(EventType.ConnectionStatusChanged, status);
        }
        console.log('connection status changed', status);
        if (self.isLogined && (status == ConnectionStatus.ConnectionStatusSecretKeyMismatch || status === ConnectionStatus.ConnectionStatusRejected)) {
            self.disconnect();
        }
    }

    onConnectToServer(host, ip, port) {
        console.log('connect to server', host, ip, port);
        self.eventEmitter.emit(EventType.ConnectToServer, host);
    }

    // /**
    //  *
    //  * @param {function} listener
    //  */
    // setOnReceiveMessageListener(listener) {
    //     if (typeof listener !== 'function') {
    //         console.log('listener should be a function');
    //         return;
    //     }
    //     self.onReceiveMessageListeners.forEach(l => {
    //         l === listener
    //         return
    //     });
    //     self.onReceiveMessageListeners.push(listener);
    // }

    // removeOnReceiMessageListener(listener) {
    //     if (typeof listener !== 'function') {
    //         console.log('listener should be a function');
    //         return;
    //     }
    //     self.onReceiveMessageListeners.splice(self.onReceiveMessageListeners.indexOf(listener), 1);
    // }

    onReceiveMessage(messages, hasMore) {
        if (!self.isLogined) {
            return;
        }
        // receiving
        if (self.connectionStatus === 2 && self.firstSync) {
            return;
        }

        if (self.connectionStatus === 1) {
            self.firstSync = false;
        }

        var msgs = JSON.parse(messages);
        msgs.forEach(m => {
            let msg = Message.fromProtoMessage(m);
            // self.onReceiveMessageListeners.forEach(listener => {
            //     listener(msg, hasMore);
            // });
            if (msg.messageContent instanceof MarkUnreadMessageContent && msg.from === self.userId) {
                let markMsg = msg.messageContent;
                let conversation = msg.conversation;
                uniWfcClient.setLastReceivedMessageUnRead(JSON.stringify(conversation), stringValue(markMsg.messageUid), stringValue(markMsg.timestamp));
            }
            if (msg) {
                self.eventEmitter.emit(EventType.ReceiveMessage, msg, hasMore);
            }
        });
    }

    onConferenceEvent(event) {
        self.eventEmitter.emit(EventType.ConferenceEvent, event)
    }

    onOnlineEvent(event) {
        let userOnlineStates = self._parseUserOnlineState(event);
        self.eventEmitter.emit(EventType.UserOnlineEvent, userOnlineStates);
    }

    onSendSuccess(message) {
        let msg = Message.fromProtoMessage(JSON.parse(message));
        self.eventEmitter.emit(EventType.MessageStatusUpdate, msg);
    }

    onSendFail(message, errorCode) {
        let msg = Message.fromProtoMessage(JSON.parse(message));
        self.eventEmitter.emit(EventType.MessageStatusUpdate, msg);
    }

    onSendPrepare(message, savedTime) {
        let msg = Message.fromProtoMessage(JSON.parse(message));
        self.eventEmitter.emit(EventType.SendMessage, msg);
    }

    onProgress(message, uploaded, total) {
        // TODO
    }

    onMediaUpload(message, remoteUrl) {
        let msg = Message.fromProtoMessage(JSON.parse(message));
        self.eventEmitter.emit(EventType.MessageStatusUpdate, msg);
    }

    onMessageUpdate(message) {
        let msg = Message.fromProtoMessage(JSON.parse(message));
        self.eventEmitter.emit(EventType.MessageStatusUpdate, msg);
    }

    _parseUserOnlineState(userOnlineStateStr) {
        let userOnlineStates = [];
        let states = JSON.parse(userOnlineStateStr);
        states.forEach(s => {
            let userOnlineState = new UserOnlineState();
            userOnlineState.userId = s.userId;
            userOnlineState.customState = new UserCustomState();
            userOnlineState.customState.state = s.customState;
            userOnlineState.customState.text = s.customText;
            userOnlineState.clientStates = [];

            let clientStates = s.states ? s.states : s.clientStates;
            if (clientStates) {
                clientStates.forEach(ss => {
                    let clientState = new UserClientState();
                    clientState.state = ss.state;
                    clientState.platform = ss.platform;
                    clientState.lastSeen = Long.fromValue(ss.lastSeen);
                    userOnlineState.clientStates.push(clientState)
                })
            }
            userOnlineStates.push(userOnlineState);
        })
        return userOnlineStates;
    }

    onGroupInfoUpdate(groupInfosStr) {
        if (!self.isLogined) {
            return;
        }

        let groupInfoArray = JSON.parse(groupInfosStr);

        let groupInfos = [];
        groupInfoArray.forEach((groupInfo => {
            groupInfos.push(Object.assign(new GroupInfo(), groupInfo));
        }))
        self.eventEmitter.emit(EventType.GroupInfosUpdate, groupInfos);
    }

    onChannelInfoUpdate(channelListIds) {
        if (!self.isLogined) {
            return;
        }
        let channelIdArray = JSON.parse(channelListIds);

        let channelInfos = [];
        channelIdArray.forEach((channelId => {
            channelInfos.push(self.getChannelInfo(channelId, false));
        }))
        self.eventEmitter.emit(EventType.ChannelInfosUpdate, channelInfos)
    }

    onGroupMemberUpdateListener(groupId, groupMembersStr) {
        if (!self.isLogined) {
            return;
        }
        let members = [];
        let memberIds = [];
        let arr = JSON.parse(groupMembersStr);
        arr.forEach(e => {
            members.push(Object.assign(new GroupMember(), e));
            memberIds.push(e.memberId)
        });
        self._preloadGroupMemberUserInfos(memberIds);
        self.eventEmitter.emit(EventType.GroupMembersUpdate, groupId, members);
    }

    onSettingUpdate() {
        if (!self.isLogined) {
            return;
        }
        // TODO 具体更新的信息
        self.eventEmitter.emit(EventType.SettingUpdate);
    }

    onRecallMessage(operatorUid, messageUid) {
        if (!self.isLogined) {
            return;
        }
        self.eventEmitter.emit(EventType.RecallMessage, operatorUid, Long.fromValue(messageUid));
    }

    onDeleteRemoteMessage(messageUid) {
        if (!self.isLogined) {
            return;
        }
        self.eventEmitter.emit(EventType.MessageDeleted, Long.fromValue(messageUid));
    }

    onUserReceivedMessage(receivedMapStr) {
        if (!self.isLogined) {
            return;
        }
        let deliveries = JSON.parse(receivedMapStr);
        let deliveryMap = new Map();
        deliveries.forEach(e => {
            deliveryMap.set(e.key, e.value);
        });
        self.eventEmitter.emit(EventType.MessageReceived, deliveryMap);
        console.log('onreceive', deliveryMap);
    }

    onUserReadedMessage(readedMapStr) {
        if (!self.isLogined) {
            return;
        }
        // [{"userId":"GNMtGtZZ","conversationType":1,"target":"Jl8jLjkk","line":0,"readDt":1590308777299} ]
        let arr = JSON.parse(readedMapStr);
        let readEntries = [];
        arr.forEach(e => {
            let entry = new ReadEntry();
            entry.userId = e.userId;
            if (e.conversation) {
                entry.conversation = Object.assign(new Conversation(), e.conversation)
            } else {
                entry.conversation = new Conversation(e.conversationType, e.target, e.line);
            }
            entry.readTime = e.readDt;
            readEntries.push(entry);
        })
        self.eventEmitter.emit(EventType.MessageRead, readEntries);
        // console.log('onread', readEntries)
    }

    onMessageDeleted(messageId) {
        if (!self.isLogined) {
            return;
        }
        self.eventEmitter.emit(EventType.DeleteMessage, messageId);
    }

    onUserInfoUpdate(userInfosStr) {
        if (!self.isLogined) {
            return;
        }
        let userInfoArray = JSON.parse(userInfosStr);

        let userInfos = userInfoArray.map(info => Object.assign(new UserInfo(), info))
        self.eventEmitter.emit(EventType.UserInfosUpdate, userInfos);
    }

    onFriendListUpdate(friendListIds) {
        if (!self.isLogined) {
            return;
        }
        console.log('friendList update, ids', friendListIds);
        let ids = JSON.parse(friendListIds);
        self.eventEmitter.emit(EventType.FriendListUpdate, ids);
    }

    onFriendRequestUpdate(newRequests = '[]') {
        if (!self.isLogined) {
            return;
        }
        console.log('friend request list update, new incomming requests', newRequests);
        let ids = JSON.parse(newRequests);
        self.eventEmitter.emit(EventType.FriendRequestUpdate, ids);
    }

    init(args = []) {
        // {"timestamp":1649176742400,"data":["onConnectionStatusChange",-6]}
        console.log('proto init')
        uniWfcClient.initProto();

        plus.globalEvent.addEventListener("wfc-event", (e) => {
            // console.debug('wfc-event', e);
            self._handleNativeEvent(e);
            // this.log = [this.interpreter(e), ...this.log];
        });

        self.connectionStatus = self.getConnectionStatus();
        self.isLogined = self.connectionStatus === ConnectionStatus.ConnectionStatusConnected;

        console.log('proto init end')

        self.registerDefaultMessageContents();
    }

    registerMessageContent(name, flag, type, clazz) {
        MessageConfig.MessageContents.push({
            name: name,
            flag: flag,
            type: type,
            contentClazz: clazz,
        });
        uniWfcClient.registerMessageFlag(type, flag);
    }

    useSM4() {
        uniWfcClient.useSM4();
    }

    connect(userId, token) {
        // 强制转成 string
        userId = userId + '';
        self.userId = userId;
        let lastActiveTime = uniWfcClient.connect(Config.IM_SERVER_HOST, userId, token);
        console.log('connect', userId, Config.IM_SERVER_HOST);
        // for testing your code
        // self.test();
        // 超过一周没有活跃，就预加载数据
        if (new Date().getTime() / 1000 - lastActiveTime > 7 * 24 * 60 * 60) {
            self.needPreloadDefaultData = true;
        }
        return lastActiveTime;
    }

    setProxyInfo(host, ip, port, username, password) {
        uniWfcClient.setProxyInfo(host, ip, port, username, password);
    }

    setPackageName(packageName) {
        // not support
        // proto.setPackageName(packageName);
    }

    disconnect(disablePush, clearSession) {
        self.userId = '';
        uniWfcClient.disconnect(disablePush, clearSession);


        //sleep 1 second wait disconnect with im server
        var now = new Date();
        var exitTime = now.getTime() + 1000;
        while (true) {
            now = new Date();
            if (now.getTime() > exitTime)
                return;
        }
    }

    registerDefaultMessageContents() {
        MessageConfig.MessageContents.map((e) => {
            uniWfcClient.registerMessageFlag(e.type, e.flag);
        });
    }

    getClientId() {
        return uniWfcClient.getClientId();
    }

    getUserId() {
        try {
            return uniWfcClient.getUserId();
        } catch (e) {
            return self.userId;
        }
    }

    getServerDeltaTime() {
        return uniWfcClient.getServerDeltaTime();
    }


    screenShot() {
        // not support
    }

    isLogin() {
        // return proto.isLogin();
        return self.isLogined;
    }

    getConnectionStatus() {
        return uniWfcClient.getConnectionStatus();
    }

    setBackupAddressStrategy(strategy) {
        uniWfcClient.setBackupAddressStrategy(strategy);
    }

    setBackupAddress(backupHost, backupPort) {
        uniWfcClient.setBackupAddress(backupHost, backupPort);
    }

    setProtoUserAgent(userAgent) {
        if (typeof userAgent !== 'string') {
            console.error('setProtoUserAgent userAgent must be string');
            return;
        }
        uniWfcClient.setUserAgent(userAgent);
    }

    addHttpHeader(header, value) {
        if (typeof header !== "string" || typeof value !== "string") {
            console.error('addHttpHeader header, value must be string')
            return;
        }
        uniWfcClient.addHttpHeader(header, value);
    }

    onAppResume() {
        // not support
    }

    onAppSuspend() {
        // not support
    }

    // setLanguage(language) {
    //     proto.setLanguage(language);
    // }

    getMyGroupList() {
        let settings = this.getUserSettings(UserSettingScope.FavoriteGroup);
        let groupInfos = settings.filter(setting => setting.value === '1')
            .map(setting => this.getGroupInfo(setting.key, false));
        return groupInfos;
    }

    /**
     * @param {string} userId
     * @param {boolean} fresh
     */
    getUserInfo(userId, fresh = false, groupId = '') {
        if (!userId || userId === '') {
            return new NullUserInfo('');
        }
        let userInfo;

        let userInfoStr = uniWfcClient.getUserInfo(userId, fresh, groupId);
        if (userInfoStr === '') {
            userInfo = new NullUserInfo(userId);
        } else {
            userInfo = Object.assign(new UserInfo(), JSON.parse(userInfoStr));
        }
        return userInfo;
    }

    getUserInfos(userIds, groupId = '') {
        if (!userIds || userIds.length === 0) {
            return [];
        }
        let users = [];
        let userInfoStrs = uniWfcClient.getUserInfos(userIds, groupId);
        if (userInfoStrs && userInfoStrs !== '') {
            let tmp = JSON.parse(userInfoStrs);
            tmp.forEach((u) => {
                let userInfo = Object.assign(new UserInfo(), u);
                users.push(userInfo)
            });
        }
        return users;
    }

    async getUserInfoEx(userId, refresh, successCB, failCB) {
        uniWfcClient.getUserInfoEx(userId, refresh, (userInfoStr) => {
            let userInfo = Object.assign(new UserInfo(), JSON.parse(userInfoStr));
            if (successCB) {
                successCB(userInfo);
            }
        }, (errorCode) => {
            if (errorCode) {
                failCB(errorCode);
            }

        });
    }

    async searchUser(keyword, searchType, page, successCB, failCB) {
        uniWfcClient.searchUser(keyword, searchType, page, (result) => {
            let userListStr = JSON.parse(result);
            let userList = [];
            if (userListStr && userListStr.length > 0) {
                userListStr.forEach(u => {
                    userList.push(Object.assign(new UserInfo(), u));
                });
            }
            if (successCB) {
                successCB(keyword, userList);
            }
        }, (errorCode) => {
            if (errorCode) {
                failCB(errorCode);
            }

        });
    }

    searchFriends(keyword) {
        let result = uniWfcClient.searchFriends(keyword);
        let userListStr = JSON.parse(result);
        let userList = [];
        if (userListStr && userListStr.length > 0) {
            userListStr.forEach(u => {
                userList.push(Object.assign(new UserInfo(), u));
            });
        }
        return userList;
    }

    searchGroups(keyword) {
        let result = uniWfcClient.searchGroups(keyword);
        let groupSearchResultListStr = JSON.parse(result);
        let groupSearchResultList = [];
        if (groupSearchResultListStr && groupSearchResultListStr.length > 0) {
            groupSearchResultListStr.forEach(g => {
                groupSearchResultList.push(GroupSearchResult.fromProtoGroupSearchResult(g));
            });
        }
        return groupSearchResultList;
    }

    getIncommingFriendRequest() {
        let result = uniWfcClient.getIncommingFriendRequest();
        let friendRequestListStr = JSON.parse(result);
        let firendRequestList = [];
        if (friendRequestListStr && friendRequestListStr.length > 0) {
            friendRequestListStr.forEach((r) => {
                firendRequestList.push(Object.assign(new FriendRequest(), r));
            });
        }
        return firendRequestList;
    }

    getOutgoingFriendRequest() {
        let result = uniWfcClient.getOutgoingFriendRequest();
        let friendRequestListStr = JSON.parse(result);
        let firendRequestList = [];
        if (friendRequestListStr && friendRequestListStr.length > 0) {
            friendRequestListStr.forEach((r) => {
                firendRequestList.push(Object.assign(new FriendRequest(), r));
            });
        }
        return firendRequestList;
    }

    getFriendRequest(userId, incomming) {
        let result = uniWfcClient.getFriendRequest(userId, incomming);
        return JSON.parse(result);
    }

    loadFriendRequestFromRemote() {
        uniWfcClient.loadFriendRequestFromRemote();
    }

    getFavUsers() {
        let result = uniWfcClient.getFavUsers();
        return JSON.parse(result);
    }

    isFavUser(groupId) {
        return uniWfcClient.isFavUser(groupId);
    }

    setFavUser(userId, fav, successCB, failCB) {
        uniWfcClient.setFavUser(userId, fav, () => {
            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    loadRemoteMessages(conversation, contentTypes, beforeUid, count, successCB, failCB) {
        if (!contentTypes) {
            contentTypes = [];
        }
        uniWfcClient.getRemoteMessages(JSON.stringify(conversation), beforeUid + '', count, (protoMsgsStr) => {
            var protoMsgs = JSON.parse(protoMsgsStr);
            let msgs = [];
            protoMsgs.map(m => {
                let msg = Message.fromProtoMessage(m);
                if (msg) {
                    msgs.push(msg);
                }
            });
            console.log('loadRemoteMessages', msgs.length);
            successCB && successCB(msgs);
        }, (errorCode) => {
            console.log("loadRemoteMessages failure:", errorCode);
            failCB && failCB(errorCode);
        }, contentTypes);
    }

    loadRemoteMessage(messageUid, successCB, failCB) {
        uniWfcClient.getRemoteMessage(stringValue(messageUid), (protoMsgsStr) => {
            var protoMsgs = JSON.parse(protoMsgsStr);
            let msgs = [];
            protoMsgs.map(m => {
                let msg = Message.fromProtoMessage(m);
                if (msg) {
                    msgs.push(msg);
                }
            });
            console.log('loadRemoteMessage', msgs.length);
            successCB && successCB(msgs);
        }, (errorCode) => {
            console.log("loadRemoteMessage failure:", errorCode);
            failCB && failCB(errorCode);
        });
    }

    getUnreadFriendRequestCount() {
        return uniWfcClient.getUnreadFriendRequestStatus();
    }

    clearUnreadFriendRequestStatus() {
        uniWfcClient.clearUnreadFriendRequestStatus();
    }

    async deleteFriend(userId, successCB, failCB) {
        uniWfcClient.deleteFriend(userId, () => {
            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            failCB(errorCode);
        });
    }

    async handleFriendRequest(userId, accept, extra, successCB, failCB) {
        uniWfcClient.handleFriendRequest(userId, accept, () => {
            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }

        }, extra);
    }

    isBlackListed(userId) {
        return uniWfcClient.isBlackListed(userId);
    }

    getBlackList() {
        let result = uniWfcClient.getBlackList();
        return JSON.parse(result);
    }

    setBlackList(userId, block, successCB, failCB) {
        uniWfcClient.setBlackList(userId, block, () => {
            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    getMyFriendList(fresh = false) {
        let idsStr = uniWfcClient.getMyFriendList(fresh);
        if (idsStr !== '') {
            return JSON.parse(idsStr);
        }
        return [];
    }

    getFriendList(fresh = false) {
        let result = uniWfcClient.getFriendList(fresh);
        return JSON.parse(result);
    }

    getFriendAlias(userId) {
        return uniWfcClient.getFriendAlias(userId);
    }

    async setFriendAlias(userId, alias, successCB, failCB) {
        uniWfcClient.setFriendAlias(userId, alias, successCB, failCB);
    }

    async createGroup(groupId, groupType, name, portrait, groupExtra = '', memberIds = [], memberExtra = '', lines = [0], notifyContent, successCB, failCB) {
        groupId = !groupId ? '' : groupId;
        groupExtra = !groupExtra ? '' : groupExtra;
        memberExtra = !memberExtra ? '' : memberExtra;
        let myUid = self.getUserId();

        if (!memberIds.includes(myUid)) {
            memberIds.push(myUid);
        }

        let payload = notifyContent ? notifyContent.encode() : '';
        let notifyContentStr = '' ? null : JSON.stringify(payload);
        //群组类型0，管理员和群主才能加人和退群，修改群信息；2，严格模式，只有群主和管理员才能操作群
        uniWfcClient.createGroup(groupId, groupType, name, portrait, groupExtra, memberIds, memberExtra, lines, notifyContentStr,
            (groupId) => {
                if (successCB) {
                    successCB(groupId);
                }
            },
            (errorCode) => {
                if (failCB) {
                    failCB(errorCode);
                }
            });
    }

    async setGroupManager(groupId, isSet, memberIds = [], lines = [0], notifyContent, successCB, failCB) {
        let payload = notifyContent ? notifyContent.encode() : '';
        let notifyContentStr = JSON.stringify(payload);
        uniWfcClient.setGroupManager(groupId, isSet, memberIds, lines, notifyContentStr, successCB, failCB);
    }

    async muteOrAllowGroupMembers(groupId, isSet, isAllow, memberIds, notifyLines = [0], notifyContent, successCB, failCB) {
        if (isAllow) {
            let payload = notifyContent ? notifyContent.encode() : '';
            let notifyContentStr = JSON.stringify(payload);
            uniWfcClient.allowGroupMember(groupId, isSet, memberIds, notifyLines, notifyContentStr, () => {
                successCB && successCB();
            }, (errorCode) => {
                failCB && failCB();
            });
        } else {
            let payload = notifyContent ? notifyContent.encode() : '';
            let notifyContentStr = JSON.stringify(payload);
            uniWfcClient.muteGroupMember(groupId, isSet, memberIds, notifyLines, notifyContentStr, () => {
                successCB && successCB();
            }, (errorCode) => {
                failCB && failCB(errorCode);
            });
        }
    }

    getGroupInfo(groupId, fresh = false) {
        let groupInfo;

        //console.log('get groupInfo', groupId, fresh);
        let groupInfoStr = uniWfcClient.getGroupInfo(groupId, fresh);
        if (groupInfoStr === '') {
            return new NullGroupInfo(groupId);
        } else {
            groupInfo = Object.assign(new GroupInfo(), JSON.parse(groupInfoStr));
            return groupInfo;
        }
    }

    getGroupInfos(groupIds, fresh = false) {
        console.log('get groupInfos', groupIds.length, fresh);
        let groupInfosStr = uniWfcClient.getGroupInfos(groupIds, fresh);
        if (groupInfosStr === '') {
            return [];
        } else {
            let arr = JSON.parse(groupInfosStr);
            let groupInfos = [];
            arr.forEach(e => {
                groupInfos.push(Object.assign(new GroupInfo(), e));
            });
            return groupInfos;
        }
    }

    async getGroupInfoEx(groupId, refresh, successCB, failCB) {
        uniWfcClient.getGroupInfoEx(groupId, refresh, (groupInfoStr) => {
            let groupInfo = Object.assign(new GroupInfo(), JSON.parse(groupInfoStr));

            if (successCB) {
                successCB(groupInfo);
            }
        }, (errorCode) => {
            if (errorCode) {
                failCB(errorCode);
            }

        });
    }

    addGroupMembers(groupId, memberIds, extra, notifyLines, notifyMessageContent, successCB, failCB) {
        if (!notifyMessageContent) {
            notifyMessageContent = new AddGroupMemberNotification(self.getUserId(), memberIds);
        }
        let payload = notifyMessageContent.encode();
        let notifyContentStr = JSON.stringify(payload);
        extra = !extra ? '' : extra;
        uniWfcClient.addMembers(groupId, memberIds, extra, notifyLines, notifyContentStr,
            () => {
                if (successCB) {
                    successCB();
                }
            },
            (errorCode) => {
                if (failCB) {
                    failCB(errorCode);
                }
            });
    }

    getGroupMemberIds(groupId, fresh = false) {
        let groupMembers = self.getGroupMembers(groupId, fresh);
        var groupMemberIds = [];
        groupMembers.forEach(e => {
            groupMemberIds.push(e.memberId);
        });
        return groupMemberIds;
    }

    getGroupMembers(groupId, fresh = false) {
        let memberIdsStr = uniWfcClient.getGroupMembers(groupId, fresh);
        var members = [];
        let arr = JSON.parse(memberIdsStr);
        arr.forEach(e => {
            members.push(Object.assign(new GroupMember(), e));
        });
        return members;
    }

    getGroupMembersByType(groupId, memberType) {
        let memberIdsStr = uniWfcClient.getGroupMembersByType(groupId, memberType);
        var members = [];
        let arr = JSON.parse(memberIdsStr);
        arr.forEach(e => {
            members.push(Object.assign(new GroupMember(), e));
        });
        return members;
    }

    getGroupMember(groupId, memberId) {
        let result = uniWfcClient.getGroupMember(groupId, memberId);
        return Object.assign(new GroupMember(), JSON.parse(result));
    }

    async getGroupMembersEx(groupId, refresh, successCB, failCB) {
        uniWfcClient.getGroupMembersEx(groupId, refresh, (memberIdsStr) => {
            var members = [];
            let arr = JSON.parse(memberIdsStr);
            arr.forEach(e => {
                members.push(Object.assign(new GroupMember(), e));
            });

            if (successCB) {
                successCB(members);
            }
        }, (errorCode) => {
            if (errorCode) {
                failCB(errorCode);
            }

        });
    }

    kickoffGroupMembers(groupId, memberIds, notifyLines, notifyMsg, successCB, failCB) {
        let payload = notifyMsg ? notifyMsg.encode() : '';
        let strCont = JSON.stringify(payload);
        uniWfcClient.kickoffMembers(groupId, memberIds, notifyLines, strCont,
            () => {
                if (successCB) {
                    successCB();
                }

            }, (errorCode) => {
                if (failCB) {
                    failCB(errorCode);
                }
            });
    }

    async quitGroup(groupId, lines = [0], notifyMessageContent, successCB, failCB) {
        let payload = notifyMessageContent ? notifyMessageContent.encode() : '';
        uniWfcClient.quitGroup(groupId, lines, JSON.stringify(payload), () => {
            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            failCB(errorCode);
        });
    }
	
	async quitGroupEx(groupId, keepMessage, lines = [0], notifyMessageContent, successCB, failCB) {
	    let payload = notifyMessageContent ? notifyMessageContent.encode() : '';
	    uniWfcClient.quitGroupEx(groupId, keepMessage, lines, JSON.stringify(payload), () => {
	        if (successCB) {
	            successCB();
	        }
	    }, (errorCode) => {
	        failCB(errorCode);
	    });
	}

    async dismissGroup(groupId, lines = [0], notifyMessageContent, successCB, failCB) {
        let payload = notifyMessageContent ? notifyMessageContent.encode() : '';
        uniWfcClient.dismissGroup(groupId, lines, JSON.stringify(payload), () => {
            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            failCB(errorCode);
        });
    }

    async modifyGroupInfo(groupId, type, newValue, lines = [0], notifyMessageContent, successCB, failCB) {
        let payload = notifyMessageContent ? notifyMessageContent.encode() : '';
        uniWfcClient.modifyGroupInfo(groupId, type, newValue, lines, JSON.stringify(payload),
            () => {
                if (successCB) {
                    successCB();
                }
            }, (errorCode) => {
                if (failCB) {
                    failCB(errorCode);
                }
            });
    }

    async modifyGroupAlias(groupId, alias, lines = [0], notifyMessageContent, successCB, failCB) {
        let payload = notifyMessageContent ? notifyMessageContent.encode() : '';
        uniWfcClient.modifyGroupAlias(groupId, alias, lines, JSON.stringify(payload), () => {
            successCB();
        }, (errorCode) => {
            failCB(errorCode);
        });
    }

    async modifyGroupMemberAlias(groupId, memberId, alias, lines = [0], notifyMessageContent, successCB, failCB) {
        let payload = notifyMessageContent ? notifyMessageContent.encode() : '';
        uniWfcClient.modifyGroupMemberAlias(groupId, memberId, alias, lines, JSON.stringify(payload), () => {
            successCB();
        }, (errorCode) => {
            failCB(errorCode);
        });
    }

    async modifyGroupMemberExtra(groupId, memberId, extra, lines = [0], notifyMessageContent, successCB, failCB) {
        let payload = notifyMessageContent ? notifyMessageContent.encode() : '';
        uniWfcClient.modifyGroupMemberExtra(groupId, memberId, extra, lines, JSON.stringify(payload), () => {
            successCB();
        }, (errorCode) => {
            failCB(errorCode);
        });
    }

    transferGroup(groupId, newOwner, lines = [0], notifyMessageContent, successCB, failCB) {
        let payload = notifyMessageContent ? notifyMessageContent.encode() : '';
        uniWfcClient.transferGroup(groupId, newOwner, lines, JSON.stringify(payload), () => {
            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    getFavGroups() {
        let result = uniWfcClient.getFavGroups();
        return JSON.parse(result);
    }

    isFavGroup(groupId) {
        return uniWfcClient.isFavGroup(groupId);
    }

    async setFavGroup(groupId, fav, successCB, failCB) {
        uniWfcClient.setFavGroup(groupId, fav, () => {
            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    getUserSetting(scope, key) {
        return uniWfcClient.getUserSetting(scope, key);
    }

    getUserSettings(scope) {
        let result = uniWfcClient.getUserSettings(scope);
        return JSON.parse(result);
    }

    async setUserSetting(scope, key, value, successCB, failCB) {
        uniWfcClient.setUserSetting(scope, key, value, () => {
            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    modifyMyInfo(modifyMyInfoEntries, successCB, failCB) {
        uniWfcClient.modifyMyInfo(modifyMyInfoEntries[0].type, modifyMyInfoEntries[0].value, () => {
            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    isGlobalSlient() {
        return uniWfcClient.isGlobalSlient();
    }

    setGlobalSlient(silent, successCB, failCB) {
        uniWfcClient.setGlobalSlient(silent, () => {
            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    isHiddenNotificationDetail() {
        return uniWfcClient.isHiddenNotificationDetail();
    }

    async setHiddenNotificationDetail(hide, successCB, failCB) {
        uniWfcClient.setHiddenNotificationDetail(hide, () => {
            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    isHiddenGroupMemberName(groupId) {
        const setting = uniWfcClient.getUserSetting(UserSettingScope.GroupHideNickname, groupId);
        return '1' === setting;
    }

    async setHiddenGroupMemberName(groupId, hide, successCB, failCB) {
        uniWfcClient.setUserSetting(UserSettingScope.GroupHideNickname, groupId, hide ? '1' : '0', () => {
            successCB();
        }, (errorCode) => {
            failCB(errorCode);
        });
    }

    isUserReceiptEnabled() {
        return uniWfcClient.isUserReceiptEnabled();
    }

    async setUserEnableReceipt(enable, successCB, failCB) {
        uniWfcClient.setUserReceiptEnable(enable, () => {
            successCB && successCB();
        }, (errorCode) => {
            failCB && failCB(errorCode);
        });
    }

    async joinChatroom(chatroomId, successCB, failCB) {
        uniWfcClient.joinChatroom(chatroomId, () => {
            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    async quitChatroom(chatroomId, successCB, failCB) {
        uniWfcClient.quitChatroom(chatroomId, () => {
            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    async getChatroomInfo(chatroomId, updateDt, successCB, failCB) {
        uniWfcClient.getChatroomInfo(chatroomId, updateDt, (info) => {
            if (successCB) {
                successCB(Object.assign(new ChatRoomInfo(), JSON.parse(info)));
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    async getChatroomMemberInfo(chatroomId, maxCount, successCB, failCB) {
        uniWfcClient.getChatroomMemberInfo(chatroomId, maxCount, (info) => {
            if (successCB) {
                successCB(Object.assign(new ChatRoomMemberInfo(), JSON.parse(info)));
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    createChannel(name, portrait, desc, extra, successCB, failCB) {
        uniWfcClient.createChannel(name, portrait, 0, desc, extra, (info) => {
            if (successCB) {
                successCB(Object.assign(new ChannelInfo(), JSON.parse(info)));
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    getChannelInfo(channelId, refresh) {
        let result = uniWfcClient.getChannelInfo(channelId, refresh);
        if (result === '') {
            return new NullChannelInfo(channelId);
        }

        return Object.assign(new ChannelInfo(), JSON.parse(result));
    }

    async modifyChannelInfo(channelId, type, newValue, successCB, failCB) {
        uniWfcClient.modifyChannelInfo(channelId, type, newValue, () => {
            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    searchChannel(keyword, successCB, failCB) {
        uniWfcClient.searchChannel(keyword, (result) => {
            if (successCB) {
                let channels = [];
                let tmp = JSON.parse(result);
                tmp.forEach(channel => {
                    channels.push(Object.assign(new ChannelInfo(), channel));
                });
                successCB(channels);
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    isListenedChannel(channelId) {
        return uniWfcClient.isListenedChannel(channelId);
    }

    async listenChannel(channelId, listen, successCB, failCB) {
        uniWfcClient.listenChannel(channelId, listen, () => {
            successCB();
        }, errorCode => {
            failCB(errorCode);
        });
    }

    // return channelIds
    getMyChannels() {
        let result = uniWfcClient.getMyChannels();
        return JSON.parse(result);
    }

    getListenedChannels() {
        let result = uniWfcClient.getListenedChannels();
        return JSON.parse(result);
    }

    async destoryChannel(channelId, successCB, failCB) {
        uniWfcClient.destoryChannel(channelId, () => {
            if (successCB) {
                successCB();
            }
        }, errorCode => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    getConversationList(types, lines) {
        var conversationListStr = uniWfcClient.getConversationInfos(types, lines);
        if (!conversationListStr){
            console.log('getConversationInfos return null')
            return [];
        }

        let conversationInfoList = [];
        let tmp = JSON.parse(conversationListStr);
        tmp.forEach(c => {
            conversationInfoList.push(ConversationInfo.protoConversationToConversationInfo(c));
        });

        return conversationInfoList;
    }

    getConversationInfo(conversation) {
        let convStr = uniWfcClient.getConversationInfo(JSON.stringify(conversation));
        return ConversationInfo.protoConversationToConversationInfo(JSON.parse(convStr));
    }

    searchConversation(keyword, types = [], lines = []) {
        let result = uniWfcClient.searchConversation(keyword, types, lines);
        let resultList = JSON.parse(result);
        var conversationSearchResult = [];
        if (resultList && resultList.length > 0) {
            resultList.forEach(r => {
                conversationSearchResult.push(ConversationSearchResult.fromProtoConversationSearchResult(r));
            });
        }
        return conversationSearchResult;
    }

    async removeConversation(conversation, clearMsg = false) {
        uniWfcClient.removeConversation(JSON.stringify(conversation), clearMsg);
    }

    setConversationTop(conversation, top, successCB, failCB) {
        uniWfcClient.setConversationTop(JSON.stringify(conversation), top, () => {
            let conversationInfo = self.getConversationInfo(conversation);
            self.eventEmitter.emit(EventType.ConversationInfoUpdate, conversationInfo);

            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    setConversationSlient(conversation, silent, successCB, failCB) {
        uniWfcClient.setConversationSlient(JSON.stringify(conversation), silent, () => {
            let conversationInfo = self.getConversationInfo(conversation);
            self.eventEmitter.emit(EventType.ConversationInfoUpdate, conversationInfo);

            if (successCB) {
                successCB();
            }
        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    setConversationDraft(conversation, draft = '') {
        let conversationInfo = self.getConversationInfo(conversation);
        if (conversationInfo.draft === draft) {
            return;
        }
        uniWfcClient.setConversationDraft(JSON.stringify(conversation), draft);
        conversationInfo = self.getConversationInfo(conversation);
        self.eventEmitter.emit(EventType.ConversationInfoUpdate, conversationInfo);
    }

    //timestamp 为毫秒，字符串类型
    setConversationTimestamp(conversation, timestamp) {
        timestamp = timestamp + '';
        uniWfcClient.setConversationTimestamp(JSON.stringify(conversation), timestamp);
        let conversationInfo = self.getConversationInfo(conversation);
        self.eventEmitter.emit(EventType.ConversationInfoUpdate, conversationInfo);
    }

    getUnreadCount(types = [], lines = [0]) {
        let unreadCountStr = uniWfcClient.getUnreadCount(types, lines);
        return Object.assign(new UnreadCount(), JSON.parse(unreadCountStr));
    }

    getConversationUnreadCount(conversation) {
        let unreadCountStr = uniWfcClient.getConversationUnreadCount(JSON.stringify(conversation));
        return Object.assign(new UnreadCount(), JSON.parse(unreadCountStr));
    }

    clearConversationUnreadStatus(conversation) {
        // console.log('clearConversationUnreadStatus', JSON.stringify(conversation));
        uniWfcClient.clearUnreadStatus(JSON.stringify(conversation));
        let conversationInfo = self.getConversationInfo(conversation);
        self.eventEmitter.emit(EventType.ConversationInfoUpdate, conversationInfo);
    }

    markConversationAsUnread(conversation, syncToOtherClient) {
        let result = uniWfcClient.setLastReceivedMessageUnRead(JSON.stringify(conversation), '0', '0');
        if (result) {
            let conversationInfo = self.getConversationInfo(conversation);
            self.eventEmitter.emit(EventType.ConversationInfoUpdate, conversationInfo);
        }
        return result;
    }

    getConversationRead(conversation) {
        let readedStr = uniWfcClient.getConversationRead(JSON.stringify(conversation));
        let readedArr = JSON.parse(readedStr);
        let result = new Map();
        if (readedArr) {
            readedArr.forEach(e => {
                result.set(e.key, e.value)
            })
        }
        return result;
    }

    clearAllUnreadStatus() {
        // TODO emit ConversationInfoUpdate event
        uniWfcClient.clearAllUnreadStatus();
    }

    getConversationFirstUnreadMessageId(conversation) {
        let messageId = uniWfcClient.getConversationFirstUnreadMessageId(JSON.stringify(conversation));
        return messageId;
    }

    setMediaMessagePlayed(messageId) {
        // TODO need to emit message update event?
        uniWfcClient.setMediaMessagePlayed(messageId);
    }

    setMessageLocalExtra(messageId, extra) {
        uniWfcClient.setMessageLocalExtra(messageId, extra);
    }

    isMyFriend(userId) {
        return uniWfcClient.isMyFriend(userId);
    }

    async sendFriendRequest(userId, reason, extra, successCB, failCB) {
        extra = !extra ? '' : extra;
        uniWfcClient.sendFriendRequest(userId, reason, extra, () => {
            if (successCB) {
                successCB();
            }

        }, (errorCode) => {
            if (failCB) {
                failCB(errorCode);
            }
        });
    }

    /**
     *
     * @param {Conversation} conversation
     * @param {number} fromIndex
     * @param {boolean} before
     * @param {number} count
     * @param {string} withUser
     * @param {array} contentTypes
     */
    getMessages(conversation, fromIndex, before = true, count = 20, withUser = '', contentTypes = []) {
        let protoMsgsStr = uniWfcClient.getMessages(JSON.stringify(conversation), contentTypes, fromIndex, before, count, withUser);
        // let protoMsgsStr = proto.getMessages('xxx', [0], fromIndex, before, count, withUser);
        var protoMsgs = JSON.parse(protoMsgsStr);
        let msgs = [];
        protoMsgs.map(m => {
            let msg = Message.fromProtoMessage(m);
            if (msg) {
                msgs.push(msg);
            }
        });
        console.log('getMessages', msgs.length);

        return msgs;
    }


    getMessagesEx(conversationTypes, lines, contentTypes, fromIndex, before = true, count = 20, withUser = '') {
        let protoMsgsStr = uniWfcClient.getMessagesEx(conversationTypes, lines, contentTypes, fromIndex, before, count, withUser);
        // let protoMsgsStr = proto.getMessages('xxx', [0], fromIndex, before, count, withUser);
        var protoMsgs = JSON.parse(protoMsgsStr);
        let msgs = [];
        protoMsgs.map(m => {
            let msg = Message.fromProtoMessage(m);
            if (msg) {
                msgs.push(msg);
            }
        });
        console.log('getMessages', msgs.length);

        return msgs;
    }

    getUserMessages(userId, conversation, fromIndex, before = true, count = 20, contentTypes = []) {
        let protoMsgsStr = uniWfcClient.getUserMessages(userId, JSON.stringify(conversation), contentTypes, fromIndex, before, count);
        var protoMsgs = JSON.parse(protoMsgsStr);
        let msgs = [];
        protoMsgs.map(m => {
            let msg = Message.fromProtoMessage(m);
            if (msg) {
                msgs.push(msg);
            }
        });
        console.log('getMessages', msgs.length);

        return msgs;
    }


    getUserMessagesEx(userId, conversationTypes, lines, fromIndex, before = true, count = 20, contentTypes = []) {
        let protoMsgsStr = uniWfcClient.getUserMessagesEx(userId, conversationTypes, lines, contentTypes, fromIndex, before, count);
        var protoMsgs = JSON.parse(protoMsgsStr);
        let msgs = [];
        protoMsgs.map(m => {
            let msg = Message.fromProtoMessage(m);
            if (msg) {
                msgs.push(msg);
            }
        });
        console.log('getMessages', msgs.length);

        return msgs;
    }

    getMessagesV2(conversation, fromIndex, before, count, withUser, successCB, failCB) {
        uniWfcClient.getMessagesV2(JSON.stringify(conversation), fromIndex, before, count, withUser, ...this._wrapperGetMessageCallbacks(successCB, failCB));
    }

    getMessagesExV2(conversationTypes, lines, fromIndex, before, count, withUser, contentTypes, successCB, failCB) {
        uniWfcClient.getMessagesExV2(conversationTypes, lines, fromIndex, before, count, withUser, contentTypes, ...this._wrapperGetMessageCallbacks(successCB, failCB));
    }

    getMessagesEx2V2(conversationTypes, lines, messageStatuses, fromIndex, before, count, withUser, successCB, failCB) {
        uniWfcClient.getMessagesEx2V2(conversationTypes, lines, messageStatuses, fromIndex, before, count, withUser, ...this._wrapperGetMessageCallbacks(successCB, failCB));
    }

    getMessagesByTimestampV2(conversation, contentTypes, timestamp, before, count, withUser, successCB, failCB) {
        uniWfcClient.getMessagesByTimestampV2(JSON.stringify(conversation), contentTypes, numberValue(timestamp), before, count, withUser, ...this._wrapperGetMessageCallbacks(successCB, failCB));
    }

    getUserMessagesV2(userId, conversation, fromIndex, before, count, successCB, failCB) {
        uniWfcClient.getUserMessagesV2(userId, JSON.stringify(conversation), fromIndex, before, count, ...this._wrapperGetMessageCallbacks(successCB, failCB));
    }

    getUserMessagesExV2(userId, conversationTypes, lines, fromIndex, before, count, contentTypes, successCB, failCB) {
        uniWfcClient.getUserMessagesExV2(userId, conversationTypes, lines, fromIndex, before, count, contentTypes, ...this._wrapperGetMessageCallbacks(successCB, failCB));
    }

    _wrapperGetMessageCallbacks(successCB, failCB) {
        let cbs = [
            protoMsgsStr => {
                var protoMsgs = JSON.parse(protoMsgsStr);
                let msgs = [];
                protoMsgs.map(m => {
                    let msg = Message.fromProtoMessage(m);
                    if (msg) {
                        msgs.push(msg);
                    }
                });
                successCB && successCB(msgs);
            },
            err => {
                failCB && failCB(err);
            }
        ]
        return cbs;
    }

    getMessageById(messageId) {
        let mStr = uniWfcClient.getMessage(messageId);
        return Message.fromProtoMessage(JSON.parse(mStr));
    }

    getMessageByUid(messageUid) {
        console.log('getMesssageByUid', Long.fromValue(messageUid).toString());
        let mStr = uniWfcClient.getMessageByUid(Long.fromValue(messageUid).toString());
        console.log('getMesssageByUid----', mStr);
        return Message.fromProtoMessage(JSON.parse(mStr));
    }

    searchMessage(conversation, keyword, withUser = '') {
        let result = uniWfcClient.searchMessage(JSON.stringify(conversation), keyword, withUser);
        let msgs = JSON.parse(result);
        let matchMsgs = [];
        if (msgs && msgs.length > 0) {
            msgs.forEach(m => {
                matchMsgs.push(Message.fromProtoMessage(m));
            });
        }

        return matchMsgs;
    }

    searchMessageEx(conversation, keyword, desc, limit, offset, withUser) {
        let result = uniWfcClient.searchMessageEx(JSON.stringify(conversation), keyword, desc, limit, offset, withUser);
        let msgs = JSON.parse(result);
        let matchMsgs = [];
        if (msgs && msgs.length > 0) {
            msgs.forEach(m => {
                matchMsgs.push(Message.fromProtoMessage(m));
            });
        }

        return matchMsgs;
    }

    searchMessageByTypes(conversation, keyword, contentTypes, desc, limit, offset, withUser = '') {
        if (!contentTypes) {
            contentTypes = [];
        }
        let result = uniWfcClient.searchMessageByTypes(JSON.stringify(conversation), keyword, contentTypes, desc, limit, offset, withUser);
        let msgs = JSON.parse(result);
        let matchMsgs = [];
        if (msgs && msgs.length > 0) {
            msgs.forEach(m => {
                matchMsgs.push(Message.fromProtoMessage(m));
            });
        }

        return matchMsgs;
    }

    searchMessageByTypesAndTimes(conversation, keyword, contentTypes, startTime, endTime, desc, limit, offset, withUser) {
        if (!contentTypes) {
            contentTypes = [];
        }
        let result = uniWfcClient.searchMessageByTypesAndTimes(JSON.stringify(conversation), keyword, contentTypes, startTime.toString(), endTime.toString(), desc, limit, offset, withUser);
        let msgs = JSON.parse(result);
        let matchMsgs = [];
        if (msgs && msgs.length > 0) {
            msgs.forEach(m => {
                matchMsgs.push(Message.fromProtoMessage(m));
            });
        }

        return matchMsgs;
    }

    searchMessageEx2(conversationTypes, lines, contentTypes, keyword, fromIndex, desc, count) {
        if (!contentTypes) {
            contentTypes = [];
        }
        let result = uniWfcClient.searchMessageEx2(conversationTypes, lines, contentTypes, keyword, fromIndex, desc, count);
        let msgs = JSON.parse(result);
        let matchMsgs = [];
        if (msgs && msgs.length > 0) {
            msgs.forEach(m => {
                matchMsgs.push(Message.fromProtoMessage(m));
            });
        }

        return matchMsgs;
    }

    async sendConversationMessage(conversation, messageContent, toUsers = [], preparedCB, progressCB, successCB, failCB) {
        let message = new Message();
        message.conversation = conversation;
        message.messageContent = messageContent;
        self.sendMessageEx(message, toUsers, preparedCB, progressCB, successCB, failCB);
    }

    async sendMessage(message, preparedCB, progressCB, successCB, failCB) {
        self.sendMessageEx(message, [], preparedCB, progressCB, successCB, failCB);
    }

    // toUsers 用来实现定向消息
    async sendMessageEx(message, toUsers = [], preparedCB, progressCB, successCB, failCB) {
		console.log('sendMessageEx')
        let strConv = JSON.stringify(message.conversation);
		console.log('sendMessageEx',message.messageContent)
        message.content = await message.messageContent.encode();
        message.from = this.userId;
        console.log('--------------p', message.content);
        let strCont = JSON.stringify(message.content);

        uniWfcClient.sendMessage(strConv, strCont, toUsers, 0,
            (args) => { //preparedCB
                let [messageId, timestamp] = args;
                message.messageId = messageId;
                message.timestamp = Long.fromValue(timestamp).toNumber();
                if (typeof preparedCB === 'function') {
                    preparedCB(messageId, Long.fromValue(timestamp).toNumber());
                }
                // 有 onSendMessageListener 触发
                // self.eventEmitter.emit(EventType.SendMessage, message);
            },
            args => { //progressCB
                let [uploaded, total] = args;
                if (typeof progressCB === 'function') {
                    progressCB(uploaded, total);
                }
                // upload progress update
            },
            args => { //successCB
                let [messageUid, timestamp] = args;
                message.status = MessageStatus.Sent;
                message.messageUid = Long.fromValue(messageUid);
                message.timestamp = Long.fromValue(timestamp).toNumber();
                // update remote url
                if (message.messageContent instanceof MediaMessageContent) {
                    let msg = self.getMessageById(message.messageId);
                    message.messageContent = msg.messageContent;
                }

                if (typeof successCB === 'function') {
                    successCB(Long.fromValue(messageUid), Long.fromValue(timestamp).toNumber());
                }
                // self.eventEmitter.emit(EventType.MessageStatusUpdate, message);
            },
            (errorCode) => { //errorCB
                message.status = MessageStatus.SendFailure;
                if (typeof failCB === 'function') {
                    failCB(errorCode);
                }
                // self.eventEmitter.emit(EventType.MessageStatusUpdate, message);
            });

    }

    // 更新了原始消息的内容
    async recallMessage(messageUid, successCB, failCB) {
        console.log('recall', messageUid);
        uniWfcClient.recall(messageUid.toString(),
            () => {
                console.log('recall, s', messageUid);
                if (successCB) {
                    successCB();
                }
                this.onRecallMessage(this.getUserId(), messageUid);
            },
            (errorCode) => {
                console.log('recall, f', messageUid, errorCode);
                if (failCB) {
                    failCB();
                }
            });
    }

    async deleteRemoteMessage(messageUid, successCB, failCB) {
        console.log('deleteRemoteMessageByUid', messageUid);
        uniWfcClient.deleteRemoteMessage(messageUid.toString(),
            () => {
                if (successCB) {
                    successCB();
                }
                this.onDeleteRemoteMessage(messageUid);
            },
            (errorCode) => {
                if (failCB) {
                    failCB();
                }
            });
    }

    async updateRemoteMessageContent(messageUid, messageContent, distribute, updateLocal, successCB, failCB) {
        console.log('updateRemoteMessageContent', messageUid);
        let protoMessageContent = messageContent.encode();
        uniWfcClient.updateRemoteMessageContent(messageUid.toString(), JSON.stringify(protoMessageContent), distribute, updateLocal,
            () => {
                if (successCB) {
                    successCB();
                }
            },
            (errorCode) => {
                if (failCB) {
                    failCB(errorCode);
                }
            });
    }

    deleteMessageById(messageId) {
        // 底层返回值是 void，先不修改底层 SDK 了
        let result = uniWfcClient.deleteMessage(messageId);
        this.onMessageDeleted(messageId);
        return result;
    }

    watchOnlineState(conversationType, targets, duration, successCB, failCB) {
        uniWfcClient.watchOnlineState(conversationType, targets, duration,
            (strStates) => {
                let userOnlineStates = self._parseUserOnlineState(strStates);
                successCB && successCB(userOnlineStates);
            },
            (errorCode) => {
                failCB && failCB(errorCode);
            });
    }

    unwatchOnlineState(conversationType, targets, successCB, failCB) {
        uniWfcClient.unwatchOnlineState(conversationType, targets,
            () => {
                if (successCB) {
                    successCB();
                }
            },
            (errorCode) => {
                if (failCB) {
                    failCB();
                }
            });
    }

    isCommercialServer() {
        return uniWfcClient.isCommercialServer();
    }

    isReceiptEnabled() {
        return uniWfcClient.isReceiptEnabled();
    }

    isGlobalDisableSyncDraft() {
        return uniWfcClient.isGlobalDisableSyncDraft();
    }

    isEnableUserOnlineState() {
        return uniWfcClient.isEnableUserOnlineState();
    }

    setDisableSyncDraft(disable, successCB, failCB) {
        this.setUserSetting(UserSettingScope.DisableSyncDraft, '', disable ? '1' : '0',
            () => successCB && successCB(),
            (err) => failCB && failCB(err));
    }

    isDisableSyncDraft() {
        return this.getUserSetting(UserSettingScope.DisableSyncDraft, '') === '1';
    }

    getAuthorizedMediaUrl(messaggeUid, mediaType, mediaPath, successCB, failCB) {
        uniWfcClient.getAuthorizedMediaUrl(messaggeUid, mediaType, mediaPath, (args) => {
            let [url, backupUrl] = args;
            successCB && successCB(url, backupUrl);
        }, failCB);
    }

    isSupportBigFilesUpload() {
        return uniWfcClient.isSupportBigFilesUpload();
    }

    getUploadMediaUrl(fileName, mediaType, contentType, successCB, failCB) {
        uniWfcClient.getUploadMediaUrl(fileName, mediaType, contentType, (args) => {
            let [uploadUrl, remoteUrl, backUploadupUrl, serverType] = args;
            successCB && successCB(uploadUrl, remoteUrl, backUploadupUrl, serverType);
        }, failCB);
    }

    getConversationFileRecords(conversation, fromUser, beforeUid, count, successCB, failCB) {
        uniWfcClient.getConversationFiles(JSON.stringify(conversation), fromUser, Long.fromValue(beforeUid).toString(), count, (frsStr) => {
            let frs = JSON.parse(frsStr);
            let fileRecords = [];
            frs.forEach(fr => {
                fileRecords.push(this._objStrToFileRecordObj(fr));
            })
            successCB && successCB(fileRecords);
        }, (errorCode) => {
            failCB && failCB(errorCode);
        });
    }

    _objStrToFileRecordObj(obj) {

        let fileRecord = new FileRecord();
        fileRecord.userId = obj.userId;
        if (obj.conversation) {
            fileRecord.conversation = Object.assign(new Conversation(), obj.conversation);
        } else {
            fileRecord.conversation = new Conversation(obj.conversationType, obj.target, obj.line);
        }
        fileRecord.messageUid = Long.fromValue(obj.messageUid);
        fileRecord.name = obj.name;
        fileRecord.url = obj.url;
        fileRecord.size = obj.size;
        fileRecord.downloadCount = obj.downloadCount;
        fileRecord.timestamp = obj.timestamp;

        return fileRecord;
    }

    getMyFileRecords(beforeUid, count, successCB, failCB) {
        uniWfcClient.getMyFiles(Long.fromValue(beforeUid).toString(), count, (frsStr) => {
            let frs = JSON.parse(frsStr);
            let fileRecords = [];
            frs.forEach(fr => {
                fileRecords.push(this._objStrToFileRecordObj(fr));
            })
            successCB && successCB(fileRecords);
        }, (errorCode) => {
            failCB && failCB(errorCode);
        });
    }

    deleteFileRecord(messageUid, successCB, failCB) {
        uniWfcClient.deleteFileRecord(Long.fromValue(messageUid).toString(), () => {
            successCB && successCB();
        }, (errorCode) => {
            failCB && failCB(errorCode);
        });
    }

    async clearMessages(conversation) {
        uniWfcClient.clearMessages(JSON.stringify(conversation));
        let conversationInfo = this.getConversationInfo(conversation);
        self.eventEmitter.emit(EventType.ConversationInfoUpdate, conversationInfo);
    }

    async clearRemoteConversationMessages(conversation, successCB, failCB) {
        uniWfcClient.clearRemoteConversationMessages(JSON.stringify(conversation),
            () => {
                successCB && successCB();
            },
            (errorCode) => {
                failCB && failCB(errorCode);
            }
        );
    }

    /**
     * 删除before时间之前的所有消息
     *
     * @param {Conversation} conversation
     * @param {String} before 时间精度到毫秒，字符串格式
     */
    async clearMessagesByTime(conversation, before) {
        uniWfcClient.clearMessagesByTime(JSON.stringify(conversation), before);
        let conversationInfo = this.getConversationInfo(conversation);
        self.eventEmitter.emit(EventType.ConversationInfoUpdate, conversationInfo);
    }

    /**
     *
     * @param {Conversation} conversation
     * @param {MessageContent} messageContent
     * @param {MessageStatus} status
     * @param {boolean} notify 是否触发onReceiveMessage
     * @param {Number} serverTime 服务器时间，精度到毫秒
     */
    insertMessage(conversation, messageContent, status, notify = false, toUsers = [], serverTime = 0) {
        let protoMessageContent = messageContent.encode();
        uniWfcClient.insertMessage(JSON.stringify(conversation), self.userId, JSON.stringify(protoMessageContent), status, notify, toUsers, serverTime);
    }

    async updateMessageContent(messageId, messageContent) {
        let protoMessageContent = messageContent.encode();
        uniWfcClient.updateMessage(messageId, JSON.stringify(protoMessageContent));
    }

    async updateMessageStatus(messageId, status) {
        uniWfcClient.updateMessageStatus(messageId, status);
    }

    async uploadMedia(fileName, data, mediaType, successCB, failCB, progressCB) {
        // var data = file.slile(0, file.size);
        // TODO
        // FIXME
        // if (data.path) {
        //     data = await fs.readFile(data.path, {encoding: 'base64'});
        // }
        if (data.indexOf("base64,") >= 0) {
            data = data.substring(data.indexOf(',') + 1);
        }
        uniWfcClient.uploadMedia(fileName, data, mediaType,
            (remoteUrl) => {
                if (successCB) {
                    successCB(remoteUrl);
                }
            },
            (errorCode) => {
                if (failCB) {
                    failCB(errorCode);
                }
            },
            (current, total) => {
                if (progressCB) {
                    progressCB(current, total);
                }
            });
    }

    sendConferenceRequest(sessionId, roomId, request, data, advance, callback) {
        uniWfcClient.sendConferenceRequest('' + sessionId, roomId, request, data,
            (resp) => {
                callback && callback(0, resp);
            },
            (errorCode) => {
                callback && callback(errorCode, null)
            }, advance === true);
    }

    searchFiles(keyword, conversation, fromUser, beforeMessageId, count, successCB, failCB) {
        uniWfcClient.searchFiles(keyword, conversation ? JSON.stringify(conversation) : '', fromUser, Long.fromValue(beforeMessageId).toString(), count,
            (files) => {
                let frs = JSON.parse(files);

                let fileRecords = [];
                frs.forEach(fr => {
                    fileRecords.push(this._objStrToFileRecordObj(fr));
                })
                successCB && successCB(fileRecords);
            },
            (errorCode) => {
                failCB && failCB(errorCode);
            })
    }

    searchMyFiles(keyword, beforeMessageUid, count, successCB, failCB) {
        uniWfcClient.searchMyFiles(keyword, Long.fromValue(beforeMessageUid).toString(), count,
            (files) => {
                let frs = JSON.parse(files);

                let fileRecords = [];
                frs.forEach(fr => {
                    fileRecords.push(this._objStrToFileRecordObj(fr));
                })
                successCB && successCB(fileRecords);
            },
            (errorCode) => {
                failCB && failCB(errorCode);
            });
    }

    getAuthCode(appId, appType, host, successCB, failCB) {
        uniWfcClient.getAuthCode(appId, appType, host, (authCode) => {
            successCB && successCB(authCode);
        }, (err) => {
            failCB && failCB(err);
        })
    }

    configApplication(appId, appType, timestamp, nonceStr, signature, successCB, failCB) {
        uniWfcClient.configApplication(appId, appType, timestamp, nonceStr, signature, () => {
            successCB && successCB();
        }, (err) => {
            failCB && failCB(err);
        })
    }

    notify(title, content) {
        uniWfcClient.notify(title, content);
    }

    clearAllNotification() {
        uniWfcClient.clearAllNotification();
    }

    setDeviceToken(type, token) {
        uniWfcClient.setDeviceToken(type, token);
    }

    chooseFile(type, successCB, failCB) {
        uniWfcClient.chooseFile(type, (res) => {
            successCB && successCB(res);
        }, (err) => {
            failCB && failCB(err);
        });
    }

    _nativeEvent2WfcEvent = {
        // 'onChannelInfoUpdate': EventType.ChannelInfosUpdate,
        // 'onClearMessage': null,
        // 'onConferenceEvent': EventType.ConferenceEvent,
        // 'onConnectToServer': EventType.ConnectToServer,
        // 'onConnectionStatusChange': EventType.ConnectionStatusChanged,
        // 'onConversationDraftUpdate': null,
        // 'onDeleteMessage': EventType.DeleteMessage,
        // 'onFriendListUpdate': EventType.FriendListUpdate,
        // 'onGroupInfoUpdate': EventType.GroupInfosUpdate,
        // 'onGroupMembersUpdate': EventType.GroupMembersUpdate,
        // 'onServiceConnected': null,
        // 'onMessageDelivered': EventType.MessageReceived,
        // 'onMessageRead': EventType.MessageRead,
        // 'onMessageUpdate': EventType.MessageStatusUpdate,
        // 'onReceiveMessage': EventType.ReceiveMessage,
        // 'onRecallMessage': EventType.RecallMessage,
        // 'onConversationRemove': null,
        // 'onMediaUpload': null,
        // 'onSettingUpdate': EventType.SettingUpdate,
        // 'onTrafficData': null,
        // 'onUserInfoUpdate': EventType.UserInfosUpdate,
        // 'onUserOnlineEvent': EventType.UserOnlineEvent,

        'onChannelInfoUpdate': this.onChannelInfoUpdate,
        'onClearMessage': null,
        'onConferenceEvent': this.onConferenceEvent,
        'onConnectToServer': this.onConnectToServer,
        'onConnectionStatusChange': this.onConnectionChanged,
        'onConversationDraftUpdate': null,
        'onDeleteMessage': this.onDeleteRemoteMessage,
        'onFriendListUpdate': this.onFriendListUpdate,
        'onFriendRequestUpdate': this.onFriendRequestUpdate,
        'onGroupInfoUpdate': this.onGroupInfoUpdate,
        'onGroupMembersUpdate': this.onGroupMemberUpdateListener,
        'onServiceConnected': null,
        'onMessageDelivered': this.onUserReceivedMessage,
        'onMessageRead': this.onUserReadedMessage,
        'onMessageUpdate': this.onMessageUpdate,
        'onReceiveMessage': this.onReceiveMessage,
        'onRecallMessage': this.onRecallMessage,
        'onConversationRemove': null,
        'onSettingUpdate': this.onSettingUpdate,
        'onTrafficData': null,
        'onUserInfoUpdate': this.onUserInfoUpdate,
        'onUserOnlineEvent': this.onOnlineEvent,
        // sendMessageListener
        'onSendSuccess': this.onSendSuccess,
        'onSendFail': this.onSendFail,
        'onSendPrepare': this.onSendPrepare,
        'onProgress': this.onProgress,
        'onMediaUpload': this.onMediaUpload,
    }

    _handleNativeEvent(e) {
        let args = e.args;
        let func = this._nativeEvent2WfcEvent[args[0]];
        if (func) {
            func(...args.slice(1));
        }
    }

    _callNativeFunc(...args) {

    }

    // 预加载数据
    // 拉取会话相关用户、群信息
    // 自己的用户信息
    // 获取所有好友、好友请求的用户信息
    _preloadDefaultData() {
        let requests = self.getIncommingFriendRequest()
        let userIdSet = new Set();
        requests.forEach(fr => {
            userIdSet.add(fr.target);
        });
        requests = self.getOutgoingFriendRequest()
        requests.forEach(fr => {
            userIdSet.add(fr.target);
        });

        let friendIds = self.getMyFriendList(false);
        friendIds.forEach(uid => userIdSet.add(uid));

        let conversationInfoList = self.getConversationList([0, 1, 3], [0, 1, 2]);
        let groupIdIds = [];
        let channelIds = [];
        conversationInfoList.forEach(info => {
            if (info.conversation.type === ConversationType.Single) {
                userIdSet.add(info.conversation.target);
            } else if (info.conversation.type === ConversationType.Group) {
                groupIdIds.push(info.conversation.target);
            } else if (info.conversation.type === ConversationType.Channel) {
                channelIds.push(info.conversation.target)
            }
            if (info.lastMessage && info.lastMessage.from) {
                userIdSet.add(info.lastMessage.from);
            }
        })
        let uids = Array.from(userIdSet);
        console.log('to preload userIds', uids, userIdSet)
        for (let i = 0; i < uids.length / 2000; i++) {
            self.getUserInfos(uids.slice(2000 * i, (i + 1) * 2000), '');
            console.log('to preload', uids.slice(2000 * i, (i + 1) * 2000))
        }

        console.log('to preload groupIds', groupIdIds)
        self.getGroupInfos(groupIdIds, false)
        groupIdIds.forEach(groupId => {
            self.getGroupMembers(groupId, false);
        })
        channelIds.forEach(channelId => {
            self.getChannelInfo(channelId)
        })

        let estimatedTime = 0;
        // 每 2000 人 5 秒
        estimatedTime += Math.round(uids.length / 2000) * 4
        // 每 10 个群 2 秒
        estimatedTime += Math.round(groupIdIds.length / 10) * 2

        return estimatedTime * 1000;
    }

    _preloadGroupMemberUserInfos(memberIds) {
        for (let i = 0; i < memberIds.length / 2000; i++) {
            self.getUserInfos(memberIds.slice(2000 * i, (i + 1) * 2000), '');
            console.log('to preload', memberIds.slice(2000 * i, (i + 1) * 2000))
        }
    }

    isNotInterested(userId) {
        const setting = this.getUserSetting('not_interested', userId);
        return setting === 'true';
    }

    setNotInterested(userId, notInterested, successCB, failCB) {
        this.setUserSetting('not_interested', userId, notInterested.toString(),
            () => successCB && successCB(),
            (err) => failCB && failCB(err));
    }
}

const self = new WfcImpl();
export default self;
