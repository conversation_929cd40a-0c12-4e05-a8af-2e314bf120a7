// utils/clientId.js
import {getItem, setItem} from "../../pages/util/storageHelper";

export function getClientId() {
    const KEY = 'wildfire_client_id'
  
    // 1. 尝试读取本地缓存
    let cid = getItem(KEY)
    if (cid) return cid                      // 已存在，直接返回
  
    // 2. 生成新 UUID（浏览器支持的安全方案）
    if (crypto?.randomUUID) {
      cid = crypto.randomUUID()
    } else {
      // fallback – 简易随机串
      cid = 'cid-' + Math.random().toString(36).slice(2) + Date.now()
    }
  
    // 3. 持久化
    setItem(KEY, cid)
    return cid
}

// 兼容默认导入和按需导入
export default getClientId
