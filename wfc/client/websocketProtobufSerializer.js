/*
 * Copyright (c) 2020 WildFireChat. All rights reserved.
 */

import aesUtil from './aesUtil.js';
import { PROTOBUF_MAGIC, PROTOBUF_VERSION, WildfireMessage } from './websocketProtobufMessages.js';

/**
 * WebSocket Protobuf序列化工具类
 * 
 * 与服务器端ProtobufSerializer.java保持一致
 * 提供protobuf消息的序列化、反序列化功能
 * 集成AES加解密功能，确保传输安全
 */
export class WebSocketProtobufSerializer {
    constructor() {
        // 魔数，用于标识protobuf消息格式
        this.PROTOBUF_MAGIC = PROTOBUF_MAGIC;
        this.VERSION = PROTOBUF_VERSION;
    }
    
    /**
     * 序列化WildfireMessage为字节数组
     * @param {WildfireMessage} message 要序列化的消息
     * @param {boolean} encrypt 是否加密
     * @param {string} encryptKey 加密密钥（可选）
     * @returns {Promise<Uint8Array|null>} 序列化后的字节数组
     */
    async serialize(message, encrypt = true, encryptKey = '') {
        try {
            // 1. 将消息对象转换为JSON字符串
            const jsonData = JSON.stringify(message);
            const jsonBytes = new TextEncoder().encode(jsonData);
            
            // 2. 如果需要加密，使用AES加密
            let dataBytes;
            if (encrypt && encryptKey) {
                dataBytes = await aesUtil.AESEncrypt(jsonBytes, encryptKey);
                if (!dataBytes) {
                    console.error('AES加密失败');
                    return null;
                }
            } else {
                dataBytes = jsonBytes;
            }
            
            // 3. 构建最终的字节数组：魔数(4) + 版本(4) + 加密标志(1) + 数据长度(4) + 数据
            const buffer = new ArrayBuffer(4 + 4 + 1 + 4 + dataBytes.length);
            const view = new DataView(buffer);
            const uint8View = new Uint8Array(buffer);
            
            let offset = 0;
            
            // 写入魔数（大端序）
            view.setUint32(offset, this.PROTOBUF_MAGIC, false);
            offset += 4;
            
            // 写入版本（大端序）
            view.setUint32(offset, this.VERSION, false);
            offset += 4;
            
            // 写入加密标志
            view.setUint8(offset, encrypt && encryptKey ? 1 : 0);
            offset += 1;
            
            // 写入数据长度（大端序）
            view.setUint32(offset, dataBytes.length, false);
            offset += 4;
            
            // 写入数据
            uint8View.set(dataBytes, offset);
            
            const result = new Uint8Array(buffer);
            console.debug('序列化消息成功: 原始大小=%d, 最终大小=%d, 加密=%s',
                jsonBytes.length, result.length, encrypt && encryptKey);
            
            return result;
            
        } catch (error) {
            console.error('序列化消息失败:', error);
            return null;
        }
    }
    
    /**
     * 反序列化字节数组为WildfireMessage
     * @param {Uint8Array} data 要反序列化的字节数组
     * @param {string} decryptKey 解密密钥（可选）
     * @returns {Promise<WildfireMessage|null>} 反序列化后的消息对象
     */
    async deserialize(data, decryptKey = '') {
        try {
            if (!data || data.length < 13) { // 最小头部长度
                console.error('数据长度不足');
                return null;
            }
            
            const view = new DataView(data.buffer, data.byteOffset, data.byteLength);
            let offset = 0;
            
            // 1. 验证魔数
            const magic = view.getUint32(offset, false);
            offset += 4;
            if (magic !== this.PROTOBUF_MAGIC) {
                console.error('无效的魔数: 0x' + magic.toString(16));
                return null;
            }
            
            // 2. 检查版本
            const version = view.getUint32(offset, false);
            offset += 4;
            if (version !== this.VERSION) {
                console.warn('版本不匹配: 期望=%d, 实际=%d', this.VERSION, version);
            }
            
            // 3. 获取加密标志
            const encrypted = view.getUint8(offset) === 1;
            offset += 1;
            
            // 4. 获取数据长度
            const dataLength = view.getUint32(offset, false);
            offset += 4;
            if (dataLength <= 0 || dataLength > data.length - offset) {
                console.error('无效的数据长度: %d', dataLength);
                return null;
            }
            
            // 5. 提取数据
            const messageData = data.slice(offset, offset + dataLength);
            
            // 6. 如果数据被加密，进行解密
            let jsonBytes;
            if (encrypted && decryptKey) {
                const decryptResult = await aesUtil.AESDecrypt(messageData, decryptKey, true);
                if (!decryptResult || !decryptResult.data) {
                    console.error('AES解密失败');
                    return null;
                }
                jsonBytes = decryptResult.data;
            } else {
                jsonBytes = messageData;
            }
            
            // 7. 将JSON字符串转换为消息对象
            const jsonData = new TextDecoder().decode(jsonBytes);
            const message = JSON.parse(jsonData);
            
            // 8. 创建WildfireMessage实例并复制属性
            const wildfireMessage = new WildfireMessage();
            Object.assign(wildfireMessage, message);
            
            console.debug('反序列化消息成功: 数据大小=%d, 加密=%s, 消息类型=%s',
                dataLength, encrypted, message.type);
            
            return wildfireMessage;
            
        } catch (error) {
            console.error('反序列化消息失败:', error);
            return null;
        }
    }
    
    /**
     * 序列化任意对象为字节数组
     * @param {Object} object 要序列化的对象
     * @param {boolean} encrypt 是否加密
     * @param {string} encryptKey 加密密钥
     * @returns {Promise<Uint8Array|null>} 序列化后的字节数组
     */
    async serializeObject(object, encrypt = false, encryptKey = '') {
        try {
            const jsonData = JSON.stringify(object);
            const jsonBytes = new TextEncoder().encode(jsonData);
            
            if (encrypt && encryptKey) {
                const encryptedBytes = await aesUtil.AESEncrypt(jsonBytes, encryptKey);
                if (!encryptedBytes) {
                    console.error('对象加密失败');
                    return null;
                }
                return encryptedBytes;
            } else {
                return jsonBytes;
            }
            
        } catch (error) {
            console.error('序列化对象失败:', error);
            return null;
        }
    }
    
    /**
     * 反序列化字节数组为指定类型的对象
     * @param {Uint8Array} data 要反序列化的字节数组
     * @param {Function} clazz 目标类型构造函数
     * @param {boolean} encrypted 数据是否加密
     * @param {string} decryptKey 解密密钥
     * @returns {Promise<Object|null>} 反序列化后的对象
     */
    async deserializeObject(data, clazz, encrypted = false, decryptKey = '') {
        try {
            let jsonBytes;
            
            if (encrypted && decryptKey) {
                const decryptResult = await aesUtil.AESDecrypt(data, decryptKey, true);
                if (!decryptResult || !decryptResult.data) {
                    console.error('对象解密失败');
                    return null;
                }
                jsonBytes = decryptResult.data;
            } else {
                jsonBytes = data;
            }
            
            const jsonData = new TextDecoder().decode(jsonBytes);
            const parsedObject = JSON.parse(jsonData);
            
            // 创建目标类型实例并复制属性
            const instance = new clazz();
            Object.assign(instance, parsedObject);
            
            return instance;
            
        } catch (error) {
            console.error('反序列化对象失败:', error);
            return null;
        }
    }
    
    /**
     * 检查数据是否为有效的protobuf格式
     * @param {Uint8Array} data 要检查的数据
     * @returns {boolean} 是否为有效格式
     */
    isValidProtobufData(data) {
        if (!data || data.length < 13) {
            return false;
        }
        
        const view = new DataView(data.buffer, data.byteOffset, data.byteLength);
        const magic = view.getUint32(0, false);
        return magic === this.PROTOBUF_MAGIC;
    }
    
    /**
     * 获取数据格式信息
     * @param {Uint8Array} data 要分析的数据
     * @returns {string} 格式信息字符串
     */
    getDataInfo(data) {
        if (!data) {
            return 'null data';
        }
        
        if (data.length < 13) {
            return `数据长度不足: ${data.length} bytes`;
        }
        
        try {
            const view = new DataView(data.buffer, data.byteOffset, data.byteLength);
            const magic = view.getUint32(0, false);
            const version = view.getUint32(4, false);
            const encrypted = view.getUint8(8) === 1;
            const dataLength = view.getUint32(9, false);
            
            return `魔数=0x${magic.toString(16).toUpperCase()}, 版本=${version}, 加密=${encrypted}, 数据长度=${dataLength}, 总长度=${data.length}`;
            
        } catch (error) {
            return '解析失败: ' + error.message;
        }
    }
}

// 创建全局实例
const websocketProtobufSerializer = new WebSocketProtobufSerializer();

export default websocketProtobufSerializer;
export { websocketProtobufSerializer };