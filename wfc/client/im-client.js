// front/src/im/im-client.js

// 引入由 pbjs 生成的JS模块
// 确保你已经执行了 pbjs -t static-module ... 命令
import { wfc } from '../proto/bundle.js';

const WsUrl = "ws://192.168.10.113:1884/ws"; // 必须与后端WebSocket路径一致

class IMClient {
    constructor() {
        this.ws = null;
        this.eventListeners = new Map();
        this.seq = 0;
        this.heartbeatInterval = null;
    }

    // 事件监听（'connect', 'disconnect', 'message', 'error'等）
    on(eventName, callback) {
        if (!this.eventListeners.has(eventName)) {
            this.eventListeners.set(eventName, []);
        }
        this.eventListeners.get(eventName).push(callback);
    }

    emit(eventName, data) {
        if (this.eventListeners.has(eventName)) {
            this.eventListeners.get(eventName).forEach(callback => callback(data));
        }
    }

    connect(userId, token) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            console.log("WebSocket is already connected.");
            return;
        }

        this.ws = new WebSocket(WsUrl);
        this.ws.binaryType = 'arraybuffer'; // 关键：必须是二进制模式

        this.ws.onopen = () => {
            console.log("WebSocket connected!");
            this.emit('connect');
            this.sendConnect(userId, token);
        };

        this.ws.onmessage = (event) => {
            // event.data 是 ArrayBuffer
            this.handleMessage(event.data);
        };

        this.ws.onclose = () => {
            console.log("WebSocket disconnected.");
            this.stopHeartbeat();
            this.emit('disconnect');
        };

        this.ws.onerror = (error) => {
            console.error("WebSocket error:", error);
            this.emit('error', error);
        };
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }

    handleMessage(arrayBuffer) {
        const uint8Array = new Uint8Array(arrayBuffer);
        const dataView = new DataView(uint8Array.buffer);
        // 野火协议：大端序4字节长度 + Protobuf数据
        const bodyLength = dataView.getInt32(0, false);

        if (bodyLength !== uint8Array.length - 4) {
            console.error("Packet length mismatch!");
            return;
        }

        const protoBody = uint8Array.slice(4);
        try {
            const message = wfc.WFCMessage.Message.decode(protoBody);
            console.log("Received Decoded Message:", JSON.parse(JSON.stringify(message))); // 打印纯JS对象
            this.emit('message', message);

            // 登录成功后，开始心跳
            if (message.head.type === wfc.WFCMessage.Message.Head.MessageType.CONNACK && message.head.code === 0) {
                console.log("Login successful, starting heartbeat.");
                this.startHeartbeat();
            }
        } catch (e) {
            console.error("Protobuf decode error:", e);
        }
    }

    // 发送封装好的数据包
    send(message) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            console.error("WebSocket is not connected.");
            return;
        }

        // 1. 序列化Protobuf消息
        const protoBuffer = wfc.WFCMessage.Message.encode(message).finish();

        // 2. 构造完整数据包：4字节长度 + Protobuf数据
        const packet = new Uint8Array(4 + protoBuffer.length);
        const dataView = new DataView(packet.buffer);
        dataView.setInt32(0, protoBuffer.length, false); // 大端序写入长度
        packet.set(protoBuffer, 4);

        // 3. 发送二进制数据
        this.ws.send(packet.buffer);
        console.log("Sent Message:", JSON.parse(JSON.stringify(message)));
    }

    // 组装并发送CONNECT请求
    sendConnect(userId, token) {
        // 使用 pbjs 生成的 wfc 对象来创建消息
        const connectMessage = wfc.WFCMessage.Message.create({
            head: {
                type: wfc.WFCMessage.Message.Head.MessageType.CONNECT,
                seq: this.seq++,
            },
            connect: {
                clientId: `web_${userId}_${Date.now()}`,
                clientVersion: "1.0.0",
                platform: 2, // 2 for Web
                token: token,
            }
        });
        this.send(connectMessage);
    }

    // 组装并发送文本消息
    sendTextMessage(targetId, line, text) {
        const publishMessage = wfc.WFCMessage.Message.create({
            head: {
                type: wfc.WFCMessage.Message.Head.MessageType.PUBLISH,
                seq: this.seq++,
            },
            message: {
                conversation: { type: 1, target: targetId, line: line || 0 }, // 1 for single chat
                content: { type: 1, searchableContent: text }, // 1 for text content
                fromUser: this.userId, // 可以在这里设置发送者
            }
        });
        this.send(publishMessage);
    }

    // 开始心跳
    startHeartbeat() {
        this.stopHeartbeat(); // 先停止旧的，防止重复
        const pingMessage = wfc.WFCMessage.Message.create({
            head: { type: wfc.WFCMessage.Message.Head.MessageType.PING, seq: this.seq++ }
        });
        this.heartbeatInterval = setInterval(() => {
            console.log("Sending PING...");
            this.send(pingMessage);
        }, 30 * 1000); // 每30秒发一次心跳
    }

    // 停止心跳
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
}

export const imClient = new IMClient();
