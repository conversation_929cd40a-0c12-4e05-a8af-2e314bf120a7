/*
 * Copyright (c) 2020 WildFireChat. All rights reserved.
 */

/**
 * 野火IM WebSocket Protobuf消息定义
 * 
 * 与服务器端WildfireProtobufMessages.java保持一致
 * 支持二进制protobuf序列化和AES加密
 */

// 消息类型枚举
export const MessageType = {
    UNKNOWN: 0,
    CONNECT_REQUEST: 1,
    CONNECT_RESPONSE: 2,
    SEND_MESSAGE: 3,
    RECEIVE_MESSAGE: 4,
    GET_MESSAGES: 5,
    GET_USER_INFO: 6,
    GET_CONVERSATION_LIST: 7,
    CREATE_GROUP: 8,
    SEND_FRIEND_REQUEST: 9,
    SET_USER_SETTING: 10,
    SEARCH_USER: 11,
    UPLOAD_MEDIA: 12,
    HEARTBEAT: 13,
    ACK: 14,
    ERROR: 15
};

// 内容类型枚举
export const ContentType = {
    TEXT: 0,
    IMAGE: 1,
    VOICE: 2,
    VIDEO: 3,
    FILE: 4,
    LOCATION: 5,
    LINK: 6,
    STICKER: 7,
    COMPOSITE: 8
};

// 会话类型枚举
export const ConversationType = {
    SINGLE: 0,
    GROUP: 1,
    CHANNEL: 2,
    CHATROOM: 3
};

// Protobuf消息常量
export const PROTOBUF_MAGIC = 0x57494C44; // "WILD"
export const PROTOBUF_VERSION = 1;

/**
 * 野火消息基础结构
 */
export class WildfireMessage {
    constructor() {
        this.type = MessageType.UNKNOWN;
        this.messageId = '';
        this.timestamp = 0;
        this.fromUser = '';
        this.toUser = '';
        this.payload = null;
        this.headers = {};
    }
    
    setSequenceNumber(sequenceNumber) {
        this.headers.sequenceNumber = sequenceNumber.toString();
    }
    
    getSequenceNumber() {
        return parseInt(this.headers.sequenceNumber || '0');
    }
}

/**
 * 连接请求
 */
export class ConnectRequest {
    constructor() {
        this.userId = '';
        this.token = '';
        this.clientId = '';
        this.platform = '';
        this.version = '';
    }
}

/**
 * 连接响应
 */
export class ConnectResponse {
    constructor() {
        this.success = false;
        this.errorMessage = '';
        this.serverTime = 0;
        this.sessionId = '';
    }
}

/**
 * 会话信息
 */
export class Conversation {
    constructor() {
        this.type = ConversationType.SINGLE;
        this.target = '';
        this.line = 0;
    }
}

/**
 * 消息内容
 */
export class MessageContent {
    constructor() {
        this.type = ContentType.TEXT;
        this.text = '';
        this.url = '';
        this.thumbnailUrl = '';
        this.binaryData = null;
        this.extra = {};
    }
}

/**
 * 发送消息请求
 */
export class SendMessageRequest {
    constructor() {
        this.conversation = new Conversation();
        this.content = new MessageContent();
        this.toUsers = [];
    }
}

/**
 * 通用响应
 */
export class CommonResponse {
    constructor() {
        this.success = false;
        this.errorCode = 0;
        this.errorMessage = '';
        this.data = null;
    }
}

/**
 * 错误码常量
 */
export const ErrorCode = {
    SUCCESS: 0,
    INVALID_PARAMETER: 1,
    AUTHENTICATION_FAILED: 2,
    PERMISSION_DENIED: 3,
    USER_NOT_FOUND: 4,
    GROUP_NOT_FOUND: 5,
    MESSAGE_NOT_FOUND: 6,
    NETWORK_ERROR: 7,
    SERVER_ERROR: 8,
    ENCRYPTION_ERROR: 9,
    TIMEOUT: 10,
    UNKNOWN_ERROR: 99
};

/**
 * 平台类型常量
 */
export const PlatformType = {
    UNKNOWN: 0,
    APP: 1,
    H5_WEB: 2,
    MINI_PROGRAM: 3
};

/**
 * 消息验证工具类
 */
export class MessageValidator {
    /**
     * 验证消息结构是否正确
     * @param {WildfireMessage} message 要验证的消息
     * @returns {Object} 验证结果 {valid: boolean, errors: string[]}
     */
    static validateMessage(message) {
        const errors = [];
        
        // 检查基本字段
        if (!message.type) {
            errors.push('消息类型不能为空');
        }
        
        if (!message.messageId) {
            errors.push('消息ID不能为空');
        }
        
        if (!message.timestamp || message.timestamp <= 0) {
            errors.push('时间戳无效');
        }
        
        return {
            valid: errors.length === 0,
            errors: errors
        };
    }
    
    /**
     * 验证连接请求
     * @param {ConnectRequest} request 连接请求
     * @returns {Object} 验证结果
     */
    static validateConnectRequest(request) {
        const errors = [];
        
        if (!request.userId || request.userId.trim() === '') {
            errors.push('用户ID不能为空');
        }
        
        if (!request.clientId || request.clientId.trim() === '') {
            errors.push('客户端ID不能为空');
        }
        
        if (!request.platform || request.platform.trim() === '') {
            errors.push('平台类型不能为空');
        }
        
        return {
            valid: errors.length === 0,
            errors: errors
        };
    }
}

/**
 * 消息工厂类
 */
export class MessageFactory {
    /**
     * 创建连接请求消息
     * @param {string} userId 用户ID
     * @param {string} token 认证令牌
     * @param {string} clientId 客户端ID
     * @param {string} platform 平台类型
     * @returns {WildfireMessage} 连接请求消息
     */
    static createConnectRequest(userId, token, clientId, platform) {
        const request = new ConnectRequest();
        request.userId = userId;
        request.token = token;
        request.clientId = clientId;
        request.platform = platform;
        request.version = '1.0.0';
        
        const message = new WildfireMessage();
        message.type = MessageType.CONNECT_REQUEST;
        message.messageId = this.generateMessageId();
        message.timestamp = Date.now();
        message.fromUser = userId;
        message.payload = JSON.stringify(request);
        
        return message;
    }
    
    /**
     * 创建发送消息请求
     * @param {Conversation} conversation 会话信息
     * @param {MessageContent} content 消息内容
     * @param {string} fromUser 发送者
     * @returns {WildfireMessage} 发送消息请求
     */
    static createSendMessageRequest(conversation, content, fromUser) {
        const request = new SendMessageRequest();
        request.conversation = conversation;
        request.content = content;
        
        const message = new WildfireMessage();
        message.type = MessageType.SEND_MESSAGE;
        message.messageId = this.generateMessageId();
        message.timestamp = Date.now();
        message.fromUser = fromUser;
        message.payload = JSON.stringify(request);
        
        return message;
    }
    
    /**
     * 创建心跳消息
     * @param {string} fromUser 发送者
     * @returns {WildfireMessage} 心跳消息
     */
    static createHeartbeatMessage(fromUser) {
        const message = new WildfireMessage();
        message.type = MessageType.HEARTBEAT;
        message.messageId = this.generateMessageId();
        message.timestamp = Date.now();
        message.fromUser = fromUser;
        message.payload = JSON.stringify({ ping: Date.now() });
        
        return message;
    }
    
    /**
     * 生成消息ID
     * @returns {string} 消息ID
     */
    static generateMessageId() {
        return 'msg_' + Date.now() + '_' + Math.floor(Math.random() * 10000);
    }
}

export default {
    MessageType,
    ContentType,
    ConversationType,
    PROTOBUF_MAGIC,
    PROTOBUF_VERSION,
    WildfireMessage,
    ConnectRequest,
    ConnectResponse,
    Conversation,
    MessageContent,
    SendMessageRequest,
    CommonResponse,
    ErrorCode,
    PlatformType,
    MessageValidator,
    MessageFactory
};