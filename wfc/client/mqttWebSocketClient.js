/*
 * MQTT over WebSocket Client Implementation using the standard 'mqtt' library.
 * This version is robust, reliable, and follows best practices.
 * Enhanced with WebSocket Protobuf support for unified communication.
 */

import { EventEmitter } from 'events';
import mqtt from 'mqtt'; // 引入 mqtt 库
import ConnectionStatus from './connectionStatus';
import getClientId from "../util/clientId";
import websocketProtobufSerializer from './websocketProtobufSerializer.js';
import { MessageFactory, MessageType, WildfireMessage } from './websocketProtobufMessages.js';
import { applyMqttClientPatch, createNoPubackOptions } from './mqttClientPatch.js';

class MqttWebSocketClient extends EventEmitter {
    constructor() {
        super();
        this.client = null;
        this.connectionStatus = ConnectionStatus.ConnectionStatusUnconnected;
        this.host = '';
        this.userId = '';
        this.token = '';
        this.clientId = '';
        this.serverDeltaTime = 0;
        this.isManualDisconnect = false;

        // WebSocket Protobuf 支持
        this.enableProtobuf = false;
        this.sessionId = '';
        this.messageQueue = [];
        this.pendingMessages = new Map();

        // 统计信息
        this.stats = {
            sent: 0,
            received: 0,
            errors: 0,
            protobufMessages: 0,
            mqttMessages: 0
        };
    }

    /**
     * Connect to MQTT broker via WebSocket.
     * @param {string} host - 主机/IP 或完整 ws:// URL。例如 '**************' 或 'ws://**************:1884/mqtt'
     * @param {string} userId - 用户 ID，用作 MQTT 用户名
     * @param {string} token - 认证 token，作为密码
     * @param {number} [port=1884] - WebSocket 端口（当 host 不是完整 ws:// URL 时生效）
     * @returns {Promise<number>} 解析为服务器时间戳
     */
    async connect(host, userId, token, port = 1884) {
        console.log('[MQTT] Connecting to:', host, 'User:', userId, 'token:', token);

        // 如果已连接，先断开
        // if (this.client && this.client.connected) {
        //     this.disconnect();
        // }

        this.host = "**************";
        this.userId = userId;
        this.token = token;
        this.clientId = getClientId(); // 保证ClientID唯一
        this.isManualDisconnect = false;

        let wsUrl;
        if (this.host.startsWith('ws://') || this.host.startsWith('wss://')) {
            wsUrl = this.host; // 已经是完整的 WebSocket URL
        } else {
            wsUrl = `ws://${this.host}:${port}/mqtt`;
        }
        console.log('[MQTT] WebSocket URL:', wsUrl);

        // 使用专门的配置来避免PUBACK消息
        const options = createNoPubackOptions({
            clientId: this.clientId,
            username: this.userId,
            password: this.token,
            keepalive: 60, // seconds
            reconnectPeriod: 1000, // ms
            connectTimeout: 30 * 1000, // ms
        });

        return new Promise((resolve, reject) => {
            this.setConnectionStatus(ConnectionStatus.ConnectionStatusConnecting);
            console.log('[MQTT] Connecting 开始连接。。。。。。。', wsUrl);
            try {
                this.client = mqtt.connect(wsUrl, options);
                console.log('[MQTT] Connecting 开始连接。。。。。。。');
            } catch (error) {
                console.error('报错了 没连上 [MQTT] Connection setup failed:', error);
                this.setConnectionStatus(ConnectionStatus.ConnectionStatusConnectFail);
                reject(error);
                return;
            }

            this.client.on('connect', (connack) => {
                console.log('成功啦！！！！！！[MQTT] Connected successfully.', connack);
                this.setConnectionStatus(ConnectionStatus.ConnectionStatusConnected);

                // 应用MQTT客户端补丁，阻止PUBACK消息发送
                applyMqttClientPatch(this.client);

                // 可以在这里处理 connack 中携带的 payload，如果需要的话
                // 例如解析 serverTime 来计算 serverDeltaTime
                resolve(Math.floor(Date.now() / 1000));
            });

            // 等待客户端完全初始化后再设置拦截器
            setTimeout(() => {
                // 方法1：重写_sendPacket方法，完全阻止PUBACK消息发送
                if (this.client && this.client._sendPacket) {
                    const originalSendPacket = this.client._sendPacket;
                    this.client._sendPacket = (packet, cb) => {
                        // 如果是PUBACK消息（类型5），直接忽略
                        if (packet.cmd === 'puback') {
                            console.log('[MQTT] 🚫 阻止发送PUBACK消息（类型5）');
                            if (cb) cb();
                            return;
                        }
                        console.log('[MQTT] ✅ 允许发送消息类型:', packet.cmd);
                        // 其他消息正常发送
                        originalSendPacket.call(this.client, packet, cb);
                    };
                }

                // 方法2：重写消息处理，避免触发PUBACK
                if (this.client && this.client._handlePublish) {
                    this.client._handlePublish = (packet, done) => {
                        console.log('[MQTT] 📨 处理PUBLISH消息，不发送PUBACK确认');
                        // 直接触发message事件，不调用done()
                        this.client.emit('message', packet.topic, packet.payload, packet);
                        // 重要：不调用done()，避免发送PUBACK
                    };
                }

                // 方法3：重写stream写入方法（最底层拦截）
                if (this.client && this.client.stream && this.client.stream.write) {
                    const originalWrite = this.client.stream.write;
                    this.client.stream.write = function (buffer, encoding, callback) {
                        // 检查是否为PUBACK消息（第一个字节的高4位为5）
                        if (buffer && buffer.length > 0) {
                            const messageType = (buffer[0] >> 4) & 0x0F;
                            if (messageType === 5) {
                                console.log('[MQTT] 🚫 在stream层阻止PUBACK消息发送');
                                if (callback) callback();
                                return true;
                            }
                        }
                        return originalWrite.call(this, buffer, encoding, callback);
                    };
                }
            }, 100);

            this.client.on('message', async (topic, payload, packet) => {
                console.log('接收到后台消息[MQTT] Message received:', { topic, payloadLength: payload.length });

                this.stats.received++;

                // 尝试解析为 Protobuf 消息
                if (this.enableProtobuf && payload.length > 13) {
                    try {
                        const protobufMessage = await this.handleProtobufMessage(payload);
                        if (protobufMessage) {
                            this.stats.protobufMessages++;
                            this.emit('protobufMessage', protobufMessage, topic);
                            return;
                        }
                    } catch (error) {
                        console.warn('Protobuf消息解析失败，尝试传统处理:', error.message);
                    }
                }

                // 传统 MQTT 消息处理
                this.stats.mqttMessages++;
                this.emit('message', { topic, payload: payload, qos: packet.qos, retain: packet.retain });
            });

            this.client.on('error', (error) => {
                console.error('后台 报错啦~~~~~[MQTT] Connection error:', error);
                // 不在这里设置失败状态，'close'事件会处理
                // reject(error); // 首次连接失败时，让 'close' 事件触发 reject
            });

            this.client.on('close', () => {
                console.log('后台 断开了  [MQTT] Connection closed.');
                // 如果不是手动断开，则认为是连接失败或丢失
                if (!this.isManualDisconnect) {
                    this.setConnectionStatus(ConnectionStatus.ConnectionStatusUnconnected);
                    // 首次连接失败时 reject
                    if (this.connectionStatus !== ConnectionStatus.ConnectionStatusConnected) {
                        reject(new Error('Failed to connect to the MQTT broker.'));
                    }
                } else {
                    this.setConnectionStatus(ConnectionStatus.ConnectionStatusUnconnected);
                }
            });

            this.client.on('offline', () => {
                console.log('离线啦 ~~~ [MQTT] Client is offline.');
                this.setConnectionStatus(ConnectionStatus.ConnectionStatusUnconnected);
            });

            this.client.on('reconnect', () => {
                console.log('重新连接啦~~~~ [MQTT] Reconnecting...');
                this.setConnectionStatus(ConnectionStatus.ConnectionStatusConnecting);
            });
        });
    }

    /**
     * Disconnect from MQTT broker.
     */
    disconnect() {
        console.log('[MQTT] Disconnecting...');
        this.isManualDisconnect = true;
        if (this.client) {
            this.client.end(true, () => { // force close, do not queue messages
                console.log('[MQTT] Disconnected by client.end()');
            });
            this.client = null;
        }
        this.setConnectionStatus(ConnectionStatus.ConnectionStatusUnconnected);
    }

    /**
     * Publish a message to a topic.
     * @param {string} topic - MQTT topic
     * @param {Buffer|Uint8Array} payload - Message payload
     * @param {number} qos - Quality of Service level (强制使用0避免确认消息)
     * @param {boolean} retain - Retain flag
     * @returns {Promise<void>}
     */
    publish(topic, payload, qos = 0, retain = false) {
        return new Promise((resolve, reject) => {
            if (!this.isConnected()) {
                // return reject(new Error('Not connected to MQTT broker'));
                this.connect(this.host, this.userId, this.token).then(() => {
                    console.log('[MQTT] Connected successfully.');
                    resolve();
                });
                return;
            }

            // payload 应该是 Buffer 或 Uint8Array
            this.client.publish(topic, payload, { qos, retain }, (error) => {
                if (error) {
                    console.error('[MQTT] Publish error:', error);
                    this.stats.errors++;
                    return reject(error);
                }
                this.stats.sent++;
                this.stats.mqttMessages++;
                resolve();
            });
        });
    }

    /**
     * 发送 WebSocket Protobuf 消息
     * @param {WildfireMessage} message - 要发送的 Protobuf 消息
     * @param {string} topic - MQTT 主题（可选，默认为 'UP'）
     * @param {number} qos - QoS 级别（强制使用0避免确认消息）
     * @returns {Promise<boolean>} 发送结果
     */
    async sendProtobufMessage(message, topic = 'UP', qos = 0) {
        try {
            if (!this.isConnected()) {
                console.log('连接未建立，消息加入队列');
                this.messageQueue.push({ message, topic, qos });
                return false;
            }

            // 设置消息基本信息
            if (!message.messageId) {
                message.messageId = MessageFactory.generateMessageId();
            }
            if (!message.timestamp) {
                message.timestamp = Date.now();
            }
            if (!message.fromUser) {
                message.fromUser = this.userId;
            }

            // 序列化消息
            const serializedData = await websocketProtobufSerializer.serialize(
                message,
                true, // 启用加密
                this.token
            );

            if (!serializedData) {
                console.error('Protobuf消息序列化失败');
                this.stats.errors++;
                return false;
            }

            // 通过 MQTT 发送二进制数据
            await this.publish(topic, serializedData, qos);

            // 记录待响应消息
            this.pendingMessages.set(message.messageId, {
                message: message,
                timestamp: Date.now()
            });

            this.stats.protobufMessages++;
            console.log(`发送Protobuf消息: ${message.type} (${message.messageId})`);

            return true;

        } catch (error) {
            console.error(`发送Protobuf消息失败: ${error.message}`);
            this.stats.errors++;
            return false;
        }
    }

    /**
     * 发送连接请求（Protobuf格式）
     * @returns {Promise<boolean>} 发送结果
     */
    async sendProtobufConnectRequest() {
        const message = MessageFactory.createConnectRequest(
            this.userId,
            this.token,
            this.clientId,
            'H5' // 平台类型
        );

        return await this.sendProtobufMessage(message);
    }

    /**
     * 发送心跳消息（Protobuf格式）
     * @returns {Promise<boolean>} 发送结果
     */
    async sendProtobufHeartbeat() {
        const message = MessageFactory.createHeartbeatMessage(this.userId);
        return await this.sendProtobufMessage(message);
    }

    /**
     * 发送文本消息（Protobuf格式）
     * @param {string} targetId - 目标用户/群组ID
     * @param {string} text - 消息文本
     * @param {number} conversationType - 会话类型
     * @returns {Promise<boolean>} 发送结果
     */
    async sendProtobufTextMessage(targetId, text, conversationType = 0) {
        const conversation = {
            type: conversationType,
            target: targetId,
            line: 0
        };

        const content = {
            type: 0, // TEXT
            text: text
        };

        const message = MessageFactory.createSendMessageRequest(conversation, content, this.userId);
        return await this.sendProtobufMessage(message);
    }

    /**
     * Subscribe to a topic.
     * @param {string} topic - MQTT topic
     * @param {number} qos - Quality of Service level (强制使用0避免确认消息)
     * @returns {Promise<void>}
     */
    subscribe(topic, qos = 0) {
        return new Promise((resolve, reject) => {
            if (!this.isConnected()) {
                return reject(new Error('Not connected to MQTT broker'));
            }

            this.client.subscribe(topic, { qos }, (error, granted) => {
                if (error) {
                    console.error('[MQTT] Subscribe error:', error);
                    return reject(error);
                }
                console.log('[MQTT] Subscribed successfully:', granted);
                resolve();
            });
        });
    }

    /**
     * Unsubscribe from a topic.
     * @param {string} topic - MQTT topic
     * @returns {Promise<void>}
     */
    unsubscribe(topic) {
        return new Promise((resolve, reject) => {
            if (!this.isConnected()) {
                return reject(new Error('Not connected to MQTT broker'));
            }

            this.client.unsubscribe(topic, {}, (error) => {
                if (error) {
                    console.error('[MQTT] Unsubscribe error:', error);
                    return reject(error);
                }
                resolve();
            });
        });
    }

    // --- Helper methods to maintain API compatibility with your old class ---

    isConnected() {
        return this.connectionStatus === ConnectionStatus.ConnectionStatusConnected;
    }

    setConnectionStatus(status) {
        if (this.connectionStatus !== status) {
            this.connectionStatus = status;
            this.emit('connectionStatusChanged', status);
            console.log('[MQTT] Connection status changed to:', status);
        }
    }

    getConnectionStatus() {
        return this.connectionStatus;
    }

    getMqttClientId() {
        return getClientId();
    }

    getUserId() {
        return this.userId;
    }

    getServerDeltaTime() {
        return this.serverDeltaTime;
    }

    /**
     * 启用/禁用 Protobuf 支持
     * @param {boolean} enable - 是否启用
     */
    setProtobufEnabled(enable) {
        this.enableProtobuf = enable;
        console.log(`[MQTT] Protobuf support ${enable ? 'enabled' : 'disabled'}`);
    }

    /**
     * 处理 Protobuf 消息
     * @param {Buffer|Uint8Array} payload - 消息载荷
     * @returns {Promise<WildfireMessage|null>} 解析后的消息
     */
    async handleProtobufMessage(payload) {
        try {
            // 转换为 Uint8Array
            const data = payload instanceof Buffer ? new Uint8Array(payload) : payload;

            // 检查是否为有效的 Protobuf 格式
            if (!websocketProtobufSerializer.isValidProtobufData(data)) {
                return null;
            }

            // 反序列化消息
            const message = await websocketProtobufSerializer.deserialize(data, this.token);

            if (message) {
                console.log(`收到Protobuf消息: ${message.type} (${message.messageId})`);

                // 处理不同类型的消息
                await this.processProtobufMessage(message);

                // 移除待响应消息
                if (this.pendingMessages.has(message.messageId)) {
                    this.pendingMessages.delete(message.messageId);
                }
            }

            return message;

        } catch (error) {
            console.error('处理Protobuf消息失败:', error);
            return null;
        }
    }

    /**
     * 处理不同类型的 Protobuf 消息
     * @param {WildfireMessage} message - 消息对象
     */
    async processProtobufMessage(message) {
        try {
            switch (message.type) {
                case MessageType.CONNECT_RESPONSE:
                    await this.handleConnectResponse(message);
                    break;

                case MessageType.RECEIVE_MESSAGE:
                    await this.handleReceiveMessage(message);
                    break;

                case MessageType.ACK:
                    await this.handleAckMessage(message);
                    break;

                case MessageType.ERROR:
                    await this.handleErrorMessage(message);
                    break;

                case MessageType.HEARTBEAT:
                    console.log('收到心跳响应');
                    break;

                default:
                    console.log(`未处理的消息类型: ${message.type}`);
                    break;
            }
        } catch (error) {
            console.error('处理Protobuf消息类型失败:', error);
        }
    }

    /**
     * 处理连接响应
     * @param {WildfireMessage} message - 连接响应消息
     */
    async handleConnectResponse(message) {
        try {
            const response = JSON.parse(message.payload);

            if (response.success) {
                this.sessionId = response.sessionId;
                console.log(`Protobuf连接成功，会话ID: ${this.sessionId}`);

                // 发送队列中的消息
                await this.sendQueuedMessages();

                this.emit('protobufConnected', response);
            } else {
                console.error(`Protobuf连接失败: ${response.errorMessage}`);
                this.emit('protobufConnectFailed', response);
            }

        } catch (error) {
            console.error('处理连接响应失败:', error);
        }
    }

    /**
     * 处理接收消息
     * @param {WildfireMessage} message - 接收到的消息
     */
    async handleReceiveMessage(message) {
        try {
            const messageData = JSON.parse(message.payload);
            console.log(`收到新消息: 来自 ${message.fromUser}`);

            this.emit('protobufReceiveMessage', messageData, message);

        } catch (error) {
            console.error('处理接收消息失败:', error);
        }
    }

    /**
     * 处理确认消息
     * @param {WildfireMessage} message - 确认消息
     */
    async handleAckMessage(message) {
        console.log(`收到确认消息: ${message.messageId}`);
        this.emit('protobufAck', message);
    }

    /**
     * 处理错误消息
     * @param {WildfireMessage} message - 错误消息
     */
    async handleErrorMessage(message) {
        try {
            const errorData = JSON.parse(message.payload);
            console.error(`服务器错误: ${errorData.errorMessage} (${errorData.errorCode})`);

            this.stats.errors++;
            this.emit('protobufError', errorData, message);

        } catch (error) {
            console.error('处理错误消息失败:', error);
        }
    }

    /**
     * 发送队列中的消息
     */
    async sendQueuedMessages() {
        if (this.messageQueue.length > 0) {
            console.log(`发送队列中的${this.messageQueue.length}条消息`);

            const messages = [...this.messageQueue];
            this.messageQueue = [];

            for (const { message, topic, qos } of messages) {
                await this.sendProtobufMessage(message, topic, qos);
                // 添加小延迟避免消息过快
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            queueLength: this.messageQueue.length,
            pendingCount: this.pendingMessages.size,
            sessionId: this.sessionId,
            protobufEnabled: this.enableProtobuf
        };
    }

    /**
     * 清理过期的待响应消息
     * @param {number} timeout - 超时时间（毫秒，默认30秒）
     */
    cleanupPendingMessages(timeout = 30000) {
        const now = Date.now();
        const expiredMessages = [];

        for (const [messageId, info] of this.pendingMessages.entries()) {
            if (now - info.timestamp > timeout) {
                expiredMessages.push(messageId);
            }
        }

        expiredMessages.forEach(messageId => {
            this.pendingMessages.delete(messageId);
            console.warn(`清理过期消息: ${messageId}`);
        });

        return expiredMessages.length;
    }

    /**
     * 重置统计信息
     */
    resetStats() {
        this.stats = {
            sent: 0,
            received: 0,
            errors: 0,
            protobufMessages: 0,
            mqttMessages: 0
        };
    }
}

export default MqttWebSocketClient;
