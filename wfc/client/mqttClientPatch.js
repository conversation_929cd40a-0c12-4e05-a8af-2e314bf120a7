/*
 * MQTT客户端补丁 - 专门用于阻止PUBACK消息发送
 * 解决后端不支持MQTT消息类型5（PUBACK）的问题
 */

/**
 * 应用MQTT客户端补丁，阻止PUBACK消息发送
 * @param {Object} mqttClient - MQTT客户端实例
 */
export function applyMqttClientPatch(mqttClient) {
    if (!mqttClient) {
        console.warn('[MQTT Patch] 客户端实例为空，无法应用补丁');
        return;
    }

    console.log('[MQTT Patch] 开始应用MQTT客户端补丁...');

    // 方法1：在最底层拦截所有发送的数据包
    const originalWrite = mqttClient.stream?.write;
    if (originalWrite) {
        mqttClient.stream.write = function(buffer, encoding, callback) {
            // 检查是否为PUBACK消息（第一个字节的高4位为5）
            if (buffer && buffer.length > 0) {
                const messageType = (buffer[0] >> 4) & 0x0F;
                if (messageType === 5) {
                    console.log('[MQTT Patch] 🚫 在底层阻止PUBACK消息发送（类型5）');
                    // 直接调用回调，假装发送成功
                    if (callback) callback();
                    return true;
                }
            }
            // 其他消息正常发送
            return originalWrite.call(this, buffer, encoding, callback);
        };
        console.log('[MQTT Patch] ✅ 已应用底层数据包拦截');
    }

    // 方法2：重写_sendPacket方法
    const originalSendPacket = mqttClient._sendPacket;
    if (originalSendPacket) {
        mqttClient._sendPacket = function(packet, cb) {
            // 如果是PUBACK消息，直接忽略
            if (packet.cmd === 'puback') {
                console.log('[MQTT Patch] 🚫 阻止发送PUBACK包（类型5）');
                if (cb) cb();
                return;
            }
            // 其他消息正常发送
            return originalSendPacket.call(this, packet, cb);
        };
        console.log('[MQTT Patch] ✅ 已应用_sendPacket拦截');
    }

    // 方法3：重写消息处理函数，避免触发PUBACK
    const originalHandlePublish = mqttClient._handlePublish;
    if (originalHandlePublish) {
        mqttClient._handlePublish = function(packet, done) {
            console.log('[MQTT Patch] 📨 处理PUBLISH消息，不发送PUBACK确认');
            // 直接触发message事件，不调用done()避免发送PUBACK
            this.emit('message', packet.topic, packet.payload, packet);
            // 重要：不调用done()，这样就不会发送PUBACK消息
        };
        console.log('[MQTT Patch] ✅ 已应用_handlePublish拦截');
    }

    // 方法4：强制设置客户端选项
    if (mqttClient.options) {
        mqttClient.options.qos = 0;
        mqttClient.options.manualAcks = true;
        console.log('[MQTT Patch] ✅ 已强制设置客户端选项');
    }

    console.log('[MQTT Patch] MQTT客户端补丁应用完成');
}

/**
 * 创建不发送PUBACK的自定义MQTT选项
 * @param {Object} baseOptions - 基础MQTT选项
 * @returns {Object} 修改后的选项
 */
export function createNoPubackOptions(baseOptions = {}) {
    return {
        ...baseOptions,
        // 强制使用QoS 0，避免需要确认
        qos: 0,
        retain: false,
        // 禁用自动确认
        manualAcks: true,
        // 设置协议版本
        protocolVersion: 4,
        // 禁用自动重连时的订阅
        resubscribe: false,
        // 清理会话
        clean: true
    };
}

export default {
    applyMqttClientPatch,
    createNoPubackOptions
};