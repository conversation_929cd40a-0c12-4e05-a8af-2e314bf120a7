/*
 * Copyright (c) 2020 WildFireChat. All rights reserved.
 */

/**
 * WebSocket Protobuf 集成扩展
 * 为现有的 WfcImpl 类添加 WebSocket Protobuf 支持
 */

import MqttWebSocketClient from './mqttWebSocketClient.js';
import websocketProtobufSerializer from './websocketProtobufSerializer.js';
import { MessageFactory, MessageType, ConversationType, ContentType } from './websocketProtobufMessages.js';
import EventType from './wfcEvent.js';

/**
 * WebSocket Protobuf 集成类
 * 扩展现有的 WfcImpl 功能
 */
export class WebSocketProtobufIntegration {
    constructor(wfcImpl) {
        this.wfcImpl = wfcImpl;
        this.mqttWebSocketClient = null;
        this.enableWebSocketProtobuf = false;
        this.webSocketProtobufSessionId = '';
        this.webSocketProtobufStats = {
            sent: 0,
            received: 0,
            errors: 0,
            protobufMessages: 0,
            mqttMessages: 0
        };
        
        // 绑定方法到 wfcImpl
        this._bindMethods();
    }

    /**
     * 绑定方法到 WfcImpl 实例
     */
    _bindMethods() {
        // 初始化方法
        this.wfcImpl._initWebSocketProtobuf = this._initWebSocketProtobuf.bind(this);
        
        // WebSocket Protobuf 控制方法
        this.wfcImpl.enableWebSocketProtobuf = this.enableWebSocketProtobuf.bind(this);
        this.wfcImpl.disableWebSocketProtobuf = this.disableWebSocketProtobuf.bind(this);
        this.wfcImpl.isWebSocketProtobufEnabled = this.isWebSocketProtobufEnabled.bind(this);
        
        // WebSocket Protobuf 消息发送方法
        this.wfcImpl.sendWebSocketProtobufMessage = this.sendWebSocketProtobufMessage.bind(this);
        this.wfcImpl.sendWebSocketProtobufTextMessage = this.sendWebSocketProtobufTextMessage.bind(this);
        this.wfcImpl.sendWebSocketProtobufHeartbeat = this.sendWebSocketProtobufHeartbeat.bind(this);
        
        // WebSocket Protobuf 统计和状态方法
        this.wfcImpl.getWebSocketProtobufStats = this.getWebSocketProtobufStats.bind(this);
        this.wfcImpl.getWebSocketProtobufSessionId = this.getWebSocketProtobufSessionId.bind(this);
        this.wfcImpl.resetWebSocketProtobufStats = this.resetWebSocketProtobufStats.bind(this);
    }

    /**
     * 初始化 WebSocket Protobuf 客户端
     */
    _initWebSocketProtobuf() {
        try {
            console.log('[WebSocket Protobuf] 初始化客户端...');
            
            if (!this.mqttWebSocketClient) {
                this.mqttWebSocketClient = new MqttWebSocketClient();
                
                // 设置事件监听器
                this._setupWebSocketProtobufEventListeners();
                
                console.log('[WebSocket Protobuf] 客户端初始化完成');
            }
            
            return true;
        } catch (error) {
            console.error('[WebSocket Protobuf] 初始化失败:', error);
            return false;
        }
    }

    /**
     * 设置 WebSocket Protobuf 事件监听器
     */
    _setupWebSocketProtobufEventListeners() {
        if (!this.mqttWebSocketClient) return;

        // 连接状态变化
        this.mqttWebSocketClient.on('connectionStatusChanged', (status) => {
            console.log(`[WebSocket Protobuf] 连接状态变化: ${status}`);
            
            // 如果启用了 Protobuf 且连接成功，发送连接请求
            if (this.enableWebSocketProtobuf && status === 1) { // ConnectionStatusConnected
                this.mqttWebSocketClient.setProtobufEnabled(true);
                this.mqttWebSocketClient.sendProtobufConnectRequest();
            }
        });

        // Protobuf 连接成功
        this.mqttWebSocketClient.on('protobufConnected', (response) => {
            console.log('[WebSocket Protobuf] Protobuf连接成功:', response);
            this.webSocketProtobufSessionId = response.sessionId;
            
            // 触发自定义事件
            if (this.wfcImpl.eventEmitter) {
                this.wfcImpl.eventEmitter.emit(EventType.WebSocketProtobufConnected, response);
            }
        });

        // Protobuf 连接失败
        this.mqttWebSocketClient.on('protobufConnectFailed', (response) => {
            console.error('[WebSocket Protobuf] Protobuf连接失败:', response);
            
            // 触发自定义事件
            if (this.wfcImpl.eventEmitter) {
                this.wfcImpl.eventEmitter.emit(EventType.WebSocketProtobufConnectFailed, response);
            }
        });

        // 接收到 Protobuf 消息
        this.mqttWebSocketClient.on('protobufMessage', (message, topic) => {
            console.log('[WebSocket Protobuf] 收到消息:', message.type, topic);
            this.webSocketProtobufStats.received++;
            this.webSocketProtobufStats.protobufMessages++;
            
            // 触发自定义事件
            if (this.wfcImpl.eventEmitter) {
                this.wfcImpl.eventEmitter.emit(EventType.WebSocketProtobufMessage, message, topic);
            }
        });

        // 接收到新消息
        this.mqttWebSocketClient.on('protobufReceiveMessage', (messageData, message) => {
            console.log('[WebSocket Protobuf] 收到新消息:', messageData);
            
            // 触发自定义事件
            if (this.wfcImpl.eventEmitter) {
                this.wfcImpl.eventEmitter.emit(EventType.WebSocketProtobufReceiveMessage, messageData, message);
            }
        });

        // Protobuf 错误
        this.mqttWebSocketClient.on('protobufError', (errorData, message) => {
            console.error('[WebSocket Protobuf] 错误:', errorData);
            this.webSocketProtobufStats.errors++;
            
            // 触发自定义事件
            if (this.wfcImpl.eventEmitter) {
                this.wfcImpl.eventEmitter.emit(EventType.WebSocketProtobufError, errorData, message);
            }
        });

        // 传统 MQTT 消息（保持兼容性）
        this.mqttWebSocketClient.on('message', (messageInfo) => {
            this.webSocketProtobufStats.mqttMessages++;
            
            // 继续使用现有的消息处理逻辑
            if (this.wfcImpl.eventEmitter) {
                this.wfcImpl.eventEmitter.emit(EventType.MqttMessage, messageInfo);
            }
        });
    }

    /**
     * 启用 WebSocket Protobuf 支持
     * @param {boolean} autoConnect 是否自动连接
     * @returns {Promise<boolean>} 启用结果
     */
    async enableWebSocketProtobuf(autoConnect = true) {
        try {
            console.log('[WebSocket Protobuf] 启用 Protobuf 支持...');
            
            if (!this.mqttWebSocketClient) {
                this._initWebSocketProtobuf();
            }
            
            this.enableWebSocketProtobuf = true;
            this.mqttWebSocketClient.setProtobufEnabled(true);
            
            if (autoConnect && this.wfcImpl.userId && this.wfcImpl.token) {
                const success = await this.mqttWebSocketClient.connect(
                    '192.168.10.113', // 使用配置的服务器地址
                    this.wfcImpl.userId,
                    this.wfcImpl.token
                );
                
                if (success) {
                    console.log('[WebSocket Protobuf] 自动连接成功');
                } else {
                    console.warn('[WebSocket Protobuf] 自动连接失败');
                }
                
                return success;
            }
            
            console.log('[WebSocket Protobuf] Protobuf 支持已启用');
            return true;
            
        } catch (error) {
            console.error('[WebSocket Protobuf] 启用失败:', error);
            return false;
        }
    }

    /**
     * 禁用 WebSocket Protobuf 支持
     */
    disableWebSocketProtobuf() {
        console.log('[WebSocket Protobuf] 禁用 Protobuf 支持...');
        
        this.enableWebSocketProtobuf = false;
        
        if (this.mqttWebSocketClient) {
            this.mqttWebSocketClient.setProtobufEnabled(false);
            this.mqttWebSocketClient.disconnect();
        }
        
        this.webSocketProtobufSessionId = '';
        console.log('[WebSocket Protobuf] Protobuf 支持已禁用');
    }

    /**
     * 检查是否启用了 WebSocket Protobuf
     * @returns {boolean} 是否启用
     */
    isWebSocketProtobufEnabled() {
        return this.enableWebSocketProtobuf && this.mqttWebSocketClient && this.mqttWebSocketClient.isConnected();
    }

    /**
     * 发送 WebSocket Protobuf 消息
     * @param {Object} message Protobuf 消息对象
     * @param {string} topic MQTT 主题
     * @returns {Promise<boolean>} 发送结果
     */
    async sendWebSocketProtobufMessage(message, topic = 'UP') {
        if (!this.isWebSocketProtobufEnabled()) {
            console.warn('[WebSocket Protobuf] Protobuf 未启用或未连接');
            return false;
        }

        try {
            const success = await this.mqttWebSocketClient.sendProtobufMessage(message, topic);
            
            if (success) {
                this.webSocketProtobufStats.sent++;
                this.webSocketProtobufStats.protobufMessages++;
            } else {
                this.webSocketProtobufStats.errors++;
            }
            
            return success;
        } catch (error) {
            console.error('[WebSocket Protobuf] 发送消息失败:', error);
            this.webSocketProtobufStats.errors++;
            return false;
        }
    }

    /**
     * 发送文本消息（WebSocket Protobuf 格式）
     * @param {string} targetId 目标用户/群组ID
     * @param {string} text 消息文本
     * @param {number} conversationType 会话类型（0=单聊，1=群聊）
     * @returns {Promise<boolean>} 发送结果
     */
    async sendWebSocketProtobufTextMessage(targetId, text, conversationType = ConversationType.SINGLE) {
        if (!this.isWebSocketProtobufEnabled()) {
            console.warn('[WebSocket Protobuf] Protobuf 未启用或未连接');
            return false;
        }

        try {
            const success = await this.mqttWebSocketClient.sendProtobufTextMessage(targetId, text, conversationType);
            
            if (success) {
                console.log(`[WebSocket Protobuf] 文本消息发送成功: ${targetId} -> ${text}`);
            }
            
            return success;
        } catch (error) {
            console.error('[WebSocket Protobuf] 发送文本消息失败:', error);
            return false;
        }
    }

    /**
     * 发送心跳消息（WebSocket Protobuf 格式）
     * @returns {Promise<boolean>} 发送结果
     */
    async sendWebSocketProtobufHeartbeat() {
        if (!this.isWebSocketProtobufEnabled()) {
            return false;
        }

        try {
            const success = await this.mqttWebSocketClient.sendProtobufHeartbeat();
            console.log(`[WebSocket Protobuf] 心跳发送${success ? '成功' : '失败'}`);
            return success;
        } catch (error) {
            console.error('[WebSocket Protobuf] 发送心跳失败:', error);
            return false;
        }
    }

    /**
     * 获取 WebSocket Protobuf 统计信息
     * @returns {Object} 统计信息
     */
    getWebSocketProtobufStats() {
        const clientStats = this.mqttWebSocketClient ? this.mqttWebSocketClient.getStats() : {};
        
        return {
            ...this.webSocketProtobufStats,
            ...clientStats,
            enabled: this.enableWebSocketProtobuf,
            connected: this.isWebSocketProtobufEnabled(),
            sessionId: this.webSocketProtobufSessionId
        };
    }

    /**
     * 获取 WebSocket Protobuf 会话ID
     * @returns {string} 会话ID
     */
    getWebSocketProtobufSessionId() {
        return this.webSocketProtobufSessionId;
    }

    /**
     * 重置 WebSocket Protobuf 统计信息
     */
    resetWebSocketProtobufStats() {
        this.webSocketProtobufStats = {
            sent: 0,
            received: 0,
            errors: 0,
            protobufMessages: 0,
            mqttMessages: 0
        };
        
        if (this.mqttWebSocketClient) {
            this.mqttWebSocketClient.resetStats();
        }
        
        console.log('[WebSocket Protobuf] 统计信息已重置');
    }
}

export default WebSocketProtobufIntegration;