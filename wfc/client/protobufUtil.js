/*
 * Protocol Buffers Serialization/Deserialization Utilities
 * For Wildfire IM WebSocket MQTT Client
 */

import Long from 'long';

/**
 * Protocol Buffers utility class for message serialization/deserialization
 */
class ProtobufUtil {
    constructor() {
        this.wireTypes = {
            VARINT: 0,
            FIXED64: 1,
            LENGTH_DELIMITED: 2,
            START_GROUP: 3,
            END_GROUP: 4,
            FIXED32: 5
        };
    }

    /**
     * Encode varint (variable-length integer)
     * @param {number|Long} value - Value to encode
     * @returns {Uint8Array} Encoded bytes
     */
    encodeVarint(value) {
        const longValue = Long.isLong(value) ? value : Long.fromValue(value);
        const bytes = [];
        
        let val = longValue;
        while (val.greaterThanOrEqual(0x80)) {
            bytes.push((val.toNumber() & 0xFF) | 0x80);
            val = val.shiftRightUnsigned(7);
        }
        bytes.push(val.toNumber() & 0xFF);
        
        return new Uint8Array(bytes);
    }

    /**
     * Decode varint from buffer
     * @param {Uint8Array} buffer - Buffer to decode from
     * @param {number} offset - Starting offset
     * @returns {{value: Long, bytesRead: number}} Decoded value and bytes read
     */
    decodeVarint(buffer, offset = 0) {
        let result = Long.ZERO;
        let shift = 0;
        let bytesRead = 0;
        
        while (offset + bytesRead < buffer.length) {
            const byte = buffer[offset + bytesRead];
            bytesRead++;
            
            result = result.or(Long.fromNumber(byte & 0x7F).shiftLeft(shift));
            
            if ((byte & 0x80) === 0) {
                break;
            }
            
            shift += 7;
            if (shift >= 64) {
                throw new Error('Varint too long');
            }
        }
        
        return { value: result, bytesRead };
    }

    /**
     * Encode string as UTF-8 bytes with length prefix
     * @param {string} str - String to encode
     * @returns {Uint8Array} Encoded bytes
     */
    encodeString(str) {
        const utf8Bytes = new TextEncoder().encode(str);
        const lengthBytes = this.encodeVarint(utf8Bytes.length);
        
        const result = new Uint8Array(lengthBytes.length + utf8Bytes.length);
        result.set(lengthBytes, 0);
        result.set(utf8Bytes, lengthBytes.length);
        
        return result;
    }

    /**
     * Decode string from buffer
     * @param {Uint8Array} buffer - Buffer to decode from
     * @param {number} offset - Starting offset
     * @returns {{value: string, bytesRead: number}} Decoded string and bytes read
     */
    decodeString(buffer, offset = 0) {
        const lengthInfo = this.decodeVarint(buffer, offset);
        const length = lengthInfo.value.toNumber();
        const stringStart = offset + lengthInfo.bytesRead;
        
        if (stringStart + length > buffer.length) {
            throw new Error('String length exceeds buffer');
        }
        
        const stringBytes = buffer.slice(stringStart, stringStart + length);
        const value = new TextDecoder().decode(stringBytes);
        
        return {
            value,
            bytesRead: lengthInfo.bytesRead + length
        };
    }

    /**
     * Encode bytes with length prefix
     * @param {Uint8Array} bytes - Bytes to encode
     * @returns {Uint8Array} Encoded bytes with length prefix
     */
    encodeBytes(bytes) {
        const lengthBytes = this.encodeVarint(bytes.length);
        const result = new Uint8Array(lengthBytes.length + bytes.length);
        result.set(lengthBytes, 0);
        result.set(bytes, lengthBytes.length);
        return result;
    }

    /**
     * Decode bytes from buffer
     * @param {Uint8Array} buffer - Buffer to decode from
     * @param {number} offset - Starting offset
     * @returns {{value: Uint8Array, bytesRead: number}} Decoded bytes and bytes read
     */
    decodeBytes(buffer, offset = 0) {
        const lengthInfo = this.decodeVarint(buffer, offset);
        const length = lengthInfo.value.toNumber();
        const bytesStart = offset + lengthInfo.bytesRead;
        
        if (bytesStart + length > buffer.length) {
            throw new Error('Bytes length exceeds buffer');
        }
        
        const value = buffer.slice(bytesStart, bytesStart + length);
        
        return {
            value,
            bytesRead: lengthInfo.bytesRead + length
        };
    }

    /**
     * Encode field tag (field number + wire type)
     * @param {number} fieldNumber - Field number
     * @param {number} wireType - Wire type
     * @returns {Uint8Array} Encoded tag
     */
    encodeTag(fieldNumber, wireType) {
        const tag = (fieldNumber << 3) | wireType;
        return this.encodeVarint(tag);
    }

    /**
     * Decode field tag
     * @param {Uint8Array} buffer - Buffer to decode from
     * @param {number} offset - Starting offset
     * @returns {{fieldNumber: number, wireType: number, bytesRead: number}} Decoded tag info
     */
    decodeTag(buffer, offset = 0) {
        const tagInfo = this.decodeVarint(buffer, offset);
        const tag = tagInfo.value.toNumber();
        
        return {
            fieldNumber: tag >>> 3,
            wireType: tag & 0x07,
            bytesRead: tagInfo.bytesRead
        };
    }

    /**
     * Encode 32-bit integer
     * @param {number} value - Value to encode
     * @returns {Uint8Array} Encoded bytes
     */
    encodeInt32(value) {
        return this.encodeVarint(value);
    }

    /**
     * Encode 64-bit integer
     * @param {number|Long} value - Value to encode
     * @returns {Uint8Array} Encoded bytes
     */
    encodeInt64(value) {
        return this.encodeVarint(value);
    }

    /**
     * Encode boolean
     * @param {boolean} value - Boolean value
     * @returns {Uint8Array} Encoded bytes
     */
    encodeBool(value) {
        return this.encodeVarint(value ? 1 : 0);
    }

    /**
     * Decode 32-bit integer
     * @param {Uint8Array} buffer - Buffer to decode from
     * @param {number} offset - Starting offset
     * @returns {{value: number, bytesRead: number}} Decoded value and bytes read
     */
    decodeInt32(buffer, offset = 0) {
        const result = this.decodeVarint(buffer, offset);
        return {
            value: result.value.toNumber(),
            bytesRead: result.bytesRead
        };
    }

    /**
     * Decode 64-bit integer
     * @param {Uint8Array} buffer - Buffer to decode from
     * @param {number} offset - Starting offset
     * @returns {{value: Long, bytesRead: number}} Decoded value and bytes read
     */
    decodeInt64(buffer, offset = 0) {
        return this.decodeVarint(buffer, offset);
    }

    /**
     * Decode boolean
     * @param {Uint8Array} buffer - Buffer to decode from
     * @param {number} offset - Starting offset
     * @returns {{value: boolean, bytesRead: number}} Decoded value and bytes read
     */
    decodeBool(buffer, offset = 0) {
        const result = this.decodeVarint(buffer, offset);
        return {
            value: !result.value.isZero(),
            bytesRead: result.bytesRead
        };
    }

    /**
     * Create message builder for constructing protobuf messages
     * @returns {MessageBuilder} Message builder instance
     */
    createMessageBuilder() {
        return new MessageBuilder(this);
    }

    /**
     * Create message reader for parsing protobuf messages
     * @param {Uint8Array} buffer - Buffer containing protobuf message
     * @returns {MessageReader} Message reader instance
     */
    createMessageReader(buffer) {
        return new MessageReader(this, buffer);
    }
}

/**
 * Message builder for constructing protobuf messages
 */
class MessageBuilder {
    constructor(protobufUtil) {
        this.protobufUtil = protobufUtil;
        this.fields = [];
    }

    /**
     * Add string field
     * @param {number} fieldNumber - Field number
     * @param {string} value - String value
     * @returns {MessageBuilder} This builder for chaining
     */
    addString(fieldNumber, value) {
        if (value !== null && value !== undefined && value !== '') {
            const tag = this.protobufUtil.encodeTag(fieldNumber, this.protobufUtil.wireTypes.LENGTH_DELIMITED);
            const data = this.protobufUtil.encodeString(value);
            this.fields.push({ tag, data });
        }
        return this;
    }

    /**
     * Add bytes field
     * @param {number} fieldNumber - Field number
     * @param {Uint8Array} value - Bytes value
     * @returns {MessageBuilder} This builder for chaining
     */
    addBytes(fieldNumber, value) {
        if (value && value.length > 0) {
            const tag = this.protobufUtil.encodeTag(fieldNumber, this.protobufUtil.wireTypes.LENGTH_DELIMITED);
            const data = this.protobufUtil.encodeBytes(value);
            this.fields.push({ tag, data });
        }
        return this;
    }

    /**
     * Add 32-bit integer field
     * @param {number} fieldNumber - Field number
     * @param {number} value - Integer value
     * @returns {MessageBuilder} This builder for chaining
     */
    addInt32(fieldNumber, value) {
        if (value !== null && value !== undefined) {
            const tag = this.protobufUtil.encodeTag(fieldNumber, this.protobufUtil.wireTypes.VARINT);
            const data = this.protobufUtil.encodeInt32(value);
            this.fields.push({ tag, data });
        }
        return this;
    }

    /**
     * Add 64-bit integer field
     * @param {number} fieldNumber - Field number
     * @param {number|Long} value - Integer value
     * @returns {MessageBuilder} This builder for chaining
     */
    addInt64(fieldNumber, value) {
        if (value !== null && value !== undefined) {
            const tag = this.protobufUtil.encodeTag(fieldNumber, this.protobufUtil.wireTypes.VARINT);
            const data = this.protobufUtil.encodeInt64(value);
            this.fields.push({ tag, data });
        }
        return this;
    }

    /**
     * Add boolean field
     * @param {number} fieldNumber - Field number
     * @param {boolean} value - Boolean value
     * @returns {MessageBuilder} This builder for chaining
     */
    addBool(fieldNumber, value) {
        if (value !== null && value !== undefined) {
            const tag = this.protobufUtil.encodeTag(fieldNumber, this.protobufUtil.wireTypes.VARINT);
            const data = this.protobufUtil.encodeBool(value);
            this.fields.push({ tag, data });
        }
        return this;
    }

    /**
     * Add nested message field
     * @param {number} fieldNumber - Field number
     * @param {Uint8Array} messageBytes - Serialized message bytes
     * @returns {MessageBuilder} This builder for chaining
     */
    addMessage(fieldNumber, messageBytes) {
        if (messageBytes && messageBytes.length > 0) {
            const tag = this.protobufUtil.encodeTag(fieldNumber, this.protobufUtil.wireTypes.LENGTH_DELIMITED);
            const lengthBytes = this.protobufUtil.encodeVarint(messageBytes.length);
            
            const data = new Uint8Array(lengthBytes.length + messageBytes.length);
            data.set(lengthBytes, 0);
            data.set(messageBytes, lengthBytes.length);
            
            this.fields.push({ tag, data });
        }
        return this;
    }

    /**
     * Build the final message
     * @returns {Uint8Array} Serialized protobuf message
     */
    build() {
        let totalLength = 0;
        for (const field of this.fields) {
            totalLength += field.tag.length + field.data.length;
        }

        const result = new Uint8Array(totalLength);
        let offset = 0;

        for (const field of this.fields) {
            result.set(field.tag, offset);
            offset += field.tag.length;
            result.set(field.data, offset);
            offset += field.data.length;
        }

        return result;
    }
}

/**
 * Message reader for parsing protobuf messages
 */
class MessageReader {
    constructor(protobufUtil, buffer) {
        this.protobufUtil = protobufUtil;
        this.buffer = buffer;
        this.offset = 0;
        this.fields = new Map();
        this.parse();
    }

    /**
     * Parse the protobuf message
     */
    parse() {
        while (this.offset < this.buffer.length) {
            const tagInfo = this.protobufUtil.decodeTag(this.buffer, this.offset);
            this.offset += tagInfo.bytesRead;

            const fieldNumber = tagInfo.fieldNumber;
            const wireType = tagInfo.wireType;

            let fieldValue;
            switch (wireType) {
                case this.protobufUtil.wireTypes.VARINT:
                    const varintInfo = this.protobufUtil.decodeVarint(this.buffer, this.offset);
                    fieldValue = varintInfo.value;
                    this.offset += varintInfo.bytesRead;
                    break;

                case this.protobufUtil.wireTypes.LENGTH_DELIMITED:
                    const bytesInfo = this.protobufUtil.decodeBytes(this.buffer, this.offset);
                    fieldValue = bytesInfo.value;
                    this.offset += bytesInfo.bytesRead;
                    break;

                case this.protobufUtil.wireTypes.FIXED32:
                    if (this.offset + 4 > this.buffer.length) {
                        throw new Error('Buffer too short for FIXED32');
                    }
                    const view32 = new DataView(this.buffer.buffer, this.buffer.byteOffset + this.offset, 4);
                    fieldValue = view32.getUint32(0, true); // little endian
                    this.offset += 4;
                    break;

                case this.protobufUtil.wireTypes.FIXED64:
                    if (this.offset + 8 > this.buffer.length) {
                        throw new Error('Buffer too short for FIXED64');
                    }
                    const view64 = new DataView(this.buffer.buffer, this.buffer.byteOffset + this.offset, 8);
                    const low = view64.getUint32(0, true);
                    const high = view64.getUint32(4, true);
                    fieldValue = Long.fromBits(low, high, true);
                    this.offset += 8;
                    break;

                default:
                    throw new Error(`Unsupported wire type: ${wireType}`);
            }

            // Store field value (handle repeated fields)
            if (this.fields.has(fieldNumber)) {
                const existing = this.fields.get(fieldNumber);
                if (Array.isArray(existing)) {
                    existing.push(fieldValue);
                } else {
                    this.fields.set(fieldNumber, [existing, fieldValue]);
                }
            } else {
                this.fields.set(fieldNumber, fieldValue);
            }
        }
    }

    /**
     * Get string field value
     * @param {number} fieldNumber - Field number
     * @param {string} defaultValue - Default value if field not found
     * @returns {string} Field value
     */
    getString(fieldNumber, defaultValue = '') {
        const value = this.fields.get(fieldNumber);
        if (value instanceof Uint8Array) {
            return new TextDecoder().decode(value);
        }
        return defaultValue;
    }

    /**
     * Get bytes field value
     * @param {number} fieldNumber - Field number
     * @param {Uint8Array} defaultValue - Default value if field not found
     * @returns {Uint8Array} Field value
     */
    getBytes(fieldNumber, defaultValue = new Uint8Array(0)) {
        const value = this.fields.get(fieldNumber);
        if (value instanceof Uint8Array) {
            return value;
        }
        return defaultValue;
    }

    /**
     * Get 32-bit integer field value
     * @param {number} fieldNumber - Field number
     * @param {number} defaultValue - Default value if field not found
     * @returns {number} Field value
     */
    getInt32(fieldNumber, defaultValue = 0) {
        const value = this.fields.get(fieldNumber);
        if (Long.isLong(value)) {
            return value.toNumber();
        }
        return defaultValue;
    }

    /**
     * Get 64-bit integer field value
     * @param {number} fieldNumber - Field number
     * @param {Long} defaultValue - Default value if field not found
     * @returns {Long} Field value
     */
    getInt64(fieldNumber, defaultValue = Long.ZERO) {
        const value = this.fields.get(fieldNumber);
        if (Long.isLong(value)) {
            return value;
        }
        return defaultValue;
    }

    /**
     * Get boolean field value
     * @param {number} fieldNumber - Field number
     * @param {boolean} defaultValue - Default value if field not found
     * @returns {boolean} Field value
     */
    getBool(fieldNumber, defaultValue = false) {
        const value = this.fields.get(fieldNumber);
        if (Long.isLong(value)) {
            return !value.isZero();
        }
        return defaultValue;
    }

    /**
     * Get nested message field value
     * @param {number} fieldNumber - Field number
     * @returns {MessageReader|null} Nested message reader
     */
    getMessage(fieldNumber) {
        const value = this.fields.get(fieldNumber);
        if (value instanceof Uint8Array) {
            return new MessageReader(this.protobufUtil, value);
        }
        return null;
    }

    /**
     * Check if field exists
     * @param {number} fieldNumber - Field number
     * @returns {boolean} True if field exists
     */
    hasField(fieldNumber) {
        return this.fields.has(fieldNumber);
    }

    /**
     * Get all field numbers
     * @returns {number[]} Array of field numbers
     */
    getFieldNumbers() {
        return Array.from(this.fields.keys());
    }
}

// Create singleton instance
const protobufUtil = new ProtobufUtil();

export default protobufUtil;
export { ProtobufUtil, MessageBuilder, MessageReader };
