/*
 * AES Encryption/Decryption Utilities
 * For Wildfire IM WebSocket MQTT Client message security
 * 兼容Java服务端AES实现
 */

/**
 * AES utility class for message encryption/decryption
 * 兼容Java服务端的AES-CBC PKCS5Padding实现
 */
class AESUtil {
    constructor() {
        this.algorithm = 'AES-CBC';
        this.keyLength = 128; // 默认128-bit key，与Java服务端保持一致
        this.ivLength = 16; // 128-bit IV
        this.keyLen = 16; // 默认密钥长度16字节，可通过useAes256设置为32字节

        // 默认AES密钥，与Java服务端保持一致
        this.defaultKey = new Uint8Array([
            0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x78, 0x79, 0x7A, 0x7B, 0x7C, 0x7D, 0x7E, 0x7F,
            0x3A, 0x1F, 0x28, 0x39, 0x4F, 0x52, 0x68, 0x79, 0x71, 0x73, 0x7A, 0x7B, 0x7C, 0x7D, 0x7E, 0x7F
        ]);

        // 时间戳基准：2018.1.1 0:0:0 的Unix时间戳
        this.timeBase = 1514736000;
    }

    /**
     * 设置AES加密模式
     * @param {boolean} aes256 - true使用AES-256(32字节密钥)，false使用AES-128(16字节密钥)
     */
    useAes256(aes256) {
        if (aes256) {
            this.keyLen = 32;
            this.keyLength = 256;
        } else {
            this.keyLen = 16;
            this.keyLength = 128;
        }
    }

    /**
     * Generate a random AES key
     * @returns {Promise<CryptoKey>} Generated AES key
     */
    async generateKey() {
        return await crypto.subtle.generateKey(
            {
                name: this.algorithm,
                length: this.keyLength
            },
            true, // extractable
            ['encrypt', 'decrypt']
        );
    }

    /**
     * Import AES key from raw bytes
     * @param {Uint8Array} keyBytes - Raw key bytes (32 bytes for AES-256)
     * @returns {Promise<CryptoKey>} Imported AES key
     */
    async importKey(keyBytes) {
        return await crypto.subtle.importKey(
            'raw',
            keyBytes,
            {
                name: this.algorithm,
                length: this.keyLength
            },
            true,
            ['encrypt', 'decrypt']
        );
    }

    /**
     * Export AES key to raw bytes
     * @param {CryptoKey} key - AES key to export
     * @returns {Promise<Uint8Array>} Raw key bytes
     */
    async exportKey(key) {
        const keyBuffer = await crypto.subtle.exportKey('raw', key);
        return new Uint8Array(keyBuffer);
    }

    /**
     * Generate random IV (Initialization Vector)
     * @returns {Uint8Array} Random IV bytes
     */
    generateIV() {
        return crypto.getRandomValues(new Uint8Array(this.ivLength));
    }

    /**
     * Encrypt data using AES-CBC
     * @param {CryptoKey} key - AES encryption key
     * @param {Uint8Array} data - Data to encrypt
     * @param {Uint8Array} iv - Initialization vector (optional, will generate if not provided)
     * @returns {Promise<{encrypted: Uint8Array, iv: Uint8Array}>} Encrypted data and IV
     */
    async encrypt(key, data, iv = null) {
        if (!iv) {
            iv = this.generateIV();
        }

        const encrypted = await crypto.subtle.encrypt(
            {
                name: this.algorithm,
                iv: iv
            },
            key,
            data
        );

        return {
            encrypted: new Uint8Array(encrypted),
            iv: iv
        };
    }

    /**
     * Decrypt data using AES-CBC
     * @param {CryptoKey} key - AES decryption key
     * @param {Uint8Array} encryptedData - Encrypted data
     * @param {Uint8Array} iv - Initialization vector
     * @returns {Promise<Uint8Array>} Decrypted data
     */
    async decrypt(key, encryptedData, iv) {
        const decrypted = await crypto.subtle.decrypt(
            {
                name: this.algorithm,
                iv: iv
            },
            key,
            encryptedData
        );

        return new Uint8Array(decrypted);
    }

    /**
     * Encrypt string message
     * @param {CryptoKey} key - AES encryption key
     * @param {string} message - Message to encrypt
     * @param {Uint8Array} iv - Initialization vector (optional)
     * @returns {Promise<{encrypted: Uint8Array, iv: Uint8Array}>} Encrypted message and IV
     */
    async encryptString(key, message, iv = null) {
        const messageBytes = new TextEncoder().encode(message);
        return await this.encrypt(key, messageBytes, iv);
    }

    /**
     * Decrypt string message
     * @param {CryptoKey} key - AES decryption key
     * @param {Uint8Array} encryptedData - Encrypted data
     * @param {Uint8Array} iv - Initialization vector
     * @returns {Promise<string>} Decrypted message
     */
    async decryptString(key, encryptedData, iv) {
        const decryptedBytes = await this.decrypt(key, encryptedData, iv);
        return new TextDecoder().decode(decryptedBytes);
    }

    /**
     * Create encrypted message package (IV + encrypted data)
     * @param {CryptoKey} key - AES encryption key
     * @param {Uint8Array} data - Data to encrypt
     * @returns {Promise<Uint8Array>} Package containing IV + encrypted data
     */
    async encryptPackage(key, data) {
        const iv = this.generateIV();
        const result = await this.encrypt(key, data, iv);

        // Combine IV and encrypted data
        const pack = new Uint8Array(iv.length + result.encrypted.length);
        pack.set(iv, 0);
        pack.set(result.encrypted, iv.length);

        return pack;
    }

    /**
     * Decrypt message package (extract IV and decrypt data)
     * @param {CryptoKey} key - AES decryption key
     * @param {Uint8Array} package - Package containing IV + encrypted data
     * @returns {Promise<Uint8Array>} Decrypted data
     */
    async decryptPackage(key, pack) {
        if (pack.length < this.ivLength) {
            throw new Error('Package too short to contain IV');
        }

        const iv = pack.slice(0, this.ivLength);
        const encryptedData = pack.slice(this.ivLength);

        return await this.decrypt(key, encryptedData, iv);
    }

    /**
     * Derive key from password using PBKDF2
     * @param {string} password - Password to derive key from
     * @param {Uint8Array} salt - Salt for key derivation
     * @param {number} iterations - Number of iterations (default: 100000)
     * @returns {Promise<CryptoKey>} Derived AES key
     */
    async deriveKeyFromPassword(password, salt, iterations = 100000) {
        const passwordBytes = new TextEncoder().encode(password);

        // Import password as key material
        const keyMaterial = await crypto.subtle.importKey(
            'raw',
            passwordBytes,
            'PBKDF2',
            false,
            ['deriveKey']
        );

        // Derive AES key
        return await crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: salt,
                iterations: iterations,
                hash: 'SHA-256'
            },
            keyMaterial,
            {
                name: this.algorithm,
                length: this.keyLength
            },
            true,
            ['encrypt', 'decrypt']
        );
    }

    /**
     * Generate random salt for key derivation
     * @param {number} length - Salt length in bytes (default: 32)
     * @returns {Uint8Array} Random salt
     */
    generateSalt(length = 32) {
        return crypto.getRandomValues(new Uint8Array(length));
    }

    /**
     * Hash data using SHA-256
     * @param {Uint8Array} data - Data to hash
     * @returns {Promise<Uint8Array>} Hash digest
     */
    async hash(data) {
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        return new Uint8Array(hashBuffer);
    }

    /**
     * Generate HMAC for message authentication
     * @param {CryptoKey} key - HMAC key
     * @param {Uint8Array} data - Data to authenticate
     * @returns {Promise<Uint8Array>} HMAC digest
     */
    async generateHMAC(key, data) {
        const signature = await crypto.subtle.sign('HMAC', key, data);
        return new Uint8Array(signature);
    }

    /**
     * Verify HMAC
     * @param {CryptoKey} key - HMAC key
     * @param {Uint8Array} data - Original data
     * @param {Uint8Array} signature - HMAC signature to verify
     * @returns {Promise<boolean>} True if signature is valid
     */
    async verifyHMAC(key, data, signature) {
        return await crypto.subtle.verify('HMAC', key, signature, data);
    }

    /**
     * Generate HMAC key
     * @returns {Promise<CryptoKey>} Generated HMAC key
     */
    async generateHMACKey() {
        return await crypto.subtle.generateKey(
            {
                name: 'HMAC',
                hash: 'SHA-256'
            },
            true,
            ['sign', 'verify']
        );
    }

    /**
     * Convert hex string to Uint8Array
     * @param {string} hex - Hex string
     * @returns {Uint8Array} Byte array
     */
    hexToBytes(hex) {
        const bytes = new Uint8Array(hex.length / 2);
        for (let i = 0; i < hex.length; i += 2) {
            bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
        }
        return bytes;
    }

    /**
     * Convert Uint8Array to hex string
     * @param {Uint8Array} bytes - Byte array
     * @returns {string} Hex string
     */
    bytesToHex(bytes) {
        return Array.from(bytes)
            .map(byte => byte.toString(16).padStart(2, '0'))
            .join('');
    }

    /**
     * Convert base64 string to Uint8Array
     * @param {string} base64 - Base64 string
     * @returns {Uint8Array} Byte array
     */
    base64ToBytes(base64) {
        const binaryString = atob(base64);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        return bytes;
    }

    /**
     * Convert Uint8Array to base64 string
     * @param {Uint8Array} bytes - Byte array
     * @returns {string} Base64 string
     */
    bytesToBase64(bytes) {
        const binaryString = String.fromCharCode(...bytes);
        return btoa(binaryString);
    }

    /**
     * 将有符号字节转换为无符号整数（兼容Java实现）
     * @param {number} data - 输入的字节数据
     * @returns {number} 0-255范围内的无符号整数值
     */
    getUnsignedByte(data) {
        return data & 0xFF;
    }

    /**
     * 将用户提供的字符串密钥转换为字节数组（兼容Java实现）
     * @param {string} userKey - 用户密钥字符串
     * @returns {Uint8Array} 转换后的密钥字节数组
     */
    convertUserKey(userKey) {
        const key = new Uint8Array(this.keyLen);
        for (let i = 0; i < this.keyLen; i++) {
            key[i] = userKey.charCodeAt(i) & 0xFF;
        }
        return key;
    }

    /**
     * 准备AES密钥和IV（兼容Java实现）
     * @param {string|Uint8Array} userKey - 用户密钥
     * @returns {Object} 包含key和iv的对象
     */
    prepareKeyAndIV(userKey) {
        let aesKey = this.defaultKey;

        // 如果用户提供了密钥
        if (userKey) {
            if (typeof userKey === 'string' && userKey.length > 0) {
                aesKey = this.convertUserKey(userKey);
            } else if (userKey instanceof Uint8Array) {
                aesKey = userKey;
            }
        }

        // 检查密钥长度并截取
        if (aesKey.length < this.keyLen) {
            throw new Error(`密钥长度不足，需要${this.keyLen}字节`);
        }

        if (aesKey.length > this.keyLen) {
            aesKey = aesKey.slice(0, this.keyLen);
        }

        // 准备IV：使用密钥的前16字节
        let ivKeys;
        if (aesKey.length === this.ivLength) {
            ivKeys = aesKey;
        } else {
            ivKeys = aesKey.slice(0, this.ivLength);
        }

        return { key: aesKey, iv: ivKeys };
    }

    /**
     * 获取当前时间戳（小时数，从2018.1.1开始计算）
     * @returns {number} 小时数
     */
    getCurrentHours() {
        return Math.floor((Date.now() / 1000 - this.timeBase) / 3600);
    }

    /**
     * 将时间戳（小时数）转换为4字节数组（小端序）
     * @param {number} hours - 小时数
     * @returns {Uint8Array} 4字节时间戳数组
     */
    hoursToBytes(hours) {
        const timeBytes = new Uint8Array(4);
        timeBytes[0] = hours & 0xFF;
        timeBytes[1] = (hours & 0xFF00) >> 8;
        timeBytes[2] = (hours & 0xFF0000) >> 16;
        timeBytes[3] = (hours & 0xFF000000) >> 24;
        return timeBytes;
    }

    /**
     * 从4字节数组解析时间戳（小时数，小端序）
     * @param {Uint8Array} timeBytes - 4字节时间戳数组
     * @returns {number} 小时数
     */
    bytesToHours(timeBytes) {
        let hours = 0;
        hours += this.getUnsignedByte(timeBytes[3]);
        hours <<= 8;
        hours += this.getUnsignedByte(timeBytes[2]);
        hours <<= 8;
        hours += this.getUnsignedByte(timeBytes[1]);
        hours <<= 8;
        hours += this.getUnsignedByte(timeBytes[0]);
        return hours;
    }

    /**
     * AES加密方法（兼容Java服务端实现）
     * @param {Uint8Array|string} data - 要加密的数据
     * @param {string|Uint8Array} userKey - 用户密钥（可选，默认使用内置密钥）
     * @returns {Promise<Uint8Array>} 加密后的数据，失败返回null
     */
    async AESEncrypt(data, userKey = null) {
        try {
            // 转换数据为字节数组
            let dataBytes;
            if (typeof data === 'string') {
                dataBytes = new TextEncoder().encode(data);
            } else if (data instanceof Uint8Array) {
                dataBytes = data;
            } else {
                throw new Error('数据类型不支持');
            }

            // 准备密钥和IV
            const { key: aesKey, iv: ivKeys } = this.prepareKeyAndIV(userKey);

            // 创建CryptoKey对象
            const cryptoKey = await crypto.subtle.importKey(
                'raw',
                aesKey,
                { name: this.algorithm },
                false,
                ['encrypt']
            );

            // 获取当前时间戳
            const currentHours = this.getCurrentHours();
            const timeBytes = this.hoursToBytes(currentHours);

            // 创建包含时间戳的数据数组
            const dataWithTime = new Uint8Array(timeBytes.length + dataBytes.length);
            dataWithTime.set(timeBytes, 0);
            dataWithTime.set(dataBytes, timeBytes.length);

            // 执行AES加密
            const encrypted = await crypto.subtle.encrypt(
                {
                    name: this.algorithm,
                    iv: ivKeys
                },
                cryptoKey,
                dataWithTime
            );

            return new Uint8Array(encrypted);
        } catch (error) {
            console.error('AES加密失败:', error);
            return null;
        }
    }

    /**
     * AES解密方法（兼容Java服务端实现）
     * @param {Uint8Array} encryptedData - 要解密的数据
     * @param {string|Uint8Array} userKey - 用户密钥（可选，默认使用内置密钥）
     * @param {boolean} checkTime - 是否检查时间戳有效性（默认true）
     * @returns {Promise<{data: Uint8Array, timeValid: boolean}>} 解密结果，失败返回null
     */
    async AESDecrypt(encryptedData, userKey = null, checkTime = true) {
        try {
            console.warn('开始解密---------------------------------》');

            // 准备密钥和IV
            const { key: aesKey, iv: ivKeys } = this.prepareKeyAndIV(userKey);

            // 创建CryptoKey对象
            const cryptoKey = await crypto.subtle.importKey(
                'raw',
                aesKey,
                { name: this.algorithm },
                false,
                ['decrypt']
            );

            // 执行AES解密
            const decrypted = await crypto.subtle.decrypt(
                {
                    name: this.algorithm,
                    iv: ivKeys
                },
                cryptoKey,
                encryptedData
            );

            const decryptedBytes = new Uint8Array(decrypted);

            // 检查解密后的数据长度是否包含时间戳
            if (decryptedBytes.length <= 4) {
                console.error('解密数据长度不足');
                return null;
            }

            // 提取时间戳
            const timeBytes = decryptedBytes.slice(0, 4);
            const hours = this.bytesToHours(timeBytes);

            // 验证时间戳
            let timeValid = true;
            if (checkTime) {
                const currentHours = this.getCurrentHours();
                if (Math.abs(currentHours - hours) > 24) {
                    timeValid = false;
                    console.warn('时间戳验证失败，数据可能过期');
                    return null;
                }
            }

            // 提取原始数据（去除时间戳）
            const originalData = decryptedBytes.slice(4);

            console.warn('解密完成---------------------------------》');

            return {
                data: originalData,
                timeValid: timeValid
            };
        } catch (error) {
            console.error('AES解密失败:', error);
            return null;
        }
    }

    /**
     * AES加密字符串（兼容Java服务端实现）
     * @param {string} message - 要加密的字符串
     * @param {string|Uint8Array} userKey - 用户密钥（可选）
     * @returns {Promise<Uint8Array>} 加密后的数据
     */
    async AESEncryptString(message, userKey = null) {
        return await this.AESEncrypt(message, userKey);
    }

    /**
     * AES解密字符串（兼容Java服务端实现）
     * @param {Uint8Array} encryptedData - 要解密的数据
     * @param {string|Uint8Array} userKey - 用户密钥（可选）
     * @param {boolean} checkTime - 是否检查时间戳有效性（默认true）
     * @returns {Promise<string>} 解密后的字符串，失败返回null
     */
    async AESDecryptString(encryptedData, userKey = null, checkTime = true) {
        const result = await this.AESDecrypt(encryptedData, userKey, checkTime);
        if (result && result.data) {
            return new TextDecoder().decode(result.data);
        }
        return null;
    }
}

// Create singleton instance
const aesUtil = new AESUtil();

export default aesUtil;
export { AESUtil };
