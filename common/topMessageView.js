var topMessageView;
function requestPermissions(scope, message) {
    return new Promise((resolve, reject) => {
        try {
            plus.android.requestPermissions(
                ["android.permission." + scope],
                function (resultObj) {
                    console.log(resultObj, "resultObj");
                    resolve(resultObj);
                    if (
                        resultObj.deniedPresent.length > 0 ||
                        resultObj.deniedAlways.length > 0
                    ) {
                        goSetting(message);
                    }
                },
                function (error) {
                    console.error('权限请求失败:', error);
                    reject(error);
                    goSetting("获取权限失败，请重试");
                }
            );
        } catch (error) {
            console.error('权限请求异常:', error);
            reject(error);
        }
    });
}
// 跳转权限设置
function goSetting(message) {
    uni.showModal({
        title: "提示",
        content: message,
        // showCancel: false, // 不显示取消按钮
        success(res) {
            if (res.confirm) {
                try {
                    var Intent = plus.android.importClass("android.content.Intent");
                    var Settings = plus.android.importClass("android.provider.Settings");
                    var Uri = plus.android.importClass("android.net.Uri");
                    var mainActivity = plus.android.runtimeMainActivity();
                    var intent = new Intent();
                    intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                    var uri = Uri.fromParts("package", mainActivity.getPackageName(), null);
                    intent.setData(uri);
                    mainActivity.startActivity(intent);
                } catch (error) {
                    console.error('跳转设置页面失败:', error);
                    uni.showToast({
                        title: '跳转设置失败',
                        icon: 'none'
                    });
                }
            } else if (res.cancel) {
                console.log('用户取消跳转设置');
            }
        },
        fail(err) {
            console.error('显示设置弹窗失败:', err);
        },
    });
}
function createTopMessage(title, message) {
    try {
        // 如果已存在topMessageView，先关闭
        if (topMessageView) {
            hideTopMessage();
        }
        
        topMessageView = new plus.nativeObj.View("topMessageView", {
            width: "100%",
            height: "100%",
            backgroundColor: "rgba(0,0,0,0.7)",
        });
        
        topMessageView.drawText(
            title,
            {
                left: "10%",
                width: "80%",
                top: "-180px",
                height: "80%",
            },
            {
                color: "#ffffff",
                size: "25px",
            }
        );
        
        topMessageView.drawText(
            message,
            {
                top: "-100px",
                left: "10%",
                width: "80%",
                height: "80%",
            },
            {
                color: "#ffffff",
                whiteSpace: "normal",
            }
        );
        
        // 显示消息提示框
        topMessageView.show();
        
        console.log('topMessage显示成功');
    } catch (error) {
        console.error('创建topMessage失败:', error);
    }
}
function hideTopMessage() {
    try {
        if (topMessageView) {
            topMessageView.close();
            topMessageView = null;
            console.log('topMessage隐藏成功');
        }
    } catch (error) {
        console.error('隐藏topMessage失败:', error);
        // 强制重置
        topMessageView = null;
    }
}
const topMessage = {
    createTopMessage: createTopMessage,
    hideTopMessage: hideTopMessage,
    requestPermissions: requestPermissions,
}
export default topMessage
