const platform = uni.getSystemInfoSync().platform;
const screenWitdh = uni.getSystemInfoSync().screenWidth;
const screenHeight = uni.getSystemInfoSync().screenHeight;

function platformVal(androidVal, iosVal) {
	return 'android' == platform ? androidVal : 'ios' == platform ? iosVal : "";
}

function calculateViewWidth(marginLeft, marginRight) {
	return screenWitdh - marginLeft - marginRight;
}

function calculateAndroidViewWidth(marginLeft, marginRight) {
	return screenHeight - marginLeft - marginRight;
}

function calculateViewX(superViewWidth,width,marginRight) {
	console.log(superViewWidth)
	console.log(superViewWidth - marginRight - width)
	return superViewWidth - marginRight - width;
}

function buildFullscreenAndWebviewBg() {
	const bodyHeight = window.innerHeight;
	const bodyWidth = window.innerWidth;
	const designHeight = bodyHeight - 80;
	const unit = parseInt(designHeight / 20);
	const numberTop = String(unit * 6);
	const sloganTop = String(unit * 7);
	const loginBtnTop = String(unit * 9);

	return {
		uiConfig: {
			setStatusBarStyle: "1",
			setNavHidden: "true",
			setLogoHidden: "true",
			setSwitchHidden: "true",
			setBackgroundUi: {
				webviewPath: "static/background_web.html",
			},
			setNumberUi: {
				top: numberTop,
			},
			setSloganUi: {
				textSize: platformVal("16", "16"),
				top: sloganTop,
			},
			setLoginBtnUi: {
				top: loginBtnTop,
			},
			setPrivacyUi: {
				expandAuthPageCheckedScope: true
			}
		}
	};
}

function buildFullscreenSingleFontName() {
	const unit = parseInt((window.innerHeight - 80) / 20);
	const logoTop = String(unit * 1 - 10);
	const sloganTop = String(unit * 5);
	const numberTop = String(unit * 6 + 20);
	const loginBtnTop = String(unit * 9);
	const switchTop = String(unit * 12);

	return {
		uiConfig: {
			globalFontName:platformVal("static/fonts/globalFont.ttf","STHeitiSC-Medium"),
			setStatusBarStyle: "1",
			setNavUi: {
				text: "一键登录9",
				textColor: "#FFFFFF",
				bgColor: "#0faeff",
				returnImgPath: "static/nav_back.png",
				returnImgWidth: "24",
				returnImgHeight: "24",
				fontName : platformVal("static/fonts/globalFont.ttf","static/testFont.ttf"),
			},
			setLogoUi: {
				imgPath: "static/mytel_app_launcher.png",
				top: logoTop,
			},
			setSloganUi: {
				top: sloganTop,
			},
			setNumberUi: {
				top: numberTop,
				fontName : "static/fonts/testFont.ttf",
				textSize : 25
			},
			setLoginBtnUi: {
				top: loginBtnTop,
			},
			setSwitchUi: {
				textColor: "#0faeff",
				top: switchTop,
			},
			setPrivacyUi: {
				expandAuthPageCheckedScope: true
			},
			setAppPrivacyOne: {
				title: '用户协议',
				url: "https://www.taobao.com"
			},
			setAppPrivacyTwo: {
				title: '隐私政策',
				url: "https://www.taobao.com"
			},
			setAppPrivacyThree: {
				title: '服务协议',
				url: "https://www.taobao.com"
			}
		}
	};
}

function buildFullscreenPrivacySignalColor() {
	const unit = parseInt((window.innerHeight - 80) / 20);
	const logoTop = String(unit * 1 - 10);
	const sloganTop = String(unit * 5);
	const numberTop = String(unit * 6 + 20);
	const loginBtnTop = String(unit * 9);
	const switchTop = String(unit * 12);

	return {
		uiConfig: {
			setStatusBarStyle: "1",
			setNavUi: {
				text: "一键登录10",
				textColor: "#FFFFFF",
				bgColor: "#0faeff",
				returnImgPath: "static/nav_back.png",
				returnImgWidth: "24",
				returnImgHeight: "24"
			},
			setLogoUi: {
				imgPath: "static/mytel_app_launcher.png",
				top: logoTop,
			},
			setSloganUi: {
				top: sloganTop,
			},
			setNumberUi: {
				top: numberTop
			},
			setLoginBtnUi: {
				top: loginBtnTop,
			},
			setSwitchUi: {
				textColor: "#0faeff",
				top: switchTop,
			},
			setPrivacyUi: {
				expandAuthPageCheckedScope: true,
				operatorColor:"#F534D2",
				oneColor:"#FF0000",
				twoColor:"#00FF00",
				threeColor:"#0000FF",
				beforeText:"请阅读并同意",
				endText:"，谢谢"
			},
			setAppPrivacyOne: {
				title: '用户协议',
				url: "https://www.taobao.com"
			},
			setAppPrivacyTwo: {
				title: '隐私政策',
				url: "https://www.taobao.com"
			},
			setAppPrivacyThree: {
				title: '服务协议',
				url: "https://www.taobao.com"
			}
		}
	};
}

export default {
	buildFullscreenAndWebviewBg: buildFullscreenAndWebviewBg,
	buildFullscreenSingleFontName: buildFullscreenSingleFontName,
	buildFullscreenPrivacySignalColor: buildFullscreenPrivacySignalColor
}