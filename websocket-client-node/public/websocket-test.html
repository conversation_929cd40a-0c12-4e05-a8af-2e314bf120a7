<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket客户端测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            font-weight: bold;
            background-color: #f1f8ff;
        }
        #messageLog {
            height: 300px;
            overflow-y: auto;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            font-family: monospace;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .log-info {
            background-color: #e8f4f8;
            border-left: 3px solid #17a2b8;
        }
        .log-success {
            background-color: #e8f8e8;
            border-left: 3px solid #28a745;
        }
        .log-error {
            background-color: #f8e8e8;
            border-left: 3px solid #dc3545;
        }
        .log-warning {
            background-color: #f8f4e8;
            border-left: 3px solid #ffc107;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.85em;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">WebSocket客户端测试</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">连接管理</div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="wsUrl" class="form-label">WebSocket服务器URL</label>
                            <input type="text" class="form-control" id="wsUrl" value="ws://192.168.10.113:1884/mqtt">
                        </div>
                        <div class="d-flex gap-2">
                            <button id="connectBtn" class="btn btn-primary">连接</button>
                            <button id="disconnectBtn" class="btn btn-danger">断开</button>
                            <button id="reconnectBtn" class="btn btn-warning">重连</button>
                            <button id="statusBtn" class="btn btn-info">查询状态</button>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">发送文本消息</div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="textMessage" class="form-label">文本消息</label>
                            <textarea class="form-control" id="textMessage" rows="3">Hello, WebSocket!</textarea>
                        </div>
                        <button id="sendTextBtn" class="btn btn-success">发送文本</button>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">发送MQTT消息</div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="mqttTopic" class="form-label">主题</label>
                            <input type="text" class="form-control" id="mqttTopic" value="test/topic">
                        </div>
                        <div class="mb-3">
                            <label for="mqttPayload" class="form-label">消息内容</label>
                            <textarea class="form-control" id="mqttPayload" rows="3">Hello, MQTT!</textarea>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="mqttQos" class="form-label">QoS</label>
                                <select class="form-select" id="mqttQos">
                                    <option value="0" selected>0</option>
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="mqttDup">
                                    <label class="form-check-label" for="mqttDup">DUP</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="mqttRetain">
                                    <label class="form-check-label" for="mqttRetain">RETAIN</label>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button id="sendPublishBtn" class="btn btn-success">发送PUBLISH</button>
                            <button id="sendEncryptedBtn" class="btn btn-warning">发送加密PUBLISH</button>
                        </div>
                    </div>
                </div>

                <div class="card" id="encryptionCard" style="display: none;">
                    <div class="card-header">加密设置</div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="encryptionKey" class="form-label">加密密钥</label>
                            <input type="text" class="form-control" id="encryptionKey" value="1234567890123456">
                            <div class="form-text">密钥长度必须为16、24或32字节</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>消息日志</span>
                <button id="clearLogBtn" class="btn btn-sm btn-outline-secondary">清空日志</button>
            </div>
            <div class="card-body">
                <div id="messageLog"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/websocket-test.js"></script>
</body>
</html>