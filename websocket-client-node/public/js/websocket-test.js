/**
 * WebSocket客户端测试页面脚本
 */

document.addEventListener('DOMContentLoaded', function() {
    // API基础URL
    const API_BASE_URL = '/api/websocket';

    // DOM元素
    const wsUrlInput = document.getElementById('wsUrl');
    const connectBtn = document.getElementById('connectBtn');
    const disconnectBtn = document.getElementById('disconnectBtn');
    const reconnectBtn = document.getElementById('reconnectBtn');
    const statusBtn = document.getElementById('statusBtn');
    const textMessageInput = document.getElementById('textMessage');
    const sendTextBtn = document.getElementById('sendTextBtn');
    const mqttTopicInput = document.getElementById('mqttTopic');
    const mqttPayloadInput = document.getElementById('mqttPayload');
    const mqttQosSelect = document.getElementById('mqttQos');
    const mqttDupCheckbox = document.getElementById('mqttDup');
    const mqttRetainCheckbox = document.getElementById('mqttRetain');
    const sendPublishBtn = document.getElementById('sendPublishBtn');
    const sendEncryptedBtn = document.getElementById('sendEncryptedBtn');
    const encryptionCard = document.getElementById('encryptionCard');
    const encryptionKeyInput = document.getElementById('encryptionKey');
    const messageLog = document.getElementById('messageLog');
    const clearLogBtn = document.getElementById('clearLogBtn');

    // 显示加密设置卡片
    sendEncryptedBtn.addEventListener('click', function() {
        encryptionCard.style.display = 'block';
    });

    // 连接WebSocket服务器
    connectBtn.addEventListener('click', function() {
        const url = wsUrlInput.value.trim();
        if (!url) {
            logMessage('错误', 'WebSocket服务器URL不能为空');
            return;
        }

        logMessage('信息', `正在连接WebSocket服务器: ${url}...`);
        fetch(`${API_BASE_URL}/connect`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ url })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                logMessage('成功', data.message);
            } else {
                logMessage('错误', data.message);
            }
        })
        .catch(error => {
            logMessage('错误', `连接失败: ${error.message}`);
        });
    });

    // 断开WebSocket连接
    disconnectBtn.addEventListener('click', function() {
        logMessage('信息', '正在断开WebSocket连接...');
        fetch(`${API_BASE_URL}/disconnect`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                logMessage('成功', data.message);
            } else {
                logMessage('错误', data.message);
            }
        })
        .catch(error => {
            logMessage('错误', `断开连接失败: ${error.message}`);
        });
    });

    // 重新连接WebSocket服务器
    reconnectBtn.addEventListener('click', function() {
        logMessage('信息', '正在重新连接WebSocket服务器...');
        fetch(`${API_BASE_URL}/reconnect`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                logMessage('成功', data.message);
            } else {
                logMessage('错误', data.message);
            }
        })
        .catch(error => {
            logMessage('错误', `重连失败: ${error.message}`);
        });
    });

    // 查询连接状态
    statusBtn.addEventListener('click', function() {
        logMessage('信息', '正在查询WebSocket连接状态...');
        fetch(`${API_BASE_URL}/status`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const status = data.connected ? '已连接' : '未连接';
                logMessage('信息', `WebSocket连接状态: ${status}`);
            } else {
                logMessage('错误', data.message);
            }
        })
        .catch(error => {
            logMessage('错误', `查询状态失败: ${error.message}`);
        });
    });

    // 发送文本消息
    sendTextBtn.addEventListener('click', function() {
        const message = textMessageInput.value.trim();
        if (!message) {
            logMessage('警告', '消息内容不能为空');
            return;
        }

        logMessage('信息', `正在发送文本消息: ${message}`);
        fetch(`${API_BASE_URL}/send`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ message })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                logMessage('成功', data.message);
            } else {
                logMessage('错误', data.message);
            }
        })
        .catch(error => {
            logMessage('错误', `发送失败: ${error.message}`);
        });
    });

    // 发送MQTT PUBLISH消息
    sendPublishBtn.addEventListener('click', function() {
        const topic = mqttTopicInput.value.trim();
        const payload = mqttPayloadInput.value.trim();
        const qos = parseInt(mqttQosSelect.value);
        const dup = mqttDupCheckbox.checked;
        const retain = mqttRetainCheckbox.checked;

        if (!topic) {
            logMessage('警告', '主题不能为空');
            return;
        }
        if (!payload) {
            logMessage('警告', '消息内容不能为空');
            return;
        }

        logMessage('信息', `正在发送MQTT PUBLISH消息: 主题=${topic}, QoS=${qos}, DUP=${dup}, RETAIN=${retain}`);
        fetch(`${API_BASE_URL}/sendPublish`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ topic, payload, qos, dup, retain })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                logMessage('成功', data.message);
            } else {
                logMessage('错误', data.message);
            }
        })
        .catch(error => {
            logMessage('错误', `发送失败: ${error.message}`);
        });
    });

    // 发送加密的MQTT PUBLISH消息
    sendEncryptedBtn.addEventListener('click', function() {
        const topic = mqttTopicInput.value.trim();
        const payload = mqttPayloadInput.value.trim();
        const key = encryptionKeyInput.value.trim();
        const qos = parseInt(mqttQosSelect.value);
        const dup = mqttDupCheckbox.checked;
        const retain = mqttRetainCheckbox.checked;

        if (!topic) {
            logMessage('警告', '主题不能为空');
            return;
        }
        if (!payload) {
            logMessage('警告', '消息内容不能为空');
            return;
        }
        if (!key) {
            logMessage('警告', '加密密钥不能为空');
            return;
        }
        if (![16, 24, 32].includes(key.length)) {
            logMessage('警告', '加密密钥长度必须为16、24或32字节');
            return;
        }

        logMessage('信息', `正在发送加密的MQTT PUBLISH消息: 主题=${topic}, QoS=${qos}, DUP=${dup}, RETAIN=${retain}`);
        fetch(`${API_BASE_URL}/sendEncryptedPublish`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ topic, payload, key, qos, dup, retain })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                logMessage('成功', data.message);
            } else {
                logMessage('错误', data.message);
            }
        })
        .catch(error => {
            logMessage('错误', `发送失败: ${error.message}`);
        });
    });

    // 清空日志
    clearLogBtn.addEventListener('click', function() {
        messageLog.innerHTML = '';
    });

    /**
     * 记录消息到日志区域
     * @param {String} type 消息类型：信息、成功、错误、警告
     * @param {String} message 消息内容
     */
    function logMessage(type, message) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        
        let logClass = 'log-info';
        switch (type) {
            case '成功':
                logClass = 'log-success';
                break;
            case '错误':
                logClass = 'log-error';
                break;
            case '警告':
                logClass = 'log-warning';
                break;
        }
        logEntry.classList.add(logClass);
        
        logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span> <strong>${type}:</strong> ${message}`;
        messageLog.appendChild(logEntry);
        messageLog.scrollTop = messageLog.scrollHeight;
    }

    // 初始化页面
    logMessage('信息', 'WebSocket客户端测试页面已加载');
});