{"name": "websocket-client-node", "version": "1.0.0", "description": "Node.js implementation of WebSocket client for MQTT messaging", "main": "app.js", "scripts": {"start": "node app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "ws": "^8.14.2", "body-parser": "^1.20.2", "cors": "^2.8.5"}, "author": "", "license": "MIT", "keywords": ["websocket", "mqtt", "client", "nodejs"]}