/**
 * WebSocket消息工具类
 * 提供各种WebSocket消息的封装功能
 */

const BinaryMessage = require('../models/BinaryMessage');
const AES = require('./AES');

class WebSocketMessageUtils {
    /**
     * 封装文本消息
     * @param {string} message 要发送的文本消息
     * @returns {string} 封装后的文本消息
     */
    static encodeTextMessage(message) {
        if (!message) {
            throw new Error('消息不能为空');
        }
        return message;
    }

    /**
     * 封装二进制消息
     * @param {Buffer|string} data 要发送的二进制数据或Base64编码的字符串
     * @returns {Buffer} 封装后的二进制消息
     */
    static encodeBinaryMessage(data) {
        if (!data) {
            throw new Error('数据不能为空');
        }

        // 如果输入是字符串，假设它是Base64编码的数据
        if (typeof data === 'string') {
            return Buffer.from(data, 'base64');
        }

        // 如果已经是Buffer，直接返回
        if (Buffer.isBuffer(data)) {
            return data;
        }

        throw new Error('数据格式不正确，必须是Buffer或Base64编码的字符串');
    }

    /**
     * 封装结构化二进制消息
     * @param {number} messageType 消息类型
     * @param {string|Buffer} payload 消息内容（字符串或Buffer）
     * @returns {Buffer} 封装后的二进制消息
     */
    static encodeStructuredBinaryMessage(messageType, payload) {
        if (messageType === undefined) {
            throw new Error('消息类型不能为空');
        }
        if (!payload) {
            throw new Error('消息内容不能为空');
        }

        // 创建二进制消息
        const binaryMessage = new BinaryMessage(messageType);
        const payloadBuffer = Buffer.isBuffer(payload) ? payload : Buffer.from(payload);
        binaryMessage.encode(payloadBuffer);

        return binaryMessage.getPayload();
    }

    /**
     * 封装MQTT PUBLISH消息
     * @param {string} topic 主题
     * @param {string|Buffer} payload 消息内容（字符串或Buffer）
     * @param {Object} options 可选参数
     * @param {number} [options.messageId] 消息ID，默认随机生成
     * @param {number} [options.qos=0] 服务质量等级 (0-2)
     * @param {boolean} [options.dup=false] 是否是重发消息
     * @param {boolean} [options.retain=false] 是否保留消息
     * @returns {Buffer} 封装后的MQTT PUBLISH消息
     */
    static encodeMqttPublishMessage(topic, payload, options = {}) {
        if (!topic) {
            throw new Error('主题不能为空');
        }
        if (!payload) {
            throw new Error('消息内容不能为空');
        }

        // 设置默认值
        const messageId = options.messageId || Math.floor(Math.random() * 65535) + 1;
        const qos = options.qos !== undefined ? options.qos : 0;
        const dup = options.dup !== undefined ? options.dup : false;
        const retain = options.retain !== undefined ? options.retain : false;

        // 转换payload为Buffer（如果它是字符串）
        const payloadBuffer = Buffer.isBuffer(payload) ? payload : Buffer.from(payload);

        // 创建MQTT PUBLISH消息
        return BinaryMessage.encodePublish(topic, payloadBuffer, messageId, qos, dup, retain);
    }

    /**
     * 封装加密的MQTT PUBLISH消息
     * @param {string} topic 主题
     * @param {string|Buffer} payload 消息内容（字符串或Buffer）
     * @param {string|Buffer} key 加密密钥
     * @param {Object} options 可选参数
     * @param {number} [options.messageId] 消息ID，默认随机生成
     * @param {number} [options.qos=0] 服务质量等级 (0-2)
     * @param {boolean} [options.dup=false] 是否是重发消息
     * @param {boolean} [options.retain=false] 是否保留消息
     * @returns {Buffer} 封装后的加密MQTT PUBLISH消息
     */
    static encodeEncryptedMqttPublishMessage(topic, payload, key, options = {}) {
        if (!topic) {
            throw new Error('主题不能为空');
        }
        if (!payload) {
            throw new Error('消息内容不能为空');
        }
        if (!key) {
            throw new Error('加密密钥不能为空');
        }

        // 设置默认值
        const messageId = options.messageId || Math.floor(Math.random() * 65535) + 1;
        const qos = options.qos !== undefined ? options.qos : 0;
        const dup = options.dup !== undefined ? options.dup : false;
        const retain = options.retain !== undefined ? options.retain : false;

        // 转换payload和key为Buffer（如果它们是字符串）
        const payloadBuffer = Buffer.isBuffer(payload) ? payload : Buffer.from(payload);
        const keyBuffer = Buffer.isBuffer(key) ? key : Buffer.from(key);

        // 加密消息内容
        const encryptedPayload = AES.AESEncrypt(payloadBuffer, keyBuffer);
        if (!encryptedPayload) {
            throw new Error('消息加密失败');
        }

        // 创建MQTT PUBLISH消息
        return BinaryMessage.encodePublish(topic, encryptedPayload, messageId, qos, dup, retain);
    }

    /**
     * 封装MQTT PINGREQ消息
     * @returns {Buffer} 封装后的MQTT PINGREQ消息
     */
    static encodeMqttPingReqMessage() {
        return BinaryMessage.encodePingReq();
    }

    /**
     * 从MQTT消息中提取有效载荷
     * @param {Buffer} mqttMessage MQTT消息Buffer
     * @returns {Buffer} 提取的有效载荷Buffer
     */
    static extractMqttPayload(mqttMessage) {
        return BinaryMessage.extractPayload(mqttMessage);
    }

    /**
     * 从MQTT PUBLISH消息中提取主题名称
     * @param {Buffer} mqttMessage MQTT消息Buffer
     * @returns {string} 提取的主题名称
     */
    static extractMqttTopicName(mqttMessage) {
        return BinaryMessage.extractTopicName(mqttMessage);
    }

    /**
     * 解密MQTT消息有效载荷
     * @param {Buffer} encryptedPayload 加密的有效载荷
     * @param {string|Buffer} key 解密密钥
     * @param {boolean} [checkTime=false] 是否检查时间戳有效性
     * @returns {Buffer} 解密后的有效载荷，失败返回null
     */
    static decryptMqttPayload(encryptedPayload, key, checkTime = false) {
        if (!encryptedPayload) {
            throw new Error('加密数据不能为空');
        }
        if (!key) {
            throw new Error('解密密钥不能为空');
        }

        // 转换key为Buffer（如果它是字符串）
        const keyBuffer = Buffer.isBuffer(key) ? key : Buffer.from(key);

        // 解密数据
        return AES.AESDecrypt(encryptedPayload, keyBuffer, checkTime);
    }
}

module.exports = WebSocketMessageUtils;
