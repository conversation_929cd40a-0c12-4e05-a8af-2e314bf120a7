/**
 * WebSocket控制器
 * 提供WebSocket相关的REST API
 */

const express = require('express');
const router = express.Router();
const WebSocketClientService = require('../services/WebSocketClientService');
const BinaryMessage = require('../models/BinaryMessage');
const AES = require('../utils/AES');

// 创建WebSocket客户端服务实例
const webSocketClientService = new WebSocketClientService();

/**
 * 连接WebSocket服务器
 * @route POST /api/websocket/connect
 */
router.post('/connect', async (req, res) => {
    try {
        const { url } = req.body;
        const result = await webSocketClientService.connect(url);
        res.json({ success: result, message: result ? '连接成功' : '连接失败' });
    } catch (error) {
        console.error('连接WebSocket服务器失败:', error);
        res.status(500).json({ success: false, message: `连接失败: ${error.message}` });
    }
});

/**
 * 发送文本消息
 * @route POST /api/websocket/send
 */
router.post('/send', (req, res) => {
    try {
        const { message } = req.body;
        if (!message) {
            return res.status(400).json({ success: false, message: '消息不能为空' });
        }

        const result = webSocketClientService.sendMessage(message);
        res.json({ success: result, message: result ? '发送成功' : '发送失败' });
    } catch (error) {
        console.error('发送消息失败:', error);
        res.status(500).json({ success: false, message: `发送失败: ${error.message}` });
    }
});

/**
 * 发送二进制消息
 * @route POST /api/websocket/sendBinary
 */
router.post('/sendBinary', (req, res) => {
    try {
        const { data } = req.body;
        if (!data) {
            return res.status(400).json({ success: false, message: '数据不能为空' });
        }

        // 将Base64编码的数据转换为Buffer
        const binaryData = Buffer.from(data, 'base64');
        const result = webSocketClientService.sendBinaryMessage(binaryData);
        res.json({ success: result, message: result ? '发送成功' : '发送失败' });
    } catch (error) {
        console.error('发送二进制消息失败:', error);
        res.status(500).json({ success: false, message: `发送失败: ${error.message}` });
    }
});

/**
 * 发送结构化二进制消息
 * @route POST /api/websocket/sendStructuredBinary
 */
router.post('/sendStructuredBinary', (req, res) => {
    try {
        const { messageType, payload } = req.body;
        if (messageType === undefined) {
            return res.status(400).json({ success: false, message: '消息类型不能为空' });
        }
        if (!payload) {
            return res.status(400).json({ success: false, message: '消息内容不能为空' });
        }

        // 创建二进制消息
        const binaryMessage = new BinaryMessage(messageType);
        const payloadBuffer = Buffer.from(payload);
        binaryMessage.encode(payloadBuffer);

        // 发送二进制消息
        const result = webSocketClientService.sendBinaryMessage(binaryMessage.getPayload());
        res.json({ success: result, message: result ? '发送成功' : '发送失败' });
    } catch (error) {
        console.error('发送结构化二进制消息失败:', error);
        res.status(500).json({ success: false, message: `发送失败: ${error.message}` });
    }
});

/**
 * 发送MQTT PUBLISH消息
 * @route POST /api/websocket/sendPublish
 */
router.post('/sendPublish', (req, res) => {
    try {
        const { topic, payload, qos, dup, retain } = req.body;
        if (!topic) {
            return res.status(400).json({ success: false, message: '主题不能为空' });
        }
        if (!payload) {
            return res.status(400).json({ success: false, message: '消息内容不能为空' });
        }

        // 创建MQTT PUBLISH消息
        const payloadBuffer = Buffer.from(payload);
        const messageId = Math.floor(Math.random() * 65535) + 1; // 生成1-65535之间的随机数作为消息ID
        const binaryMessage = BinaryMessage.encodePublish(topic, payloadBuffer, messageId, qos || 0, dup || false, retain || false);

        // 发送二进制消息
        const result = webSocketClientService.sendBinaryMessage(binaryMessage);
        res.json({ success: result, message: result ? '发送成功' : '发送失败' });
    } catch (error) {
        console.error('发送MQTT PUBLISH消息失败:', error);
        res.status(500).json({ success: false, message: `发送失败: ${error.message}` });
    }
});

/**
 * 发送加密的MQTT PUBLISH消息
 * @route POST /api/websocket/sendEncryptedPublish
 */
router.post('/sendEncryptedPublish', (req, res) => {
    try {
        const { topic, payload, key, qos, dup, retain } = req.body;
        if (!topic) {
            return res.status(400).json({ success: false, message: '主题不能为空' });
        }
        if (!payload) {
            return res.status(400).json({ success: false, message: '消息内容不能为空' });
        }
        if (!key) {
            return res.status(400).json({ success: false, message: '加密密钥不能为空' });
        }

        // 加密消息内容
        const payloadBuffer = Buffer.from(payload);
        const keyBuffer = Buffer.from(key);
        const encryptedPayload = AES.AESEncrypt(payloadBuffer, keyBuffer);
        if (!encryptedPayload) {
            return res.status(500).json({ success: false, message: '消息加密失败' });
        }

        // 创建MQTT PUBLISH消息
        const messageId = Math.floor(Math.random() * 65535) + 1; // 生成1-65535之间的随机数作为消息ID
        const binaryMessage = BinaryMessage.encodePublish(topic, encryptedPayload, messageId, qos || 0, dup || false, retain || false);

        // 发送二进制消息
        const result = webSocketClientService.sendBinaryMessage(binaryMessage);
        res.json({ success: result, message: result ? '发送成功' : '发送失败' });
    } catch (error) {
        console.error('发送加密的MQTT PUBLISH消息失败:', error);
        res.status(500).json({ success: false, message: `发送失败: ${error.message}` });
    }
});

/**
 * 重新连接WebSocket服务器
 * @route POST /api/websocket/reconnect
 */
router.post('/reconnect', async (req, res) => {
    try {
        const result = await webSocketClientService.reconnect();
        res.json({ success: result, message: result ? '重连成功' : '重连失败' });
    } catch (error) {
        console.error('重连WebSocket服务器失败:', error);
        res.status(500).json({ success: false, message: `重连失败: ${error.message}` });
    }
});

/**
 * 断开WebSocket连接
 * @route POST /api/websocket/disconnect
 */
router.post('/disconnect', (req, res) => {
    try {
        webSocketClientService.disconnect();
        res.json({ success: true, message: '断开连接成功' });
    } catch (error) {
        console.error('断开WebSocket连接失败:', error);
        res.status(500).json({ success: false, message: `断开连接失败: ${error.message}` });
    }
});

/**
 * 获取连接状态
 * @route GET /api/websocket/status
 */
router.get('/status', (req, res) => {
    try {
        const connected = webSocketClientService.isConnected();
        res.json({ success: true, connected, message: connected ? '已连接' : '未连接' });
    } catch (error) {
        console.error('获取连接状态失败:', error);
        res.status(500).json({ success: false, message: `获取状态失败: ${error.message}` });
    }
});

module.exports = router;