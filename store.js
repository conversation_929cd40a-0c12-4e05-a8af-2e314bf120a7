import ConnectionStatus from "./wfc/client/connectionStatus";
import wfc from "./wfc/client/wfc";
import EventType from "./wfc/client/wfcEvent";
import ConversationType from "./wfc/model/conversationType";
import {
    eq,
    gt,
    numberValue,
    stringValue
} from "./wfc/util/longUtil";
import helper from "./pages/util/helper";
import pinyin from 'pinyin/esm/pinyin'
import GroupType from "./wfc/model/groupType";
// import {imageThumbnail, videoDuration, videoThumbnail} from "./ui/util/imageUtil";
import MessageContentMediaType from "./wfc/messages/messageContentMediaType";
import Conversation from "./wfc/model/conversation";
import MessageContentType from "./wfc/messages/messageContentType";
import Message from "./wfc/messages/message";
import ImageMessageContent from "./wfc/messages/imageMessageContent";
import VideoMessageContent from "./wfc/messages/videoMessageContent";
import FileMessageContent from "./wfc/messages/fileMessageContent";
import ForwardType from "./pages/conversation/message/forward/ForwardType";
import TextMessageContent from "./wfc/messages/textMessageContent";
import SearchType from "./wfc/model/searchType";
import Config from "./config";
import {
    clear,
    getItem,
    setItem
} from "./pages/util/storageHelper";
import CompositeMessageContent from "./wfc/messages/compositeMessageContent";
// import {getConversationPortrait} from "./ui/util/imageUtil";
import DismissGroupNotification from "./wfc/messages/notification/dismissGroupNotification";
import KickoffGroupMemberNotification from "./wfc/messages/notification/kickoffGroupMemberNotification";
import QuitGroupNotification from "./wfc/messages/notification/quitGroupNotification";
import MessageStatus from "./wfc/messages/messageStatus";
import MessageConfig from "./wfc/client/messageConfig";
import PersistFlag from "./wfc/messages/persistFlag";
import NullGroupInfo from "./wfc/model/nullGroupInfo";
import NullUserInfo from "./wfc/model/nullUserInfo";
import LeaveChannelChatMessageContent from "./wfc/messages/leaveChannelChatMessageContent";
import ArticlesMessageContent from "./wfc/messages/articlesMessageContent";
import EnterChannelChatMessageContent from "./wfc/messages/enterChannelChatMessageContent";
import NullChannelInfo from "./wfc/model/NullChannelInfo";
import CallStartMessageContent from "./wfc/av/messages/callStartMessageContent";
import GiftCustomMessageContent from "./wfc_custom_message/giftCustomMessageContent";
import {
    storeToRefs
} from "pinia";
import {
    pstore
} from "./pstore";
import SoundMessageContent from "./wfc/messages/soundMessageContent";
import appServerApi from "./api/appServerApi";
import LocationMessageContent from "./wfc/messages/locationMessageContent";
import CardMessageContent from "./wfc/messages/cardMessageContent";
// import WebSocketClient from './websocket'; //自定义websocket
import GroupIdentityCache from './utils/GroupIdentityCache';
import messageLimitManager from './utils/messageLimitManager'
/**
 * 一些说明
 * _开头的字段，是为了UI层展示方便，而打补丁上出去的
 * __开头的字段，仅内部使用
 * _开头的函数，是内部函数
 * 外部不直接更新字段，而是通过提供各种action方法更新
 */
// let websocketClient;
let conversationState;
let contactState;
let searchState;
let pickState;
let miscState;
let momentNoticeTimer;
const CONVERSATION_CACHE_KEY = 'cached_conversation_list';

let store = {
    debug: true,
    state: {
        conversation: null,
        contact: null,
        search: null,
        pick: null,
        misc: {
            // 新增网络状态
            networkStatus: true, // 默认有网络
            // 添加群状态跟踪
            canSendMessage: true, // 是否可以发送消息
            groupStatusMessage: '', // 群状态提示消息
        },
        messageReadStatus: new Map(), // 存储消息ID和已读状态的映射
        messageIdMapping: new Map(), // 存储野火IM消息ID和服务器消息ID的映射
        lastReadMessageId: null, // 记录最后发送已读消息的ID
        shareData: null, // 存储分享数据
    },

    getters: {
        getMessageById: function (messageId) {
            return store.getMessageById(messageId);
        },
        getMessagesByConversationId: function (conversationId) {
            return store.getMessagesByConversationId(conversationId);
        },
        getNetworkStatus: (state) => state.misc.networkStatus,
    },

    init() {
        console.log('init store')
        wfc.init()
        try {
            // 使用 Promise 包装异步操作
            const getClientInfo = () => {
                return new Promise((resolve, reject) => {
                    resolve("123456789")
                })
            }

            // 处理客户端信息
            getClientInfo()
                .then((clientId) => {
                    console.log('push clientId', clientId)
                    wfc.setDeviceToken(7, clientId)
                })
                .catch((error) => {
                    console.error('获取客户端信息失败:', error)
                })
        } catch (error) {
            console.error('初始化推送失败:', error)
        }

        // 初始化网络监听
        this.dispatch('initNetworkListener');

        // 将websocketClient实例保存到全局对象，方便在组件中访问
        // if (typeof window !== 'undefined') {
        //     window.wfcWebsocket = websocketClient;
        //     console.log('将WebSocket实例保存到全局window对象');
        // }

        const {
            conversationStore,
            contactStore,
            pickStore,
            searchStore,
            miscStore
        } = storeToRefs(pstore())
        console.log('store init', conversationStore, contactStore, searchStore, pickStore, miscStore)
        console.log('wfc.isReceiptEnabled & wfc.isUserReceiptEnabled', wfc.isReceiptEnabled(), wfc.isUserReceiptEnabled())
        conversationState = conversationStore.value;
        contactState = contactStore.value;
        searchState = searchStore.value;
        pickState = pickStore.value;
        miscState = miscStore.value;

        // 尝试加载缓存
        const cachedConversationList = this.loadConversationCache();
        if (cachedConversationList && Array.isArray(cachedConversationList) && cachedConversationList.length > 0) {
            // 如果有缓存，先使用缓存数据
            try {
                conversationState.conversationInfoList = cachedConversationList.map(item => {
                    // 这里需要根据你的数据结构进行适当的转换
                    // 假设缓存的数据结构与你的数据结构一致
                    return item && typeof item === 'object' ? item : null;
                }).filter(item => item !== null); // 过滤掉无效的项
                console.log('使用缓存数据初始化会话列表');
            } catch (error) {
                console.error('处理缓存数据时出错:', error);
                conversationState.conversationInfoList = [];
            }
        }else{
			console.log('没有缓存数据 可以初始化会话列表',cachedConversationList);
		}
        store.state.conversation = conversationState;
        store.state.contact = contactState;
        store.state.search = searchState;
        store.state.pick = pickState;
        store.state.misc = miscState;

        // websocketClient = new WebSocketClient('wss://api.ykjrzl.com/ws');
        // websocketClient.connect();

        wfc.eventEmitter.on(EventType.ConnectionStatusChanged, (status) => {
            miscState.connectionStatus = status;
            console.log('connection status changed', status);
            if (status === ConnectionStatus.ConnectionStatusConnected) {
                this._loadDefaultData();
            } else if (status === ConnectionStatus.ConnectionStatusLogout ||
                status === ConnectionStatus.ConnectionStatusRejected ||
                status === ConnectionStatus.ConnectionStatusSecretKeyMismatch ||
                status === ConnectionStatus.ConnectionStatusTokenIncorrect) {
                let userId = getItem('userId')
                let im_token = getItem('im_token')
                wfc.connect(userId, im_token)
            } else if (status === ConnectionStatus.ConnectionStatusKickedOff) {
                //被踢下线了
                wfc.disconnect();
                setItem("userId", "")
                setItem("token", "")
                setItem("im_token", "")
                clear()
                _reset();
            }
        });

        wfc.eventEmitter.on(EventType.UserInfosUpdate, (userInfos) => {
            console.log('store UserInfosUpdate', userInfos, miscState.connectionStatus)
            this._reloadSingleConversationIfExist(userInfos);
            // TODO optimize
            this._loadCurrentConversationMessages();
            this._loadFriendList();
            this._loadFriendRequest();
            this._loadSelfUserInfo();
            // TODO 其他相关逻辑
        });

        wfc.eventEmitter.on(EventType.SettingUpdate, () => {
            console.log('store SettingUpdate')
            this._loadDefaultConversationList();
            this._loadFavContactList();
            this._loadFavGroupList();
            this._loadChannelList();
            this._loadCurrentConversationMessages();
        });

        wfc.eventEmitter.on(EventType.FriendRequestUpdate, (newFrs) => {
            console.log('store FriendRequestUpdate', newFrs)
            this._loadFriendRequest();
        });

        wfc.eventEmitter.on(EventType.FriendListUpdate, (updatedFriendIds) => {
            console.log('store FriendListUpdate', updatedFriendIds)
            this._loadFriendList();
            this._loadFriendRequest();
            this._loadFavContactList();
            this._loadDefaultConversationList();
            this._loadCurrentConversationMessages();
        });

        wfc.eventEmitter.on(EventType.GroupInfosUpdate, (groupInfos) => {
            console.log('store GroupInfosUpdate', groupInfos)
            const gInfo = groupInfos[0];
            // TODO optimize
            console.log('store GroupInfosUpdate', groupInfos)
            this._reloadGroupConversationIfExist(groupInfos);
            
            // 如果当前会话是其中一个群聊，重新检查群状态
            if (conversationState.currentConversationInfo && 
                conversationState.currentConversationInfo.conversation.type === ConversationType.Group) {
                
                const currentGroupId = conversationState.currentConversationInfo.conversation.target;
                const updatedGroup = groupInfos.find(g => g.target === currentGroupId);
                
                if (updatedGroup) {
                    const isGroupDismissed = this.isGroupDismissed(currentGroupId);
                    const isUserMember = this.isCurrentUserGroupMember(currentGroupId);
                    
                    console.log('群信息变化，重新检查群状态:', {
                        groupId: currentGroupId,
                        isGroupDismissed,
                        isUserMember
                    });
                    
                    if (isGroupDismissed) {
                        miscState.canSendMessage = false;
                        miscState.groupStatusMessage = '无法在已解散的群聊中发送消息';
                    } else if (!isUserMember) {
                        miscState.canSendMessage = false;
                        miscState.groupStatusMessage = '无法在已退出的群聊中发送消息';
                    } else {
                        miscState.canSendMessage = true;
                        miscState.groupStatusMessage = '';
                    }
                }
            }
            
            this._loadFavGroupList();
            // TODO 其他相关逻辑
        });

        wfc.eventEmitter.on(EventType.GroupMembersUpdate, (groupId, members) => {
            console.log('store GroupMembersUpdate', groupId, members)
            
            // 检查群聊是否应该显示在会话列表中
            const shouldShow = this.shouldShowGroupInConversationList(groupId);
            if (!shouldShow) {
                console.log('群成员更新后发现群聊不应显示，从会话列表中移除:', groupId);
                // 从会话列表中移除该群聊
                let conversation = new Conversation(ConversationType.Group, groupId, 0);
                conversationState.conversationInfoList = conversationState.conversationInfoList.filter(info => !info
                    .conversation.equal(conversation));
                // 更新缓存
                this.saveConversationCache(conversationState.conversationInfoList);
                
                // 如果当前正在查看这个群聊，则清除当前会话
                if (conversationState.currentConversationInfo && 
                    conversationState.currentConversationInfo.conversation.type === ConversationType.Group &&
                    conversationState.currentConversationInfo.conversation.target === groupId) {
                    this.setCurrentConversationInfo(null);
                }
                return;
            }
            
            // TODO optimize
            this._reloadGroupConversationIfExist([new NullGroupInfo(groupId)]);
            
            // 如果当前会话是该群聊，重新检查群状态
            if (conversationState.currentConversationInfo && 
                conversationState.currentConversationInfo.conversation.type === ConversationType.Group &&
                conversationState.currentConversationInfo.conversation.target === groupId) {
                
                const isGroupDismissed = this.isGroupDismissed(groupId);
                const isUserMember = this.isCurrentUserGroupMember(groupId);
                
                console.log('群成员变化，重新检查群状态:', {
                    groupId,
                    isGroupDismissed,
                    isUserMember
                });
                
                if (isGroupDismissed) {
                    miscState.canSendMessage = false;
                    miscState.groupStatusMessage = '无法在已解散的群聊中发送消息';
                } else if (!isUserMember) {
                    miscState.canSendMessage = false;
                    miscState.groupStatusMessage = '无法在已退出的群聊中发送消息';
                } else {
                    miscState.canSendMessage = true;
                    miscState.groupStatusMessage = '';
                }
            }
            //     this._loadFavGroupList();
            // TODO 其他相关逻辑
        });

        wfc.eventEmitter.on(EventType.ChannelInfosUpdate, (groupInfos) => {
            console.log('EventType.ChannelInfosUpdate store ChannelInfosUpdate', groupInfos)
            this._loadDefaultConversationList();
            this._loadChannelList();
        });

        wfc.eventEmitter.on(EventType.ConversationInfoUpdate, (conversationInfo) => {
            console.log('EventType.ConversationInfoUpdate store ConversationInfoUpdate--------------------------->', conversationInfo)
            this._reloadConversation(conversationInfo.conversation)
            if (conversationState.currentConversationInfo && conversationState.currentConversationInfo.conversation.equal(conversationInfo.conversation)) {
                this._loadCurrentConversationMessages();
            }
        });


        wfc.eventEmitter.on(EventType.ReceiveMessage, (msg, hasMore) => {
            // console.log('EventType.ReceiveMessage receiveMessage', msg);
            if (miscState.connectionStatus === ConnectionStatus.ConnectionStatusReceiveing) {
                return;
            }

            if (msg.messageContent instanceof DismissGroupNotification ||
                (msg.messageContent instanceof KickoffGroupMemberNotification && msg.messageContent.kickedMembers.indexOf(
                    wfc.getUserId()) >= 0) ||
                (msg.messageContent instanceof QuitGroupNotification && msg.messageContent.operator === wfc.getUserId())
            ) {
                // 如果是群解散或被踢出群，从主动退群记录中移除
                if (msg.messageContent instanceof DismissGroupNotification ||
                    (msg.messageContent instanceof KickoffGroupMemberNotification && msg.messageContent.kickedMembers.indexOf(
                        wfc.getUserId()) >= 0)) {
                    this.removeFromQuitGroupRecord(msg.conversation.target);
                }
                
                this.setCurrentConversationInfo(null);
                return;
            }

            if (!hasMore) {
                this._reloadConversation(msg.conversation)
            }
            if (conversationState.currentConversationInfo && msg.conversation.equal(conversationState
                .currentConversationInfo.conversation)) {
                // 移动端，目前只有单聊会发送typing消息
                if (msg.messageContent.type === MessageContentType.Typing) {
                    let groupId = msg.conversation.type === 1 ? msg.conversation.target : '';
                    let userInfo = wfc.getUserInfo(msg.from, false, groupId)
                    userInfo = Object.assign({}, userInfo);
                    userInfo._displayName = wfc.getGroupMemberDisplayNameEx(userInfo);
                    conversationState.inputtingUser = userInfo;

                    if (!conversationState.inputClearHandler) {
                        conversationState.inputClearHandler = () => {
                            conversationState.inputtingUser = null;
                        }
                    }
                    clearTimeout(conversationState.inputClearHandler);
                    setTimeout(conversationState.inputClearHandler, 3000)
                } else {
                    clearTimeout(conversationState.inputClearHandler);
                    conversationState.inputtingUser = null;
                }

                if (!this._isDisplayMessage(msg) || msg.messageContent.type === MessageContentType
                    .RecallMessage_Notification) {
                    return;
                }

                // 会把下来加载更多加载的历史消息给清理了
                let lastTimestamp = 0;
                let msgListLength = conversationState.currentConversationMessageList.length;
                if (msgListLength > 0) {
                    lastTimestamp = conversationState.currentConversationMessageList[msgListLength - 1].timestamp;
                }
                this._patchMessage(msg, lastTimestamp);
                let msgIndex = conversationState.currentConversationMessageList.findIndex(m => {
                    return m.messageId === msg.messageId ||
                        (gt(m.messageUid, 0) && eq(m.messageUid, msg.messageUid)) ||
                        (m.messageContent.type === MessageContentType.Streaming_Text_Generating &&
                            (msg.messageContent.type === MessageContentType.Streaming_Text_Generating || msg.messageContent
                                .type === MessageContentType.Streaming_Text_Generated) &&
                            m.messageContent.streamId === msg.messageContent.streamId
                        )
                })
                if (msgIndex > -1) {
                    // FYI: https://v2.vuejs.org/v2/guide/reactivity#Change-Detection-Caveats
                    conversationState.currentConversationMessageList.splice(msgIndex, 1, msg);
                    console.log('msg duplicate', msg.messageId, msg.messageUid)
                    return;
                }
                conversationState.currentConversationMessageList.push(msg);
            }

            // 没有使用 uikit 默认的通知处理
            if (msg.conversation.type !== 2 && miscState.isAppHidden && (miscState.enableNotification || msg.status ===
                MessageStatus.AllMentioned || msg.status === MessageStatus.Mentioned)) {
                this.notify(msg);
            }
        });

        wfc.eventEmitter.on(EventType.RecallMessage, (operator, messageUid) => {
            console.log('EventType.RecallMessage store RecallMessage', operator, messageUid)
            this._reloadConversationByMessageUidIfExist(messageUid);
            if (conversationState.currentConversationInfo) {
                let msg = wfc.getMessageByUid(messageUid);
                if (msg && msg.conversation.equal(conversationState.currentConversationInfo.conversation)) {
                    if (conversationState.currentConversationMessageList) {
                        let lastTimestamp = 0;
                        conversationState.currentConversationMessageList = conversationState.currentConversationMessageList
                            .map(msg => {
                                if (eq(msg.messageUid, messageUid)) {
                                    let newMsg = wfc.getMessageByUid(messageUid);
                                    this._patchMessage(newMsg, lastTimestamp);
                                    return newMsg;
                                }
                                lastTimestamp = msg.timestamp;
                                return msg;
                            });
                    }
                }
            }
        });

        wfc.eventEmitter.on(EventType.UserOnlineEvent, (userOnlineStatus) => {
            console.log('EventType.UserOnlineEvent store UserOnlineEvent', userOnlineStatus)
            userOnlineStatus.forEach(e => {
                miscState.userOnlineStateMap.set(e.userId, e);
            })
            // 更新在线状态
            contactState.friendList = this._patchAndSortUserInfos(contactState.friendList, '');
        })
        // 服务端删除
        wfc.eventEmitter.on(EventType.MessageDeleted, (messageUid) => {
            this._reloadConversationByMessageUidIfExist(messageUid);
            if (conversationState.currentConversationInfo) {

                if (conversationState.currentConversationMessageList) {
                    conversationState.currentConversationMessageList = conversationState.currentConversationMessageList
                        .filter(msg => !eq(msg.messageUid, messageUid))
                }
            }
        });
        // 本地删除
        wfc.eventEmitter.on(EventType.DeleteMessage, (messageId) => {
            this._reloadConversationByMessageIdIfExist(messageId);
            if (conversationState.currentConversationInfo) {
                if (conversationState.currentConversationMessageList) {
                    conversationState.currentConversationMessageList = conversationState.currentConversationMessageList
                        .filter(msg => msg.messageId !== messageId)
                }
            }
        });

        wfc.eventEmitter.on(EventType.SendMessage, (message) => {
            // 删除频道，或者从频道会话切到其他会话时，会发送一条离开频道的消息
            if (message.messageContent instanceof LeaveChannelChatMessageContent) {
                return;
            }
            this._reloadConversation(message.conversation);
            if (!this._isDisplayMessage(message)) {
                return;
            }
            if (!conversationState.currentConversationInfo || !message.conversation.equal(conversationState.currentConversationInfo.conversation)) {
                console.log('not current conv')
                return;
            }
            let index = conversationState.currentConversationMessageList.findIndex(m => m.messageId === message.messageId);
            if (index !== -1) {
                return;
            }
            let length = conversationState.currentConversationMessageList.length;
            let lastTimestamp = 0;
            if (length > 0) {
                let lastMessage = conversationState.currentConversationMessageList[length - 1];
                lastTimestamp = lastMessage.timestamp;
            }
            this._patchMessage(message, lastTimestamp)

            conversationState.currentConversationMessageList.push(message);
        });

        /**
         * 目前可以回调自己已读消息的通知，
         * messageStatus会更新成6
         */
        wfc.eventEmitter.on(EventType.MessageStatusUpdate, (message) => {
            console.log('message status update', message)
            // 直接触发全局事件通知组件更新
			if(message.conversation.type === ConversationType.Group){
                let messageUidStr = `${message.messageUid.high}-${message.messageUid.low}`;
                uni.$emit('messageReadStatusUpdated', {
                    type: ConversationType.Group,
                    messageId: messageUidStr
                });
            }
            // if(message.conversation.type === ConversationType.Single && message.status === MessageStatus.Readed){
            //     let messageSendList = getItem("messageSendList");
            //     console.log('获取到的messageSendList:', messageSendList, typeof messageSendList);

            //     // 确保messageSendList是Set对象，如果不是则初始化为空Set
            //     if (!messageSendList || !(messageSendList instanceof Set)) {
            //         console.warn('messageSendList不是Set对象，初始化为空Set');
            //         messageSendList = new Set();
            //     }
            //     if(!messageSendList.has(message.messageId)){
            //         messageSendList.add(message.messageId)
            //         setItem('messageSendList', messageSendList)
            //     }
            // }
            if (!this._isDisplayMessage(message)) {
                return;
            }
            if (!conversationState.currentConversationInfo || !message.conversation.equal(conversationState
                .currentConversationInfo.conversation)) {
                console.log('not current conv')
                return;
            }

            let index = conversationState.currentConversationMessageList.findIndex(m => m.messageId === message
                .messageId);
            if (index < 0) {
                return;
            }
            let msg = conversationState.currentConversationMessageList[index];
            Object.assign(msg, message)

            if (conversationState.currentConversationInfo.lastMessage && conversationState.currentConversationInfo
                .lastMessage.messageId === message.messageId) {
                Object.assign(conversationState.currentConversationInfo.lastMessage, message);
            }
        });

        wfc.eventEmitter.on(EventType.MessageRead, (readEntries) => {
            console.log('message read ', readEntries)
            // optimization
            if (conversationState.currentConversationInfo) {
                // wfc.getConversationRead 每次返回同一个对象，只是该对象的值不一样。
                // 由于 VUE 不能检测到初始化时，不存在的属性的变更，故此处重新 new 一个新的对象，并赋值。
                // FYI:https://vuejs.org/v2/guide/reactivity.html
                conversationState.currentConversationRead = new Map(wfc.getConversationRead(conversationState.currentConversationInfo.conversation));
            }
        });

        let userId = getItem('userId')
        let token = getItem('token')
        let im_token = getItem('im_token')
        console.log('store.js', 'init', userId, token, im_token)
        if (!userId || !im_token) {
            console.log('store.js', 'init', 'userId or token or im_token is null')
        } else {
            // let number = wfc.connect(userId, token);
            // console.log('store.js', 'init token', number)
            let number1 = wfc.connect(userId, im_token);
            console.log('store.js', 'init im_token', number1)
        }
        console.log('wfc.getConnectionStatus()', wfc.getConnectionStatus())
        if (wfc.getConnectionStatus() === ConnectionStatus.ConnectionStatusConnected) {
            this._loadDefaultData();
        }

        miscState.connectionStatus = wfc.getConnectionStatus();
        appServerApi.getGiftList()
        // 确保定时器在每次显示页面时都在运行
        if (!this.momentNoticeTimer) {
            this.momentNoticeTimer = setInterval(() => {
                this.checkMomentNotice()
            }, 30000)
        }
    },

    // 初始化网络监听
    initNetworkListener({commit}) {
        const handleNetworkChange = (status) => {
            commit('UPDATE_NETWORK_STATUS', status.isConnected);

            // 网络恢复时的操作
            if (status.isConnected) {
                console.log('网络恢复，执行重连等操作');
                // 示例：尝试重新连接WebSocket
                // if (websocketClient && websocketClient.isClosed()) {
                //     websocketClient.connect();
                // }
                // 示例：同步未发送的消息
                store.dispatch('retryPendingMessages');
            } else {
            //     console.warn('网络断开，暂停实时操作');
            //     // 示例：停止消息发送
            //     websocketClient?.close();
            }
        };

        // 多端兼容方案
        if (typeof uni !== 'undefined') {
            // UniApp 环境
            uni.onNetworkStatusChange(handleNetworkChange);
            // 初始获取状态
            uni.getNetworkType({
                success: (res) => {
                    commit('UPDATE_NETWORK_STATUS', res.networkType !== 'none');
                }
            });
        } else if (typeof window !== 'undefined') {
            // H5 环境
            const updateStatus = () => {
                commit('UPDATE_NETWORK_STATUS', navigator.onLine);
            };

            window.addEventListener('online', updateStatus);
            window.addEventListener('offline', updateStatus);
            // 初始化状态
            updateStatus();
        }
    },

    // 示例：重试未发送消息
    retryPendingMessages() {
        const pendingMessages = store.state.conversation.sendingMessages;
        pendingMessages.forEach(msg => {
            wfc.sendMessage(msg);
        });
    },

    _loadDefaultData() {
        console.log('store.js', '_loadDefaultData');
        this._loadFavGroupList();
        this._loadChannelList();
        this._loadFriendList();
        this._loadFavContactList();
        this._loadFriendRequest();
        this._loadSelfUserInfo();
        this._loadDefaultConversationList();
        this._loadUserLocalSettings();
        conversationState.isMessageReceiptEnable = wfc.isReceiptEnabled() && wfc.isUserReceiptEnabled();
        if (conversationState.currentConversationInfo) {
            this._loadCurrentConversationMessages();
        }
    },

    // conversation actions

    _isDisplayMessage(message) {
        // return [PersistFlag.Persist, PersistFlag.Persist_And_Count].indexOf(MessageConfig.getMessageContentPersitFlag(message.messageContent.type)) > -1;
        return message.messageId !== 0 || message.messageContent.type === MessageContentType.Streaming_Text_Generating;
    },

    _loadDefaultConversationList() {
        this._loadConversationList([0, 1, 3], [0])
    },

    _loadConversationList(conversationType = [0, 1, 3], lines = [0]) {
        let conversationList = wfc.getConversationList(conversationType, lines);
        let toLoadUserIdSet = new Set();
        let toLoadGroupIds = [];
        console.log('_loadConversationList size', conversationList.length);
        conversationList.forEach(info => {
            if (info.conversation.type === ConversationType.Single) {
                toLoadUserIdSet.add(info.conversation.target)
                if (info.lastMessage) {
                    toLoadUserIdSet.add(info.lastMessage.from);
                }
            } else if (info.conversation.type === ConversationType.Group) {
                toLoadGroupIds.push(info.conversation.target)
                if (info.lastMessage) {
                    toLoadUserIdSet.add(info.lastMessage.from);
                }
            }
        })
        let userInfoMap = new Map();
        let groupInfoMap = new Map();
        toLoadUserIdSet.forEach(uid => {
            userInfoMap.set(uid, new NullUserInfo(uid));
        })
        toLoadGroupIds.forEach(gid => {
            groupInfoMap.set(gid, new NullGroupInfo(gid))
        })

        console.log('to load userIds', toLoadUserIdSet.size);
        wfc.getUserInfos([...toLoadUserIdSet], '')
            .forEach(u => {
                userInfoMap.set(u.uid, u);
            });
        wfc.getGroupInfos(toLoadGroupIds)
            .forEach(g => {
                groupInfoMap.set(g.target, g);
            });

        conversationList.forEach(info => {
            this._patchConversationInfo(info, true, userInfoMap, groupInfoMap);
            // side affect
            if (conversationState.currentConversationInfo &&
                conversationState.currentConversationInfo.conversation.equal(info.conversation)) {
                conversationState.currentConversationInfo = info;
                this._patchCurrentConversationOnlineStatus();
            }
        });
        
        // 过滤掉不应该显示的群聊
        conversationList = conversationList.filter(info => {
            if (info.conversation.type === ConversationType.Group) {
                const shouldShow = this.shouldShowGroupInConversationList(info.conversation.target);
                if (!shouldShow) {
                    console.log('过滤掉不应显示的群聊:', info.conversation.target);
                    return false;
                }
            }
            return true;
        });
        
        conversationState.conversationInfoList = conversationList;
        // 保存缓存
        this.saveConversationCache(conversationList);
    },

    // 添加缓存保存方法
    saveConversationCache(conversationList) {
        try {
            setItem(CONVERSATION_CACHE_KEY, conversationList);
        } catch (e) {
            console.error('保存会话列表缓存失败:', e);
        }
    },

    // 添加缓存读取方法
    loadConversationCache() {
        try {
            const cachedList = getItem(CONVERSATION_CACHE_KEY);
			console.log('loadConversationCache',cachedList)
            if (cachedList && Array.isArray(cachedList) && cachedList.length > 0) {
                // 确保每个元素都是有效的对象，并过滤掉不应该显示的群聊
                const validItems = cachedList
                    .filter(item => item && typeof item === 'object')
                    .filter(item => {
                        // 如果是群聊，检查是否应该显示
                        if (item.conversation && item.conversation.type === ConversationType.Group) {
                            const shouldShow = this.shouldShowGroupInConversationList(item.conversation.target);
                            if (!shouldShow) {
                                console.log('从缓存中过滤掉不应显示的群聊:', item.conversation.target);
                                return false;
                            }
                        }
                        return true;
                    });
                
                if (validItems.length > 0) {
                    return validItems;
                }
            }
            return null;
        } catch (e) {
            console.error('读取会话列表缓存失败:', e);
            return  null;
        }
    },

    _reloadConversation(conversation, insertIfNoExist = true) {
        let conversationInfo = wfc.getConversationInfo(conversation);
        if (conversationInfo) {
            conversationInfo = this._patchConversationInfo(conversationInfo);
        }
        let index = conversationState.conversationInfoList.findIndex(info => info.conversation.equal(conversation));
        if (index >= 0) {
            Object.assign(conversationState.conversationInfoList[index], conversationInfo);
        } else {
            if (insertIfNoExist && gt(conversationInfo.timestamp, 0) && conversation.type !== ConversationType.ChatRoom) {
                conversationState.conversationInfoList.push(conversationInfo);
            } else {
                return conversationInfo;
            }
        }

        if (conversationState.currentConversationInfo && conversationState.currentConversationInfo.conversation.equal(
            conversation)) {
            conversationState.currentConversationInfo = conversationInfo;
        }

        // sort
        conversationState.conversationInfoList.sort((a, b) => {
            if ((a.top && b.top) || (!a.top && !b.top)) {
                return gt(a.timestamp, b.timestamp) ? -1 : 1;
            } else {
                if (a.top) {
                    return -1;
                } else {
                    return 1;
                }
            }
        })
        return conversationInfo;
    },

    _reloadSingleConversationIfExist(userInfos) {
        if (userInfos.length > 10) {
            this._loadDefaultConversationList();
        } else {
            userInfos.forEach(ui => {
                let conv = new Conversation(ConversationType.Single, ui.uid, 0);
                this._reloadConversation(conv, false);
            })
        }
    },

    _reloadGroupConversationIfExist(groupInfos) {
        if (groupInfos.length > 10) {
            this._loadDefaultConversationList();
        } else {
            groupInfos.forEach(gi => {
                // 检查群聊是否应该显示在会话列表中
                const shouldShow = this.shouldShowGroupInConversationList(gi.target);
                if (!shouldShow) {
                    console.log('群聊不应显示，跳过重新加载群聊会话:', gi.target);
                    // 从会话列表中移除该群聊（如果存在）
                    let conversation = new Conversation(ConversationType.Group, gi.target, 0);
                    conversationState.conversationInfoList = conversationState.conversationInfoList.filter(info => !info
                        .conversation.equal(conversation));
                    // 更新缓存
                    this.saveConversationCache(conversationState.conversationInfoList);
                    return;
                }
                
                let conv = new Conversation(ConversationType.Group, gi.target, 0);
                this._reloadConversation(conv, false);
            })
        }
    },

    _reloadConversationByMessageIdIfExist(messageId) {
        if (messageId === 0) {
            return;
        }
        let toLoadConversationInfo = null;
        for (let i = 0; i < conversationState.conversationInfoList.length; i++) {
            let info = conversationState.conversationInfoList[i];
            if (info.lastMessage && info.lastMessage.messageId === messageId) {
                toLoadConversationInfo = info;
                break;
            }
        }

        if (toLoadConversationInfo) {
            this._reloadConversation(toLoadConversationInfo.conversation)
        }
    },
    _reloadConversationByMessageUidIfExist(messageUid) {
        let toLoadConversationInfo = null;
        for (let i = 0; i < conversationState.conversationInfoList.length; i++) {
            let info = conversationState.conversationInfoList[i];
            if (info.lastMessage && eq(info.lastMessage.messageUid, messageUid)) {
                toLoadConversationInfo = info;
                break;
            }
        }

        if (toLoadConversationInfo) {
            this._reloadConversation(toLoadConversationInfo.conversation)
        }
    },

    setCurrentConversation(conversation) {
        if (!conversation) {
            this.setCurrentConversationInfo(null)
            return;
        }
        if (conversationState.currentConversationInfo && conversation.equal(conversationState.currentConversationInfo
            .conversation)) {
            return;
        }
        let convs = conversationState.conversationInfoList.filter(info => info.conversation.equal(conversation));
        let info;
        if (convs && convs.length > 0) {
            info = convs[0];
        } else {
            wfc.setConversationTimestamp(conversation, new Date().getTime());
            info = this._reloadConversation(conversation);
        }
        this.setCurrentConversationInfo(info);
    },

    setCurrentConversationInfo(conversationInfo) {
        if (!conversationInfo) {
            if (conversationState.currentConversationInfo) {
                let conversation = conversationState.currentConversationInfo.conversation;
                if ([ConversationType.Single, ConversationType.Group].indexOf(conversation.type) >= 0) {
                    wfc.unwatchOnlineState(conversation.type, [conversation.target]);
                } else if (conversation.type === ConversationType.Channel) {
                    let content = new LeaveChannelChatMessageContent();
                    wfc.sendConversationMessage(conversation, content);
                } else if (conversation.type === ConversationType.ChatRoom) {
                    wfc.quitChatroom(conversation.target)
                }
            }
            conversationState.currentConversationInfo = null;
            conversationState.shouldAutoScrollToBottom = false;
            conversationState.currentConversationMessageList.length = 0;
            conversationState.currentConversationOldestMessageId = 0;
            conversationState.currentConversationOldestMessageUid = 0;
            conversationState.currentConversationRead = null;
            conversationState.enableMessageMultiSelection = false;
            
            // 重置群状态
            miscState.canSendMessage = true;
            miscState.groupStatusMessage = '';

            return;
        }

        if (conversationState.currentConversationInfo && conversationState.currentConversationInfo.conversation.equal(
            conversationInfo.conversation)) {
            return;
        }

        let conversation = conversationInfo.conversation;
        
        // 检查群状态
        if (conversation.type === ConversationType.Group) {
            const groupId = conversation.target;
            const isGroupDismissed = this.isGroupDismissed(groupId);
            const isUserMember = this.isCurrentUserGroupMember(groupId);
            
            console.log('检查群状态:', {
                groupId,
                isGroupDismissed,
                isUserMember
            });
            
            if (isGroupDismissed) {
                miscState.canSendMessage = false;
                miscState.groupStatusMessage = '无法在已解散的群聊中发送消息';
            } else if (!isUserMember) {
                miscState.canSendMessage = false;
                miscState.groupStatusMessage = '无法在已退出的群聊中发送消息';
            } else {
                miscState.canSendMessage = true;
                miscState.groupStatusMessage = '';
            }
        } else {
            // 非群聊会话，重置状态
            miscState.canSendMessage = true;
            miscState.groupStatusMessage = '';
        }

        if (conversation.type === ConversationType.Group || (conversation.type === ConversationType.Single && !wfc
            .isMyFriend(conversation.target))) {
            wfc.watchOnlineState(conversation.type, [conversation.target], 1000, (states) => {
                states.forEach((e => {
                    miscState.userOnlineStateMap.set(e.userId, e);
                }))
                this._patchCurrentConversationOnlineStatus();

            }, (err) => {
                console.log('watchOnlineState error', err);
            })
        }
        if (conversation.type === ConversationType.Channel) {
            let content = new EnterChannelChatMessageContent();
            wfc.sendConversationMessage(conversation, content);
        } else if (conversation.type === ConversationType.ChatRoom) {
            wfc.joinChatroom(conversation.target);
        }
        conversationState.currentConversationInfo = conversationInfo;
        conversationState.shouldAutoScrollToBottom = true;
        conversationState.currentConversationMessageList.length = 0;
        conversationState.currentConversationOldestMessageId = 0;
        conversationState.currentConversationOldestMessageUid = 0;
        this._loadCurrentConversationMessages();

        conversationState.currentConversationRead = wfc.getConversationRead(conversationInfo.conversation);

        conversationState.enableMessageMultiSelection = false;
        conversationState.showChannelMenu = false;
        if (conversation.type === ConversationType.Channel) {
            let channelInfo = wfc.getChannelInfo(conversation.target, true);
            if (channelInfo.menus && channelInfo.menus.length > 0) {
                conversationState.showChannelMenu = true;
            }
        } else if (conversation.type === ConversationType.Group) {
            wfc.getGroupInfo(conversation.target, true);
        } else if (conversation.type === ConversationType.Single) {
            wfc.getUserInfo(conversation.target, true);
        }
        conversationState.quotedMessage = null;
        conversationState.currentVoiceMessage = null;

        clearTimeout(conversationState.inputClearHandler);
        conversationState.inputtingUser = null;

        pickState.messages.length = 0;

        // console.log('跳转进入会话页面，设置页面参数 ------------- setCurrentConversationInfo', JSON.stringify(conversationState))
    },

    quitGroup(groupId) {
        wfc.quitGroup(groupId, [0], null, () => {
            // 记录用户主动退群
            this.recordUserQuitGroup(groupId);
            
            this.setCurrentConversationInfo(null)
            let conversation = new Conversation(ConversationType.Group, groupId, 0);
            conversationState.conversationInfoList = conversationState.conversationInfoList.filter(info => !info
                .conversation.equal(conversation));
            
            // 关键：立即更新缓存，移除已退出的群聊
            this.saveConversationCache(conversationState.conversationInfoList);
            
            console.log('已退出群聊并更新缓存:', groupId);
            
        }, (err) => {
            console.log('quit group error', err)
        })
    },
    dismissGroup(groupId) {
        console.log('开始解散群组:', groupId);

        // 先调用后端接口解散群组
        appServerApi.disbandGroup({ gid: groupId })
            .then(() => {
                console.log('后端解散群组成功，开始调用WFC客户端解散');
                // 后端成功后再调用WFC客户端解散
                wfc.dismissGroup(groupId, [0], null, () => {
                    console.log('WFC客户端解散群组成功');
                    this.setCurrentConversationInfo(null)
                    let conversation = new Conversation(ConversationType.Group, groupId, 0);
                    conversationState.conversationInfoList = conversationState.conversationInfoList.filter(info => !info
                        .conversation.equal(conversation));
                }, (err) => {
                    console.log('WFC客户端解散群组失败:', err)
                })
            })
            .catch(err => {
                console.log('后端解散群组失败:', err);
                // 如果后端失败，仍然尝试WFC客户端解散（保持原有逻辑）
                wfc.dismissGroup(groupId, [0], null, () => {
                    console.log('WFC客户端解散群组成功（后端失败后的备用方案）');
                    this.setCurrentConversationInfo(null)
                    let conversation = new Conversation(ConversationType.Group, groupId, 0);
                    conversationState.conversationInfoList = conversationState.conversationInfoList.filter(info => !info
                        .conversation.equal(conversation));
                }, (err) => {
                    console.log('WFC客户端解散群组也失败:', err)
                })
            })
    },
    subscribeChannel(channelId, subscribe) {
        wfc.listenChannel(channelId, subscribe, () => {
            //this.setCurrentConversationInfo(null)
        }, (err) => {
            console.log('unsubscribe channel error', err)
        })
    },

    toggleMessageMultiSelection(message) {
        conversationState.enableMessageMultiSelection = !conversationState.enableMessageMultiSelection;
        pickState.messages.length = 0;
        if (conversationState.enableMessageMultiSelection && message) {
            pickState.messages.push(message);
        }
    },

    toggleChannelMenu(toggle) {
        conversationState.showChannelMenu = toggle;
    },

    selectOrDeselectMessage(message) {
        let index = pickState.messages.findIndex(m => m.messageId === message.messageId);
        if (index >= 0) {
            pickState.messages.splice(index, 1);
        } else {
            pickState.messages.push(message);
        }
    },

    deleteSelectedMessages() {
        conversationState.enableMessageMultiSelection = false;
        if (pickState.messages.length < 1) {
            return;
        }
        pickState.messages.sort((m1, m2) => m1.messageId - m2.messageId);
        pickState.messages.forEach(m => {
            wfc.deleteMessage(m.messageId);
        });
        pickState.messages.length = 0;
    },

    forwardMessage(forwardType, targetConversations, messages, extraMessageText) {
        targetConversations.forEach(conversation => {
            // let msg =new Message(conversation, message.messageContent)
            // wfc.sendMessage(msg)
            // 或者下面这种
            if (forwardType === ForwardType.NORMAL || forwardType === ForwardType.ONE_BY_ONE) {
                messages.forEach(message => {
                    if (message.messageContent instanceof ArticlesMessageContent) {
                        let linkContents = message.messageContent.toLinkMessageContent();
                        linkContents.forEach(lm => {
                            wfc.sendConversationMessage(conversation, lm);
                        })

                    } else {
                        message.messageContent = this._filterForwardMessageContent(message)
                        wfc.sendConversationMessage(conversation, message.messageContent);
                    }
                });
            } else {
                // 合并转发
                let compositeMessageContent = new CompositeMessageContent();
                let title = '';
                let msgConversation = messages[0].conversation;
                if (msgConversation.type === ConversationType.Single) {
                    let users = store.getUserInfos([wfc.getUserId(), msgConversation.target], '');
                    // 修复_displayName错误，使用安全的访问方式
                    let user1Name = users[0] ? (users[0]._displayName || users[0].displayName || '用户1') : '用户1';
                    let user2Name = users[1] ? (users[1]._displayName || users[1].displayName || '用户2') : '用户2';
                    title = user1Name + '和' + user2Name + '的聊天记录';
                } else {
                    title = '群的聊天记录';
                }
                compositeMessageContent.title = title;
                let msgs = messages.map(m => {
                    m.messageContent = this._filterForwardMessageContent(m)
                    return m
                })
                compositeMessageContent.setMessages(msgs);

                wfc.sendConversationMessage(conversation, compositeMessageContent);
            }

            if (extraMessageText) {
                let textMessage = new TextMessageContent(extraMessageText)
                wfc.sendConversationMessage(conversation, textMessage);
            }
        });
    },

    forwardByCreateConversation(forwardType, users, messages, extraMessageText) {
        this.createConversation(users,
            (conversation) => {
                console.log('conversation=', conversation)
                this.forwardMessage(forwardType, [conversation], messages, extraMessageText)

            },
            (err) => {
                console.error('createConversation error', err)
            })
    },

    setShouldAutoScrollToBottom(scroll) {
        conversationState.shouldAutoScrollToBottom = scroll;
    },

    /**
     *
     * @param src {String} 媒体url
     * @param thumbUrl {String} 缩略图url
     * @param thumb {String} base64格式的缩略图，但不包含'data:image/png;base64,'
     * @param autoplay
     */
    previewMedia(src, thumbUrl, thumb, autoplay = true) {
        conversationState.previewMediaItems.length = 0;
        conversationState.previewMediaItems.push({
            src: src,
            thumb: thumbUrl ? thumbUrl : 'data:image/png;base64,' + thumb,
            autoplay: autoplay,
        });
        conversationState.previewMediaIndex = 0;
        console.log('preview media', conversationState.previewMediaItems, conversationState.previewMediaIndex)
    },
    previewMedias(mediaItems, index) {
        conversationState.previewMediaItems.length = 0;
        conversationState.previewMediaItems.push(...mediaItems);
        conversationState.previewMediaIndex = index;
        console.log('preview medias', conversationState.previewMediaItems, conversationState.previewMediaIndex)
    },

    playVoice(message) {
        if (conversationState.currentVoiceMessage) {
            conversationState.currentVoiceMessage._isPlaying = false;
        }
        conversationState.currentVoiceMessage = message;
    },

    /**
     *
     * @param message
     * @param {Boolean} continuous  true，预览周围的媒体消息；false，只预览第一个参数传入的那条媒体消息
     */
    previewMessage(message, continuous) {
        conversationState.previewMediaItems.length = 0;
        conversationState.previewMediaIndex = 0;
        if (continuous && conversationState.currentConversationMessageList.length > 0) {
            let mediaMsgs = conversationState.currentConversationMessageList.filter(m => [MessageContentType.Image,
                MessageContentType.Video
            ].indexOf(m.messageContent.type) > -1)
            let msg;
            for (let i = 0; i < mediaMsgs.length; i++) {
                msg = mediaMsgs[i];
                if (msg.messageId === message.messageId) {
                    conversationState.previewMediaIndex = i;
                }
                conversationState.previewMediaItems.push({
                    src: msg.messageContent.remotePath,
                    thumb: 'data:image/png;base64,' + msg.messageContent.thumbnail,
                    autoplay: true,
                });
            }
        } else {
            conversationState.previewMediaIndex = 0;
            conversationState.previewMediaItems.push({
                src: message.messageContent.remotePath,
                thumb: 'data:image/png;base64,' + message.messageContent.thumbnail,
                autoplay: true,
            });
        }
    },

    previewCompositeMessage(compositeMessage, focusMessageUid) {
        conversationState.previewMediaItems.length = 0;
        conversationState.previewMediaIndex = 0;

        let mediaMsgs = compositeMessage.messageContent.messages.filter(m => [MessageContentType.Image, MessageContentType
            .Video
        ].indexOf(m.messageContent.type) > -1)
        let msg;
        for (let i = 0; i < mediaMsgs.length; i++) {
            msg = mediaMsgs[i];
            if (eq(msg.messageUid, focusMessageUid)) {
                conversationState.previewMediaIndex = i;
            }
            conversationState.previewMediaItems.push({
                src: msg.messageContent.remotePath,
                thumb: 'data:image/png;base64,' + msg.messageContent.thumbnail,
                autoplay: true,
            });
        }

    },

    /**
     *
     * @param conversation
     * @param {File | string} file html File 类型或者url，绝对路径只在electron里面生效
     * @param {number} duration 视频时长，仅在发送视频时有用
     * @return {Promise<boolean>}
     */
    async sendFile(conversation, file, duration) {
        // 检查临时会话消息发送限制
        const limitResult = messageLimitManager.checkCanSendMessage(conversation);
        if (!limitResult.canSend) {
            uni.showToast({
                title: limitResult.reason,
                icon: 'none',
                duration: 3000
            });
            return false;
        }
        
        if (file.size && file.size > 100 * 1024 * 1024) {
            if (!wfc.isSupportBigFilesUpload() || conversation.type === ConversationType.SecretChat) {
                console.log('file too big, and not support upload big file')
                return true;
            }
        }
        let fileOrLocalPath = null;
        let remotePath = null;
        if (typeof file === 'string') {
            if (!file.startsWith('http')) {
                fileOrLocalPath = file;
            } else {
                remotePath = file;
            }

            file = {
                path: file,
                name: file.substring((file.lastIndexOf('/') + 1))
            }
        } else {
            fileOrLocalPath = file;
        }
        let msg = new Message();
        msg.conversation = conversation;

        let mediaType = helper.getMediaType(file.name.split('.').slice(-1).pop());
        // todo other file type
        let messageContentmediaType = {
            'pic': MessageContentMediaType.Image,
            'video': MessageContentMediaType.Video,
            'doc': MessageContentMediaType.File,
        }[mediaType];

        let messageContent;
        switch (messageContentmediaType) {
            case MessageContentMediaType.Image:
                messageContent = new ImageMessageContent(fileOrLocalPath, remotePath);
                break;
            case MessageContentMediaType.Video:
                // 使用传入的duration参数，如果没有则默认为0
                messageContent = new VideoMessageContent(fileOrLocalPath, remotePath, '', duration || 0);
                break;
            case MessageContentMediaType.File:
                messageContent = new FileMessageContent(fileOrLocalPath, remotePath);
                break;
            default:
                console.log('not support file')
                return false;
        }
        msg.messageContent = messageContent;
        wfc.sendMessage(msg,
            (messageId) => {
                msg.messageId = messageId;
                console.log('sf, pr', messageId)
                
                // 记录临时会话消息发送
                messageLimitManager.recordMessageSent(conversation);
                
                // 如果是临时会话，显示剩余消息数提示
                const tipMessage = messageLimitManager.getTempChatTip(conversation);
                if (tipMessage) {
                    setTimeout(() => {
                        uni.showToast({
                            title: tipMessage,
                            icon: 'none',
                            duration: 2000
                        });
                    }, 1000);
                }
            },
            (progress, total) => {
                // console.log('sf p', msg.messageId, Math.ceil(progress / total * 100))
                let sm = conversationState.sendingMessages.find(e => e.messageId === msg.messageId);
                if (sm) {
                    sm.progress = progress;
                    sm.total = total;
                } else {
                    conversationState.sendingMessages.push({
                        messageId: msg.messageId,
                        progress,
                        total
                    });
                }
            },
            (messageUid) => {
                console.log('sf s', messageUid)

                conversationState.sendingMessages = conversationState.sendingMessages.filter(e => e.messageId !== msg
                    .messageId);
            },
            (error) => {
                console.log('sf e', error)

                conversationState.sendingMessages = conversationState.sendingMessages.filter(e => e.messageId !== msg
                    .messageId);
            }
        );
    },
    /**
     *
     * @param conversation
     * @return {Promise<boolean>}
     */
    async sendGift(conversation, id, num, receiveUid) {
        // 检查临时会话消息发送限制
        const limitResult = messageLimitManager.checkCanSendMessage(conversation);
        if (!limitResult.canSend) {
            uni.showToast({
                title: limitResult.reason,
                icon: 'none',
                duration: 3000
            });
            return false;
        }
        
        let messageContent = new GiftCustomMessageContent(
            id,
            num,
            receiveUid
        );
        console.log('sendGift', messageContent)
        
        // 发送礼物消息并记录限制
        wfc.sendConversationMessage(conversation, messageContent, [],
            // preparedCB - 消息准备完成回调
            (messageId, timestamp) => {
                // 记录临时会话消息发送
                messageLimitManager.recordMessageSent(conversation);
                
                // 如果是临时会话，显示剩余消息数提示
                const tipMessage = messageLimitManager.getTempChatTip(conversation);
                if (tipMessage) {
                    setTimeout(() => {
                        uni.showToast({
                            title: tipMessage,
                            icon: 'none',
                            duration: 2000
                        });
                    }, 1000);
                }
            },
            // progressCB - 进度回调
            null,
            // successCB - 成功回调
            null,
            // failCB - 失败回调
            (error) => {
                console.error('礼物消息发送失败:', error);
                uni.showToast({
                    title: '礼物发送失败',
                    icon: 'none'
                });
            }
        );
    },
    quoteMessage(message) {
        conversationState.quotedMessage = message;
    },

    getConversationInfo(conversation) {
        let info = wfc.getConversationInfo(conversation);
        return this._patchConversationInfo(info, false);
    },

    // updateMessageReadStatus(state, {messageId, readCount}) {
    //     wfc.updateMessageStatus(messageId, MessageStatus.Readed);
    //     let msg = state.currentConversationMessageList.find(m => m.messageId === messageId);
    //     if (msg) {
    //         msg.readCount = readCount;
    //     }
    // },

    /**
     * 获取会话消息
     * @param {Conversation} conversation 会话
     * @param {number} fromIndex 其实消息的 messageId
     * @param {boolean} before 获取其实消息之前，还是之后的消息
     * @param {string} withUser 过滤该用户发送或接收的消息
     * @param {function (Message[]) } callback 消息列表会回调
     */
    getMessages(conversation, fromIndex = 0, before = true, withUser = '', callback) {
        wfc.getMessagesV2(conversation, fromIndex, before, 20, withUser, msgs => {
            console.log('新加获取会话消息::::::::::::::: success', msgs)
            msgs = msgs.map(m => this._patchMessage(m, 0));
            callback && callback(msgs);
        }, err => {
            console.error('getMessageV2 error', err)
            callback && callback([]);
        });
    },

    getMessageInTypes(conversation, contentTypes, timestamp, before = true, withUser = '', callback) {
        wfc.getMessagesByTimestampV2(conversation, contentTypes, timestamp, before, 20, withUser, msgs => {
            msgs = msgs.map(m => this._patchMessage(m, 0));
            setTimeout(() => callback && callback(msgs), 200)
        }, err => {
            callback && callback([]);
        });
    },

    _loadCurrentConversationMessages() {
        if (!conversationState.currentConversationInfo) {
            return;
        }
        let conversation = conversationState.currentConversationInfo.conversation;
        // console.log('_loadCurrentConversationMessages-------------------------->', conversation)
        setItem("conversation", conversation)
        wfc.getMessagesV2(conversation, 0, true, 20, '', msgs => {
            // console.log('新加_loadCurrentConversationMessages::::::::::::::: success', msgs)
            try {
                // 获取已发送消息列表（现在存储为数组）
                if (msgs && msgs.length > 0 && conversation.type === ConversationType.Group) {
                    let messageSendList = getItem("messageSendList");

                    // 确保messageSendList是Set对象，正确处理数组到Set的转换
                    let messageSendSet;
                    if (messageSendList && Array.isArray(messageSendList)) {
                        // 如果获取到的是数组，转换为Set
                        messageSendSet = new Set(messageSendList);
                        // console.log('从数组转换为Set，包含', messageSendSet.size, '个消息ID:', Array.from(messageSendSet));
                    } else if (messageSendList && messageSendList instanceof Set) {
                        // 如果已经是Set对象，直接使用
                        messageSendSet = messageSendList;
                        // console.log('直接使用Set对象，包含', messageSendSet.size, '个消息ID');
                    } else {
                        // 初始化为空Set
                        messageSendSet = new Set();
                        // console.log('初始化空Set');
                    }

                    // 过滤掉已在发送列表中的消息
                    let filteredMsgs = msgs.filter(msg => {
                        // 构造消息ID，确保格式一致
                        const messageUidKey = `${msg.messageUid.high}-${msg.messageUid.low}`;
                        const shouldFilter = messageSendSet.has(messageUidKey);
                        return !shouldFilter; // 返回不在Set中的消息
                    });

                    console.log(`消息过滤结果: 原始${msgs.length}条，过滤后${filteredMsgs.length}条`);
                    console.log('过滤详情:', {
                        原始消息ID列表: msgs.map(m => `${m.messageUid.high}-${m.messageUid.low}`),
                        已发送列表: Array.from(messageSendSet),
                        过滤后消息ID列表: filteredMsgs.map(m => `${m.messageUid.high}-${m.messageUid.low}`)
                    });
                    // 只对过滤后的消息执行后续操作
                    if (filteredMsgs.length > 0) {
                        // 更新消息状态为已读
                        // filteredMsgs.forEach(msg => {
                        //     console.log('updateMessageStatus::::::::::::::: success', msg);
                        //     // 确保消息有 conversationId 属性
                        //     if (!msg.conversationId && conversation.target) {
                        //         msg.conversationId = conversation.target;
                        //     }
                        //     wfc.updateMessageStatus(msg.messageId, MessageStatus.Readed);
                        // });

                        // 发送已读消息到WebSocket
                        // websocketClient.sendReadMessage({
                        //     conversation: conversation,
                        //     msgs: filteredMsgs
                        // });
                        console.log('已发送已读消息到WebSocket，消息数量:', filteredMsgs.length);
                    } else {
                        console.log('没有需要处理的消息（全部已过滤）');
                    }
                } else if (msgs && msgs.length > 0 && conversation.type === ConversationType.Single) {
                    // 发送已读消息到WebSocket
                    // websocketClient.sendReadMessage({
                    //     conversation: conversation,
                    //     msgs: msgs
                    // });
                    console.log('已发送已读消息到WebSocket，消息数量:', msgs.length);
                }
            } catch (error) {
                console.error('处理消息过滤和已读状态时出错:', error);
                // 出错时仍然保证基本流程继续
            }
            if(msgs && msgs.length > 0){
                conversationState.currentConversationMessageList = msgs;
            }
            // 确保每条消息都被缓存，并建立消息ID与会话ID的映射
            if (msgs && msgs.length > 0) {
                msgs.forEach(msg => {
                    // 确保消息有 conversationId 属性
                    if (!msg.conversationId && conversation.target) {
                        msg.conversationId = conversation.target;
                    }
                    // 缓存消息
                    this._cacheMessage(msg);

                    // 维护消息ID与会话ID的映射关系
                    if (typeof window !== 'undefined' && msg.messageId) {
                        if (!window._messageToConversation) window._messageToConversation = {};
                        window._messageToConversation[msg.messageId] = conversation.target;
                        console.log('[Store] 建立消息ID与会话ID的映射:', msg.messageId, '->', conversation.target);
                    }
                });

                // 确保会话消息缓存存在
                if (typeof window !== 'undefined') {
                    if (!window._conversationMessages) window._conversationMessages = {};
                    window._conversationMessages[conversation.target] = [...msgs];
                    console.log('[Store] 已缓存会话消息:', conversation.target, msgs.length, '条');
                }
            }

            this._patchCurrentConversationMessages();
            if (msgs.length) {
                conversationState.currentConversationOldestMessageId = msgs[0].messageId;
            }
            for (let i = 0; i < msgs.length; i++) {
                if (gt(msgs[i].messageUid, 0)) {
                    conversationState.currentConversationOldestMessageUid = msgs[0].messageUid;
                    break;
                }
            }
            // console.log('_loadCurrentConversationMessages success', conversation, msgs)
        }, err => {
            console.error('_loadCurrentConversationMessages error', err);
        });
    },

    _patchCurrentConversationMessages() {
        let lastTimestamp = 0;
        let msgs = conversationState.currentConversationMessageList;
        msgs.forEach(m => {
            this._patchMessage(m, lastTimestamp);
            lastTimestamp = m.timestamp;
        });
    },

    _onloadConversationMessages(conversation, messages) {
        console.log('-----------------> _onloadConversationMessages', conversation, messages)
        if (!messages || messages.length === 0) {
            return false;
        }
        let loadNewMsg = false;
        let lastTimestamp = 0;
        let newMsgs = [];

        // 预处理所有消息，避免多次重排DOM
        messages.forEach(m => {
            let index = conversationState.currentConversationMessageList.findIndex(cm => cm.messageId === m.messageId)
            if (index === -1) {
                this._patchMessage(m, lastTimestamp);
                lastTimestamp = m.timestamp;
                newMsgs.push(m);
                loadNewMsg = true;
            }
        });

        // 一次性更新消息列表，减少DOM重排
        if (newMsgs.length > 0) {
            conversationState.currentConversationMessageList = newMsgs.concat(conversationState.currentConversationMessageList);
            // 添加标记，指示这是新加载的历史消息，用于滚动定位
            newMsgs.forEach(msg => {
                msg._isNewlyLoaded = true;
            });
        }

        return loadNewMsg;
    },

    loadConversationHistoryMessages(loadedCB, completeCB) {
        if (!conversationState.currentConversationInfo) {
            return;
        }
        let conversation = conversationState.currentConversationInfo.conversation;
        console.log('loadConversationHistoryMessage', conversation, conversationState.currentConversationOldestMessageId,
            stringValue(conversationState.currentConversationOldestMessageUid));
        let loadRemoteHistoryMessageFunc = () => {
            wfc.loadRemoteConversationMessages(conversation, [], conversationState.currentConversationOldestMessageUid,
                20,
                (msgs, hasMore) => {
                    console.log('loadRemoteConversationMessages response', msgs.length);
                    if (msgs.length === 0) {
                        // 拉回来的消息，本地全都有时，会走到这儿
                        if (hasMore) {
                            loadedCB();
                        } else {
                            completeCB();
                        }
                    } else {
                        // 可能拉回来的时候，本地已经切换会话了
                        if (conversation.equal(conversationState.currentConversationInfo.conversation)) {
                            conversationState.currentConversationOldestMessageUid = msgs[0].messageUid;
                            msgs = msgs.filter(m => m.messageId !== 0);
                            this._onloadConversationMessages(conversation, msgs);
                        }
                        this._reloadConversation(conversation);
                        loadedCB();
                    }
                },
                (error) => {
                    completeCB();
                });
        }

        wfc.getMessagesV2(conversation, conversationState.currentConversationOldestMessageId, true, 20, '', lmsgs => {
            if (lmsgs.length > 0) {
                if (!conversation.equal(conversationState.currentConversationInfo.conversation)) {
                    return;
                }
                conversationState.currentConversationOldestMessageId = lmsgs[0].messageId;
                if (gt(lmsgs[0].messageUid, 0)) {
                    conversationState.currentConversationOldestMessageUid = lmsgs[0].messageUid;
                }
                let loadNewMsg = this._onloadConversationMessages(conversation, lmsgs)
                if (!loadNewMsg) {
                    loadRemoteHistoryMessageFunc();
                } else {
                    // loadedCB();
                    setTimeout(() => loadedCB(), 200)
                }
            } else {
                loadRemoteHistoryMessageFunc();
            }
        }, err => {
            completeCB();
        });
    },

    setConversationTop(conversation, top) {
        wfc.setConversationTop(conversation, top,
            () => {
                this._reloadConversation(conversation);
            },
            (err) => {
                console.log('setConversationTop error', err)
            });
    },

    setConversationSilent(conversation, silent) {
        wfc.setConversationSlient(conversation, silent,
            () => {
                this._reloadConversation(conversation);
            },
            (err) => {
                console.log('setConversationSilent error', err)
            });
    },

    markConversationAsUnread(conversation, isUnread) {
        wfc.markConversationAsUnread(conversation, isUnread);
    },

    removeConversation(conversation) {
        wfc.removeConversation(conversation, false);
        if (conversationState.currentConversationInfo && conversationState.currentConversationInfo.conversation.equal(
            conversation)) {
            this.setCurrentConversationInfo(null);
        }
        conversationState.conversationInfoList = conversationState.conversationInfoList.filter(info => !info.conversation
            .equal(conversation));
    },

    // getMessageById(messageId) {
    //     if (!messageId) return null;
    //
    //     try {
    //         console.log('[Store] 尝试根据ID查找消息:', messageId);
    //
    //         // 1. 首先从当前会话中查找
    //         if (conversationState && conversationState.currentConversationInfo &&
    //             conversationState.currentConversationMessageList) {
    //             const foundMsg = conversationState.currentConversationMessageList.find(
    //                 msg => (msg.messageId === messageId || msg.id === messageId)
    //             );
    //
    //             if (foundMsg) {
    //                 console.log('[Store] 在当前会话中找到消息:', messageId);
    //                 return foundMsg;
    //             }
    //         }
    //
    //         // 2. 尝试从全局缓存中查找
    //         if (typeof window !== 'undefined' && window._cachedMessages && window._cachedMessages[messageId]) {
    //             console.log('[Store] 在全局缓存中找到消息:', messageId);
    //             return window._cachedMessages[messageId];
    //         }
    //
    //         // 3. 尝试从消息ID到会话ID的映射中查找
    //         let conversationId = null;
    //         if (typeof window !== 'undefined' && window._messageToConversation) {
    //             conversationId = window._messageToConversation[messageId];
    //         }
    //
    //         // 如果全局映射中没有，尝试从本地存储中获取
    //         if (!conversationId) {
    //             try {
    //                 const idMappingKey = `msg_conv_${messageId}`;
    //                 conversationId = uni.getStorageSync(idMappingKey);
    //             } catch (e) {
    //                 console.error('[Store] 从本地存储中获取消息映射失败:', e);
    //             }
    //         }
    //
    //         // 如果找到了会话ID，尝试从该会话的消息列表中查找
    //         if (conversationId) {
    //             console.log('[Store] 通过映射找到消息所属会话:', conversationId);
    //
    //             // 尝试从会话消息缓存中查找
    //             if (typeof window !== 'undefined' && window._conversationMessages &&
    //                 window._conversationMessages[conversationId]) {
    //                 const msgs = window._conversationMessages[conversationId];
    //                 const foundMsg = msgs.find(
    //                     msg => (msg.messageId === messageId || msg.id === messageId)
    //                 );
    //
    //                 if (foundMsg) {
    //                     console.log('[Store] 在会话缓存中找到消息:', messageId);
    //                     return foundMsg;
    //                 }
    //             }
    //
    //             // 尝试通过 getMessagesByConversationId 方法获取该会话的所有消息
    //             const allConvMsgs = this.getMessagesByConversationId(conversationId);
    //             if (allConvMsgs && allConvMsgs.length > 0) {
    //                 const foundMsg = allConvMsgs.find(
    //                     msg => (msg.messageId === messageId || msg.id === messageId)
    //                 );
    //
    //                 if (foundMsg) {
    //                     console.log('[Store] 通过会话ID在全部消息中找到消息:', messageId);
    //                     return foundMsg;
    //                 }
    //             }
    //         }
    //
    //         // 4. 直接从本地存储中查找
    //         try {
    //             const localKey = `msg_${messageId}`;
    //             const localMsg = uni.getStorageSync(localKey);
    //             if (localMsg) {
    //                 console.log('[Store] 在本地存储中找到消息:', messageId);
    //                 return localMsg;
    //             }
    //         } catch (e) {
    //             console.error('[Store] 从本地存储中获取消息失败:', e);
    //         }
    //
    //         console.warn('[Store] 未找到消息:', messageId);
    //         return null;
    //     } catch (error) {
    //         console.error('[Store] 获取消息时出错:', error);
    //         return null;
    //     }
    // },

    getMessageByUid(messageUid) {
        let msg = wfc.getMessageByUid(messageUid);
        if (msg) {
            this._patchMessage(msg, 0);
        }
        return msg;
    },

    checkMomentNotice() {
        // console.log("获取朋友圈更新角标信息")
        // 先获取缓存的通知数据
        const cachedNotices = getItem('setMomentNotices') || []
        if (cachedNotices.length > 0) {
            // 更新角标
            uni.setTabBarBadge({
                index: 3,
                text: '' + cachedNotices.length
            })
        } else {
            // 更新角标
            uni.removeTabBarBadge({
                index: 3
            })
        }
        appServerApi.getMomentNotice().then(result => {
            if (result && result.data && Array.isArray(result.data) && result.data.length > 0) {
                // 合并新旧数据，去重
                const newNotices = result.data
                const mergedNotices = this.mergeNotices(cachedNotices, newNotices)

                // 将合并后的数据存储到 store 中
                setItem('setMomentNotices', mergedNotices)

                // 更新角标
                uni.setTabBarBadge({
                    index: 3,
                    text: '' + mergedNotices.length
                })

            }
        }).catch(error => {
            console.error('获取动态提醒失败:', error)
        })
    },

    _patchMessage(m, lastTimestamp = 0, userInfoMap) {
        // TODO
        // _from
        // _showTime
        if (m.conversation.type === ConversationType.Single) {
            m._from = userInfoMap ? userInfoMap.get(m.from) : wfc.getUserInfo(m.from, false, '');
        }
        if (!m._from) {
            m._from = wfc.getUserInfo(m.from, false, m.conversation.type === ConversationType.Group ? m.conversation
                .target : '');
        }
        if (m.conversation.type === ConversationType.Group) {
            m._from._displayName = wfc.getGroupMemberDisplayNameEx(m._from);
        } else {
            m._from._displayName = wfc.getUserDisplayNameEx(m._from);
        }
        if (numberValue(lastTimestamp) > 0 && numberValue(m.timestamp) - numberValue(lastTimestamp) > 5 * 60 * 1000) {
            m._showTime = true;
        }
        m._timeStr = helper.timeFormat(m.timestamp)

        if (m.messageContent instanceof CompositeMessageContent) {
            this._patchCompositeMessageContent(m.messageContent);
        }

        // TODO 如果Im server支持备选网络，需要根据当前的网络情况，判断当前是处于主网络，还是备选网络，并动态修改媒体类消息的remotePath，不然可能会出现不能正常加载的情况
        // 如何判断是主网络，还是备选网络，这儿提供一种思路：分别通过主网络和备选网络测试访问im server的/api/version接口
        // 判断是主网络，还是备选网络，一般启动的时候，检测到网络网络变化的时候，在判断一次。
        // if(m.messageContent instanceof MediaMessageContent){
        // TODO 动态修改remotePath
        // }

        return m;
    },

    _patchCompositeMessageContent(compositeMessageContent) {
        let messages = compositeMessageContent.messages;
        messages.forEach(m => {
            this._patchMessage(m, 0)
        })
    },

    _patchConversationInfo(info, patchLastMessage = true, userInfoMap, groupInfoMap) {
        if (info.conversation.type === ConversationType.Single) {
            info.conversation._target = userInfoMap ? userInfoMap.get(info.conversation.target) : wfc.getUserInfo(info
                .conversation.target, false);
            if (info.conversation._target) {
                info.conversation._target._displayName = wfc.getUserDisplayNameEx(info.conversation._target);
            }
        } else if (info.conversation.type === ConversationType.Group) {
            info.conversation._target = groupInfoMap ? groupInfoMap.get(info.conversation.target) : wfc.getGroupInfo(info
                .conversation.target, false);
            if (info.conversation._target) {
                info.conversation._target._isFav = wfc.isFavGroup(info.conversation.target);
                info.conversation._target._displayName = info.conversation._target.remark ? info.conversation._target.remark :
                    info.conversation._target.name;
            }
        } else if (info.conversation.type === ConversationType.Channel) {
            info.conversation._target = wfc.getChannelInfo(info.conversation.target, false);
            info.conversation._target._displayName = info.conversation._target.name;

        } else if (info.conversation.type === ConversationType.ChatRoom) {
            wfc.getChatroomInfo(info.conversation.target, 0, (chatRoomInfo) => {
                info.conversation._target = chatRoomInfo;
            }, err => {
                console.log('get chatRoomInfo error', err);
                info.conversation._target = {};
            });
        }
        if (gt(info.timestamp, 0)) {
            info._timeStr = helper.dateFormat(info.timestamp);
        } else {
            info._timeStr = '';
        }

        // 显示的时候，再 patch
        if (info.lastMessage && info.lastMessage.conversation !== undefined && patchLastMessage) {
            //this._patchMessage(info.lastMessage, 0, userInfoMap)
            if (!info.lastMessage._from) {
                info.lastMessage._from = undefined;
            }
        }

        if (info.unreadCount) {
            info._unread = info.unreadCount.unread + info.unreadCount.unreadMention + info.unreadCount.unreadMentionAll;
        }
        // if (info.conversation.equal(avenginekitproxy.conversation)) {
        //     info._isVoipOngoing = true;
        // } else {
        //     info._isVoipOngoing = false;
        // }

        return info;
    },

    addDownloadingMessage(messageId) {
        conversationState.downloadingMessages.push({
            messageId: messageId,
            progress: 0,
            total: Number.MAX_SAFE_INTEGER,
        });
        console.log('add downloading')
    },

    isDownloadingMessage(messageId) {
        return conversationState.downloadingMessages.findIndex(dm => dm.messageId === messageId) >= 0;
    },

    isSendingMessage(messageId) {
        return conversationState.sendingMessages.has(messageId);
    },
    getDownloadingMessageStatus(messageId) {
        return conversationState.downloadingMessages.find(dm => dm.messageId === messageId);
    },

    getSendingStatus(messageId) {
        return conversationState.sendingMessages.find(e => e.messageId === messageId);
    },

    addFloatingConversation(conversation) {
        conversationState.floatingConversations.push(conversation);
    },

    removeFloatingConversation(conversation) {
        conversationState.floatingConversations = conversationState.floatingConversations.filter(c => !c.equal(
            conversation))
    },

    isConversationInCurrentWindow(conversation) {
        if (miscState.isMainWindow) {
            let index = conversationState.floatingConversations.findIndex(fc => fc.equal(conversation));
            return index === -1;
        } else {
            return conversationState.currentConversationInfo && conversationState.currentConversationInfo.conversation
                .equal(conversation);
        }
    },

    // contact actions

    _loadSelfUserInfo() {
        contactState.selfUserInfo = wfc.getUserInfo(wfc.getUserId(), false);
    },

    _loadFriendList() {
        let friends = wfc.getMyFriendList(false);
        let fileHelperIndex = friends.indexOf(Config.FILE_HELPER_ID);
        if (fileHelperIndex < 0) {
            friends.push(Config.FILE_HELPER_ID);
        }
        if (friends && friends.length > 0) {
            let friendList = wfc.getUserInfos(friends, '');
            contactState.friendList = this._patchAndSortUserInfos(friendList, '');
        }
    },

    getUserOnlineState(userId) {
        let userOnlineState = miscState.userOnlineStateMap.get(userId);
        if (userOnlineState) {
            return userOnlineState.desc();
        }
        return '';
    },

    _patchCurrentConversationOnlineStatus() {
        let convInfo = conversationState.currentConversationInfo;
        if (convInfo && convInfo.conversation.type === ConversationType.Single) {
            conversationState.currentConversationInfo.conversation._targetOnlineStateDesc = this.getUserOnlineState(convInfo
                .conversation.target);
        } else {
            //TODO
        }
    },
    _loadFriendRequest() {
        let requests = wfc.getIncommingFriendRequest()

        requests.sort((a, b) => numberValue(b.timestamp) - numberValue(a.timestamp))
        requests = requests.length >= 20 ? requests.slice(0, 20) : requests;
        let uids = [];
        requests.forEach(fr => {
            uids.push(fr.target);
        });
        let userInfos = wfc.getUserInfos(uids, '')
        requests.forEach(fr => {
            let userInfo = userInfos.find((u => u.uid === fr.target));
            fr._target = userInfo;
        });

        contactState.friendRequestList = requests;
        contactState.unreadFriendRequestCount = wfc.getUnreadFriendRequestCount();
    },

    _patchAndSortUserInfos(userInfos, groupId = '', compareFn) {
        userInfos = userInfos.map(u => {
            if (groupId) {
                u._displayName = wfc.getGroupMemberDisplayNameEx(u);
            } else {
                u._displayName = wfc.getUserDisplayNameEx(u);
            }
            u._pinyin = pinyin(u._displayName, {
                style: 0
            }).join('').trim().toLowerCase();
            let firstLetter = u._pinyin[0];
            if (firstLetter >= 'a' && firstLetter <= 'z') {
                u.__sortPinyin = 'a' + u._pinyin;
            } else {
                u.__sortPinyin = 'z' + u._pinyin;
            }
            u._firstLetters = pinyin(u._displayName, {
                style: 4
            }).join('').trim().toLowerCase();
            return u;
        });
        if (compareFn) {
            userInfos = userInfos.sort(compareFn);
        } else {
            userInfos = userInfos.sort((a, b) => a.__sortPinyin.localeCompare(b.__sortPinyin));
        }

        userInfos.forEach(u => {
            let uFirstLetter = u.__sortPinyin[1];
            if (uFirstLetter >= 'a' && uFirstLetter <= 'z') {
                u._category = uFirstLetter;
            } else {
                u._category = '#';
            }
            u._userOnlineStatusDesc = this.getUserOnlineState(u.uid);
        });
        return userInfos;
    },

    _loadFavGroupList() {
        contactState.favGroupList = wfc.getFavGroupList();
    },

    _loadChannelList() {
        let channelIds = wfc.getListenedChannels();
        if (channelIds) {
            contactState.channelList = channelIds.map(channelId => wfc.getChannelInfo(channelId, false));
            contactState.channelList = contactState.channelList.filter(ch => {
                return !(ch instanceof NullChannelInfo)
            });
        }
    },
    _loadFavContactList() {
        let favUserIds = wfc.getFavUsers();
        if (favUserIds.length > 0) {
            contactState.favContactList = this.getUserInfos(favUserIds, '')
            contactState.favContactList.forEach(u => {
                u._category = '☆ 星标朋友';
            })
        } else {
            contactState.favContactList = [];
        }
    },

    reloadFavGroupList() {
        this._loadFavGroupList();
    },

    setCurrentFriendRequest(friendRequest) {
        contactState.currentFriendRequest = friendRequest;
        contactState.currentFriend = null;
        contactState.currentOrganization = null;
        contactState.currentGroup = null;
    },

    setCurrentFriend(friend) {
        contactState.currentFriendRequest = null;
        contactState.currentFriend = friend;
        contactState.currentOrganization = null;
        contactState.currentGroup = null;
    },

    setCurrentGroup(group) {
        contactState.currentFriendRequest = null;
        contactState.currentFriend = null;
        contactState.currentOrganization = null;
        contactState.currentGroup = group;
    },


    setCurrentOrganization(organization) {
        contactState.currentFriendRequest = null;
        contactState.currentFriend = null;
        contactState.currentGroup = null;
        // contactState.currentChannel = null;
        contactState.currentOrganization = organization;
    },

    toggleGroupList() {
        contactState.expandGroup = !contactState.expandGroup;
    },

    toggleFriendRequestList() {
        contactState.expandFriendRequestList = !contactState.expandFriendRequestList;
    },

    toggleFriendList() {
        contactState.expandFriendList = !contactState.expandFriendList;
    },

    setSearchQuery(query, options) {
        searchState.query = query;
        if (query) {
            console.log('search', query, options)
            if (options.contact) {
                searchState.contactSearchResult = this.filterContact(query);
            }
            if (options.group) {
                searchState.groupSearchResult = this.filterGroupConversation(query);
            }
            if (options.conversation) {
                searchState.conversationSearchResult = this.searchConversation(query);
            }
            // searchState.messageSearchResult = this.searchMessage(query);
            // 默认不搜索新用户
            if (options.user) {
                this.searchUser(query);
            }

        } else {
            searchState.contactSearchResult = [];
            searchState.conversationSearchResult = [];
            searchState.groupSearchResult = [];
            searchState.messageSearchResult = [];
            searchState.userSearchResult = [];
        }
    },

    searchUser(query) {
        console.log('search user', query)
        wfc.searchUser(query, SearchType.General, 0, ((keyword, userInfos) => {
            console.log('search user result', query, userInfos)
            if (searchState.query === keyword) {
                searchState.userSearchResult = userInfos.filter(u => !wfc.isMyFriend(u.uid));
            }
        }), (err) => {
            console.log('search user error', query, err)
            if (searchState.query === query) {
                searchState.userSearchResult = [];
            }
        });
    },

    // TODO 到底是什么匹配了
    filterContact(query) {
        let result = contactState.friendList.filter(u => {
            return u._displayName.indexOf(query) > -1 || u._firstLetters.indexOf(query.toLowerCase()) > -1 || u._pinyin
                .indexOf(query.toLowerCase()) > -1
        });

        console.log('friend searchResult', result)
        return result;
    },

    searchFiles(keyword, beforeMessageUid, successCB, failCB) {
        if (!keyword) {
            return;
        }
        wfc.searchFiles(keyword, null, '', beforeMessageUid, 0, 20,
            (files) => {
                this._patchFileRecords(files);
                successCB && successCB(files);
            },
            (errorCode) => {
                console.log('search file error', errorCode);
                failCB && failCB(errorCode);
            })
    },

    filterUsers(users, filter) {
        if (!users || !filter || !filter.trim()) {
            return users;
        }
        let queryPinyin = pinyin(filter, {
            style: 0
        }).join('').trim().toLowerCase();
        let result = users.filter(u => {
            return u._displayName.indexOf(filter) > -1 || u._displayName.indexOf(queryPinyin) > -1 ||
                u._pinyin.indexOf(filter) > -1 || u._pinyin.indexOf(queryPinyin) > -1 ||
                u._firstLetters.indexOf(filter) > -1 || u._firstLetters.indexOf(queryPinyin) > -1
        });
        return result;
    },

    // TODO 匹配类型，是群名称匹配上了，还是群成员的名称匹配上了？
    // 目前只搜索群名称
    filterFavGroup(query) {
        console.log('to search group', contactState.favGroupList)
        let queryPinyin = pinyin(query, {
            style: 0
        }).join('').trim().toLowerCase();
        let result = contactState.favGroupList.filter(g => {
            let groupNamePinyin = pinyin(g.name, {
                style: 0
            }).join('').trim().toLowerCase();
            return g.name.indexOf(query) > -1 || g.name.indexOf(queryPinyin) > -1 ||
                groupNamePinyin.indexOf(query) > -1 || groupNamePinyin.indexOf(queryPinyin) > -1
        });

        console.log('group searchResult', result)
        return result;
    },

    // TODO
    filterConversation(query) {
        return conversationState.conversationInfoList.filter(info => {
            let displayNamePinyin = pinyin(info.conversation._target._displayName, {
                style: 0
            }).join('').trim().toLowerCase();
            let firstLetters = pinyin(info.conversation._target._displayName, {
                style: 4
            }).join('').trim().toLowerCase();
            return info.conversation._target._displayName.indexOf(query) > -1 || displayNamePinyin.indexOf(query
                .toLowerCase()) > -1 || firstLetters.indexOf(query) > -1
        })
    },

    filterGroupConversation(query) {
        // query = query.toLowerCase();
        // let groups = conversationState.conversationInfoList.filter(info => info.conversation.type === ConversationType.Group).map(info => info.conversation._target);
        // return groups.filter(groupInfo => {
        //     let namePinyin = pinyin(groupInfo.name, {style: 0}).join('').trim().toLowerCase();
        //     let firstLetters = pinyin(groupInfo.name, {style: 4}).join('').trim().toLowerCase();
        //     return groupInfo.name.indexOf(query) > -1 || namePinyin.indexOf(query) > -1 || firstLetters.indexOf(query) > -1
        // })
        let gsr = wfc.searchGroups(query)
        return gsr.map(r => r.groupInfo);
    },

    searchMessage(conversation, query) {
        let msgs = wfc.searchMessage(conversation, query)
        msgs = msgs.reverse();
        return msgs.map(m => this._patchMessage(m, 0));
    },

    searchMessageInTypes(conversation, contentTypes, query, offset) {
        let msgs = wfc.searchMessageByTypes(conversation, query, contentTypes, true, 20, offset)
        return msgs.map(m => this._patchMessage(m, 0));
    },

    searchConversation(query, types = [0, 1, 2], lines = [0, 1, 2]) {
        let results = wfc.searchConversation(query, types, lines);
        return results.map(r => {
            let info = wfc.getConversationInfo(r.conversation);
            r._conversationInfo = this._patchConversationInfo(info, false);
            return r;
        })
    },

    // pick actions
    pickOrUnpickUser(user) {
        let index = pickState.users.findIndex(u => u.uid === user.uid);
        if (index >= 0) {
            pickState.users = pickState.users.filter(u => user.uid !== u.uid)
        } else {
            pickState.users.push(user);
        }
    },

    isUserPicked(user) {
        let index = pickState.users.findIndex(u => u.uid === user.uid);
        return index >= 0;
    },

    // pick actions
    pickOrUnpickOrganization(org) {
        let index = pickState.organizations.findIndex(o => o.id === org.id);
        if (index >= 0) {
            pickState.organizations = pickState.organizations.filter(o => o.id !== org.id)
        } else {
            pickState.organizations.push(org);
        }
    },

    isOrganizationPicked(org) {
        let index = pickState.organizations.findIndex(o => o.id === org.id);
        return index >= 0;
    },

    pickOrUnpickConversation(conversation) {
        let index = pickState.conversations.findIndex(c => (conversation.target === c.target && conversation.line === c
            .line && conversation.type === c.type))
        if (index >= 0) {
            pickState.conversations = pickState.conversations.filter(c => !(conversation.target === c.target && conversation
                .line === c.line && conversation.type === c.type))
        } else {
            pickState.conversations.push(conversation);
        }
    },

    // misc actions
    createConversation(users, successCB, failCB) {
        if (users.length === 1) {
            let conversation = new Conversation(ConversationType.Single, users[0].uid, 0);
            this.setCurrentConversation(conversation);
            successCB && successCB(conversation);
            return;
        }

        let groupName = contactState.selfUserInfo.displayName;
        let groupMemberIds = [];
        let groupMemberPortraits = [contactState.selfUserInfo.portrait];
        for (let i = 0; i < users.length; i++) {
            groupMemberIds.push(users[i].uid)
            if (i <= 3) {
                groupName += '、' + users[i].displayName;
            }
            if (i < 8) {
                groupMemberPortraits.push(users[i].portrait)
            }
        }
        groupName = groupName.substr(0, groupName.length - 1);

        wfc.createGroup(null, GroupType.Restricted, groupName, null, null, groupMemberIds, null, [0], null,
            (groupId) => {
                let conversation = new Conversation(ConversationType.Group, groupId, 0)
                this.setCurrentConversation(conversation);
                console.log('创建群', contactState)
                // console.log('创建群', groupId, groupName, conversation, groupMemberIds)
                // console.log('---===---', contactState)
                // 同步建群信息
                appServerApi.createGroup({
                    // ...gInfo,
                    creator: contactState.selfUserInfo.uid,
                    gid: groupId,
                    name: groupName,
                })
                successCB && successCB(conversation);
            }, (error) => {
                console.log('create group error', error)
                failCB && failCB(error);
            });
    },

    _loadUserLocalSettings() {
        let userId = wfc.getUserId();
        // 默认允许通知
        let setting = getItem(userId + '-' + 'notification');
        miscState.enableNotification = setting === null || setting === '' || setting === '1'
        setting = getItem(userId + '-' + 'notificationDetail');
        miscState.enableNotificationMessageDetail = setting === null || setting === '1'
    },

    setEnableNotification(enable) {
        miscState.enableNotification = enable;
        setItem(contactState.selfUserInfo.uid + '-' + 'notification', enable ? '1' : '0')
    },

    setEnableNotificationDetail(enable) {
        miscState.enableNotificationMessageDetail = enable;
        setItem(contactState.selfUserInfo.uid + '-' + 'notificationDetail', enable ? '1' : '0')
    },

    // clone一下，别影响到好友列表
    getUserInfos(userIds, groupId) {
        let userInfos = wfc.getUserInfos(userIds, groupId);
        let userInfosCloneCopy = userInfos.map(u => Object.assign({}, u));
        return this._patchAndSortUserInfos(userInfosCloneCopy, groupId);
    },

    // clone一下，别影响到好友列表
    getGroupMemberUserInfos(groupId, includeSelf = true, sortByPinyin = false, fresh = false) {

        let memberIds = wfc.getGroupMemberIds(groupId, fresh);
        let userInfos = wfc.getUserInfos(memberIds, groupId);
        if (!includeSelf) {
            userInfos = userInfos.filter(u => u.uid !== wfc.getUserId())
        }
        let userInfosCloneCopy = userInfos.map(u => Object.assign({}, u));
        if (sortByPinyin) {
            return this._patchAndSortUserInfos(userInfosCloneCopy, groupId);
        } else {
            let compareFn = (u1, u2) => {
                let index1 = memberIds.findIndex(id => id === u1.uid)
                let index2 = memberIds.findIndex(id => id === u2.uid)
                return index1 - index2;
            }
            return this._patchAndSortUserInfos(userInfosCloneCopy, groupId, compareFn);
        }
    },

    // clone一下，别影响到好友列表
    getConversationMemberUsrInfos(conversation, fresh = false) {
        let userInfos = [];
        if (conversation.type === 0) {
            if (conversation.target !== contactState.selfUserInfo.uid) {
                userInfos.push(wfc.getUserInfo(wfc.getUserId(), false));
            }
            userInfos.push(wfc.getUserInfo(conversation.target, false));
            let userInfosCloneCopy = userInfos.map(u => Object.assign({}, u));
            userInfos = this._patchAndSortUserInfos(userInfosCloneCopy, '');
        } else if (conversation.type === 1) {
            userInfos = this.getGroupMemberUserInfos(conversation.target, true, false, fresh);
        }
        return userInfos;
    },

    //前端拼接身份
    async getGroupMembersWithIdentity(groupId, includeSelf = true, sortByPinyin = false) {
        let members = this.getGroupMemberUserInfos(groupId, includeSelf, sortByPinyin)
        let identities = await GroupIdentityCache.getGroupIdentities(groupId)
        let owner = wfc.getGroupMembersByType(groupId, 2);
        let admins = wfc.getGroupMembersByType(groupId, 1);
        console.log('前端拼接身份', identities, owner, admins)
        let identityMembers = members.map(person => {
            let identityName = '成员'
            if (admins.find(a => a.memberId === person.uid)) {
                identityName = '管理员'
                person.type = 1
                person._category = '群主、管理员'
            } else if (owner.find(o => o.memberId === person.uid)) {
                identityName = '群主'
                person.type = 2
                person._category = '群主、管理员'
            } else {
                person.type = 0
                // 使用全局缓存获取身份信息
                const identity = identities[person.uid];
                return {
                    ...person,
                    identityName: identity || identityName,
                    isMyFriend: wfc.isMyFriend(person.uid)
                };
            }
            return {...person, identityName: identityName, isMyFriend: wfc.isMyFriend(person.uid)};
        });

        return identityMembers.filter(m => m.type === 2).concat(identityMembers.filter(m => m.type === 1)).concat(identityMembers.filter(m => m.type !== 1 && m.type !== 2))
    },
    getMyFileRecords(beforeUid, count, successCB, failCB) {
        if (!successCB) {
            return;
        }
        wfc.getMyFileRecords(beforeUid, 0, count, fileRecords => {
            this._patchFileRecords(fileRecords)
            successCB(fileRecords);
        }, failCB)
    },

    getConversationFileRecords(conversation, fromUser, beforeMessageUid, count, successCB, failCB) {
        wfc.getConversationFileRecords(conversation, fromUser, beforeMessageUid, 0, count, fileRecords => {
            this._patchFileRecords(fileRecords)
            successCB(fileRecords);
        }, failCB);
    },

    deleteFriend(target) {
        // 阻止删除文件传输助手
        if (target === Config.FILE_HELPER_ID) {
            console.log('不能删除文件传输助手');
            uni.showToast({
                title: '不能删除文件传输助手',
                icon: 'none'
            });
            return;
        }
        wfc.deleteFriend(target, () => {
            let conv = new Conversation(ConversationType.Single, target, 0);
            wfc.removeConversation(conv, true);
            conversationState.conversationInfoList = conversationState.conversationInfoList.filter(info => !info
                .conversation.equal(conv))
        }, (err) => {
            console.log('deleteFriend error', err);
        });
    },

    _patchFileRecords(fileRecords) {
        fileRecords.forEach(fileRecord => {
            let groupId = fileRecord.conversation.type === 1 ? fileRecord.conversation.target : '';
            if (groupId) {
                fileRecord._userDisplayName = wfc.getGroupMemberDisplayName(groupId, fileRecord.userId);
            } else {
                fileRecord._userDisplayName = wfc.getUserDisplayName(fileRecord.userId);
            }
            let conversationInfo = wfc.getConversationInfo(fileRecord.conversation);
            this._patchConversationInfo(conversationInfo, false);

            if (fileRecord.conversation.type === 0) {
                fileRecord._conversationDisplayName = '与' + conversationInfo.conversation._target._displayName + '的聊天';
            } else {
                fileRecord._conversationDisplayName = conversationInfo.conversation._target._displayName;
            }
            if (fileRecord.name.indexOf(FileMessageContent.FILE_NAME_PREFIX) === 0) {
                fileRecord.name = fileRecord.name.substring(fileRecord.name.indexOf(FileMessageContent.FILE_NAME_PREFIX) +
                    FileMessageContent.FILE_NAME_PREFIX.length);
            }
            fileRecord._timeStr = helper.dateFormat(fileRecord.timestamp);
            fileRecord._sizeStr = helper.humanSize(fileRecord.size)
            fileRecord._fileIconName = helper.getFiletypeIcon(fileRecord.name.substring(fileRecord.name.lastIndexOf(
                '.')))
        });
    },

    clearConversationUnreadStatus(conversation) {
        let info = wfc.getConversationInfo(conversation);
        if (info && (info.unreadCount.unread + info.unreadCount.unreadMention + info.unreadCount.unreadMentionAll) > 0) {
            wfc.clearConversationUnreadStatus(conversation);

        }
    },
    clearAllUnreadStatus() {
        wfc.clearAllUnreadStatus();
        conversationState.conversationInfoList.forEach(info => {
            info.unreadCount = new UnreadCount();
        });

    },
    //清空聊天记录
    clearMessages(conversation) {
        wfc.clearMessages(conversation);
    },
    notify(msg) {
        let content = msg.messageContent;
        let tip
        //Todo
        if (msg.direction === 0 /* && !(type===0 && target===file_transfer_id)*/) {
            return;
        }
        if (MessageConfig.getMessageContentPersitFlag(content.type) === PersistFlag.Persist_And_Count) {
            if (msg.status !== MessageStatus.AllMentioned && msg.status !== MessageStatus.Mentioned) {
                let silent = false;
                for (const info of conversationState.conversationInfoList) {
                    if (info.conversation.equal(msg.conversation)) {
                        silent = info.isSilent;
                        break;
                    }
                }
                if (silent) {
                    return;
                }
                tip = "新消息来了";
            } else {
                tip = "有人@你";
            }

            wfc.notify(tip, miscState.enableNotificationMessageDetail ? content.digest(msg) : '')
        }
    },

    _filterForwardMessageContent(message) {
        let content = message.messageContent
        if (content instanceof CallStartMessageContent) {
            content = new TextMessageContent(content.digest(message))
        } else if (content instanceof SoundMessageContent) {
            content = new TextMessageContent(content.digest(message) + ' ' + content.duration + "''");
        }
        return content
    },
    shareContent(data) {
        //'weixin', 'qq', 'qzone', 'weibo', 'email', 'sms'
        // 分享微信
        uni.share({
            provider: data.provider, // 分享服务提供商，这里以微信为例
            type: data.type, // 分享内容的类型，0表示网页，1表示图片，2表示视频，3表示音乐，4表示文章
            title: data.title, // 分享的标题
            summary: data.summary, // 分享的摘要
            // 使用动态的话题分享链接
            href: `https://web.ykjrhl.com/share/topic.html?topicId=${data.topicId}`, // 分享的链接
            imageUrl: data.imageUrl, // 图片的URL，当type为1时需要
            videoUrl: '', // 视频的URL，当type为2时需要
            musicUrl: '', // 音乐的URL，当type为3时需要
            scene: data.scene,
            success: (res) => {
                // 分享成功的回调
                console.log('Share success:', res);
            },
            fail: (err) => {
                // 分享失败的回调
                console.error('Share fail:', err);
            },
            complete: () => {
                // 分享结束的回调（成功或失败都会执行）
                console.log('Share complete');
            }
        });
    },

    shareCard(data) {
        //'weixin', 'qq', 'qzone', 'weibo', 'email', 'sms'
        // 分享微信
        uni.share({
            provider: data.provider, // 分享服务提供商，这里以微信为例
            type: data.type, // 分享内容的类型，0表示网页，1表示图片，2表示视频，3表示音乐，4表示文章
            title: data.title, // 分享的标题
            summary: data.summary, // 分享的摘要
            // 使用动态的话题分享链接
            href: `https://web.ykjrhl.com/share/card.html?cardId=${data.cardId}`, // 分享的链接
            imageUrl: data.imageUrl, // 图片的URL，当type为1时需要
            videoUrl: '', // 视频的URL，当type为2时需要
            musicUrl: '', // 音乐的URL，当type为3时需要
            scene: data.scene,
            success: (res) => {
                // 分享成功的回调
                console.log('Share success:', res);
            },
            fail: (err) => {
                // 分享失败的回调
                console.error('Share fail:', err);
            },
            complete: () => {
                // 分享结束的回调（成功或失败都会执行）
                console.log('Share complete');
            }
        });
    },
    // 发送用户名片消息
    sendCardMessage(conversation, targetUserId) {
        let targetUser = wfc.getUserInfo(targetUserId);
        let cardContent = new CardMessageContent(
            0,  // cardType: 0 表示用户名片
            targetUserId,  // target
            targetUser.displayName, // 目标用户的显示名称
            targetUser.portrait,    // 目标用户的头像
            wfc.getUserId()  // 当前发送者的 ID
        );
        wfc.sendConversationMessage(conversation, cardContent);
    },

    // 发送群名片消息
    sendGroupCardMessage(conversation, targetGroupId) {
        let cardContent = new CardMessageContent({
            type: 1,
            target: targetGroupId
        });
        wfc.sendConversationMessage(conversation, cardContent);
    },

    // 发送吧名片消息
    sendBarCardMessage(conversation, barId, barName, barPortrait) {
        let cardContent = new CardMessageContent(2, barId, barName, barPortrait, wfc.getUserId());
        cardContent.type = MessageContentType.UserCard;
        
        // 先发送文字消息
        const text = `🎉 邀请你加入"${barName}"贴吧！

💬 一起来参与讨论，分享有趣的内容吧～

📝 贴吧ID: ${barId}

👆 点击下方链接直接进入贴吧：`;

        let textContent = new TextMessageContent(text);
        wfc.sendConversationMessage(conversation, textContent);
        
        // 再发送吧名片
        wfc.sendConversationMessage(conversation, cardContent);
    },

    // 选择并发送定位消息
    async sendLocationMessage(conversation) {
        try {
            uni.chooseLocation({
                success: (location) => {
                    let locationContent = new LocationMessageContent(
                        location.address,
                        location.latitude.toFixed(6),
                        location.longitude.toFixed(6),
                        ''
                    );
                    // 发送消息
                    wfc.sendConversationMessage(conversation, locationContent);
                },
                fail: (err) => {
                    console.error('选择位置失败:', err)
                }
            })


        } catch (error) {
            console.error('发送定位消息失败:', error);
            // 处理错误
        }
    },

    // 获取定位信息的私有方法
    _getLocation() {
        return new Promise((resolve, reject) => {
            uni.getLocation({
                type: 'gcj02', // 使用国测局坐标系
                success: async (res) => {
                    try {
                        console.log('获取定位成功res', res)

                        resolve({
                            latitude: res.latitude,
                            longitude: res.longitude,
                            address: res.address
                        });
                    } catch (error) {
                        console.error('获取定位失败:', error);
                        reject(error);
                    }
                },
                fail: (error) => {
                    console.error('获取定位失败:', error);
                    reject(error);
                }
            });
        });
    },

    async syncMessageReadStatus({commit}, messageId) {
        try {
            const result = await appServerApi.getReadUsers(messageId);
            commit('updateMessageReadStatus', {
                messageId,
                readCount: result.length
            });
            return result;
        } catch (error) {
            console.error('同步消息已读状态失败:', error);
            throw error;
        }
    },

    // 新增 actions
    actions: {
        // 初始化网络监听
        initNetworkListener({commit}) {
            const handleNetworkChange = (status) => {
                commit('UPDATE_NETWORK_STATUS', status.isConnected);

                // 网络恢复时的操作
                if (status.isConnected) {
                    console.log('网络恢复，执行重连等操作');
                    // 示例：尝试重新连接WebSocket
                    // if (websocketClient && websocketClient.isClosed()) {
                    //     websocketClient.reconnect();
                    // }
                    // 示例：同步未发送的消息
                    store.dispatch('retryPendingMessages');
                } else {
                    console.warn('网络断开，暂停实时操作');
                    // 示例：停止消息发送
                    // websocketClient?.close();
                }
            };

            // 多端兼容方案
            if (typeof uni !== 'undefined') {
                // UniApp 环境
                uni.onNetworkStatusChange(handleNetworkChange);
                // 初始获取状态
                uni.getNetworkType({
                    success: (res) => {
                        commit('UPDATE_NETWORK_STATUS', res.networkType !== 'none');
                    }
                });
            } else if (typeof window !== 'undefined') {
                // H5 环境
                const updateStatus = () => {
                    commit('UPDATE_NETWORK_STATUS', navigator.onLine);
                };

                window.addEventListener('online', updateStatus);
                window.addEventListener('offline', updateStatus);
                // 初始化状态
                updateStatus();
            }
        },

        // 示例：重试未发送消息
        retryPendingMessages() {
            const pendingMessages = store.state.conversation.sendingMessages;
            pendingMessages.forEach(msg => {
                wfc.sendMessage(msg);
            });
        },
    },

    mutations: {
        UPDATE_NETWORK_STATUS(state, status) {
            state.misc.networkStatus = status;
            console.log('网络状态变更:', status ? '已连接' : '已断开');
        },
        updateLastReadMessageId(state, messageId) {
            state.lastReadMessageId = messageId;
            console.log('更新最后已读消息ID:', messageId);
        },
        updateMessageReadStatus(state, {messageId, readCount}) {
            console.log('更新消息已读状态:', messageId, readCount);
            state.messageReadStatus.set(messageId, readCount);
        },
        UPDATE_MESSAGE_READ_STATUS(state, {messageId, value, updateTime, serverConfirmed = false}) {
            try {
                console.log('[Store] 更新消息已读状态:', messageId, value, serverConfirmed ? '(服务器确认)' : '(本地标记)');

                if (!messageId) {
                    console.error('[Store] 无法更新消息已读状态: 消息ID为空');
                    return;
                }

                // 获取当前用户ID
                const currentUserId = getItem('userId') || state.userData?.id || '';
                if (!currentUserId) {
                    console.error('[Store] 无法获取当前用户ID，无法更新消息读取状态');
                    return;
                }

                // 更新状态管理中的已读状态
                if (!state.messageReadStatus) {
                    state.messageReadStatus = new Map();
                }
                state.messageReadStatus.set(messageId, value);

                // 如果是当前会话，直接更新消息对象
                if (state.currentConversation && state.currentConversation.messages) {
                    const targetMessage = state.currentConversation.messages.find(
                        msg => msg.messageId === messageId || msg.id === messageId
                    );

                    if (targetMessage) {
                        console.log('[Store] 在当前会话中找到消息,更新已读状态:', messageId);
                        targetMessage.isRead = value > 0;
                        targetMessage.readTime = updateTime || Date.now();

                        // 如果是服务器确认的，设置特殊标记
                        if (serverConfirmed) {
                            targetMessage.serverConfirmedRead = true;
                        }
                    } else {
                        console.log('[Store] 在当前会话中未找到消息:', messageId);
                    }
                } else {
                    console.log('[Store] 当前没有加载会话或消息列表');

                }

                // 存储到本地，确保刷新页面后保持状态
                const readStatusKey = `msg_read_status_${messageId}`;
                uni.setStorageSync(readStatusKey, {
                    isRead: value > 0,
                    readTime: updateTime || Date.now(),
                    serverConfirmed: serverConfirmed
                });

                // 创建/更新全局对象来跟踪已读消息
                if (typeof window !== 'undefined') {
                    if (!window._lastReadMessages) window._lastReadMessages = {};
                    window._lastReadMessages[messageId] = updateTime || Date.now();

                    // 如果是服务器确认的，记录在专门的对象中
                    if (serverConfirmed) {
                        if (!window._serverConfirmedReadMessages) window._serverConfirmedReadMessages = {};
                        window._serverConfirmedReadMessages[messageId] = updateTime || Date.now();
                    }
                }

                console.log('[Store] 消息已读状态更新完成:', messageId, value);
            } catch (error) {
                console.error('[Store] 更新消息已读状态出错:', error);
            }
        },
        UPDATE_BATCH_MESSAGES_READ_STATUS(state, {messageIds, conversationId, timestamp, serverConfirmed = false}) {
            try {
                console.log('[Store] 批量更新消息已读状态:',
                    messageIds ? `${messageIds.length}条消息` : '基于会话和时间戳',
                    serverConfirmed ? '(服务器确认)' : '(本地标记)');

                // 获取当前用户ID
                const currentUserId = getItem('userId') || state.userData?.id || '';
                if (!currentUserId) {
                    console.error('[Store] 无法获取当前用户ID，无法批量更新消息读取状态');
                    return;
                }

                // 要更新的消息ID列表
                let targetMessageIds = messageIds || [];

                // 如果提供了会话ID但没有消息ID列表，则查找该会话中的所有消息
                if (!targetMessageIds.length && conversationId) {
                    // 从不同来源收集消息
                    let allMessages = [];

                    // 1. 检查当前会话
                    if (state.currentConversation &&
                        state.currentConversation.target === conversationId &&
                        state.currentConversation.messages) {
                        allMessages = [...state.currentConversation.messages];
                    }

                    // 2. 尝试从缓存的会话中查找
                    if (state.conversationMessages && state.conversationMessages[conversationId]) {
                        const cachedMessages = state.conversationMessages[conversationId];
                        // 合并消息，避免重复
                        allMessages = [...allMessages, ...cachedMessages].filter((msg, index, self) =>
                            index === self.findIndex(m => (m.messageId || m.id) === (msg.messageId || msg.id))
                        );
                    }

                    // 3. 使用getter方法获取更完整的消息列表
                    if (this.getters && this.getters.getMessagesByConversationId) {
                        const getterMessages = this.getters.getMessagesByConversationId(conversationId);
                        if (getterMessages && getterMessages.length) {
                            // 合并消息，避免重复
                            allMessages = [...allMessages, ...getterMessages].filter((msg, index, self) =>
                                index === self.findIndex(m => (m.messageId || m.id) === (msg.messageId || msg.id))
                            );
                        }
                    }

                    // 筛选出符合条件的消息ID
                    targetMessageIds = allMessages
                        .filter(msg =>
                            // 只处理当前用户发送的消息
                            msg.from === currentUserId &&
                            // 只处理未读消息
                            !msg.isRead &&
                            // 如果提供了时间戳，则只处理早于该时间戳的消息
                            (!timestamp || (msg.timestamp || msg.time || 0) <= timestamp)
                        )
                        .map(msg => msg.messageId || msg.id)
                        .filter(id => id); // 过滤掉空ID

                    console.log(`[Store] 在会话${conversationId}中找到${targetMessageIds.length}条需要更新的消息`);
                }

                // 如果没有需要更新的消息，则直接返回
                if (!targetMessageIds.length) {
                    console.log('[Store] 没有找到需要更新的消息');
                    return;
                }

                // 批量更新消息状态
                const updateTime = timestamp || Date.now();

                // 更新状态管理中的记录
                targetMessageIds.forEach(msgId => {
                    if (!state.messageReadStatus) {
                        state.messageReadStatus = new Map();
                    }
                    state.messageReadStatus.set(msgId, 1);

                    // 存储到本地，确保刷新页面后保持状态
                    const readStatusKey = `msg_read_status_${msgId}`;
                    uni.setStorageSync(readStatusKey, {
                        isRead: true,
                        readTime: updateTime,
                        serverConfirmed: serverConfirmed
                    });

                    // 更新全局对象
                    if (typeof window !== 'undefined') {
                        if (!window._lastReadMessages) window._lastReadMessages = {};
                        window._lastReadMessages[msgId] = updateTime;

                        // 如果是服务器确认的，记录在专门的对象中
                        if (serverConfirmed) {
                            if (!window._serverConfirmedReadMessages) window._serverConfirmedReadMessages = {};
                            window._serverConfirmedReadMessages[msgId] = updateTime;
                        }
                    }
                });

                // 如果是当前会话，直接更新消息对象
                if (state.currentConversation &&
                    state.currentConversation.messages &&
                    (!conversationId || state.currentConversation.target === conversationId)) {

                    targetMessageIds.forEach(msgId => {
                        const targetMessage = state.currentConversation.messages.find(
                            msg => (msg.messageId === msgId || msg.id === msgId)
                        );

                        if (targetMessage) {
                            targetMessage.isRead = true;
                            targetMessage.readTime = updateTime;

                            // 如果是服务器确认的，设置特殊标记
                            if (serverConfirmed) {
                                targetMessage.serverConfirmedRead = true;
                            }
                        }
                    });
                }

                // 如果提供了会话ID，更新该会话的最后读取时间戳
                if (conversationId) {
                    // 更新会话级别的读取时间戳
                    if (typeof window !== 'undefined') {
                        if (!window._userReadTimestamps) window._userReadTimestamps = {};
                        window._userReadTimestamps[conversationId] = updateTime;
                        console.log('[Store] 更新会话已读时间戳:', conversationId, updateTime);
                    }

                    // 尝试更新会话对象中的已读时间戳
                    if (state.conversations) {
                        const targetConversation = state.conversations.find(conv => conv.target === conversationId);
                        if (targetConversation) {
                            targetConversation.lastReadTime = updateTime;
                            console.log('[Store] 更新会话对象中的已读时间戳:', conversationId, updateTime);
                        }
                    }
                }

                console.log('[Store] 批量更新消息已读状态完成:', targetMessageIds.length, '条消息');

                // 触发全局事件，通知其他组件更新
                uni.$emit('batchMessageReadStatusUpdated', {
                    messageIds: targetMessageIds,
                    conversationId,
                    timestamp: updateTime,
                    serverConfirmed
                });

            } catch (error) {
                console.error('[Store] 批量更新消息已读状态出错:', error);
            }
        }
    },

    commit(mutation, payload) {
        if (this.mutations[mutation]) {
            this.mutations[mutation](this.state, payload);
        } else {
            console.warn(`Unknown mutation: ${mutation}`);
        }
    },

    dispatch(action, payload) {
        if (this.actions[action]) {
            return this.actions[action]({
                state: this.state,
                commit: this.commit.bind(this)
            }, payload);
        } else {
            console.warn(`Unknown action: ${action}`);
        }
    },

    // 用于获取指定会话的所有消息
    getMessagesByConversationId(conversationId) {
        if (!conversationId) return [];

        try {
            console.log('[Store] 尝试获取会话的所有消息:', conversationId);

            // 收集所有来源的消息
            let allMessages = [];

            // 1. 从当前会话中获取
            if (conversationState && conversationState.currentConversationInfo &&
                conversationState.currentConversationInfo.conversation &&
                conversationState.currentConversationInfo.conversation.target === conversationId &&
                conversationState.currentConversationMessageList) {

                allMessages = [...conversationState.currentConversationMessageList];
                console.log('[Store] 从当前会话中获取消息:', allMessages.length, '条');
            }

            // 2. 从缓存的会话对象中获取
            if (this.state.conversation && this.state.conversation.currentConversationMessageList &&
                this.state.conversation.currentConversationInfo &&
                this.state.conversation.currentConversationInfo.conversation &&
                this.state.conversation.currentConversationInfo.conversation.target === conversationId) {

                const stateMessages = [...this.state.conversation.currentConversationMessageList];
                console.log('[Store] 从state中获取消息:', stateMessages.length, '条');

                // 合并消息，避免重复
                allMessages = [...allMessages, ...stateMessages].filter((msg, index, self) =>
                    index === self.findIndex(m => (m.messageId || m.id) === (msg.messageId || msg.id))
                );
            }

            // 3. 从缓存的会话集合中获取
            if (this.state.conversationMessages && this.state.conversationMessages[conversationId]) {
                const cachedMessages = this.state.conversationMessages[conversationId];
                console.log('[Store] 从state缓存中获取消息:', cachedMessages.length, '条');

                // 合并消息，避免重复
                allMessages = [...allMessages, ...cachedMessages].filter((msg, index, self) =>
                    index === self.findIndex(m => (m.messageId || m.id) === (msg.messageId || msg.id))
                );
            }

            // 4. 从全局缓存中获取
            if (typeof window !== 'undefined' && window._conversationMessages) {
                const globalCachedMessages = window._conversationMessages[conversationId];
                if (globalCachedMessages && globalCachedMessages.length) {
                    console.log('[Store] 从全局缓存中获取消息:', globalCachedMessages.length, '条');

                    // 合并消息，避免重复
                    allMessages = [...allMessages, ...globalCachedMessages].filter((msg, index, self) =>
                        index === self.findIndex(m => (m.messageId || m.id) === (msg.messageId || msg.id))
                    );
                }
            }

            // 5. 尝试从本地存储中获取映射表，查找消息
            try {
                const mappingKey = `msg_mapping_${conversationId}`;
                const msgIds = uni.getStorageSync(mappingKey);
                if (msgIds && Array.isArray(msgIds) && msgIds.length > 0) {
                    console.log('[Store] 从本地映射表找到消息ID:', msgIds.length, '条');

                    // 尝试加载这些消息
                    for (const msgId of msgIds) {
                        const localMsg = uni.getStorageSync(`msg_${msgId}`);
                        if (localMsg && !allMessages.some(m => (m.messageId === localMsg.messageId || m.id === localMsg.messageId))) {
                            allMessages.push(localMsg);
                        }
                    }
                }
            } catch (e) {
                console.error('[Store] 读取本地消息映射出错:', e);
            }

            if (allMessages.length > 0) {
                // 排序，确保消息按时间顺序排列
                allMessages.sort((a, b) => (a.timestamp || 0) - (b.timestamp || 0));
                console.log('[Store] 找到会话的消息:', conversationId, allMessages.length, '条');
            } else {
                console.log('[Store] 未找到会话的消息:', conversationId);
            }

            return allMessages;
        } catch (error) {
            console.error('[Store] 获取会话消息出错:', error);
            return [];
        }
    },

    // 为接收到的消息添加全局缓存
    _cacheMessage(message) {
        if (!message || !message.messageId) return;

        try {
            // 确保全局缓存对象存在
            if (typeof window !== 'undefined') {
                if (!window._cachedMessages) window._cachedMessages = {};
                window._cachedMessages[message.messageId] = message;

                // 同时按会话ID分组缓存
                if (!window._conversationMessages) window._conversationMessages = {};

                // 获取会话ID
                let conversationId = null;
                if (message.conversation && message.conversation.target) {
                    conversationId = message.conversation.target;
                } else if (message.conversationId) {
                    conversationId = message.conversationId;
                }

                if (conversationId) {
                    if (!window._conversationMessages[conversationId]) {
                        window._conversationMessages[conversationId] = [];
                    }

                    // 避免重复添加
                    const existingIndex = window._conversationMessages[conversationId].findIndex(
                        msg => (msg.messageId === message.messageId || msg.id === message.messageId)
                    );

                    if (existingIndex === -1) {
                        window._conversationMessages[conversationId].push(message);
                    }

                    // 建立消息ID到会话ID的映射
                    if (!window._messageToConversation) window._messageToConversation = {};
                    window._messageToConversation[message.messageId] = conversationId;

                    // 也保存到本地存储，确保离线时也能访问
                    try {
                        // 将消息ID添加到会话消息映射表中
                        const mappingKey = `msg_mapping_${conversationId}`;
                        let msgIds = uni.getStorageSync(mappingKey) || [];
                        if (!Array.isArray(msgIds)) msgIds = [];

                        if (!msgIds.includes(message.messageId)) {
                            msgIds.push(message.messageId);
                            uni.setStorageSync(mappingKey, msgIds);
                        }

                        // 保存消息ID与会话ID的映射
                        const idMappingKey = `msg_conv_${message.messageId}`;
                        uni.setStorageSync(idMappingKey, conversationId);
                    } catch (e) {
                        console.error('[Store] 无法保存消息映射到本地存储:', e);
                    }
                }

                // 也可以存储到本地
                try {
                    const localKey = `msg_${message.messageId}`;
                    uni.setStorageSync(localKey, message);
                } catch (e) {
                    console.error('[Store] 无法保存消息到本地存储:', e);
                }
            }
        } catch (error) {
            console.error('[Store] 缓存消息时出错:', error);
        }
    },

    // 修改loadCurrentConversationMessages方法，添加消息缓存
    loadCurrentConversationMessages(successCB, failCB) {
        if (!this.state.conversation.currentConversationInfo) {
            return;
        }
        let conversation = this.state.conversation.currentConversationInfo.conversation;
        // to load messages
        console.log('loadCurrentConversationMessages');

        wfc.getMessagesV2(conversation, 0, true, 20, 0, []).then(
            messages => {
                console.log('loadCurrentConversationMessages successCB');
                // 对每条消息进行缓存
                if (messages && messages.length > 0) {
                    messages.forEach(msg => this._cacheMessage(msg));
                }

                this.state.conversation.currentConversationMessageList = messages;
                console.log('----------------------------------------loadCurrentConversationMessages successCB', JSON.stringify(this.state.conversation.currentConversationMessageList));
                if (successCB) {
                    successCB(messages);
                }
            }
        ).catch(err => {
            if (failCB) {
                failCB(err);
            }
        });
    },

    // 用于根据消息ID查找消息
    getMessageById(messageId) {
        if (!messageId) return null;

        try {
            console.log('getMessageById    [Store] 尝试获取消息:', messageId);

            // 1. 从当前会话中查找
            if (this.state.conversation &&
                this.state.conversation.currentConversationMessageList &&
                this.state.conversation.currentConversationMessageList.length > 0) {

                const message = this.state.conversation.currentConversationMessageList.find(
                    msg => msg.messageId === messageId || msg.id === messageId
                );

                if (message) {
                    console.log('[Store] 在当前会话中找到消息:', messageId);
                    return message;
                }
            }

            // 2. 从缓存的消息中查找
            if (typeof window !== 'undefined' && window._cachedMessages && window._cachedMessages[messageId]) {
                console.log('[Store] 在全局缓存中找到消息:', messageId);
                return window._cachedMessages[messageId];
            }

            // 3. 尝试从本地存储获取
            try {
                const localKey = `msg_${messageId}`;
                const localMessage = uni.getStorageSync(localKey);
                if (localMessage) {
                    console.log('[Store] 在本地存储中找到消息:', messageId);
                    return localMessage;
                }
            } catch (e) {
                console.error('[Store] 从本地存储读取消息失败:', e);
            }

            // 4. 如果有消息ID到会话ID的映射，尝试从会话消息中查找
            if (typeof window !== 'undefined' && window._messageToConversation && window._messageToConversation[messageId]) {
                const conversationId = window._messageToConversation[messageId];
                const messages = this.getMessagesByConversationId(conversationId);

                if (messages && messages.length > 0) {
                    const message = messages.find(msg => msg.messageId === messageId || msg.id === messageId);
                    if (message) {
                        console.log('[Store] 通过会话映射找到消息:', messageId);
                        return message;
                    }
                }
            }

            console.warn('[Store] 未找到消息:', messageId);
            return null;
        } catch (error) {
            console.error('[Store] 获取消息时出错:', error);
            return null;
        }
    },

    // 设置分享数据
    setShareData(data) {
        this.state.shareData = data;
        console.log('设置分享数据:', data);
    },
    
    // 获取分享数据
    getShareData() {
        return this.state.shareData;
    },
    
    // 清除分享数据
    clearShareData() {
        this.state.shareData = null;
        console.log('清除分享数据');
    },

    // 检测当前用户是否还是群成员
    isCurrentUserGroupMember(groupId) {
        try {
            if (!groupId) {
                return false;
            }
            
            const currentUserId = wfc.getUserId();
            if (!currentUserId) {
                return false;
            }
            
            // 先尝试强制刷新获取群成员ID列表
            let groupMemberIds = wfc.getGroupMemberIds(groupId, true); // 强制刷新
            if (!groupMemberIds || !Array.isArray(groupMemberIds)) {
                // 如果强制刷新失败，再尝试缓存数据
                groupMemberIds = wfc.getGroupMemberIds(groupId, false);
            }
            
            if (!groupMemberIds || !Array.isArray(groupMemberIds)) {
                // 如果获取群成员失败，可能是网络问题，暂时允许发送消息
                console.warn('无法获取群成员列表，暂时允许发送消息');
                return true;
            }
            
            // 检查当前用户是否在群成员ID列表中
            const isMember = groupMemberIds.includes(currentUserId);
            //console.log('群成员检测结果:', {
            //    groupId,
            //    currentUserId,
            //    memberCount: groupMemberIds.length,
            //    isMember
            //});
            
            return isMember;
        } catch (error) {
            console.error('检查群成员状态失败:', error);
            // 出错时暂时允许发送消息，避免影响正常使用
            return true;
        }
    },

    // 检测群是否已解散
    isGroupDismissed(groupId) {
        try {
            if (!groupId) {
                return false;
            }
            
            const groupInfo = wfc.getGroupInfo(groupId, false);
            
            // 如果群信息不存在或者群类型表明已解散，返回true
            if (!groupInfo || groupInfo.type === -1) {
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('检查群解散状态失败:', error);
            return false;
        }
    },

    // 判断群聊是否应该在会话列表中显示
    shouldShowGroupInConversationList(groupId) {
        try {
            if (!groupId) {
                return false;
            }
            
            // 检查群是否存在和是否解散
            const isGroupDismissed = this.isGroupDismissed(groupId);
            const isCurrentUserMember = this.isCurrentUserGroupMember(groupId);
            
            //console.log('检查群聊显示条件:', {
            //    groupId,
            //    isGroupDismissed,
            //    isCurrentUserMember
            //});
            
            // 如果群被解散，仍然显示（用户可以看到解散的群聊历史）
            if (isGroupDismissed) {
                //console.log('群已解散，仍然显示在会话列表中:', groupId);
                return true;
            }
            
            // 如果用户仍然是群成员，显示
            if (isCurrentUserMember) {
                //console.log('用户仍是群成员，显示在会话列表中:', groupId);
                return true;
            }
            
            // 如果用户不是群成员，检查是否主动退群
            const quitGroupsKey = `quit_groups_${wfc.getUserId()}`;
            let quitGroups = [];
            try {
                quitGroups = getItem(quitGroupsKey) || [];
            } catch (e) {
                console.error('获取主动退群记录失败:', e);
            }
            
            // 如果在主动退群记录中，不显示
            if (Array.isArray(quitGroups) && quitGroups.includes(groupId)) {
                console.log('用户主动退群，不显示在会话列表中:', groupId);
                return false;
            }
            
            // 其他情况（被踢出群等），仍然显示
            //console.log('用户被踢出群或其他原因，仍然显示在会话列表中:', groupId);
            return true;
            
        } catch (error) {
            console.error('检查群聊显示条件时出错:', error);
            // 出错时采用保守策略，显示群聊
            return true;
        }
    },
    
    // 记录用户主动退群
    recordUserQuitGroup(groupId) {
        try {
            const quitGroupsKey = `quit_groups_${wfc.getUserId()}`;
            let quitGroups = getItem(quitGroupsKey) || [];
            
            if (!Array.isArray(quitGroups)) {
                quitGroups = [];
            }
            
            if (!quitGroups.includes(groupId)) {
                quitGroups.push(groupId);
                setItem(quitGroupsKey, quitGroups);
                console.log('已记录用户主动退群:', groupId);
            }
        } catch (error) {
            console.error('记录主动退群失败:', error);
        }
    },
    
    // 从主动退群记录中移除群ID（用于被踢出群或群解散的情况）
    removeFromQuitGroupRecord(groupId) {
        try {
            const quitGroupsKey = `quit_groups_${wfc.getUserId()}`;
            let quitGroups = getItem(quitGroupsKey) || [];
            
            if (!Array.isArray(quitGroups)) {
                return;
            }
            
            const index = quitGroups.indexOf(groupId);
            if (index > -1) {
                quitGroups.splice(index, 1);
                setItem(quitGroupsKey, quitGroups);
                console.log('已从主动退群记录中移除群ID:', groupId);
            }
        } catch (error) {
            console.error('从主动退群记录中移除群ID失败:', error);
        }
    },
}

function _reset() {
    conversationState._reset();
    contactState._reset();
    searchState._reset();
    pickState._reset();
    miscState._reset();

    uni.reLaunch({
        url: '/pages/login/LoginPage'
    });
}

export default store;