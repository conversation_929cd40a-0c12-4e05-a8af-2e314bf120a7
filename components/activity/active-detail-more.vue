<template>
	<view class="sign-pop-content">
		<view class="top-line"></view>
		<view class="sign-title"> 活动分享 </view>
		<view class="ps">
			注：活动分享收益根据活动主办方
		</view>
		<view class="share-type">
			<view class="type-item">
				<image src="@/assets/images/activity/wx.png" />
				<text>微信</text>
			</view>
			<view class="type-item">
				<image src="@/assets/images/activity/friend_circle.png" />
				<text>朋友圈</text>
			</view>
			<view class="type-item">
				<image src="@/assets/images/activity/group.png" />
				<text>好友</text>
			</view>
			<view class="type-item">
				<image src="@/assets/images/activity/link.png" />
				<text>复制链接</text>
			</view>
		</view>
		<view class="op-block">
			<view class="row-flex">
				<text>不感兴趣</text>
				<text class="label">减少这类活动</text>
			</view>
			<view class="row-flex">
				<text>拉黑活动方</text>
				<text class="label">用户名</text>
			</view>
			<view class="row-flex">
				<text>举报</text>
				<text class="label">原因</text>
			</view>
		</view>
	</view>
</template>

<script>
</script>

<style lang="scss" scoped>

	.sign-pop-content {
		width: 100%;
		height: auto;
		padding: 10px 25px 32px;
		background: linear-gradient(180deg, #E7F0FF 0%, #FFFFFF 100%);
		border-radius: 20px 20px 0px 0px;


		.top-line {
			width: 38px;
			height: 4px;
			margin: 0 auto;
			border-radius: 10px;
			background-color: #3A6CF6;
		}

		.sign-title {
			font-size: 18px;
			font-weight: 550;
			margin-top: 18px;
			margin-bottom: 12px;
		}

		.ps {
			margin: 8px 0 20px;
			font-size: 12px;
			font-weight: 400;
			color: #333;
		}
	}

	.share-type {
		width: 100%;
		margin-top: 20px;
		display: flex;
		justify-content: space-between;

		.type-item {
			flex: 4;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			gap: 9px;

			image {
				width: 56px;
				height: 56px;
			}

			text {
				font-size: 12px;
				line-height: 15.91px;

				color: #333;
			}
		}
	}

	.op-block {
		width: 100%;
		border-radius: 12px;
		background-color: #fff;
		padding: 14px;
		margin-top: 34px;
		border-radius: 10px;
		font-size: 16px;

		.row-flex {
			height: 50px;

			&:not(:last-of-type) {
				border-bottom: 1px solid #EFEFEF;
			}
		}
	}

	.label {
		color: #999;
	}
</style>