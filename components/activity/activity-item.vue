<template>
	<view class="item-wrap" @click="handleClickItem">
		<view class="top-block">
			<image src="@/assets/images/find/test.png" alt="" />
			<view class="top-right-block">
				<text class="title">{{currentData.title}}</text>
				<view class="init-user">

					<view class="user">
						<image src="@/assets/images/find/test_avatar.png" mode=""></image>
						{{currentData.user}}
					</view>
					<text class="price">{{currentData.count}}</text>
				</view>
			</view>

		</view>
		<view class="bottom-block">
			<view class="address">
				<view class="address-info">
					<text class="label">地点：</text>
					<text>{{currentData.address}}</text>
				</view>
				<view class="time">
					{{currentData.time}}
				</view>
			</view>
			<!-- <view class="participant-container">
				<view class="avatar">
					<image v-for="(item, index) in pants" src="@/assets/images/find/test_avatar.png" alt="" />
					<image src="@/assets/images/find/more.png" alt="" />

					<text class="pant-user">20/40参与</text>
				</view>
				<image class="add-icon" src="@/assets/images/find/add.png" alt="" />
			</view> -->
			<Participant :currentData="currentData"/>
		</view>
	</view>
</template>

<script>
	import Participant from './participant.vue'
	export default {
		components: {
			Participant
		},
		props: {
			currentData:{}
		},
		data() {
			return {
				pants: [
					'@/assets/images/find/test-avatar.png',
					'@/assets/images/find/test-avatar.png',
					'@/assets/images/find/test-avatar.png',
					'@/assets/images/find/test-avatar.png',
				]
			}
		},
		methods: {
			handleClickItem() {
				uni.navigateTo({
					url: '/pages/activity/ActivityDetail',
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.item-wrap {
		display: flex;
		flex-direction: column;
		height: 190px;
		background-color: #fff;
		padding: 7px;
		gap: 12px;
		font-family: MiSans;
		margin-top: 12px;
		border-radius: 10px;
	}

	.top-block {
		display: flex;
		gap: 12px;

		image {
			width: 98px;
			height: 98px;
			border-radius: 10px;
			overflow: hidden;
		}

		.top-right-block {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			gap: 15px;
			width: 100%;

			.init-user {
				width: 100%;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.user {
					display: flex;
					align-items: center;
					padding: 2px 6px;
					border-radius: 15px;
					background: #F5F5F5;
					color: #999;
				}

				image {
					width: 24px;
					height: 24px;
					margin-right: 8px;
				}

				.price {
					font-size: 16px;
					font-weight: 400;
					color: #F22;

				}
			}
		}

		.title {
			font-size: 16px;
			font-weight: 550;
			line-height: 24px;

		}

		.init-user {
			font-size: 14px;
			font-weight: 400;
			line-height: 18.56px;

		}
	}

	.bottom-block {
		.address {
			width: 100%;
			height: 30px;
			gap: 0px;
			border-radius: 5px;
			opacity: 0px;
			background: #F5F5F5;
			font-size: 14px;
			font-weight: 400;
			padding: 0 12px;

			display: flex;
			align-items: center;
			justify-content: space-between;

			.label {
				color: #999;
			}

		}

		.participant-container {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 8px;

			.pant-user {
				font-size: 12px;
				color: #386BF6;
				margin-left: 16px;
			}
		}

		.avatar {
			display: flex;
			align-items: center;
			position: relative;

			image {
				// position: relative;
				// left:-6px;
				// transform: translateX(-6px);
				width: 26px;
				height: 26px;
				border-radius: 50%;
				overflow: hidden;

				&:first-of-type {
					transform: translateX(0);
				}

				&:nth-child(1) {
					transform: translateX(-4px);
				}

				&:nth-child(2) {
					transform: translateX(-12px);
				}

				&:nth-child(3) {
					transform: translateX(-18px);
				}

				&:nth-child(4) {
					transform: translateX(-24px);
				}

				&:nth-child(5) {
					transform: translateX(-30px);
				}
			}
		}
	}

	.add-icon {
		width: 30px;
		height: 30px;
	}
</style>