<template>
  <view @touchmove.stop.prevent="moveHandle('touchmove')" @click="moveHandle('click')" v-if="isActive">
    <view class="main-action-menu" :animation="animationData" :style="{ top: height + 'px' }">
      <view class="main-action-menu-top-arrow"></view>
      <view class="menu-list">

        <view class="menu-item" @click="go2SearchFriend">
          <view class="menu-item-icon">
            <view class="wxfont add2"></view>
          </view>
          <view class="text">添加朋友</view>
        </view>
        <view class="menu-item" @click="go2CreateGroup">
          <view class="menu-item-icon">
            <view class="wxfont message2"></view>
          </view>
          <view class="text">发起群聊</view>
        </view>
        <view class="menu-item" @click="go3CreateGroup">
          <view class="menu-item-icon">
            <view class="wxfont group"></view>
          </view>
          <view class="text">广场</view>
        </view>
        <view class="menu-item" @click="go2ScanQrCode">
          <view class="menu-item-icon">
            <view class="wxfont qr_code"></view>
          </view>
          <view class="text">扫一扫</view>
        </view>
      </view>
    </view>
    <view class="main-action-menu-background-model"></view>
  </view>
</template>

<script>
import Config from "../../config";
import store from "../../store";
import WfcScheme from "../../wfcScheme";
import wfc from "../../wfc/client/wfc";
import topMessage from '@/common/topMessageView'

export default {
  data() {
    return {
      height: 0,//距离顶部高度oo
      isActive: false,
      animationData: {}
    };
  },
  props: {
    list: {
      type: Array,
      default() {
        return [{}];
      }
    }
  },
  onShow() {

  },
  mounted() {
    this.getstatusBarHeight();
    let animation = uni.createAnimation({
      duration: 300,
      timingFunction: 'linear'
    });
    this.animation = animation;
  },
  methods: {
    go2CreateGroup() {
      let sharedContactState = store.state.contact;
      let users = sharedContactState.favContactList.concat(sharedContactState.friendList);
      users = users.filter(u => {
        return u.uid !== Config.FILE_HELPER_ID
      });
      this.$pickUsers({
        users: users,
        successCB: users => {
          // TODO 创建群聊会比较慢，可以在这儿加一个 loading 页面
          store.createConversation(users, (conversation) => {
            // 不加延时的话，不能正常切换页面，会报莫名其妙的错误
            setTimeout(() => {
              this.$go2ConversationPage();
            }, 50)
          }, err => {

          })
        }
      });
    },
    go3CreateGroup() {
      uni.switchTab({
        url: '/pages/bar/BarMainPage',
        success: () => {
          console.log('to conversation list success');
        },
        fail: e => {
          console.log('to conversation list error', e);
        },
        complete: () => {
          console.log('switch tab complete')
        }
      });

    },
    go2SearchFriend() {
      uni.navigateTo({
        url: '/pages/contact/SearchUserPage',
        fail: err => {
        }
      })
    },
    async go2ScanQrCode() {
      uni.scanCode({
          success: (res) => {
              console.log('条码类型：' + res.scanType);
              console.log('条码内容：' + res.result);
              if (res.result) {
                  // TODO
                  // wildfirechat://pcsession/xxxx
                  this.onScanQrCode(res.result)
              }
          }
      });
    },
    showAnimation() {
      this.animation.opacity(1).step();
      this.animationData = this.animation.export();
    },
    hideAnimation() {
      this.animation.opacity(0).step();
      this.animationData = this.animation.export();
    },
    moveHandle(e) {
      this.hide()
    },
    toggle() {
      if (this.isActive) {
        this.hide();
      } else {
        this.show();
      }
    },
    show() {
      this.isActive = true
      setTimeout(() => {
        this.showAnimation()
      }, 30)
    },
    hide() {
      if (this.isActive) {
        this.isActive = false
        this.hideAnimation()
      }
    },
    getstatusBarHeight() {
      let SystemInfo = uni.getSystemInfoSync();
      // #ifdef H5
      this.height = SystemInfo.safeArea.top + SystemInfo.windowTop;
      // #endif
    },
    onScanQrCode(qrcode) {
      // 处理 URL 类型
      if (qrcode.startsWith('http://') || qrcode.startsWith('https://')) {
        // 新增：处理贴吧URL格式
        if (qrcode.includes('bar.html?BarID=')) {
            const barIdPart = qrcode.split('BarID=')[1];
            const barId = barIdPart.split('&')[0]; // 处理可能的额外URL参数
            
            console.log('扫描到贴吧链接, 贴吧ID:', barId);
            uni.navigateTo({
                url: `/pages/bar/BarHome?id=${barId}`,
                fail: (err) => {
                    console.error('跳转到贴吧主页失败:', err);
                    uni.showToast({
                        title: '跳转贴吧失败',
                        icon: 'none'
                    });
                }
            });
            return; // 处理完毕
        }

        // 处理新的群组URL格式
        if (qrcode.includes('?GroupID=')) {
          const groupIdPart = qrcode.split('GroupID=')[1]; // 获取GroupID=后面的内容
          
          // 处理可能的额外URL参数
          const groupId = groupIdPart.split('&')[0];
          
          console.log('扫描到群组链接, 群ID:', groupId);
          // 跳转到群组详情页面
          uni.navigateTo({
            url: `/pages/group/GroupDetailPage?groupId=${groupId}`,
            fail: (err) => {
              console.error('跳转失败:', err);
              uni.showToast({
                title: '跳转失败',
                icon: 'none'
              });
            }
          });
          return;
        }

        // 处理新的用户URL格式
        if (qrcode.includes('?UserID=')) {
          const userIdPart = qrcode.split('UserID=')[1]
          // 处理可能的额外URL参数
          const userId = userIdPart.split('&')[0]
          
          console.log('扫描到用户链接, 用户ID:', userId)
          // 跳转到用户详情页面
          uni.navigateTo({
            url: `/pages/contact/UserDetailPage?userId=${userId}`,
            fail: (err) => {
              console.error('跳转失败:', err)
              uni.showToast({
                title: '跳转失败',
                icon: 'none'
              })
            }
          })
          return
        }
        
        // 其他网址直接跳转到WebPage
        uni.navigateTo({
          url: `/pages/misc/WebPage?url=${encodeURIComponent(qrcode)}`,
          fail: (err) => {
            console.error('跳转失败:', err)
            uni.showToast({
              title: '跳转失败',
              icon: 'none'
            })
          }
        })
        return
      }

      // 处理地球号
      if (qrcode.startsWith('EARTHID:')) {
        const userId = qrcode.substring(8) // 截取 EARTHID: 后面的内容
        uni.navigateTo({
          url: `/pages/contact/UserDetailPage?userId=${userId}`,
          fail: (err) => {
            console.error('跳转失败:', err)
            uni.showToast({
              title: '跳转失败',
              icon: 'none'
            })
          }
        })
        return
      }
      
      // 处理其他字符串
      uni.navigateTo({
        url: `/pages/misc/StringPage?content=${encodeURIComponent(qrcode)}`,
        fail: (err) => {
          console.error('跳转失败:', err)
          uni.showToast({
            title: '跳转失败',
            icon: 'none'
          })
        }
      })
    }
  }
};
</script>
<style scoped>
.main-action-menu {
  width: 260rpx;
  position: fixed;
  z-index: 9999;
  top: -10px;
  right: 16rpx;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  opacity: 0;
}

.main-action-menu-background-model {
  background-color: rgba(0, 0, 0, 0);
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 9998;
}

.main-action-menu-top-arrow {
  width: 0px;
  height: 0px;
  border: 10px solid transparent;
  border-bottom-color: #4C4C4C;
  margin-left: auto;
  margin-right: 20rpx;
}

.menu-list {
  width: 100%;
  background-color: #4C4C4C;
  border-radius: 20rpx;
}

.menu-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0rpx 36rpx;
  padding-right: 0;
}

.menu-item-icon {
  width: 38rpx;
  height: 38rpx;
  margin-right: 20rpx;
  color: #fff;
}

.menu-item-icon .wxfont {
  font-size: 40rpx;
}

.menu-item .text {
  color: #fff;
  font-size: 28rpx;
  border-bottom: 1px #535353 solid;
  padding: 28rpx 0rpx;
  flex: 1;
}

.menu-item:nth-last-child(1) .text {
  border: none;
}
</style>
