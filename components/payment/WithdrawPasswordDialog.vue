<template>
  <view class="withdraw-dialog" v-if="visible">
    <view class="dialog-mask" @click="handleClose"></view>
    <view class="dialog-content">
      <!-- 顶部标题栏 -->
      <view class="dialog-header">
        <text class="close-icon" @click="handleClose">×</text>
        <text class="header-title">请输入支付密码</text>
      </view>
      
      <!-- 金额展示 -->
      <view class="amount-section">
        <text class="amount-label">提现</text>
        <text class="amount-value">¥ {{amount}}</text>
      </view>
      
      <!-- 费用信息 -->
      <view class="fee-info">
        <view class="fee-row">
          <text>服务费</text>
          <text>¥ {{serviceFee}}</text>
        </view>
        <view class="fee-row">
          <text>费率</text>
          <text>{{feeRate}}%</text>
        </view>
      </view>

      <!-- 密码输入框 -->
      <view class="password-input">
        <view 
          v-for="(item, index) in 6" 
          :key="index"
          :class="['input-item', { active: keyboard.valueToLocaleString?.length === index }]"
        >
          <view class="dot" v-if="keyboard.valueToLocaleString?.length > index"></view>
        </view>
      </view>

      <!-- 数字键盘 -->
      <view class="keyboard">
        <pan-keyboard ref="panKeyboardRef" :maxValue="999999" :isCheck='checkValue' @onSubmit='onSubmit' @onChange="onChange" @onError="onError" :isDecimal="false"></pan-keyboard>
        <view class="placeholder" :style="placeholderStyle"></view>
      </view>
    </view>
  </view>
</template>

<script>
import PanKeyboard from '@/components/pan-keyboard/pan-keyboard'

export default {
  name: 'WithdrawPasswordDialog',
  components: {
    PanKeyboard
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    amount: {
      type: [Number, String],
      default: '0.00'
    },
    serviceFee: {
      type: [Number, String],
      default: '0.00'
    },
    feeRate: {
      type: [Number, String],
      default: '0'
    }
  },
  data() {
    return {
      keyboard: {},
      height: 0
    }
  },
  computed: {
    checkValue() {
      return true
    },
    placeholderStyle() {
      const style = {}
      style.height = `${this.height}rpx`
      // #ifndef MP-WEIXIN
      const { safeAreaInsets } = uni.getSystemInfoSync()
      style.paddingBottom = `${safeAreaInsets.bottom}px`
      // #endif
      return style
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.keyboard = {}
        if (this.$refs.panKeyboardRef) {
          this.$refs.panKeyboardRef.setKeyboard('')
        }
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
      this.keyboard = {}
    },
    onSubmit(e) {
      this.keyboard = e
      if (this.keyboard.valueToLocaleString?.length === 6) {
        this.$emit('confirm', this.keyboard.valueToLocaleString)
        this.keyboard = {}
        this.$refs.panKeyboardRef.setKeyboard('')
      }
    },
    onChange(e) {
      this.keyboard = e
      if (this.keyboard.valueToLocaleString?.length === 6) {
        this.$emit('confirm', this.keyboard.valueToLocaleString)
        this.keyboard = {}
        this.$refs.panKeyboardRef.setKeyboard('')
      }
    },
    onError() {
      console.log('超过限制')
    }
  }
}
</script>

<style lang="scss" scoped>
.withdraw-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  
  .dialog-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.4);
  }
  
  .dialog-content {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    border-radius: 16px 16px 0 0;
    padding: 24px 20px 0;
  }
  
  .dialog-header {
    position: relative;
    text-align: center;
    margin-bottom: 32px;
    
    .close-icon {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      font-size: 24px;
      color: #333;
      padding: 4px;
    }
    
    .header-title {
      font-size: 16px;
      color: #333;
      font-weight: normal;
    }
  }
  
  .amount-section {
    text-align: center;
    margin-bottom: 24px;
    
    .amount-label {
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
    }
    
    .amount-value {
      font-size: 28px;
      font-weight: bold;
      color: #333;
    }
  }
  
  .fee-info {
    background-color: #fff;
    padding: 0;
    margin-bottom: 24px;
    
    .fee-row {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .keyboard {
    background-color: #F5F5F5;
    margin: 0 -20px;
    padding: 30rpx;
  }
}

.password-input {
  display: flex;
  justify-content: space-between;
  margin: 0 auto 24px;
  width: 240px;
  background-color: #fff;
  
  .input-item {
    width: 36px;
    height: 36px;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    background-color: #fff;
    
    &.active {
      border-color: #007AFF;
    }
    
    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #333;
    }
  }
}

.placeholder {
  width: 100%;
  height: 10rpx;
  background-color: #F5F5F5;
  /* #ifdef MP-WEIXIN*/
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  /*#endif*/
}
</style>
