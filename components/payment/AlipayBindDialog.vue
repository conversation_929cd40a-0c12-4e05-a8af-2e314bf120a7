<template>
  <uni-popup ref="popup" type="center" :mask-click="false">
    <view class="alipay-bind-dialog">
      <view class="dialog-title">绑定支付宝账号</view>
      <view class="input-group">
        <input 
          class="input-item" 
          type="text" 
          v-model="alipayAccount" 
          placeholder="请输入支付宝账号"
        />
        <input 
          class="input-item" 
          type="text" 
          v-model="realname" 
          placeholder="请输入真实姓名"
        />
      </view>
      <view class="dialog-buttons">
        <button class="cancel-btn" @click="handleCancel">取消</button>
        <button class="confirm-btn" @click="handleConfirm">确认</button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
import appServerApi from '@/api/appServerApi'
import uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'

export default {
  name: 'AlipayBindDialog',
  components: {
    uniPopup
  },
  data() {
    return {
      alipayAccount: '',
      realname: ''
    }
  },
  methods: {
    show() {
      this.$refs.popup.open('center')
    },
    hide() {
      this.$refs.popup.close()
    },
    async handleConfirm() {
      if (!this.alipayAccount || !this.realname) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none'
        })
        return
      }
      
      try {
        uni.showLoading({
          title: '绑定中...'
        })
        
        await appServerApi.bindAlipay({
          alipayAccount: this.alipayAccount,
          realname: this.realname
        })
        
        uni.hideLoading()
        uni.showToast({
          title: '绑定成功',
          icon: 'success'
        })
        
        this.$emit('success')
        this.hide()
      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: error.message || '绑定失败',
          icon: 'none'
        })
      }
    },
    handleCancel() {
      this.hide()
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.alipay-bind-dialog {
  width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  
  .dialog-title {
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 40rpx;
  }
  
  .input-group {
    margin-bottom: 40rpx;
    
    .input-item {
      width: 100%;
      height: 88rpx;
      border: 2rpx solid #eee;
      border-radius: 12rpx;
      padding: 0 24rpx;
      margin-bottom: 24rpx;
      font-size: 28rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .dialog-buttons {
    display: flex;
    justify-content: space-between;
    
    button {
      width: 240rpx;
      height: 80rpx;
      border-radius: 40rpx;
      font-size: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &.cancel-btn {
        background: #f5f5f5;
        color: #666;
      }
      
      &.confirm-btn {
        background: #178e57;
        color: #fff;
      }
    }
  }
}
</style> 