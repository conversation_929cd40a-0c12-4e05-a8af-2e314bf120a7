<template>
    <div class="payment-popup" v-if="visible">
        <div class="payment-mask" @click="handleClose"></div>
        <div class="payment-container">
            <div class="payment-header">
                <div class="close-btn" @click="handleClose">×</div>
                <div class="header-content">
                    <div class="payment-title">选择到账银行卡</div>
                    <div class="payment-subtitle">请留意银行到账时间</div>
                </div>
                <div class="close-btn" style="visibility: hidden">×</div>
            </div>     
            <div class="payment-body">
                <div v-if="isLoading" class="loading-container">
                    <div class="loading-spinner"></div>
                    <text>加载中...</text>
                </div>
                <div v-else-if="bankCards.length === 0" class="empty-state">
                    <text>暂无绑定的银行卡</text>
                    <div class="add-card" @click="handleAddCard">
                        <image src="/static/image/icon/add.png" class="add-icon"></image>
                        <text class="add-text">添加银行卡</text>
                        <text class="arrow-right"></text>
                    </div>
                </div>
                <template v-else>
                    <div class="card-list">
                        <div v-for="card in bankCards" :key="card.id" 
                            class="payment-item" 
                            :class="{ active: selectedCard === card.id }" 
                            @click="selectCard(card.id)">
                            <div class="payment-item-left">
                                <image :src="card.icon" :alt="card.name" class="bank-icon"></image>
                                <div class="card-info">
                                    <text class="card-name">{{ card.name }} {{ card.type }} ({{ card.lastFour }})</text>
                                    <text class="instant-text">立即到账</text>
                                </div>
                            </div>
                            <div class="payment-item-right">
                                <div v-if="selectedCard === card.id" class="check-icon">✓</div>
                            </div>
                        </div>
                    </div>
                    <div class="add-card" @click="handleAddCard">
                        <image src="/static/image/icon/add.png" class="add-icon"></image>
                        <text class="add-text">使用新卡提现</text>
                        <text class="arrow-right"></text>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script>
import appServerApi from '@/api/appServerApi'

export default {
    name: 'PaymentSelector',
    props: {
        visible: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            selectedCard: null,
            bankCards: [],
            isLoading: false
        }
    },
    watch: {
        visible(newVal) {
            if (newVal) {
                this.fetchBankCards()
            }
        }
    },
    methods: {
        fetchBankCards() {
            this.isLoading = true
            appServerApi.getBankCardList()
                .then(res => {
                    if (res && res.data) {
                        this.bankCards = res.data.map(card => {
                            return {
                                id: card.id,
                                name: card.bankName,
                                type: '储蓄卡',
                                lastFour: card.cardNo.slice(-4),
                                cardNo: card.cardNo,
                                icon: this.getBankIcon(card.bankName)
                            }
                        })
                        
                        // If we have cards, select the first one by default
                        if (this.bankCards.length > 0 && !this.selectedCard) {
                            this.selectedCard = this.bankCards[0].id
                        }
                    }
                })
                .catch(err => {
                    uni.showToast({
                        title: err.message || '获取银行卡列表失败',
                        icon: 'none'
                    })
                })
                .finally(() => {
                    this.isLoading = false
                })
        },
        getBankIcon(bankName) {
            // 根据银行名称返回对应的logo
            const bankLogos = {
                '招商银行': '/static/bank-logos/cmb.png',
                '工商银行': '/static/bank-logos/icbc.png',
                '建设银行': '/static/bank-logos/ccb.png',
                '农业银行': '/static/bank-logos/abc.png',
                '中国银行': '/static/bank-logos/boc.png'
            }
            return bankLogos[bankName] || '/static/bank-logos/default.png'
        },
        handleClose() {
            this.$emit('update:visible', false)
        },
        selectCard(cardId) {
            this.selectedCard = cardId
            const selectedCard = this.bankCards.find(card => card.id === cardId)
            this.$emit('confirm', { card: selectedCard })
            this.handleClose()
        },
        handleAddCard() {
            // Set a flag to check for refreshing bank card list when we return
            uni.setStorageSync('refreshBankCardSelector', 'true')
            
            uni.navigateTo({
                url: '/pages/wallet/addBankCard',
                success: () => {
                    // Handle any success actions if needed
                },
                fail: (err) => {
                    console.error('导航到添加银行卡页面失败:', err)
                    uni.showToast({
                        title: '页面跳转失败',
                        icon: 'none'
                    })
                }
            })
        }
    },
    // Since this is a component not a page, we should use Vue lifecycle methods
    mounted() {
        // Initialize if visible on mount
        if (this.visible) {
            this.fetchBankCards()
        }
    },
    // Check for refresh when component is updated and becomes visible
    updated() {
        const shouldRefresh = uni.getStorageSync('refreshBankCardSelector')
        if (shouldRefresh === 'true' && this.visible) {
            // Clear the flag
            uni.removeStorageSync('refreshBankCardSelector')
            // Refresh bank cards
            this.fetchBankCards()
        }
    }
}
</script>

<style lang="scss" scoped>
.payment-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
}

.payment-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
}

.payment-container {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    border-radius: 20rpx 20rpx 0 0;
    padding: 0;
}

.payment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    position: relative;

    .header-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8rpx;
    }

    .payment-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
    }

    .close-btn {
        font-size: 40rpx;
        color: #999;
        padding: 10rpx;
        line-height: 1;
    }
}

.payment-subtitle {
    font-size: 26rpx;
    color: #999;
    text-align: center;
}

.payment-body {
    padding: 0 32rpx;
}

.card-list {
    margin-bottom: 20rpx;
}

.payment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #f5f5f5;

    .payment-item-left {
        display: flex;
        align-items: center;
        gap: 20rpx;

        .bank-icon {
            width: 40rpx;
            height: 40rpx;
        }

        .card-info {
            display: flex;
            flex-direction: column;
            gap: 4rpx;

            .card-name {
                font-size: 28rpx;
                color: #333;
            }

            .instant-text {
                font-size: 24rpx;
                color: #999;
            }
        }
    }

    .payment-item-right {
        .check-icon {
            color: #007AFF;
            font-size: 32rpx;
        }
    }
}

.add-card {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    margin-bottom: 40rpx;

    .add-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 20rpx;
    }

    .add-text {
        flex: 1;
        font-size: 28rpx;
        color: #333;
    }

    .arrow-right {
        width: 20rpx;
        height: 20rpx;
        border-top: 3rpx solid #999;
        border-right: 3rpx solid #999;
        transform: rotate(45deg);
        margin-right: 8rpx;
    }
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 0;
    
    text {
        margin-top: 20rpx;
        font-size: 28rpx;
        color: #999;
    }
}

.loading-spinner {
    width: 40rpx;
    height: 40rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #007AFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx 0;
    
    text {
        font-size: 28rpx;
        color: #999;
        margin-bottom: 40rpx;
    }
    
    .add-card {
        width: 100%;
    }
}
</style>