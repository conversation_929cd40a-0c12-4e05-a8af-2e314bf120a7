<template>
    <div class="payment-popup" v-if="visible">
        <div class="payment-mask" @click="handleClose"></div>
        <div class="payment-container">
            <div class="payment-header">
                <div class="payment-title">请输入充值金额</div>
                <div class="close-btn" @click="handleClose">×</div>
            </div>

            <div class="payment-body">
                <view class="money-box">
                    <view class="money-box-title">注:金币兑换比例1:10，最低充值1元，最高充值1000元，仅支持整数充值</view>
                    <view class="flex_r_c text_50 text_weight_bold money-box-row">
                        <view class="money-box-row-">￥</view>
                        <input class="money-input" type="number" v-model="amount" @input="handleAmountInput" />
                    </view>
                </view>

                <div v-for="method in paymentMethods" :key="method.id" class="payment-item" :class="{ active: selectedMethod?.id === method.id }" @click="selectMethod(method)">
                    <div class="payment-item-left">
                        <image :src="method.icon" :alt="method.name" class="payment-icon"></image>
                        <span class="payment-name">{{ method.name }}</span>
                    </div>
                    <div class="payment-item-right">
                        <div v-if="method.id === 'wechat'" class="balance">
                            默认
                            <!-- : ¥{{ balance }} -->
                        </div>
                        <div class="radio-button"></div>
                    </div>
                </div>
            </div>

            <div class="payment-footer">
                <button class="confirm-btn" @click="handleConfirm">
                    确认
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import util from '@/utils/util'
import appServerApi from '@/api/appServerApi'
export default {
    name: 'GoldPaymentSelector',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        // amount: {
        //     type: [String, Number],
        //     default: '100',
        // },
        balance: {
            type: String,
            default: '0.00',
        },
    },
    data() {
        return {
            amount: '',
            keyboard: {},
            selectedMethod: {
                id: 'wechat',
                name: '微信支付',
                icon: '/static/image/icon/wechat.png',
            },
            paymentMethods: [
                {
                    id: 'wechat',
                    name: '微信支付',
                    icon: '/static/image/icon/wechat.png',
                },
                {
                    id: 'alipay',
                    name: '支付宝',
                    icon: '/static/image/icon/alipay.png',
                },
                {
                    id: 'balance',
                    name: '余额支付',
                    icon: '/static/image/icon/balance.png',
                }
            ],
        }
    },
    computed: {
        checkValue() {
            let valueNumber = this.keyboard.valueNumber

            // 可以在实时这里校验输入的值
            // if (valueNumber < 100) return false;
            // if (valueNumber % 10) return false;
            return true
        },
        placeholderStyle() {
            const style = {}
            style.height = `${this.height}rpx`
            // #ifndef MP-WEIXIN
            const { safeAreaInsets } = uni.getSystemInfoSync()
            style.paddingBottom = `${safeAreaInsets.bottom}px`
            // #endif
            return style
        },
    },
    methods: {
        handleClose() {
            this.$emit('update:visible', false)
            this.$emit('close')
        },
        selectMethod(method) {
            this.selectedMethod = method
        },
        handleAmountInput() {
            // Remove all input validation to allow any user input
        },
        async handleConfirm() {
            if (!this.amount) {
                uni.showToast({
                    title: '请输入充值金额',
                    icon: 'none'
                });
                return;
            }

            const amountNum = Number(this.amount);
            if (!Number.isInteger(amountNum)) {
                uni.showToast({
                    title: '请输入整数金额',
                    icon: 'none'
                });
                this.amount = '';
                return;
            }

            if (amountNum < 1) {
                uni.showToast({
                    title: '最低充值金额为1元',
                    icon: 'none'
                });
                this.amount = '';
                return;
            }
            
            if (amountNum > 1000) {
                uni.showToast({
                    title: '最高充值金额为1000元',
                    icon: 'none'
                });
                this.amount = '';
                return;
            }
            
            var obj = {
                amount: amountNum,
                payType: '',
            }
            console.log('this.selectedMethod',this.selectedMethod)
            switch (this.selectedMethod.id) {
                case 'wechat':
                    obj.payType = 2
                    break
                case 'alipay':
                    obj.payType = 1
                    break
                case 'balance':
                    obj.payType = 0
                    break
                default:
                    break
            }
            const response = await appServerApi.buyGold(obj)
            console.log(response)
            switch (this.selectedMethod.id) {
                case 'wechat':
                    util.wxpay(
                        response,
                        function (res) {
                            console.log('成功', res)
                            uni.showToast({
                                icon: 'success',
                                title: '充值成功',
                                duration: 1000,
                            })
                            this.$emit('confirm', {status:'success'}) 
                        },
                        function (res) {
                            console.log('失败', res)
                            uni.showToast({
                                title: '支付失败',
                                icon: 'none',
                            })
                        }
                    )
                    break
                case 'alipay':
                    util.alipay(
                        response,
                        function (res) {
                            console.log('成功', res)
                            uni.showToast({
                                title: '支付成功',
                                icon: 'none',
                            })
                            this.$emit('confirm', {status:'success'}) 
                        },
                        function (res) {
                            console.log('失败', res)
                            uni.showToast({
                                title: '支付失败',
                                icon: 'none',
                            })
                        }
                    )
                    break
                case 'balance':
                    if(response.error){
                        uni.showToast({
                            title: response.error,
                            icon: 'none',
                        })
                    }else{
                        uni.showToast({
                            title: '支付成功',
                            icon: 'none',
                        })
                        this.$emit('confirm', {status:'success'}) 
                    }
                    break
                default:
                    break
            }
        },
    },
}
</script>

<style lang="scss" scoped >
.payment-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
}

.payment-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
}

.payment-container {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    border-radius: 16px 16px 0 0;
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.payment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 0 20px;
}

.payment-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.close-btn {
    font-size: 24px;
    color: #909399;
    cursor: pointer;
    padding: 4px;
}

.payment-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.amount-info {
    text-align: center;
    font-size: 16px;
    color: #606266;
    margin-bottom: 20px;
}

.amount {
    font-size: 32px;
    font-weight: bold;
    color: #f56c6c;
    margin-left: 4px;
}

.payment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    margin-bottom: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8fafc;
    border: 1px solid transparent;
}

.payment-item:hover {
    background: #f0f5ff;
    transform: translateY(-1px);
}

.payment-item.active {
    background: #f0f5ff;
    border: 1px solid #1989fa;
}

.payment-item:last-child {
    margin-bottom: 0;
}

.payment-item-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.payment-icon {
    width: 28px;
    height: 28px;
    object-fit: contain;
}

.payment-name {
    font-size: 15px;
    color: #2c3e50;
    font-weight: 500;
    margin-left: 12px;
}

.payment-item-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.balance {
    font-size: 14px;
    color: #606266;
    background: #f4f4f5;
    padding: 4px 8px;
    border-radius: 4px;
}

.radio-button {
    width: 20px;
    height: 20px;
    border: 2px solid #dcdfe6;
    border-radius: 50%;
    position: relative;
    transition: all 0.2s ease;
}

.active .radio-button {
    border-color: #1989fa;
    background: #fff;
}

.active .radio-button::after {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    background: #1989fa;
    border-radius: 50%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.2s ease;
}

.payment-footer {
    padding: 16px 20px;
    border-top: 1px solid #ebeef5;
}

.confirm-btn {
    width: 100%;
    height: 44px;
    background: #1989fa;
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
}

.confirm-btn:hover {
    background: #66b1ff;
}

.confirm-btn:active {
    background: #3a8ee6;
}

@media (max-width: 768px) {
    .payment-container {
        border-radius: 12px 12px 0 0;
    }

    .amount {
        font-size: 28px;
    }
}
.money-box {
    position: relative;
    top: -30rpx;
    box-sizing: border-box;
    width: 100%;
    border-radius: 30rpx 30rpx 0 0;
    background-color: #fff;

    .line {
        width: calc(100% - 40rpx);
        margin-top: 40rpx;
        border-bottom: 1px solid #dcdcdc;
    }

    .money-box-title {
        width: 100%;
        color: #999999;
        font-size: 12px;
    }

    .money-box-text {
        position: relative;
        margin-top: 30rpx;
        font-size: 30rpx;
        color: #616161;

        .money-box-text_ {
            position: absolute;
            left: 0;
        }
    }

    .money-box-row {
        width: 100%;
        height: 70px;
        font-size: 36px;
        border-bottom: 1px solid #EFEFEF;
        display: flex;
        align-items: center;
         

        .money-box-row- {
            // margin-top: 4rpx;
        }

        .money-box-row-icon {

        }
        .money-input{
            font-size: 36px;
        }
    }
}
</style>