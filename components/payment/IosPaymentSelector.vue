<template>
    <div class="payment-popup" v-if="visible">
        <div class="payment-mask" @click="handleClose"></div>
        <div class="payment-container">
            <div class="payment-header">
                <div class="back-btn" @click="handleClose">
                    <span class="back-text">返回</span>
                </div>
                <div class="user-info">
                    <span class="user-name">{{ userName }}</span>
                </div>
            </div>

            <div class="payment-body">
                <div class="balance-info">
                    <div class="info-text">当前金币不足</div>
                </div>

                <div class="amount-grid">
                    <div 
                        v-for="item in amountOptions" 
                        :key="item.coins"
                        class="amount-item"
                        :class="{ active: selectedAmount === item.amount }"
                        @click="selectAmount(item.amount)"
                    >
                        <div class="coin-amount">
                            <span class="coin-number">{{ item.coins }}</span>
                            <span class="coin-unit">金币</span>
                            <span v-if="item.tag" class="coin-tag">{{ item.tag }}</span>
                        </div>
                        <div class="price">{{ item.amount }}元</div>
                    </div>
                </div>
            </div>

            <div class="payment-footer">
                <div class="notice">平台倡导量入为出，理性消费</div>
                <button class="recharge-btn" @click="handleRecharge">
                    立即充值{{ selectedAmount }}元
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import util from '@/utils/util'
import appServerApi from '@/api/appServerApi'

export default {
    name: 'IosPaymentSelector',
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        userName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            selectedAmount: 1,
            iapChannel: null,
            amountOptions: [
                { coins: 7, amount: 1, tag: '推荐' },
                { coins: 42, amount: 6 },
                { coins: 70, amount: 10 },
                { coins: 686, amount: 98 },
                { coins: 2086, amount: 298 },
                { coins: 3626, amount: 518 }
            ],
            // 根据环境判断是否启用测试模式
            testMode: process.env.NODE_ENV === 'development' || process.env.VUE_APP_ENV === 'test',
            // 强制关闭测试模式的开关（生产环境中设置为true）
            forceDisableTestMode: true  // 设置为true来强制关闭测试模式
        }
    },
    mounted() {
        // 初始化IAP支付通道
        this.initIapChannel();
    },
    methods: {
        handleClose() {
            this.$emit('update:visible', false)
            this.$emit('close')
        },
        selectAmount(amount) {
            this.selectedAmount = amount
        },
        
        // 初始化IAP支付通道
        async initIapChannel() {
            try {
                // 检查是否支持 Apple Pay
                if (!uni.getSystemInfoSync().platform.includes('ios')) {
                    console.log('当前设备不支持IAP支付');
                    return;
                }
                
                // 使用新版API获取支付通道
                const providerRes = await new Promise((resolve, reject) => {
                    uni.getProvider({
                        service: 'payment',
                        success: (res) => {
                            resolve(res);
                        },
                        fail: (error) => {
                            reject(error);
                        }
                    });
                });
                
                // 查找IAP支付通道
                this.iapChannel = providerRes.providers.find(provider => provider.id === 'appleiap');
                
                // 支付通道初始化后，检查未完成订单
                if (this.iapChannel) {
                    this.checkPendingTransactions();
                }
            } catch (error) {
                console.error('初始化IAP支付通道失败:', error);
            }
        },
        
        // 验证苹果支付凭证（因为缺少appServerApi.verifyApplePay方法，此处自行实现验证逻辑）
        async verifyApplePay(payload) {
            try {
                // 更安全的测试模式判断：只有在明确的开发环境或者手动设置时才启用
                const isTestMode = !this.forceDisableTestMode && this.testMode && (
                    process.env.NODE_ENV === 'development' || 
                    process.env.VUE_APP_ENV === 'test' ||
                    window.location.hostname === 'localhost' ||
                    window.location.hostname.includes('test')
                );
                
                console.log('当前环境信息:', {
                    NODE_ENV: process.env.NODE_ENV,
                    VUE_APP_ENV: process.env.VUE_APP_ENV,
                    hostname: window.location.hostname,
                    testMode: this.testMode,
                    forceDisableTestMode: this.forceDisableTestMode,
                    isTestMode: isTestMode
                });
                
                // 测试模式：直接返回成功，用于开发环境测试
                if (isTestMode) {
                    console.log('测试模式：模拟支付成功');
                    uni.showToast({
                        title: '测试模式：金币充值成功',
                        icon: 'success'
                    });
                    return { 
                        success: true, 
                        message: '测试模式：支付验证成功', 
                        data: {
                            coins: payload.coins || this.getCoinsForAmount(this.selectedAmount)
                        }
                    };
                }
                
                console.log('正式环境：开始验证真实支付');
                
                // 正常模式：调用服务器API进行验证
                console.log('支付验证数据详情:', JSON.stringify(payload).substring(0, 100) + '...');
                
                // 格式4: 使用更完整的参数，包括添加更多可能需要的上下文信息
                const requestData = {
                    'receipt-data': payload.receipt,
                    'sandbox': true,  // 是否是沙盒环境（测试环境）
                    'productId': payload.productId || this.getProductId(this.selectedAmount),
                    'amount': payload.amount || this.selectedAmount,
                    'coins': payload.coins || this.getCoinsForAmount(this.selectedAmount)
                };
                
                console.log('发送到服务器的数据格式:', JSON.stringify(requestData).substring(0, 100) + '...');
                
                // 调用后端接口验证支付凭证，设置rawResponseData为true以获取完整响应
                const result = await appServerApi._post('/iap/gold/charge', requestData, false, true);
                
                console.log('服务器完整响应:', result);
                
                // 检查结果 - 注意appServerApi._post方法在设置rawResponseData为true时返回整个响应
                if (result && result.code === 200) {
                    uni.showToast({
                        title: '金币充值成功',
                        icon: 'success'
                    });
                    return { success: true, message: '支付验证成功', data: result.data };
                } else {
                    // 处理服务器返回的错误信息
                    const errorMsg = (result && result.msg) || '支付验证失败，请稍后再试';
                    console.error('服务器返回错误详情:', JSON.stringify(result));
                    throw new Error(errorMsg);
                }
            } catch (error) {
                console.error('支付验证失败详情:', error);
                // 确保error有效 - 如果error直接是字符串，则直接使用
                const errorMessage = typeof error === 'string' ? error : 
                                    (error && error.message ? error.message : '支付验证失败，请稍后再试');
                uni.showToast({
                    title: errorMessage,
                    icon: 'none'
                });
                return { success: false, message: errorMessage };
            }
        },
        
        // 检查未完成订单
        async checkPendingTransactions() {
            try {
                if (!this.iapChannel) return;
                
                const transactions = await new Promise((resolve, reject) => {
                    this.iapChannel.restoreCompletedTransactions({
                        manualFinishTransaction: true,
                        username: this.userName
                    }, (res) => {
                        resolve(res);
                    }, (err) => {
                        reject(err);
                    });
                });
                
                // 如果有未完成订单，处理它们
                if (transactions && transactions.length > 0) {
                    for (const transaction of transactions) {
                        try {
                            if (transaction.transactionState === "1") { // 已购买但未完成
                                // 验证支付凭证
                                const verifyResult = await this.verifyApplePay({
                                    receipt: transaction.transactionReceipt
                                });
                                
                                if (verifyResult.success) {
                                    // 关闭订单
                                    await this.finishTransaction(transaction);
                                    
                                    uni.showToast({
                                        title: '恢复成功',
                                        icon: 'success'
                                    });
                                } else {
                                    // 即使验证失败，也尝试关闭订单，避免反复提示
                                    await this.finishTransaction(transaction);
                                }
                            } else if (transaction.transactionState === "2") { // 失败的订单
                                // 关闭失败的订单
                                await this.finishTransaction(transaction);
                            }
                        } catch (txError) {
                            console.error('处理未完成订单失败:', txError);
                            // 即使处理失败，也尝试关闭订单
                            try {
                                await this.finishTransaction(transaction);
                            } catch (closeError) {
                                console.error('关闭未完成订单失败:', closeError);
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('检查未完成订单失败:', error);
            }
        },
        
        // 根据产品ID获取金额
        getAmountByProductId(productId) {
            const productMap = this.getProductIdMap();
            for (const [amount, id] of Object.entries(productMap)) {
                if (id === productId) {
                    return Number(amount);
                }
            }
            return 1; // 默认返回1元
        },
        
        // 获取产品ID映射
        getProductIdMap() {
            return {
                1: '673752988666881',
                6: '673752988666886',
                10: '6737529886668810',
                98: '6737529886668898',
                298: '67375298866688298',
                518: '67375298866688'
            };
        },
        
        async handleRecharge() {
            try {
                // 检查是否支持 Apple Pay
                if (!uni.getSystemInfoSync().platform.includes('ios')) {
                    uni.showToast({
                        title: '当前设备不支持 Apple Pay',
                        icon: 'none'
                    });
                    return;
                }

                // 确保已初始化支付通道
                if (!this.iapChannel) {
                    await this.initIapChannel();
                    
                    if (!this.iapChannel) {
                        throw new Error('未找到 Apple Pay 支付通道');
                    }
                }

                // 根据选择的金额获取对应的商品ID
                const productId = this.getProductId(this.selectedAmount);
                
                // 获取商品信息（使用正确的requestProduct方法）
                const products = await new Promise((resolve, reject) => {
                    this.iapChannel.requestProduct([productId], (result) => {
                        resolve(result);
                    }, (error) => {
                        reject(error);
                    });
                });
                
                if (!products || products.length === 0) {
                    throw new Error('获取商品信息失败');
                }

                // 使用uni.requestPayment发起支付
                const paymentResult = await new Promise((resolve, reject) => {
                    uni.requestPayment({
                        provider: 'appleiap',
                        orderInfo: {
                            productid: productId,
                            username: this.userName, // 用户标识
                            manualFinishTransaction: true, // 手动关闭订单
                        },
                        success: (res) => {
                            resolve(res);
                        },
                        fail: (err) => {
                            reject(err);
                        }
                    });
                });

                // 处理支付结果
                if (paymentResult) {
                    // 获取产品ID
                    const productId = this.getProductId(this.selectedAmount);
                    
                    // 验证支付凭证，使用自定义的verifyApplePay方法，传递更多信息
                    const verifyResult = await this.verifyApplePay({
                        receipt: paymentResult.transactionReceipt,
                        transactionId: paymentResult.transactionId, // 添加交易ID
                        productId: productId, // 添加产品ID
                        amount: this.selectedAmount, // 添加金额
                        coins: this.getCoinsForAmount(this.selectedAmount) // 添加金币数量
                    });

                    if (verifyResult.success) {
                        // 关闭订单
                        await this.finishTransaction(paymentResult);
                        
                        // 使用从验证结果中获取金币数
                        const earnedCoins = verifyResult.data && verifyResult.data.coins ? 
                            verifyResult.data.coins : this.getCoinsForAmount(this.selectedAmount);
                        
                        this.$emit('confirm', {
                            status: 'success',
                            amount: this.selectedAmount,
                            coins: earnedCoins
                        });
                        
                        this.handleClose();
                    } else {
                        throw new Error('支付验证失败');
                    }
                }
            } catch (error) {
                console.error('充值失败:', error);
                // 确保error.message存在
                const errorMessage = error && error.message ? error.message : '充值失败，请稍后再试';
                uni.showToast({
                    title: errorMessage,
                    icon: 'none'
                });
            }
        },
        
        // 根据金额获取对应的商品ID
        getProductId(amount) {
            // 使用统一的方法获取商品ID映射
            const productMap = this.getProductIdMap();
            return productMap[amount] || productMap[1];
        },
        
        // 根据金额获取对应的金币数量
        getCoinsForAmount(amount) {
            const option = this.amountOptions.find(opt => opt.amount === amount);
            return option ? option.coins : 0;
        },

        // 关闭订单
        async finishTransaction(transaction) {
            try {
                if (!this.iapChannel) {
                    await this.initIapChannel();
                    
                    if (!this.iapChannel) {
                        throw new Error('未找到 Apple Pay 支付通道');
                    }
                }

                await new Promise((resolve, reject) => {
                    this.iapChannel.finishTransaction(transaction, () => {
                        resolve();
                    }, (error) => {
                        reject(error);
                    });
                });
            } catch (error) {
                console.error('关闭订单失败:', error);
                throw error;
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.payment-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
}

.payment-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
}

.payment-container {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background: #fff;
    display: flex;
    flex-direction: column;
    padding-top: env(safe-area-inset-top, 20px);
}

.payment-header {
    padding: 16px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f5f5f5;
    position: relative;
    min-height: 44px;

    .back-btn {
        padding: 8px;
        margin-right: 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #333;
        position: relative;
        z-index: 2;
        
        .back-text {
            font-size: 14px;
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .back-text:before {
            content: "<";
            font-size: 16px;
            margin-right: 2px;
        }
    }

    .user-info {
        flex: 1;
        text-align: center;
        position: absolute;
        left: 0;
        right: 0;
        z-index: 1;
    }

    .user-name {
        font-size: 18px;
        font-weight: 500;
        color: #333;
    }
}

.payment-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.balance-info {
    margin-bottom: 24px;
    
    .info-text {
        font-size: 16px;
        color: #333;
        text-align: center;
    }
}

.amount-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    padding: 0 12px;
}

.amount-item {
    background: #f8f8f8;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    cursor: pointer;
    position: relative;
    border: 1px solid transparent;

    &.active {
        background: #fff;
        border-color: #ff4d4f;
    }

    .coin-amount {
        margin-bottom: 8px;
        position: relative;
    }

    .coin-number {
        font-size: 20px;
        font-weight: bold;
        color: #333;
    }

    .coin-unit {
        font-size: 14px;
        color: #666;
        margin-left: 4px;
    }

    .coin-tag {
        position: absolute;
        top: -12px;
        right: -12px;
        background: #ff4d4f;
        color: #fff;
        font-size: 12px;
        padding: 2px 6px;
        border-radius: 10px;
    }

    .price {
        font-size: 14px;
        color: #666;
    }
}

.payment-footer {
    padding: 16px 20px;
    border-top: 1px solid #f5f5f5;
    padding-bottom: calc(20px + env(safe-area-inset-bottom, 0px));

    .notice {
        text-align: center;
        font-size: 12px;
        color: #999;
        margin-bottom: 12px;
    }

    .recharge-btn {
        width: 100%;
        height: 44px;
        background: #ff4d4f;
        color: #fff;
        border: none;
        border-radius: 22px;
        font-size: 16px;
        font-weight: 500;
    }
}
</style>