<template>
	<view>
		<h2 class="sectionWrap__title"><strong class="sectionWrap__title__deco">{{titlesx}}</strong></h2>
	</view>
</template>

<script>
	
	export default {
	  name: 'title',
	  data () {
	    return {
	    }
	  },
	  props: {
	    titlesx: {
	      type: String,
	      default: ''
	    },
	    color: {
	      type: String,
	      default: '#999'
	    },
		},
	}
</script>

<style>
	.sectionWrap__title__deco {
	    font-weight: lighter
	}
	.sectionWrap__title { 
	    color: #999;
	    font-weight: 400;
		font-size: 14px;
	    line-height: 30px;
	    text-align: center;
		position: relative;
		margin-top: 100px;
	}
 
	
	
	
	.sectionWrap__title:before {
	    background: #EFEFEF;
	    left: 20%
	}
	
	.sectionWrap__title:after {
	    background: #EFEFEF;
	    right: 20%
	}
	
 	 .sectionWrap__title:after,.sectionWrap__title:before {
	    content: "";
	    height: 1px;
	    margin-top: -1.5px;
	    position: absolute;
	    top: 50%;
	    width: 30px
	} 
	
	
	.sectionWrap__title__deco {
	    display: block;
	    margin: 0 auto;
	    position: relative;
		padding:0 15px;
	    
	}
	
/* 	.sectionWrap__title__deco:after,.sectionWrap__title__deco:before {
	    background-color: #999;
	    content: "";
	    height: 10px;
	    margin-top: -6px;
	    position: absolute;
	    top: 50%;
	    transform: rotate(45deg);
	    width: 10px
	} */
	
	.sectionWrap__title__deco:before {
	    left: -10px
	}
	
	.sectionWrap__title__deco:after {
	    right: -10px
	}
</style>