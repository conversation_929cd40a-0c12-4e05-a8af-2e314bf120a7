import MessageContent from "../wfc/messages/messageContent";
import CustomMessageContentType from "./customMessageContentType";

export default class CardCustomMessageContent extends MessageContent {
    cardId;
    constructor(cardId) {
        super(CustomMessageContentType.MESSAGE_CONTENT_TYPE_CARD);
        this.cardId = cardId;
    }

    // 会话列表显示对应会话时，会显示会话最后一条消息的摘要信息
	digest() {
	    return '名片消息';
	}
	
    // 发送消息时，会进行 encode 操作，实际发送的是 payload
    encode() {
        let payload = super.encode();
        payload.content = JSON.stringify(this.cardId);
        return payload;
    }

    // 收到消息时，会执行 decode 操作，从 payload 解析到具体的消息内容，encode 和 decode 的操作要对应起来
    decode(payload) {
        super.decode(payload);
        this.cardId = JSON.parse(payload.content);
    }

}