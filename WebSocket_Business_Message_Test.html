<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>野火IM WebSocket业务消息测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #4a90e2, #357abd);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }

        .main-content {
            display: flex;
            height: 600px;
        }

        .control-panel {
            width: 400px;
            padding: 30px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
        }

        .message-panel {
            flex: 1;
            padding: 30px;
            display: flex;
            flex-direction: column;
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: bold;
            text-align: center;
        }

        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4a90e2;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            flex: 1;
        }

        .btn-primary {
            background: #4a90e2;
            color: white;
        }

        .btn-primary:hover {
            background: #357abd;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .message-log {
            flex: 1;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.5;
        }

        .message-item {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 6px;
            border-left: 4px solid;
        }

        .message-item.sent {
            background: #e3f2fd;
            border-left-color: #2196f3;
        }

        .message-item.received {
            background: #f3e5f5;
            border-left-color: #9c27b0;
        }

        .message-item.info {
            background: #fff3e0;
            border-left-color: #ff9800;
        }

        .message-item.error {
            background: #ffebee;
            border-left-color: #f44336;
        }

        .message-time {
            color: #666;
            font-size: 11px;
            margin-right: 10px;
        }

        .examples {
            margin-top: 500px;
            background: #f8f9fa;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }

        .examples h3 {
            margin-top: 0;
            color: #333;
        }

        .example-box {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .example-box h4 {
            margin: 0 0 10px 0;
            color: #4a90e2;
        }

        .example-box pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            margin: 0;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>野火IM WebSocket业务消息测试</h1>
        <p>测试WebSocket到MQTT的消息发送功能</p>
    </div>

    <div class="main-content">
        <div class="control-panel">
            <div id="connectionStatus" class="status disconnected">
                未连接
            </div>

            <div class="form-group">
                <label for="websocketUrl">WebSocket地址:</label>
                <input type="text" id="websocketUrl" value="ws://**************:1884/mqtt"
                       placeholder="ws://host:port">
            </div>

            <div class="button-group">
                <button class="btn btn-primary" onclick="connect()">连接</button>
                <button class="btn btn-secondary" onclick="disconnect()">断开</button>
            </div>

            <div class="form-group">
                <label for="messageType">消息类型:</label>
                <select id="messageType">
                    <option value="send_text">发送文本消息</option>
                    <option value="send_message">完整消息格式</option>
                    <option value="ping">心跳检测</option>
                    <option value="status">状态查询</option>
                    <option value="echo">回声测试</option>
                </select>
            </div>

            <div class="form-group">
                <label for="sender">发送者ID:</label>
                <input type="text" id="sender" value="websocket_test_user"
                       placeholder="发送者用户ID">
            </div>

            <div class="form-group">
                <label for="target">目标ID:</label>
                <input type="text" id="target" value="target_user"
                       placeholder="目标用户ID">
            </div>

            <div class="form-group">
                <label for="conversationType">会话类型:</label>
                <select id="conversationType">
                    <option value="0">私聊</option>
                    <option value="1">群聊</option>
                    <option value="2">频道</option>
                </select>
            </div>

            <div class="form-group">
                <label for="messageContent">消息内容:</label>
                <textarea id="messageContent" rows="3"
                          placeholder="请输入消息内容">Hello WebSocket Business Message!</textarea>
            </div>

            <div class="button-group">
                <button class="btn btn-success" onclick="sendMessage()">发送消息</button>
                <button class="btn btn-warning" onclick="sendMockMessage()">发送Mock</button>
            </div>

            <div class="button-group">
                <button class="btn btn-secondary" onclick="clearMessages()">清空日志</button>
            </div>
        </div>

        <div class="message-panel">
            <h3 style="margin-top: 0;">消息日志</h3>
            <div id="messageLog" class="message-log"></div>
        </div>
    </div>

    <div class="examples">
        <h3>消息格式示例</h3>

        <div class="example-box">
            <h4>1. 发送文本消息（简化格式）</h4>
            <pre>{
  "type": "send_text",
  "sender": "user123",
  "target": "user456",
  "content": "Hello World!",
  "conversationType": 0
}</pre>
        </div>

        <div class="example-box">
            <h4>2. 完整消息格式</h4>
            <pre>{
  "type": "send_message",
  "sender": "user123",
  "target": "user456",
  "conversationType": 0,
  "content": "详细消息内容",
  "data": {
    "toUsers": ["user456", "user789"]
  },
  "timestamp": 1703123456789
}</pre>
        </div>

        <div class="example-box">
            <h4>3. 心跳检测</h4>
            <pre>{
  "type": "ping"
}</pre>
        </div>

        <div class="example-box">
            <h4>4. 状态查询</h4>
            <pre>{
  "type": "status"
}</pre>
        </div>
    </div>
</div>

<script>
    let websocket = null;
    let messageCount = 0;

    function connect() {
        const url = document.getElementById('websocketUrl').value;
        if (!url) {
            alert('请输入WebSocket地址');
            return;
        }

        updateStatus('connecting', '连接中...');
        addMessage('info', `连接到: ${url}`);

        try {
            websocket = new WebSocket(url);

            websocket.onopen = function (event) {
                updateStatus('connected', '已连接');
                addMessage('info', 'WebSocket连接成功');
            };

            websocket.onmessage = function (event) {
                try {
                    const data = JSON.parse(event.data);
                    addMessage('received', `收到消息: ${JSON.stringify(data, null, 2)}`);
                } catch (e) {
                    addMessage('received', `收到消息: ${event.data}`);
                }
            };

            websocket.onclose = function (event) {
                updateStatus('disconnected', '连接已断开');
                addMessage('info', `连接断开: ${event.code} - ${event.reason}`);
            };

            websocket.onerror = function (error) {
                updateStatus('disconnected', '连接错误');
                addMessage('error', `连接错误: ${error}`);
            };

        } catch (error) {
            updateStatus('disconnected', '连接失败');
            addMessage('error', `连接失败: ${error.message}`);
        }
    }

    function disconnect() {
        if (websocket) {
            websocket.close();
            websocket = null;
        }
    }

    function sendMessage() {
        if (!websocket || websocket.readyState !== WebSocket.OPEN) {
            alert('WebSocket未连接');
            return;
        }

        const messageType = document.getElementById('messageType').value;
        const sender = document.getElementById('sender').value;
        const target = document.getElementById('target').value;
        const conversationType = parseInt(document.getElementById('conversationType').value);
        const content = document.getElementById('messageContent').value;

        let message = {};

        switch (messageType) {
            case 'send_text':
                message = {
                    type: 'send_text',
                    sender: sender,
                    target: target,
                    content: content,
                    conversationType: conversationType,
                    timestamp: Date.now()
                };
                break;

            case 'send_message':
                message = {
                    type: 'send_message',
                    sender: sender,
                    target: target,
                    conversationType: conversationType,
                    content: content,
                    data: {
                        toUsers: target ? [target] : []
                    },
                    timestamp: Date.now()
                };
                break;

            case 'ping':
                message = {type: 'ping'};
                break;

            case 'status':
                message = {type: 'status'};
                break;

            case 'echo':
                message = {
                    type: 'echo',
                    content: content
                };
                break;

            default:
                alert('未知的消息类型');
                return;
        }

        const messageStr = JSON.stringify(message);
        websocket.send(messageStr);
        addMessage('sent', `发送消息: ${messageStr}`);
    }

    function sendMockMessage() {
        if (!websocket || websocket.readyState !== WebSocket.OPEN) {
            alert('WebSocket未连接');
            return;
        }

        const mockMessage = {
            type: 'send_text',
            sender: `mock_user_${Date.now() % 1000}`,
            target: 'mock_target',
            content: `Mock消息测试 - ${new Date().toLocaleTimeString()}`,
            conversationType: 0,
            timestamp: Date.now()
        };

        const messageStr = JSON.stringify(mockMessage);
        websocket.send(messageStr);
        addMessage('sent', `发送Mock消息: ${messageStr}`);
    }

    function updateStatus(type, text) {
        const statusElement = document.getElementById('connectionStatus');
        statusElement.className = `status ${type}`;
        statusElement.textContent = text;
    }

    function addMessage(type, content) {
        const logElement = document.getElementById('messageLog');
        const messageElement = document.createElement('div');
        messageElement.className = `message-item ${type}`;

        const time = new Date().toLocaleTimeString();
        messageElement.innerHTML = `
                <span class="message-time">[${time}]</span>
                <span class="message-content">${content}</span>
            `;

        logElement.appendChild(messageElement);
        logElement.scrollTop = logElement.scrollHeight;

        messageCount++;
    }

    function clearMessages() {
        document.getElementById('messageLog').innerHTML = '';
        messageCount = 0;
    }

    // 页面加载完成后自动连接
    window.onload = function () {
        addMessage('info', 'WebSocket业务消息测试页面已加载');
        addMessage('info', '点击"连接"按钮开始测试');
    };

    // 页面关闭时断开连接
    window.onbeforeunload = function () {
        if (websocket) {
            websocket.close();
        }
    };
</script>
</body>
</html> 
