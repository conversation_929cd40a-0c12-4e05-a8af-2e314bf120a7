<template>
  <view class="conversation-list" @scroll="onScroll">
    <view v-if="connectionStatusDesc || unread === undefined" style="text-align: center; padding: 5px 0">
      {{ connectionStatusDesc }}
    </view>
    <view v-if="isLoading" style="text-align: center; padding: 20px 0;">
      <image src="/static/image/loading.gif" style="width: 32px; height: 32px;"></image>
      <view style="margin-top: 10px; color: #999;">加载中...</view>
    </view>
    <view class="conversation-rq" style="padding: 0;"
          v-else-if="sharedConversationState.conversationInfoList && sharedConversationState.conversationInfoList.length>0">
      <uni-list :border="true" @scroll="onScroll">
        <view class="conversation-item"
              :id="'unread-conversation-'+index"
              @click="showConversation(conversationInfo)"
              :class="[conversationInfo.top ? 'conversationInfo-item-top' : '',
                              currentActiveConversation && conversationInfoKey(conversationInfo) === conversationInfoKey(currentActiveConversation) ? 'conversation-item-active' : '']"
              v-for="(conversationInfo,index) in sharedConversationState.conversationInfoList"
              :key="conversationInfoKey(conversationInfo)">
          <view class="rq-hine" v-if="index > 0" style="height: 0;"></view>
          <ConversationItemView :conversation-info="conversationInfo"
                                :default-avatar="getDefaultGroupAvatar(conversationInfo)"
                                @contextmenu.native.prevent="showConversationContextMenu($event, conversationInfo)"
                                @contextmenu="showConversationContextMenu($event, conversationInfo)"/>
        </view>
      </uni-list>
    </view>
    <view class="rqd" v-else>
      <image class="rqdimg" src="/static/image/icon/kxx.png"></image>
      <view class="rqda">
        你还没有消息
      </view>
      <view class="rqdb">
        快去和好友聊天吧
      </view>
    </view>
    <chunLei-popups v-model="showContextMenu" :popData="contextMenuItems" @tapPopup="onContextMenuItemSelect"
                    :x="contextMenuX" :y="contextMenuY" direction="column" theme="dark" :triangle="false" dynamic/>
    <main-action-menu ref="mainActionMenu"></main-action-menu>

    <!-- 通知权限请求弹窗 -->
    <view class="notification-permission-popup" v-if="false">
      <view class="popup-content">
        <view class="popup-icon">
          <image src="/static/image/notification/notification-icon.png" mode="aspectFit"></image>
        </view>
        <view class="popup-title">是否允许"地球岛"发送通知</view>
        <view class="popup-desc">包括消息推送、桌面图标角标、声音和振动提醒等，可在"设置"中修改。</view>
        <view class="popup-buttons">
          <button class="popup-btn cancel-btn" @tap="cancelNotificationPermission">拒绝</button>
          <button class="popup-btn confirm-btn" @tap="confirmNotificationPermission">始终允许</button>
        </view>
      </view>
    </view>

    <!-- 删除确认弹窗 -->
    <uni-popup ref="deleteConfirmPopup" type="dialog">
      <uni-popup-dialog
          type="warn"
          cancelText="取消"
          confirmText="删除"
          title="确认删除"
          content="删除后，将清空该群聊的消息记录"
          @confirm="confirmDeleteConversation"
          @close="cancelDeleteConversation">
      </uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import ConversationItemView from './ConversationItemView'
import store from '../../store'
import ConnectionStatus from '../../wfc/client/connectionStatus'
import {getItem, setItem} from '../util/storageHelper'
import organizationServerApi from '../../api/organizationServerApi'
import permision from '../../common/permission'
import topMessage from '../../common/topMessageView'

export default {
  name: 'ConversationListPage',
  data() {
    return {
      sharedConversationState: {
        conversationInfoList: []
      },
      sharedMiscState: store.state.misc,
      showContextMenu: false,
      contextMenuX: 0,
      contextMenuY: 0,
      contextMenuItems: [],
      momentNoticeTimer: null,
      showNotificationPermission: false,
      isLoading: true,
      currentActiveConversation: null,
      // 添加未读消息导航相关变量
      unreadConversations: [],
      currentUnreadIndex: -1,
      lastTabClickTime: 0,
      doubleClickTimeout: null,
      pendingDeleteConversationInfo: null, // 添加属性用于暂存待删除的会话信息
    }
  },
  onReady() {
    //动态修改状态栏的颜色
    uni.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#D3E4FE',
    })
    // 添加监听 refreshConversationList 事件
    uni.$on('refreshConversationList', this.refreshConversationList);

  },
  onLoad() {
    //直接加载会话列表
    this.loadConversationList()
  },
  onShow() {
    console.log('conversationList onShow')
    this.updateTabBarBadge()
    this.checkNotificationPermission()
  },

  onHide() {
    console.log('conversationList onHide')
    this.$refs.mainActionMenu.hide()
    this.showContextMenu = false;
  },

  onNavigationBarButtonTap(e) {
    console.log('onNavigationBarButtonTap', e.index)
    switch (e.index) {
      case 0:
        this.showContextMenu = false
        this.$refs.mainActionMenu.toggle()
        break
      case 1:
        this.$refs.mainActionMenu.hide()
        uni.navigateTo({
          url: '/pages/search/SearchPortalPage',
        })
        break
      default:
        break
    }
  },

  onTabItemTap(e) {
    // 处理底部标签栏的点击事件
    if (e.index === 0) { // 消息tab的索引
      const now = Date.now();
      // 判断是否是双击（300ms内的两次点击）
      if (now - this.lastTabClickTime < 300) {
        // 是双击事件
        this.handleDoubleClickOnMessageTab();
        clearTimeout(this.doubleClickTimeout);
        this.doubleClickTimeout = null;
      } else {
        // 单击事件，设置延时器判断是否会有第二次点击
        this.doubleClickTimeout = setTimeout(() => {
          // 单击操作（可选）
          this.doubleClickTimeout = null;
        }, 300);
      }
      this.lastTabClickTime = now;
    }
  },

  methods: {
    search(res) {
      uni.showToast({
        title: '搜索：' + res.value,
        icon: 'none',
      })
    },
    onsearch(res) {
      this.$refs.mainActionMenu.hide()
      uni.navigateTo({
        url: '/pages/search/SearchPortalPage',
      })
    },
    showConversation(conversationInfo) {
      // console.log(conversationInfo)
      store.setCurrentConversationInfo(conversationInfo)
      this.$go2ConversationPage()
    },

    removeConversation(conversationInfo) {
      store.removeConversation(conversationInfo.conversation)
    },

    conversationInfoKey(conversationInfo) {
      let conv = conversationInfo.conversation
      return conv.target + '-' + conv.type + '-' + conv.line
    },
    scrollActiveElementCenter() {
      let el = this.$el.getElementsByClassName('active')[0]
      el &&
      el.scrollIntoView({
        behavior: 'instant',
        block: 'center',
      })
    },

    onScroll() {
      // TODO
    },

    showConversationContextMenu(e, conversationInfo) {
      // 判断事件类型并获取坐标
      let clientX, clientY;
      if (e.touches && e.touches.length > 0) {
        // 触摸事件
        clientX = e.touches[0].clientX;
        clientY = e.touches[0].clientY;
      } else if (e.changedTouches && e.changedTouches.length > 0) {
        // 长按事件在某些情况下使用changedTouches
        clientX = e.changedTouches[0].clientX;
        clientY = e.changedTouches[0].clientY;
      } else {
        // 鼠标事件
        clientX = e.clientX;
        clientY = e.clientY;
      }

      this.contextMenuX = clientX;
      this.contextMenuY = clientY;
      this.contextMenuItems = [];
      this.currentActiveConversation = conversationInfo;

      this.contextMenuItems.push({
        title: conversationInfo.top ? '取消置顶' : '置顶',
        tag: 'top',
        conversationInfo: conversationInfo,
      })

      this.contextMenuItems.push({
        title: conversationInfo.isSilent ? '取消静音' : '静音',
        tag: 'silent',
        conversationInfo: conversationInfo,
      })

      this.contextMenuItems.push({
        title: '删除会话',
        tag: 'delete',
        conversationInfo: conversationInfo,
      })

      this.contextMenuItems.push({
        title:
            conversationInfo._unread === 0
                ? '标记为未读'
                : '标记为已读',
        tag: 'mark',
        conversationInfo: conversationInfo,
      })
      this.showContextMenu = true
    },

    onContextMenuItemSelect(t) {
      switch (t.tag) {
        case 'delete':
          store.removeConversation(t.conversationInfo.conversation)
          break
        case 'top':
          store.setConversationTop(
              t.conversationInfo.conversation,
              t.conversationInfo.top > 0 ? 0 : 1
          )
          break
        case 'silent':
          store.setConversationSilent(
              t.conversationInfo.conversation,
              !t.conversationInfo.isSilent
          )
          break
        case 'mark':
          let conversation = t.conversationInfo.conversation
          if (t.conversationInfo._unread === 0) {
            store.markConversationAsUnread(conversation, false)
          } else {
            store.clearConversationUnreadStatus(conversation)
          }
          break
        default:
          // uni.showToast({
          //   title: 'TODO ' + t.title,
          //   icon: 'none',
          // })
          break
      }

      this.currentActiveConversation = null
    },

    updateTabBarBadge() {
      let newValue = this.unread
      if (newValue > 0) {
        uni.setTabBarBadge({
          index: 0,
          text: '' + newValue,
        })
        plus.runtime.setBadgeNumber(newValue)
      } else {
        uni.removeTabBarBadge({
          index: 0,
        })
        plus.runtime.setBadgeNumber(0);
      }
    },

    getDefaultGroupAvatar(conversationInfo) {
      if (conversationInfo.conversation.type === 1) {
        // 1 表示群聊
        const name = conversationInfo.conversation.name || '群聊'
        const firstChar = name.charAt(0)
        return {
          type: 'text',
          content: firstChar,
          background: this.getRandomColor(name),
        }
      }
      return null
    },

    getRandomColor(str) {
      // 根据字符串生成固定的颜色，这样同一个群名会得到相同的颜色
      let hash = 0
      for (let i = 0; i < str.length; i++) {
        hash = str.charCodeAt(i) + ((hash << 5) - hash)
      }

      // 预定义一组好看的颜色
      const colors = [
        '#386BF6', // 蓝色
        '#F6A638', // 橙色
        '#38B2F6', // 浅蓝
        '#F64D38', // 红色
        '#9E38F6', // 紫色
        '#3AF638', // 绿色
      ]

      return colors[Math.abs(hash) % colors.length]
    },

    // 添加合并通知的方法
    mergeNotices(oldNotices, newNotices) {
      // 创建一个Map用于去重，key为通知的唯一标识
      const noticeMap = new Map()

      // 先添加旧的通知
      oldNotices.forEach(notice => {
        // 使用 momentId + type + fromUserId 作为唯一标识
        const key = `${notice.momentId}-${notice.type}-${notice.fromUserId}`
        noticeMap.set(key, notice)
      })

      // 添加新的通知，如果有相同的会覆盖旧的
      newNotices.forEach(notice => {
        const key = `${notice.momentId}-${notice.type}-${notice.fromUserId}`
        noticeMap.set(key, notice)
      })

      // 将Map转换回数组，并按时间倒序排序（最新的在前面）
      return Array.from(noticeMap.values()).sort((a, b) => {
        return new Date(b.createTime) - new Date(a.createTime)
      })
    },

    goToSearch() {
      this.$refs.mainActionMenu.hide()
      uni.navigateTo({
        url: '/pages/search/SearchPortalPage',
      })
    },

    refreshConversationList() {
      console.log('收到刷新会话列表事件');
      // 强制刷新会话列表
      if (store.state.conversation) {
        // 直接调用 store 的方法刷新会话列表
        if (typeof store._loadDefaultConversationList === 'function') {
          store._loadDefaultConversationList();
        } else {
          console.warn('_loadDefaultConversationList 方法不存在');
        }

        // 强制刷新视图
        this.$forceUpdate();
      }
    },


    // 添加加载会话列表方法
    loadConversationList() {
      console.log('开始加载会话列表')
      this.isLoading = true

      try {
        // 初始化store中的会话状态
        if (!store.state.conversation || !store.state.conversation.conversationInfoList) {
          store.init()
        }

        // 更新本地状态
        this.sharedConversationState = store.state.conversation

        // 如果store中已有会话列表数据
        if (this.sharedConversationState.conversationInfoList && this.sharedConversationState.conversationInfoList.length > 0) {
          console.log('从store中获取到会话列表，数量:', this.sharedConversationState.conversationInfoList.length)
        } else {
          console.log('store中没有会话列表数据，尝试从WFC获取')
          // 从WFC获取会话列表
          store._loadDefaultConversationList();
          // const conversationList = wfc.getConversationList([0, 1, 3], [0])
          // console.log('从WFC获取到会话列表，数量:', conversationList.length)

          // 如果store.state.conversation已初始化，则更新其conversationInfoList
          // if (store.state.conversation) {
          //     store.state.conversation.conversationInfoList = conversationList
          //     this.sharedConversationState = store.state.conversation
          // } else {
          //     // 如果store未初始化，则直接更新本地状态
          //     this.sharedConversationState.conversationInfoList = conversationList
          // }
        }
      } catch (error) {
        console.error('加载会话列表失败:', error)
        uni.showToast({
          title: '加载会话列表失败',
          icon: 'none'
        })
      } finally {
        this.isLoading = false
      }
    },

    // 处理消息标签双击事件
    handleDoubleClickOnMessageTab() {
      // 找出所有有未读消息的会话
      this.findUnreadConversations();

      // 如果没有未读消息，不做任何操作并直接返回
      if (this.unreadConversations.length === 0) {
        return;
      }

      // 增加当前未读索引，如果到达末尾则回到开始
      this.currentUnreadIndex = (this.currentUnreadIndex + 1) % this.unreadConversations.length;

      // 滚动到当前索引指向的未读消息
      this.scrollToUnreadConversation(this.currentUnreadIndex);
    },

    // 查找所有有未读消息的会话
    findUnreadConversations() {
      this.unreadConversations = [];

      // 只有在有会话列表的情况下进行查找
      if (this.sharedConversationState.conversationInfoList && this.sharedConversationState.conversationInfoList.length > 0) {
        // 遍历所有会话，找出有未读消息的
        this.sharedConversationState.conversationInfoList.forEach((conversationInfo, index) => {
          // 检查会话是否有未读消息
          if (conversationInfo.unreadCount && conversationInfo.unreadCount.unread > 0) {
            this.unreadConversations.push({
              index, // 在原始列表中的索引
              conversationInfo // 会话信息对象
            });
          }
        });
      }

      // 如果是首次执行，重置当前索引
      if (this.currentUnreadIndex === -1 && this.unreadConversations.length > 0) {
        this.currentUnreadIndex = -1; // 设为-1，这样第一次调用会显示第一条
      }
    },

    // 滚动到指定的未读会话
    scrollToUnreadConversation(unreadIndex) {
      if (unreadIndex < 0 || unreadIndex >= this.unreadConversations.length) {
        return;
      }

      const targetConversation = this.unreadConversations[unreadIndex];
      const listIndex = targetConversation.index;

      // 移除高亮显示
      // this.currentActiveConversation = targetConversation.conversationInfo;

      console.log('尝试滚动到未读消息，索引:', listIndex);

      // 方法2: 查找并直接操作滚动容器
      setTimeout(() => {
        const query = uni.createSelectorQuery().in(this);

        // 选择会话列表容器和目标会话元素
        query.select('.conversation-list').boundingClientRect();
        query.selectAll('.conversation-item').boundingClientRect();

        query.exec(res => {
          if (res && res[0] && res[1] && res[1].length > listIndex) {
            const scrollContainer = res[0];
            const targetElement = res[1][listIndex];

            console.log('滚动容器:', scrollContainer);
            console.log('目标元素:', targetElement);

            // 计算目标元素相对于容器的偏移量
            // 注意：在某些环境中，元素的top是相对于窗口的，需要减去容器的top
            const relativeTop = targetElement.top - scrollContainer.top;

            console.log('相对偏移量:', relativeTop);

            // 直接设置容器的scrollTop (方法2-1)

            // 使用uni-app的滚动方法 (方法2-2)
            uni.pageScrollTo({
              scrollTop: relativeTop,
              duration: 300,
              success: () => console.log('pageScrollTo成功执行'),
              fail: err => console.error('pageScrollTo执行失败:', err)
            });
          } else {
            console.error('查询元素失败', res);
          }
        });
      }, 200);

      // 方法3: 保存元素ID并使用scrollIntoViewById
      const elementId = `unread-conversation-${listIndex}`;

      // 使用$nextTick确保DOM已更新
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this);
        query.select(`#${elementId}`).boundingClientRect(data => {
          if (data) {
            console.log('通过ID找到的元素:', data);
            // 使用uni.createSelectorQuery的方法滚动
            uni.pageScrollTo({
              selector: `#${elementId}`,
              duration: 300
            });
          }
        }).exec();
      });

      // 移除未读消息序号提示
      // uni.showToast({
      //     title: `第${unreadIndex + 1}/${this.unreadConversations.length}条未读`,
      //     icon: 'none'
      // });
    },

    // 添加删除确认弹窗的处理方法
    confirmDeleteConversation() {
      if (this.pendingDeleteConversationInfo) {
        // 执行实际的删除会话操作、
        store.removeConversation(this.pendingDeleteConversationInfo.conversation)
        // wfc.removeConversation(this.pendingDeleteConversationInfo.conversation, true, { // Use wfc to remove conversation
        //     onSuccess: () => {
        //         console.log('会话删除成功');
        //         this.pendingDeleteConversationInfo = null; // 清空暂存
        //     },
        //     onFailure: (errorCode) => {
        //         console.error('会话删除失败:', errorCode);
        //         this.pendingDeleteConversationInfo = null; // 清空暂存
        //         uni.showToast({
        //             title: '删除失败',
        //             icon: 'none'
        //         });
        //     },
        // });
      }
    },

    cancelDeleteConversation() {
      // 用户点击取消，清空暂存的会话信息
      this.pendingDeleteConversationInfo = null;
    },
  },
  activated() {
    this.scrollActiveElementCenter()
  },

  computed: {
    connectionStatusDesc() {
      let desc = ''
      switch (this.sharedMiscState.connectionStatus) {
        case ConnectionStatus.ConnectionStatusConnecting:
          desc = '正在连接...'
          break
        case ConnectionStatus.ConnectionStatusReceiveing:
          desc = '正在同步...'
          break
        case ConnectionStatus.ConnectionStatusConnected:
          organizationServerApi
              .login()
              .then((r) => console.log('org login result', r))
              .catch((reason) =>
                  console.log('org login fail ', reason)
              )
          desc = ''
          break
        case ConnectionStatus.ConnectionStatusUnconnected:
          desc = '连接失败'
          break
      }
      return desc
    },
    unread() {
      let count = 0
      this.sharedConversationState.conversationInfoList.forEach(
          (info) => {
            if (info.isSilent) {
              return
            }
            let unreadCount = info.unreadCount
            count += unreadCount.unread
          }
      )
      return count
    },
  },

  watch: {
    // 监听上下文菜单的显示状态
    showContextMenu(newVal) {
      // 当菜单关闭时，重置当前活动会话
      if (!newVal) {
        this.currentActiveConversation = null;
      }
    }
  },

  updated() {
    this.updateTabBarBadge()
  },

  components: {
    ConversationItemView,
  },

  mounted() {
    // console.log('mounted xxx')
    // 启动定时器检查新动态
    // this.momentNoticeTimer = setInterval(() => {
    //     this.checkMomentNotice()
    // }, 3000000) // 每30秒执行一次
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    // if (this.momentNoticeTimer) {
    //     clearInterval(this.momentNoticeTimer)
    //     this.momentNoticeTimer = null
    // }
    // 移除事件监听
    uni.$off('refreshConversationList');
  }
}
</script>

<style lang="css" scoped>
.conversation-list {
  height: var(--page-full-height-without-header-and-tabbar);
  overflow: auto;
  background: linear-gradient(#d3e4fe, #ffffff);
}

.rqd {
  width: 100%;
  margin-top: 120px;
  text-align: center;
}

.rqd .rqda {
  color: #000;
  text-align: center;
  font-family: MiSans;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: 28px;
  margin-top: 15px;
}

.rqd .rqdb {
  color: #999;
  text-align: center;
  font-family: MiSans;
  font-size: 14px;
  font-style: normal;
  font-weight: 330;
  line-height: 22px;
  margin-top: 10px;
}

.rqdimg {
  width: 120px;
  height: 112px;
  text-align: center;
  margin: 0 auto;
}

.conversation-list .top {
  /* background-color: #f1f1f1; */
}

.conversationInfo-item-top {
  background-color: #efefef;
}

.conversation-item {
  position: relative;
}

/*     .conversation-item::after {
        content: "";
        position: absolute;
        left: 65px;
        right: 0;
        bottom: 0;
        border-bottom: 1px solid #f4f4f4;
    } */

.default-group-avatar {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #fff;
  font-weight: 500;
}

.search-bar,
.search-input-container,
.search-placeholder,
.search-container,
.search-input {
  display: none;
}

/* 通知权限弹窗样式 */
.notification-permission-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: center; /* 恢复为垂直居中 */
  justify-content: center;
  padding-top: 0; /* 移除顶部填充 */
}

.popup-content {
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx 40rpx 30rpx; /* 调整顶部内边距 */
  margin-bottom: 0;
}

.popup-icon {
  display: none; /* 直接移除图标，减少上部留白 */
}

.popup-title {
  font-size: 36rpx;
  font-weight: 500;
  text-align: center;
  margin-bottom: 15rpx;
  margin-top: 0; /* 移除标题顶部边距 */
  color: #333;
}

.popup-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx; /* 减少描述下方间距 */
  line-height: 1.5;
}

.popup-buttons {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.popup-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 32rpx;
  border: none;
}

.cancel-btn {
  background-color: #ffffff; /* 改为白色背景 */
  color: #333333; /* 文字颜色加深 */
  border: 1px solid #cccccc; /* 添加边框 */
  font-weight: 500; /* 加粗文字 */
}

.confirm-btn {
  background-color: #386bf6;
  color: #fff;
}

.conversation-item-active {
  background-color: rgba(0, 0, 0, 0.05);
}
</style>