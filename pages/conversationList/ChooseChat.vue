<template>
    <CustomHeader title="选择一个聊天" backgroundColor="#f8f8f8">
        <template v-slot:right>
            <!-- <text style="font-size: 18px;">多选</text> -->
        </template>
    </CustomHeader>
    <view class="chat-container">
        <!-- 顶部搜索栏（带Q图标） -->
        <view class="search-bar">
            <view class="search-icon"> <i class="icon-ion-android-search" style="font-size: 24px"></i></view>
            <input class="search-input" placeholder="搜索" placeholder-class="placeholder-style" />
        </view>

        <!-- 最近转发（横向滚动） -->
        <view class="section" v-if="forwardList.length!=0">
            <text class="section-title">最近转发</text>
            <scroll-view scroll-x class="forward-scroll">
                <view v-for="(item, index) in forwardList" :key="index" class="forward-item">
                    <image class="avatar" :src="item.avatar" />
                    <text class="name">{{ item.name }}</text>
                </view>
            </scroll-view>
        </view>

        <!-- 最近聊天（带创建按钮） -->
        <view class="section">
            <view class="section-header">
                <text class="section-title">最近聊天</text>
                <text class="create-btn" @click="createChat">+ 创建新的聊天</text>
            </view>

            <view class="chat-list">
                <view v-for="(chat, index) in chatList" :key="index" class="chat-item" @click="handleChatClick(chat)">
                    <image class="avatar" :src="getPortrait(chat)" @error="imgUrlAlt" />
                    <view class="chat-content">
                        <text class="chat-name">{{ chat.conversation._target.name }}</text>
                        <!-- <text class="chat-message">{{ chat.lastMessage }}</text> -->
                    </view>
                </view>
            </view>
        </view>
        <!-- 底部弹窗 -->
        <uni-popup ref="popup" type="bottom">
            <view class="popup-content">
                <text class="popup-title">发送给:</text>
                <view class="popup-item" hover-class="popup-item-hover">
                    <view class="chat-item">
                        <image class="avatar" :src="getPortrait(chatitem)" @error="imgUrlAlt" />
                        <view class="chat-content">
                            <text class="chat-name">{{ chatitem.conversation._target.name }}</text>
                            <!-- <text class="chat-message">{{ chat.lastMessage }}</text> -->
                        </view>
                    </view>
                    <view><i class="icon-ion-ios-arrow-right"></i></view>
                </view>
                <view class="comment">
                    <view class="comment-input" :class="{ 'input-focused': isInputFocused }">
                        <input v-model="commentText" placeholder="请输入评论内容" @focus="onFocus" @blur="isInputFocused = false" />
                    </view>
                    <view class="send-button" v-if="isInputFocused" @click="handleSend">发送</view>
                </view>
                <view class="button-container">
                    <view class="cancel-button" @click="handleCancel">取消</view>
                    <view class="confirm-button" @click="handleConfirm">发送</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import store from '../../store'
import Config from '../../config'
import wfc from '../../wfc/client/wfc'
import TextMessageContent from '../../wfc/messages/textMessageContent'
import LinkMessageContent from '../../wfc/messages/linkMessageContent'
import CardCustomMessageContent from '../../wfc_custom_message/cardCustomMessageContent'
import CustomMessageConfig from '../../wfc_custom_message/customMessageConfig'

export default {
    components: {
        CustomHeader,
    },
    data() {
        return {
            sharedConversationState: store.state.conversation,
            forwardList: [],

            // 最近聊天数据
            chatList: [],
            chatitem: null,
            commentText: '',
            isInputFocused: false,
            // 分享数据
            shareData: null,
        }
    },
    onLoad(options) {
        console.log('sharedConversationState', this.sharedConversationState)
        this.chatList = this.sharedConversationState.conversationInfoList
        
        // 获取分享数据
        this.getShareData()
    },
    onUnload() {
        // 页面卸载时清除分享数据
        store.clearShareData();
    },
    computed: {
        filteredChats() {
            return this.chats.filter((chat) =>
                chat.name.includes(this.searchQuery)
            )
        },
    },
    methods: {
        // 获取分享数据
        getShareData() {
            // 从store中获取分享数据
            this.shareData = store.getShareData();
            console.log('从store获取分享数据:', this.shareData);
        },
        
        onFocus(e) {
            this.isInputFocused = true
            // 微信小程序可通过 createSelectorQuery 获取节点位置
            const query = uni.createSelectorQuery().in(this)
            query
                .select('input')
                .boundingClientRect((res) => {
                    this.scrollTop = res.top - 50 // 带偏移量的滚动
                })
                .exec()
        },
        createChat() {
            // uni.navigateTo({ url: '/pages/chat/create' }) // 示例跳转逻辑
        },
        imgUrlAlt(e) {
            e.target.src = Config.DEFAULT_PORTRAIT_URL
        },
        getPortrait(e) {
            const conversation = e.conversation
            return conversation._target?.portrait || Config.DEFAULT_PORTRAIT_URL
        },
        handleChatClick(chat) {
            this.chatitem = chat
            this.$refs.popup.open()
        },
        
        // 处理取消按钮
        handleCancel() {
            this.$refs.popup.close()
            this.commentText = ''
            this.isInputFocused = false
        },
        
        // 处理确认发送按钮
        handleConfirm() {
            this.handleSend()
        },
        
        // 处理发送逻辑
        handleSend() {
            if (!this.chatitem) {
                uni.showToast({
                    title: '请选择聊天对象',
                    icon: 'none'
                })
                return
            }
            
            if (!this.shareData) {
                uni.showToast({
                    title: '没有可分享的内容',
                    icon: 'none'
                })
                return
            }
            
            try {
                const conversation = this.chatitem.conversation
                
                // 如果有附加评论，先发送评论
                if (this.commentText.trim()) {
                    const commentContent = new TextMessageContent(`💬 ${this.commentText.trim()}`)
                    wfc.sendConversationMessage(conversation, commentContent)
                }
                
                // 发送链接卡片消息
                if (this.shareData.topicId) {
                    // 分享帖子 - 使用链接卡片
                    const linkContent = new LinkMessageContent()
                    linkContent.title = this.shareData.title || '分享帖子'
                    linkContent.contentDigest = this.shareData.content ? 
                        (this.shareData.content.length > 100 ? 
                            this.shareData.content.substring(0, 100) + '...' : 
                            this.shareData.content) : 
                        '点击查看详情'
                    linkContent.url = `https://web.ykjrhl.com/share/topic.html?topicId=${this.shareData.topicId}`
                    linkContent.thumbnail = this.shareData.thumbnail || this.shareData.avatar || '/static/logo.png'
                    
                    wfc.sendConversationMessage(conversation, linkContent)
                } else if (this.shareData.id) {
                    // 分享名片 - 使用自定义名片消息
                    // 注册自定义消息类型（如未全局注册可重复调用无副作用）
                    CustomMessageConfig.registerCustomMessages();
                    const cardContent = new CardCustomMessageContent(this.shareData.id)
                    wfc.sendConversationMessage(conversation, cardContent)
                } else {
                    // 兜底：发送文本消息
                    const textContent = new TextMessageContent('分享内容')
                    wfc.sendConversationMessage(conversation, textContent)
                }
                
                uni.showToast({
                    title: '发送成功',
                    icon: 'success'
                })
                
                // 关闭弹窗并返回
                this.$refs.popup.close()
                setTimeout(() => {
                    uni.navigateBack()
                }, 1000)
                
            } catch (error) {
                console.error('发送失败:', error)
                uni.showToast({
                    title: '发送失败',
                    icon: 'none'
                })
            }
        },
    },
}
</script>
<style lang="scss">
.button-container {
    display: flex;
    justify-content: space-between; /* 或者使用其他合适的布局方式 */
}

.cancel-button,
.confirm-button {
    flex: 1;
    margin: 0 5px; /* 根据需要调整间距 */
    margin-top: 20px;
    text-align: center;
    padding: 5px 5px; /* 添加内边距 */
    border: none; /* 去掉默认边框 */
    border-radius: 4px; /* 添加圆角 */
    transition: background-color 0.3s; /* 添加过渡效果 */
}

.cancel-button {
    background-color: #f4f4f4; /* 设置取消按钮背景颜色 */
    color: #333; /* 设置取消按钮文字颜色 */
}

.cancel-button:hover {
    background-color: #ddd; /* 设置取消按钮悬停背景颜色 */
}

.confirm-button {
    background-color: #007aff; /* 设置确定按钮背景颜色 */
    color: #fff; /* 设置确定按钮文字颜色 */
}

.confirm-button:hover {
    background-color: #0056b3; /* 设置确定按钮悬停背景颜色 */
}

.popup-content {
    background: #f8f8f8;
    padding: 20px 20px;
    .popup-title {
        // font-size: 14px;
        font-weight: 500;
    }
    .popup-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20px;
        image {
            width: 80rpx;
            height: 80rpx;
        }
        .chat-item {
            display: flex;
            align-items: center;
        }
        .chat-content {
            margin-left: 10px;
        }
    }
}
.chat-container {
    background: #f8f8f8;
    min-height: 100vh;
    padding: 20rpx 30rpx;
}

/* 搜索栏样式 */
.search-bar {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 8rpx;
    padding: 20rpx;
    margin-bottom: 30rpx;
    .search-icon {
        color: #999;
        font-weight: bold;
        margin-right: 15rpx;
    }
    .search-input {
        flex: 1;
        font-size: 28rpx;
    }
    .placeholder-style {
        color: #ccc;
    }
}

/* 公共区块样式 */
.section {
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    .section-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
    }
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;
        .create-btn {
            color: #007aff;
            font-size: 28rpx;
        }
    }
}

/* 最近转发横向滚动 */
.forward-scroll {
    white-space: nowrap;
    margin-top: 20rpx;
    .forward-item {
        display: inline-block;
        width: 100rpx;
        margin-right: 30rpx;
        text-align: center;
        .avatar {
            width: 100rpx;
            height: 100rpx;
            border-radius: 16rpx;
        }
        .name {
            display: block;
            font-size: 24rpx;
            color: #666;
            margin-top: 10rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}

/* 聊天列表样式 */
.chat-list {
    .chat-item {
        display: flex;
        padding: 30rpx 0;
        border-bottom: 1rpx solid #eee;
        align-items: center;
        &:last-child {
            border: none;
        }

        .avatar {
            width: 80rpx;
            height: 80rpx;
            border-radius: 8rpx;
            margin-right: 20rpx;
        }

        .chat-content {
            .chat-name {
                font-size: 34rpx;
                color: #333;
                max-width: 500rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}
.comment {
    // background-color: #ffffff; /* 整体背景颜色 */
    // padding: 15px; /* 添加内边距 */
    border-radius: 10px; /* 圆角边框 */
    // box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
    display: flex; /* 使用flex布局 */
    align-items: center; /* 垂直居中 */
    margin-top: 20px;
}

.comment-input {
    transition: width 0.3s; /* 添加过渡效果 */
    flex: 1; /* 使输入框占据剩余空间 */
    border: 1px solid #ccc; /* 添加边框 */
    border-radius: 10px; /* 圆角边框 */
    // padding: 10px; /* 内边距 */
    padding: 5px 10px; /* 内边距 */
    background-color: #f9f9f9; /* 输入框背景颜色 */
    font-size: 16px; /* 字体大小 */
    margin-bottom: 20px; /* 添加底部边距以偏移输入框 */
}

.comment-input.input-focused {
    border-color: #007aff; /* 聚焦时边框颜色 */
    box-shadow: 0 0 5px rgba(0, 122, 255, 0.5); /* 聚焦时阴影效果 */
}

.send-button {
    margin-left: 15px; /* 添加左边距 */
    cursor: pointer; /* 添加鼠标悬停效果 */
    background-color: #007aff; /* 设置发送按钮背景颜色 */
    color: #fff; /* 设置文字颜色 */
    border-radius: 10px; /* 圆角边框 */
    padding: 5px 10px; /* 内边距 */
    transition: background-color 0.3s; /* 添加过渡效果 */
    font-size: 16px; /* 字体大小 */
}

.send-button:hover {
    background-color: #0056b3; /* 悬停时背景颜色 */
}
</style>