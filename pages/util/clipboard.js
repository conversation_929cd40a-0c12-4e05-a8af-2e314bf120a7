// import { Blob } from 'blob-polyfill';

export const copyToClipboard = async (content, type = 'text') => {
    try {
        // #ifdef H5
        if (type === 'text') {
            await navigator.clipboard.writeText(content);
        } else if (type === 'image') {
            try {
                // 现代浏览器支持 clipboard API
                if (navigator.clipboard && navigator.clipboard.write) {
                    const response = await fetch(content);
                    const blob = await response.blob();
                    await navigator.clipboard.write([
                        new window.ClipboardItem({
                            [blob.type]: blob
                        })
                    ]);
                } else {
                    // 降级处理：使用传统方法
                    const img = document.createElement('img');
                    img.src = content;
                    document.body.appendChild(img);
                    const selection = window.getSelection();
                    const range = document.createRange();
                    range.selectNode(img);
                    selection.removeAllRanges();
                    selection.addRange(range);
                    document.execCommand('copy');
                    document.body.removeChild(img);
                }
            } catch (err) {
                console.error('复制图片失败', err);
            }
        }
        // #endif

    } catch (error) {
        console.error('复制失败:', error);
    }
};

export const copyImg = (imgUrl) => {
    return copyToClipboard(imgUrl, 'image');
};

export const copyText = (text) => {
    return copyToClipboard(text, 'text');
};

