import wfc from '../../wfc/client/wfc';
import store from '../../store';

// 默认分组名称
const DEFAULT_GROUP_NAME = '默认分组';

// 用户设置的作用域和键名
const USER_SETTING_SCOPE = 'friendGroup';
const USER_SETTING_KEY = 'groups';

/**
 * 获取所有好友分组
 * @returns {Object} 分组及其好友列表
 */
export function getFriendGroups() {
    // 获取所有好友
    const friendIds = wfc.getMyFriendList(true);
    const friendInfos = wfc.getUserInfos(friendIds, '');
    
    // 获取分组信息
    const groupsStr = wfc.getUserSetting(USER_SETTING_SCOPE, USER_SETTING_KEY);
    let groupNames = [];
    
    try {
        if (groupsStr) {
            groupNames = JSON.parse(groupsStr);
        }
    } catch (e) {
        console.error('解析好友分组信息失败', e);
    }
    
    // 确保默认分组存在
    if (!groupNames.includes(DEFAULT_GROUP_NAME)) {
        groupNames.unshift(DEFAULT_GROUP_NAME);
    }
    
    // 分组映射
    const groups = {};
    groupNames.forEach(name => {
        groups[name] = [];
    });
    
    // 遍历所有好友，按分组整理
    friendInfos.forEach(friend => {
        const userId = friend.uid;
        const settingKey = `friend_group_${userId}`;
        let groupName = wfc.getUserSetting(USER_SETTING_SCOPE, settingKey) || DEFAULT_GROUP_NAME;
        
        // 确保分组存在
        if (!groups[groupName]) {
            groupName = DEFAULT_GROUP_NAME;
        }
        
        // 将好友添加到对应分组
        groups[groupName].push(friend);
    });
    
    return groups;
}

/**
 * 创建新分组
 * @param {string} groupName 分组名称
 * @param {Function} successCallback 成功回调
 * @param {Function} errorCallback 失败回调
 * @returns {boolean} 是否创建成功（验证通过）
 */
export function createFriendGroup(groupName, successCallback, errorCallback) {
    if (!groupName || groupName === DEFAULT_GROUP_NAME) {
        return false;
    }
    
    // 获取当前所有分组
    const groupsStr = wfc.getUserSetting(USER_SETTING_SCOPE, USER_SETTING_KEY);
    let groupNames = [];
    
    try {
        if (groupsStr) {
            groupNames = JSON.parse(groupsStr);
        }
    } catch (e) {
        console.error('解析好友分组信息失败', e);
    }
    
    // 检查分组是否已存在
    if (groupNames.includes(groupName)) {
        return false;
    }
    
    // 添加新分组
    groupNames.push(groupName);
    
    // 保存分组信息
    wfc.setUserSetting(USER_SETTING_SCOPE, USER_SETTING_KEY, JSON.stringify(groupNames), 
        () => {
            console.log('创建分组成功');
            if (typeof successCallback === 'function') {
                successCallback();
            }
        }, 
        (errorCode) => {
            console.error('创建分组失败', errorCode);
            if (typeof errorCallback === 'function') {
                errorCallback(errorCode);
            }
        }
    );
    
    return true;
}

/**
 * 删除分组
 * @param {string} groupName 要删除的分组名称
 * @param {Function} successCallback 成功回调
 * @param {Function} errorCallback 失败回调
 * @returns {boolean} 是否删除成功（验证通过）
 */
export function deleteFriendGroup(groupName, successCallback, errorCallback) {
    if (!groupName || groupName === DEFAULT_GROUP_NAME) {
        if (typeof errorCallback === 'function') {
            errorCallback(new Error('不能删除默认分组'));
        }
        return false;
    }
    
    try {
        // 获取当前所有分组
        const groupsStr = wfc.getUserSetting(USER_SETTING_SCOPE, USER_SETTING_KEY);
        let groupNames = [];
        
        try {
            if (groupsStr) {
                groupNames = JSON.parse(groupsStr);
            }
        } catch (e) {
            console.error('解析好友分组信息失败', e);
            if (typeof errorCallback === 'function') {
                errorCallback(e);
            }
            return false;
        }
        
        // 检查分组是否存在
        const index = groupNames.indexOf(groupName);
        if (index === -1) {
            if (typeof errorCallback === 'function') {
                errorCallback(new Error('分组不存在'));
            }
            return false;
        }
        
        // 先获取该分组下的所有好友
        const groups = getFriendGroups();
        const friendsInGroup = groups[groupName] || [];
        
        // 删除分组
        groupNames.splice(index, 1);
        
        // 保存分组信息
        wfc.setUserSetting(USER_SETTING_SCOPE, USER_SETTING_KEY, JSON.stringify(groupNames), 
            () => {
                console.log('删除分组成功');
                
                // 立即调用成功回调，不等待好友移动完成
                if (typeof successCallback === 'function') {
                    successCallback();
                }
                
                // 将该分组的好友移动到默认分组作为后台操作
                if (friendsInGroup && friendsInGroup.length > 0) {
                    console.log(`后台处理：将 ${groupName} 分组下的 ${friendsInGroup.length} 个好友移至默认分组`);
                    
                    // 移动每个好友
                    for (let i = 0; i < friendsInGroup.length; i++) {
                        const friend = friendsInGroup[i];
                        if (friend && friend.uid) {
                            // 单独处理每个好友的移动
                            changeFriendGroup(
                                friend.uid, 
                                DEFAULT_GROUP_NAME, 
                                () => {
                                    console.log(`好友 ${friend.displayName || friend.uid} 已移至默认分组`);
                                },
                                (error) => {
                                    console.error(`好友 ${friend.displayName || friend.uid} 移动失败: ${error}`);
                                    // 失败不影响整体流程
                                }
                            );
                        }
                    }
                } else {
                    console.log(`${groupName} 分组下没有好友，无需移动`);
                }
            }, 
            (errorCode) => {
                console.error('删除分组失败', errorCode);
                if (typeof errorCallback === 'function') {
                    errorCallback(errorCode);
                }
            }
        );
        
        return true;
    } catch (error) {
        console.error('删除分组过程中发生错误:', error);
        if (typeof errorCallback === 'function') {
            errorCallback(error);
        }
        return false;
    }
}

/**
 * 重命名分组
 * @param {string} oldName 原分组名称
 * @param {string} newName 新分组名称
 * @param {Function} successCallback 成功回调
 * @param {Function} errorCallback 失败回调
 * @returns {boolean} 是否重命名成功（验证通过）
 */
export function renameFriendGroup(oldName, newName, successCallback, errorCallback) {
    if (!oldName || !newName || oldName === DEFAULT_GROUP_NAME || newName === DEFAULT_GROUP_NAME) {
        return false;
    }
    
    // 获取当前所有分组
    const groupsStr = wfc.getUserSetting(USER_SETTING_SCOPE, USER_SETTING_KEY);
    let groupNames = [];
    
    try {
        if (groupsStr) {
            groupNames = JSON.parse(groupsStr);
        }
    } catch (e) {
        console.error('解析好友分组信息失败', e);
    }
    
    // 检查原分组是否存在
    const oldIndex = groupNames.indexOf(oldName);
    if (oldIndex === -1) {
        return false;
    }
    
    // 检查新分组名称是否已存在
    if (groupNames.includes(newName)) {
        return false;
    }
    
    // 修改分组名称
    groupNames[oldIndex] = newName;
    
    // 保存分组信息
    wfc.setUserSetting(USER_SETTING_SCOPE, USER_SETTING_KEY, JSON.stringify(groupNames), 
        () => {
            console.log('重命名分组成功');
            
            // 更新该分组下所有好友的分组信息
            const groups = getFriendGroups();
            if (groups[oldName]) {
                let movePromises = [];
                
                // 移动每个好友
                groups[oldName].forEach(friend => {
                    // 使用Promise来跟踪移动操作
                    const promise = new Promise((resolve) => {
                        changeFriendGroup(friend.uid, newName, resolve);
                    });
                    movePromises.push(promise);
                });
                
                // 所有移动完成后调用成功回调
                Promise.all(movePromises).then(() => {
                    if (typeof successCallback === 'function') {
                        successCallback();
                    }
                });
            } else {
                if (typeof successCallback === 'function') {
                    successCallback();
                }
            }
        }, 
        (errorCode) => {
            console.error('重命名分组失败', errorCode);
            if (typeof errorCallback === 'function') {
                errorCallback(errorCode);
            }
        }
    );
    
    return true;
}

/**
 * 修改好友分组
 * @param {string} userId 好友用户ID
 * @param {string} groupName 分组名称
 * @param {Function} successCallback 成功回调
 * @param {Function} errorCallback 失败回调
 * @returns {boolean} 是否修改成功（验证通过）
 */
export function changeFriendGroup(userId, groupName, successCallback, errorCallback) {
    if (!userId) {
        return false;
    }
    
    // 使用用户设置存储好友分组信息
    const settingKey = `friend_group_${userId}`;
    
    wfc.setUserSetting(USER_SETTING_SCOPE, settingKey, groupName, 
        () => {
            console.log('修改好友分组成功');
            if (typeof successCallback === 'function') {
                successCallback();
            }
        }, 
        (errorCode) => {
            console.error('修改好友分组失败', errorCode);
            if (typeof errorCallback === 'function') {
                errorCallback(errorCode);
            }
        }
    );
    
    return true;
}

/**
 * 获取好友所在分组
 * @param {string} userId 好友用户ID
 * @returns {string} 分组名称
 */
export function getFriendGroup(userId) {
    if (!userId) {
        return DEFAULT_GROUP_NAME;
    }
    
    // 使用用户设置存储好友分组信息
    const settingKey = `friend_group_${userId}`;
    const groupName = wfc.getUserSetting(USER_SETTING_SCOPE, settingKey);
    
    if (groupName) {
        return groupName;
    }
    
    return DEFAULT_GROUP_NAME;
}

export default {
    getFriendGroups,
    createFriendGroup,
    deleteFriendGroup,
    renameFriendGroup,
    changeFriendGroup,
    getFriendGroup,
    DEFAULT_GROUP_NAME,
    USER_SETTING_SCOPE,
    USER_SETTING_KEY
}; 