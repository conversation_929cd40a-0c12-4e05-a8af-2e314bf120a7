<template>
    <div @click.stop="" class="user-info-container">
        <div class="user-item-container">
            <div class="left-item-count">
                <image :src="userInfo.portrait || defaultAvatar" @error="imgUrlAlt" />
                <div class="category-item">
                    <span class="title">{{ getFriendDisplayName() }}</span>
                    <span class="group-tag" v-if="friendGroup !== DEFAULT_GROUP_NAME">{{ friendGroup }}</span>
                </div>
            </div>
            <div class="right-item-count" @click="showMoveToGroupDialog">
                移动
            </div>
        </div>
    </div>
</template>

<script>
import Config from '../../config';
import friendGroupService from './friendGroupService';

export default {
    name: 'UserItemView',
    props: {
        userInfo: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            friendAlias: this.userInfo.friendAlias,
            defaultAvatar: Config.DEFAULT_PORTRAIT_URL,
            DEFAULT_GROUP_NAME: friendGroupService.DEFAULT_GROUP_NAME
        }
    },
    methods: {
        getFriendDisplayName() {
            return this.userInfo.friendAlias || this.userInfo.displayName || this.userInfo.uid;
        },
        
        showMoveToGroupDialog() {
            this.$emit('move-group', this.userInfo);
        },
        
        imgUrlAlt(e) {
            e.target.src = Config.DEFAULT_PORTRAIT_URL;
        }
    },

    computed: {
        friendGroup() {
            return friendGroupService.getFriendGroup(this.userInfo.uid);
        }
    },
}
</script>

<style lang="css" scoped>
.user-info-container {
    padding-left: 60px;
    padding-right: 20px;
}
.user-item-container {
    /* width: calc(100% - 45px); */
    height: 50px;
    display: flex;
    flex-direction: row;
    align-items: center;
    z-index: 1000;
    padding-left: 10px;
    color: #262626;
    font-size: 16px;
    position: sticky;
    justify-content: space-between;
    /* background-color: #fafafa; */
    position: relative;
}
.left-item-count {
    display: flex;
    align-items: center;
    font-size: 16px;
}
.right-item-count {
    color: #386bf6;
    font-size: 14px;
}
.user-item-container image {
    max-width: 40px;
    max-height: 40px;
    margin-right: 10px;
    border-radius: 8px;
}
.category-item {
    display: flex;
    flex-direction: column;
}
.group-tag {
    font-size: 12px;
    color: #999;
    margin-top: 2px;
}
</style>
