<template>
	<view class="review-container" v-if="isComment">
		<CustomHeader backgroundColor="#FFF" :leftWidth="30" leftIcon="back" color="#000" @clickLeft="toggleComment">
			<template v-slot:title>
				<text style="font-size: 18px;">点评</text>
			</template>
			<template v-slot:right>
				<text style="width:30px; font-size: 12px; color: #386BF6" @click="submitComment">提交</text>
			</template>
		</CustomHeader>
		<view class="review-container-bg">
			<!-- Overall Rating -->
			<view class="overall-wrapper">
				<text>基础评价</text>
				<view class="overall-rating">
					<view class="overall-content" v-bind:class="{selected:overallRating<0}">
						<image :src= "overallRating<0? '/static/image/comment/low-select.png':'/static/image/comment/low-unselect.png'" @click="setOverallRating(-1)" class="rating-emoj"/>
						<view class="star-desc">{{starDesc[0]}}</view>
					</view>
					<view class="overall-content" v-bind:class="{selected:overallRating==0}">
						<image :src= "overallRating==0?'/static/image/comment/middle-select.png':'/static/image/comment/middle-unselect.png'" @click="setOverallRating(0)" class="rating-emoj"/>
						<view class="star-desc">{{starDesc[1]}}</view>
					</view>
					<view class="overall-content" v-bind:class="{selected:overallRating>0}">
						<image :src= "overallRating>0?'/static/image/comment/happy-select.png':'/static/image/comment/happy-unselect.png'" @click="setOverallRating(1)" class="rating-emoj"/>
						<view class="star-desc">{{starDesc[2]}}</view>
					</view>
				</view>
			</view>
			<view class="comment-input">
				<textarea v-model="commentText" placeholder="留下你的点评内容，让更多人了解ta，喜欢ta" />
				<view class="image-picker">
					<view class="image-list">
						<!-- 已选择的图片 -->
						<view v-for="(image, index) in images" :key="index" class="image-item">
							<image :src="image" mode="aspectFill" class="preview-image"
								@click="previewImage(images, index)" />
							<view class="delete-btn" @click="deleteImage(index)">
								<text class="delete-icon">×</text>
							</view>
						</view>
						<!-- 添加按钮 -->

						<view class="photo-upload" @click="uploadPhoto" v-if="images.length < 9">
							<image src="/static/image/comment/camera.png" mode="aspectFit" />
							<text>上传照片/视频</text>
						</view>
					</view>
				</view>
				<view class="check-show-name">
					<view class="rqla rqlb" v-if="isHideName" @click="onCheckHideName">
						<image class="rqla1" src="/static/image/login/g2.png"></image>
					</view>
					<view class="rqla" v-if="!isHideName" @click="onCheckHideName"></view>
					<view>匿名评价</view>
					<view class="tip">您的点评内容将在用户主页展示</view>
				</view>
			</view>
			<!-- Five-Star Rating -->
			<view class="five-star-rating">
				<view class="rating-title-container">
					<text class="rating-title">五芒星评星</text>
					<image src="/static/image/comment/tips.png" @click="onTips" />
				</view>

				<view class="rating-item" v-for="(rating, index) in fiveStarRatings" :key="index">
					<text>{{ rating.label }}</text>
					<view class="custom-rate">
						<image v-for="n in 5" :key="n" :src="n <= rating.value ? redStar : grayStar"
							@click="setFiveStarRating(index, n)" class="star" />
					</view>
				</view>
				<view class="rating-tips">
					专业的维度评价，能帮助他人全面了解，您的点评很重要
				</view>
			</view>
		</view>
	</view>

	<view class="rating-container" v-else>
		<CustomHeader backgroundColor="#F5F5F5" color="#000">
			<template v-slot:title>
				<text style="font-size: 18px;">点评列表</text>
			</template>
			<template v-slot:right>
				<text style="width:30px; font-size: 12px; color: #386BF6" @click="toggleComment">点评</text>
			</template>
		</CustomHeader>

		<view class="comment-type">
			<view class="type-btn" v-bind:class="{typeSelected:curType=='All'}" @click="filterType('All')">全部</view>
			<view class="type-btn" v-bind:class="{typeSelected:curType=='1'}" @click="filterType('1')">好评</view>
			<view class="type-btn" v-bind:class="{typeSelected:curType=='0'}" @click="filterType('0')">中评</view>
			<view class="type-btn" v-bind:class="{typeSelected:curType=='-1'}" @click="filterType('-1')">差评</view>
		</view>
		<!-- 点评列表 -->
		<view v-if="comments.length>0">
			<view class="post-list-container">
				<view v-for="(comment, index) in comments" :key="comment.topicId || index" class="post-item"  @click="toCommentDetail($event, comment)">
					<view class="post-header">
						<image :src="comment.avatar" class="post-avatar" lazy-load />
					</view>
					<view class="post-content">
						<view class="post-info">
							<text class="post-author">{{ comment.name }}</text>
						</view>
						<view class="post-content-text">
							<CollapseText :content="comment.content">
							</CollapseText>
						</view>
						<view class="post-image-container" v-if="comment.url?.length>0">
							<div class="post-image-group2">
								<image v-for="(item,index) in comment?.url" :src="item" mode="aspectFill"
									class="post-image4" @click.stop="previewImage(index,comment?.url)" />
							</div>
						</view>
						<view class="post-footer">
							<view>
								<text class="footer-date">{{comment.createTime}}</text>
							</view>
							<view class="footer-item">
								<view class="footer-item-sub" @click.stop="showReplyInput(comment,index)">
									<text class="item-count">{{ comment.floorNum }}</text>
									<image src="../../assets/images/bar/pinglun.png" alt="" />
								</view>
								<view class="footer-item-sub" @click.stop="handleLike(comment,index)">
									<text class="item-count">{{ comment.likeNum }}</text>
									<uni-icons :type="comment.isLiked ? 'hand-up-filled' : 'hand-up'" size="20" :color="comment.isLiked ? '#FF0000' : '#666'" />
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 底部状态提示 -->
			<view v-if="!hasMore && comments.length > 0" class="loading-text">没有更多了</view>
			<view v-if="!hasMore && comments.length === 0" class="loading-text">暂无数据</view>
		</view>
		<view v-else class="empty-block">
			<view class="rqd">
				<image class="rqdimg" src="/static/image/icon/kxx.png"></image>
				<view class="rqda">
					暂无评论
				</view>
				<view class="rqdb">
					赶紧点击右上角进行点评吧
				</view>
        	</view>
		</view>
		<!-- 添加评论弹窗 -->
        <view class="comment-popup" v-if="showReply">
            <view class="comment-mask" @click="showReply = false"></view>
            <view class="comment-content">
                <input v-model="replyText" class="comment-input" placeholder="请输入评论内容" focus />
                <button class="comment-submit" @click="submitReply">发送</button>
            </view>
        </view>
	</view>
	<uni-popup ref="popup" type="dialog">
      <uni-popup-dialog 
	  	type="info"
	  	title="点评介绍" cancelText=" "
		confirmText="知道了" 
		showClose:false
		@confirm="closePopup"
		>
        <view slot="default">
          <scroll-view scroll-y="true" style="height: 200px;">
			<view class="popup-content">
			 <view class="pop-title">
			 	靠谱
			 </view>
			 <view>
			 	可靠性：这个人总是能够按时完成任务，并且在承诺的时间内交付高质量的工作。无论是在个人生活还是职业环境中，他们都是那个可以被信赖去负责重要事项的人。
			 </view>
			  <view>
			 	责任感：遇到问题或挑战时，他们会主动承担责任，寻找解决方案而不是推卸责任。他们的行为证明了他们对团队和个人目标的坚定承诺。
			 </view>
			  <view class="pop-title">
			 	热情
			 </view>
			 <view>
			 	积极态度：无论是面对日常工作还是追求个人兴趣，他们都表现出极大的热忱和积极性。这种态度不仅激励自己不断进步，也感染着周围的人，营造出一个充满活力的工作环境。
			 </view>
			  <view>
			 	投入度高：对于自己热爱的事物，他们会全身心地投入，愿意花费额外的时间和精力去学习新技能或者帮助他人。他们对待每一个项目都充满了激情，试图将其做到最好
			 </view>
			  <view class="pop-title">
			 	能力
			 </view>
			 <view>
			 	专业技能：在自己的领域内拥有深厚的知识基础和技术能力，能够高效解决问题并提出创新性的想法。持续不断地提升自我，紧跟行业发展趋势，确保自己的技能始终保持在前沿水平。
			 </view>
			  <view>
			 	学习能力强：具备快速学习新知识和适应变化的能力，能够在短时间内掌握新的工具和技术，并将之应用到实际工作中去。这使得他们在面对未知挑战时依然能够保持竞争力。
			 </view>
			</view>
		</scroll-view>
        </view>
      </uni-popup-dialog>
    </uni-popup>
	 
</template>

<script>
	import CustomHeader from "../../components/custom-header";
	import CollapseText from '@/components/CollapseText'
	import appServerApi from '@/api/appServerApi'
	import { setItem } from '../util/storageHelper'
	export default {
		components: {
			CustomHeader,
			CollapseText
		},
		data() {
			return {
				userId: '',
				overallRating: 1,
				commentText: '',
				fiveStarRatings: [{
						label: '靠谱',
						value: 5
					},
					{
						label: '热情',
						value: 5
					},
					{
						label: '能力',
						value: 5
					},
				],
				isComment: false,
				redStar: '/static/image/comment/star-on.png',
				grayStar: '/static/image/comment/star-off.png',
				isHideName: false,
				starDesc:['差评','中评','好评'],
				images: [],
				comments: [],
				hasMore: true,
				isRefreshing: false,
				pageNo: 1,
				pageSize: 10,
				total: 0,
				loading: false,
				loadingTimer: null,
				showReply:false,
				replyText:'',
				isMyFriend: false,
				curType:'All',
			}
		},
		onLoad(option) {
			this.userId = option.userId
			this.isComment = option.isComment == "true"
			this.isMyFriend = option.isMyFriend == "true"
			this.fetchComments()
		},
		onReachBottom() {
			if (this.hasMore) {
				// this.status = 'loading'
				this.pageNo++
				this.fetchComments('push')
			}
		},
		methods: {
			toggleComment() {
				this.isComment = !this.isComment
			},
			setOverallRating(n) {
				this.overallRating = n;
			},
			setFiveStarRating(index, n) {
				this.fiveStarRatings[index].value = n;
			},
			onCheckHideName() {
				this.isHideName = !this.isHideName
			},
			onTips() {
				console.log('onTips')
				this.$refs.popup.open('center')
			},
			closePopup(){
				this.$refs.popup.close()
			},
			uploadPhoto() {
				uni.chooseImage({
					count: 9 - this.images.length,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						const newImages = res.tempFilePaths.filter(newPath => {
							const isDuplicate = this.images.some(existingPath => existingPath ==
								newPath)
							if (isDuplicate) {
								uni.showToast({
									title: '已添加过相同的图片',
									icon: 'none'
								})
							}
							return !isDuplicate
						})
						if (newImages.length > 0) {
							this.images = [...this.images, ...newImages]
						}
					},
					fail: (err) => {
						console.error('选择图片失败:', err)
					}
				})
			},
			previewImage(urls, current) {
				uni.previewImage({
					urls: urls, // 图片数组
					current: current, // 当前图片索引
					indicator: 'number', // 显示页码
					loop: true, // 支持循环预览
					success: () => {
						console.log('图片预览成功')
					},
					fail: (err) => {
						console.error('图片预览失败:', err)
					}
				})
			},
			deleteImage(index) {
				this.images.splice(index, 1)
			},
			async submitComment() {
				if (!this.commentText.trim() && this.images.length === 0) {
					uni.showToast({
						title: '请输入内容或选择图片',
						icon: 'none'
					})
					return
				}
				let imageUrls = []
				if (this.images.length > 0) {
					try {
						// 使用 Promise.all 和 async/await 处理多图上传
						imageUrls = await Promise.all(
							this.images.map(async (image) => {
								return new Promise((resolve, reject) => {
									appServerApi.uploadimgFile(
										image,
										(res) => {
											console.log('上传成功:', res)
											if (res && res.data) {
												resolve(res.data)
											} else {
												reject(new Error('上传返回数据异常'))
											}
										},
										(error) => {
											console.error('上传失败:', error)
											reject(error)
										}
									)
								})
							})
						)
						console.log('所有图片上传完成，地址列表:', imageUrls)
					} catch (error) {
						console.error('图片上传过程出错:', error)
						uni.hideLoading()
						uni.showToast({
							title: '图片上传失败',
							icon: 'none'
						})
						return
					}
				}

				appServerApi.postUserComment({
						content: this.commentText.trim(),
						targetId: this.userId,
						value1: this.fiveStarRatings[0].value,
						value2: this.fiveStarRatings[1].value,
						value3: this.fiveStarRatings[2].value,
						baseScore: this.overallRating,
						anonymous: this.isHideName ? 1 : 0,
						url: imageUrls,
					})
					.then((res) => {
						uni.showToast({
							title: '评论成功',
							icon: 'success',
						})
						this.fetchComments('reset')
						this.toggleComment()
					})
					.catch((err) => {
						console.error('评价失败:', err)
						// 提取API返回的具体错误信息
						let errorMessage = '评价失败'
						if (err && typeof err === 'string') {
							errorMessage = err
						} else if (err && err.msg) {
							errorMessage = err.msg
						} else if (err && err.message) {
							errorMessage = err.message
						}
						
						uni.showToast({
							title: errorMessage,
							icon: 'none',
							duration: 3000
						})
					})


			},

			onRefresherPull(e) {
				// 下拉触发的事件
				console.log('onRefresherPull', e)
			},
			onRefresherRefresh(e) {
				console.log('onRefresherRefresh', e)
				this.fetchComments('reset')
				// if (e.currentTarget.offsetTop < 45) {
				// }
			},
			onRefresherRestore(e) {
				console.log('onRefresherRestore', e)
				// 恢复下拉刷新控件的位置
			},
			onRefresherAbort() {
				// 取消下拉刷新
			},
			onScroll(e) {
				if (this.loadingTimer) return

				this.loadingTimer = setTimeout(() => {
					const {
						scrollTop,
						scrollHeight,
						clientHeight
					} = e.target
					// 提前 100px 触发加载
					if (scrollHeight - scrollTop - clientHeight < 100) {
						this.loadMore()
					}
					this.loadingTimer = null
				}, 100)
			},
			previewImage(index, images) {
				// 只预览当前图片和前后各一张
				const previewUrls = images.slice(
					Math.max(0, index - 1),
					Math.min(images.length, index + 2)
				)
				uni.previewImage({
					current: index,
					urls: previewUrls,
				})
			},
			// 获取推荐话题列表
			async fetchComments(type = 'push') {
				if (this.loading) return
				this.isRefreshing = true

				if (type == 'reset') {
					this.pageNo = 1
					this.comments = [] // 重置列表
				}

				try {
					this.loading = true
					let params = {
						pageNo: this.pageNo,
						pageSize: this.pageSize,
						userId: this.userId
					}
					if(this.curType !== 'All'){
						params = {...params,score:this.curType}
					}
					const response = await appServerApi.getUserCommentList(params)

					this.isRefreshing = false
					const topics = response?.data?.result || []
					const formattedTopics = topics.map((topic) => ({
							topicId: topic.id,
							name: topic.user.displayName || '',
							content: topic.content || '',
							avatar: topic.user.avatar,
							subComment: topic.subComment,
							likeNum: topic.likeNum || '',
							floorNum: topic.subCommentNum || '',
							isLiked: topic.hasLike,
							url: topic.url,
							createTime:topic.createTime?.substring(10) || '',
							score:topic.score
						}))

					if (this.pageNo === 1) {
						this.comments = formattedTopics
					} else {
						this.comments = [...this.comments, ...formattedTopics]
					}

					this.hasMore = this.comments.length < (response?.data?.total || 0)
				} catch (error) {
					console.error('获取推荐话题失败:', error)
					uni.showToast({
						title: '获取数据失败',
						icon: 'none',
					})
				} finally {
					this.loading = false
				}
			},
			handleLike(comment,index) {
		
				if (!comment || !comment.topicId) {
					console.error('评论数据无效')
					return
				}
				const isLiked = !comment.isLiked
			
				appServerApi
					.likeComment({ commentId: comment.topicId})
					.then((res) => {
						this.$set(this.comments, index, {
							...this.comments[index],
							isLiked: isLiked,
							likeNum: isLiked ? this.comments[index].likeNum +1 : this.comments[index].likeNum-1
						});
						console.log('点赞评论:', JSON.stringify(comment))
					})
					.catch((err) => {
						console.error('点赞失败:', err)
						uni.showToast({
							title: '操作失败',
							icon: 'none',
						})
					})
			},
			showReplyInput(post, index) {
				console.log('准备评论点评:', JSON.stringify(post))
				if (!post || !post.topicId) {
					console.error('评论数据无效')
					return
				}
				this.currentPost = { ...post , index: index}
				this.showReply = true
				this.replyText = ''
			},
			submitReply() {
				if (!this.replyText.trim()) {
					uni.showToast({
						title: '请输入评论内容',
						icon: 'none',
					})
					return
				}
				console.log('提交评论:', this.replyText)
				console.log('this.currentPost:', JSON.stringify(this.currentPost))
				appServerApi
					.replyComment({
						parentId: this.currentPost.topicId,
						content: this.replyText.trim()
					})
					.then((res) => {
						uni.showToast({
							title: '评论成功',
							icon: 'success',
						})
						this.$set(this.comments, this.currentPost.index, {
							...this.comments[this.currentPost.index],
							floorNum: this.currentPost.floorNum + 1
						});
						this.showReply = false
						this.replyText = ''
					})
					.catch((err) => {
						console.error('评论失败:', err)
						uni.showToast({
							title: '评论失败',
							icon: 'none',
						})
					})
			},
			toCommentDetail(event, item) {
				console.log('点击评价:', item)
				setItem("curUserComment", item)
				
				uni.navigateTo({
					url: '/pages/user/CommentDetail?id=' + item.topicId,
				})
			},
			filterType(type){
				this.curType = type
				this.fetchComments('reset')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.review-container {
		.review-container-bg {
			background: #f5f5f5;
		}

		.header {
			display: flex;
			justify-content: space-between;
			padding: 20rpx 0;
			border-bottom: 1px solid #eee;

			.submit {
				color: #007AFF;
			}
		}

		.overall-wrapper {
			padding: 20px;
			font-size: 16px;
			background: #fff;
			margin-bottom: 10px;
			.overall-rating{
				display:flex;
				margin-top: 20px;
				.overall-content{
					width:33%;
					font-size:14px;
					color:#999999;
					align-items: center;
					display: flex;
    				flex-direction: column;
				}
				.selected{
					color:#030303;
				}
				.rating-emoj{
					width:48px;
					height:48px;
				}
			}
			
		}

		.comment-input {
			background: #fff;
			border-radius: 10px 10px 0px 0px;
			padding: 16px;

			textarea {
				width: 100%;
				height: 116px;
				overflow: auto;
			}
		}

		.check-show-name {
			display: flex;
			align-items: center;
			margin-top: 20px;

			.rqla {
				width: 14px;
				height: 14px;
				background-color: #ffffff;
				border-radius: 50%;
				border: 1px solid #999;
				margin-right: 8px;

				.rqla1 {
					width: 12px;
					height: 12px;
				}
			}

			.rqlb {
				border: 1px solid #386bf6;
				background-color: #386bf6;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 8px;
			}

			.tip {
				font-size: 12px;
				color: #999999;
				line-height: 14px;
				margin-left: 10px;
				margin-top: 4px;
			}
		}

		.image-picker {

			.image-list {
				display: flex;
				flex-wrap: wrap;
				margin-right: -10px;
				margin-bottom: -10px;

				.image-item {
					width: 80px;
					height: 80px;
					position: relative;
					margin-right: 20px;
					margin-bottom: 10px;

					.preview-image {
						width: 100%;
						height: 100%;
						border-radius: 4px;
						cursor: pointer;
					}

					.delete-btn {
						position: absolute;
						top: -8px;
						right: -8px;
						width: 20px;
						height: 20px;
						background: rgba(0, 0, 0, 0.5);
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;

						.delete-icon {
							color: #fff;
							font-size: 16px;
							line-height: 1;
						}
					}
				}

				.photo-upload {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					width: 90px;
					height: 90px;
					background: #F5F5F5;
					border-radius: 10px 10px 10px 10px;

					image {
						width: 48px;
						height: 48px;
					}

					text {
						font-size: 11px;
						color: #000;
					}
				}
			}
		}


		.five-star-rating {
			margin-top: 10px;
			background: #fff;
			padding: 20px;

			.rating-title-container {
				display: flex;
				align-items: center;

				.rating-title {
					font-weight: bold;
					margin-right: 6px;
				}

				image {
					width: 16px;
					height: 16px;
				}
			}

			.rating-item {
				display: flex;
				align-items: center;
				margin: 10px 0;
			}

			.rating-tips {
				font-size: 12px;
				color: #999999;
				line-height: 14px;
				padding: 24px 0;

			}
		}

		.custom-rate {
			display: flex;
			margin-left: 15px;

			.star {
				width: 18px;
				height: 18px;
				margin-right: 10px;
			}
		}
	}

	.rating-container {
		height:100vh;
		overflow: auto;
		background-color: #f5f5f5;

		.comment-type{
			display:flex;
			padding:10px 20px;
			.type-btn{
				width: 50px;
				height: 36px;
				background:#EAEAEA;
				border-radius: 10px;
				text-align:center;
				line-height:36px;
				margin-right:10px;
			}
			.typeSelected{
				color: #386BF6;
				background: rgba(56,107,246,0.2)
			}
		}
		.post-list-container {
			padding: 0 12px;

			.post-item {
				background-color: #ffffff;
				margin-bottom: 12px;
				border-radius: 12px;
				overflow: hidden;
				display: flex;
				padding: 14px;
				box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

				/* 帖子头部 */
				.post-header {
					.post-avatar {
						width: 38px;
						height: 38px;
						border-radius: 8px;
						margin-right: 12px;
					}
				}
			}

			.post-content {
				width: 100%;
				font-size: 14px;
				display: flex;
				flex-direction: column;
				
				.post-info {
					margin-bottom: 8px;
				}
				
				.post-author {
					font-size: 15px;
					color: #333;
				}
				
				.post-content-text {
					color: #666;
					font-size: 12px;
					line-height: 1.5;
					padding: 2px 0 8px;
				}
				
				.post-image-group2 {
					display: flex;
					flex-wrap: wrap;
					margin-top: 8px;
					.post-image4 {
						width: 30%;
						height: 100px;
						margin: 2.5px;
						border-radius: 4px;
					}
				}
			}

			.post-footer {
				display: flex;
				justify-content: space-between;
				padding-top: 10px;
			}
			.footer-date {
				font-size: 12px;
				color: #999999;
			}
			.footer-item {
				display: flex;
				justify-content: flex-end;
				line-height: 20px;
				.footer-item-sub {
					width: 60px;
					display: flex;
					gap: 4px;
					justify-content: flex-end;
					.item-count{
						text-overflow: ellipsis;
						overflow: hidden;
    					white-space: nowrap;
						text-align: end;
						width:26px;
					}
					image {
						width: 20px;
						height: 20px;
					}
				}
			}
		}

		.comment-popup {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			z-index: 999;
		}

		.comment-mask {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.4);
		}

		.comment-content {
			position: relative;
			background: #fff;
			padding: 20rpx;
			display: flex;
			gap: 20rpx;
			align-items: center;
		}

		.comment-input {
			flex: 1;
			height: 72rpx;
			border: 1px solid #e4e4e4;
			border-radius: 36rpx;
			padding: 0 24rpx;
			font-size: 28rpx;
		}

		.comment-submit {
			width: 120rpx;
			height: 72rpx;
			background: #386bf6;
			color: #fff;
			border-radius: 36rpx;
			font-size: 28rpx;
			line-height: 72rpx;
			text-align: center;
		}
		.loading-text {
			font-size: 14px;
			color: #999;
			text-align: center;
		}

		.empty-block{
			.rqd {
				width: 100%;
				margin-top: 120px;
				text-align: center;
			    .rqda {
					color: #000;
					text-align: center;
					font-family: MiSans;
					font-size: 20px;
					font-style: normal;
					font-weight: 500;
					line-height: 28px;
					margin-top: 15px;
				}

			    .rqdb {
					color: #999;
					text-align: center;
					font-family: MiSans;
					font-size: 14px;
					font-style: normal;
					font-weight: 330;
					line-height: 22px;
					margin-top: 10px;
				}
			}
			.rqdimg {
				width: 120px;
				height: 112px;
				text-align: center;
				margin: 0 auto;
			}
		}
	}
	.uni-dialog-title {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		justify-content: center;
		padding-top: 10px;
		color:#386bf6;
	}
	.popup-content {
		width: 100%;
		height: 500px;
		align-items: center;
		font-size: 12px;
		.pop-title{
			font-size: 14px;
			font-weight: 600;
			text-align: left;
			width: 100%;
		}
		view{
			padding: 10px;
		}
	}

</style>

