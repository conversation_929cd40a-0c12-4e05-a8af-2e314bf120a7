<template>
    <view>
        <scroll-view :scroll-y="true" :scroll-into-view="toView" :scroll-with-animation="true" style="width: 100%">
            <view v-for="(g) in groupedUsers" :key="'pro_'+g.category.toUpperCase()" :id="'pro_'+g.category.toUpperCase()">
                <div ref="contactItem" class="contact-item">
                    <div v-if="showCategoryLabel" class="label" :style="paddingStyle" v-bind:class="{sticky:enableCategoryLabelSticky}">
                        <p>{{ g.category.toUpperCase() }}</p>
                    </div>
                    <view class="conversation-rq">
                        <uni-list style="width: 100%">
                            <view v-for="(user, index) in g?.users" :key="user.uid">
                                <view class="rq-hine" v-if="index > 0"></view>
                                <div class="content" :name="'user-'+user.uid" :style="paddingStyle" v-bind:class="{active: (sharedContactState.currentFriend
                        && user._category === sharedContactState.currentFriend._category
                        && user.uid === sharedContactState.currentFriend.uid) || (currentUser && currentUser.uid === user.uid)}" @click.stop="clickUserItem(user)">
                                    <img class="avatar" :src="user.portrait" alt="" @error="imgUrlAlt">
                                    <view v-if ="showIdentity">
                                        <view v-if ="user.type === 2" class="user-identity-ower">
                                            {{user.identityName}}                                     
                                        </view>
                                        <view v-else-if ="user.type === 1" class="user-identity-admin">
                                            {{user.identityName}}                                     
                                        </view>
                                        <view v-else-if ="user.identityName && user.identityName !=='成员'" class="user-identity-other">
                                            {{user.identityName}}                                     
                                        </view>
                                    </view>
                                    <div style="padding-left: 10px">
                                        <p class="single-line"> {{ user._displayName ? user._displayName : user.displayName }}</p>
                                        <p v-if="user._userOnlineStatusDesc" class="single-line user-online-status">
                                            {{ user._userOnlineStatusDesc }}
                                        </p>
                                    </div>
                                    <view v-if="!user.isMyFriend && meId !== user.uid" class="add-friend-btn" @click.stop="addFriend(user)">
                                         添加 
                                    </view>
                                </div>
                            </view>
                        </uni-list>
                    </view>
                </div>
            </view>
            
        </scroll-view>
        <view class="letter-nav" v-if="showNavigationbar && sortByType==='pinyin'">
            <view class="item" v-for="(item) in indexList" :key="item" @tap="toSelectIndex(item,$event)">
                {{ item }}
            </view>
        </view>
    </view>
</template>

<script>
import store from '../../store'
import UserCardView from './UserCardView'
import Config from '../../config'
import UniList from '../../components/uni-list/uni-list.vue'
import wfc from '@/wfc/client/wfc'
export default {
    name: 'UserListInGroup',
    props: {
        users: {
            type: Array,
            required: false,
        },
        currentUser: {
            type: Object,
            default: null,
        },
        // 是否显示导航栏
        showNavigationbar: {
            type: Boolean,
            required: false,
            default: true,
        },
        showCategoryLabel: {
            type: Boolean,
            required: false,
            default: true,
        },
        enableCategoryLabelSticky: {
            type: Boolean,
            required: false,
            default: false,
        },
        showIdentity: {
            type: Boolean,
            required: false,
            default: true,
        },
        clickUserItemFunc: {
            type: Function,
            required: false,
        },
        paddingLeft: {
            type: String,
            required: false,
            default: '5px',
        },
        sortByType:{
            type:String,
            required: false,
            default: 'pinyin'
        }
    },
    data() {
        return {
            meId:wfc.getUserId(),
            sharedContactState: store.state.contact,
            toView: '',
            indexList: [
                '#',
                'A',
                'B',
                'C',
                'D',
                'E',
                'F',
                'G',
                'H',
                'I',
                'J',
                'K',
                'L',
                'M',
                'N',
                'O',
                'P',
                'Q',
                'R',
                'S',
                'T',
                'U',
                'V',
                'W',
                'X',
                'Y',
                'Z',
            ],
        }
    },
    methods: {
        toSelectIndex(item, e) {
            console.log('toSelectIndex', item)
            e.preventDefault()
            e.stopPropagation()
            this.toView = 'pro_' + item
             
        },

        clickUserItem(user) {
            if (this.clickUserItemFunc) {
                this.clickUserItemFunc(user)
            } else {
                uni.navigateTo({
                    url: '/pages/contact/UserDetailPage?userId='+user.uid,
                })
            }
        },
        addFriend(user){
            store.setCurrentFriend(user)
            uni.navigateTo({
                url: '/pages/contact/NewFriendDetailPage',
                success: () => {
                    console.log('nav to NewFriendDetailPage success')
                },
                fail: (err) => {
                    console.log('nav to NewFriendDetailPage err', err)
                },
            })
        },
        scrollActiveElementCenter() {
            let el = this.$el.getElementsByClassName('active')[0]
            el &&
                el.scrollIntoView({
                    behavior: 'instant',
                    block: 'center',
                })
        },

        imgUrlAlt(e) {
            e.target.src = Config.DEFAULT_PORTRAIT_URL
        },
        groupedUsersByIdentity() {
            let usersClone=  this.users.slice()
            let groupedUsers = []
            let current = {}
            let lastCategory = null
            usersClone.sort((a, b) => a.name.localeCompare(b.identityName));
            usersClone = usersClone.filter(m=>m.type ===2 ).concat(usersClone.filter(m=>m.type === 1)).concat(usersClone.filter(m=>m.type !==1 && m.type !==2 ))
            usersClone?.forEach((user) => {
                if (!lastCategory || lastCategory !== user.identityName) {
                    lastCategory = user.identityName
                    current = {
                        category: user.identityName,
                        users: [user],
                    }
                    groupedUsers.push(current)
                } else {
                    current.users.push(user)
                }
            })
            return groupedUsers
        },
    },

    mounted() {
        this.$nextTick(() => {
            const scrollView = this.$el.querySelector('scroll-view');
            console.log('Scroll view height:', scrollView.offsetHeight);
            console.log('Content height:', scrollView.scrollHeight);
        });
    },

    activated() {
        this.scrollActiveElementCenter()
    },

    unmounted() {},

    computed: {
        groupedUsers() {
            let groupedUsers = []
            if (!this.showCategoryLabel) {
                groupedUsers.push({
                    category: 'not-show-category',
                    users: this.users,
                })
            } else if(this.sortByType === 'identity'){
                return this.groupedUsersByIdentity()
            } 
            else {
                let current = {}
                let lastCategory = null
                this.users?.forEach((user) => {
                    if (!lastCategory || lastCategory !== user._category) {
                        lastCategory = user._category
                        current = {
                            category: user._category,
                            users: [user],
                        }
                        groupedUsers.push(current)
                    } else {
                        current.users.push(user)
                    }
                })
            }
            return groupedUsers
        },
        paddingStyle() {
            return {
                paddingLeft: this.paddingLeft,
            }
        },
    },
    components: {
        UniList,
        UserCardView,
    },
}
</script>

<style lang="css" scoped>
.conversation-rq {
    width: calc(100%);
}
.category-item-container {
    width: calc(100% - 45px);
    height: 50px;
    display: flex;
    flex-direction: row;
    align-items: center;
    z-index: 1000;
    padding-left: 10px;
    color: #262626;
    font-size: 14px;
    position: sticky;
    /* background-color: #fafafa; */
    position: relative;
}

.category-item-container image {
    max-width: 40px;
    max-height: 40px;
    margin-right: 10px;
}

.category-item {
    display: flex;
    width: 100%;
    align-items: center;
}

.category-item .title {
    font-size: 16px;
}

.category-item .desc {
    color: #ffffff;
    width: 20px;
    height: 20px;
    font-size: 12px;
    border-radius: 50%;
    background-color: red;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 15px;
}

.category-item span:last-of-type {
    margin-right: 15px;
}

.scroll-container {
    height: calc(100vh - 60px);
}

.rqd {
    width: 100%;
    margin-top: 20px;
    text-align: center;
}

.rqd .tch image {
    width: 14px;
    height: 14px;
    margin-right: 10px;
}

.rqd .tch {
    margin: 20px auto;
    width: calc(100% - 40px);
    height: 46px;
    border-radius: 10px;
    background: #386bf6;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-family: MiSans;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.rqd .rqdimg {
    width: 104px;
    height: 102px;
    text-align: center;
    margin: 0 auto;
}

.letter-nav {
    position: fixed;
    top:50px;
    right: 10px;
    z-index: 9;
}

.letter-nav .item {
    padding: 3px 5px;
    /* background-color: #d6d6d6; */
    color: #386bf6;
    text-align: center;
    font-family: MiSans;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    text-transform: uppercase;
    line-height: 1;
    height: calc(3% - 6px);
}

.contact-item {
    --user-item-padding-left: 30px;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 8px;
}
.user-identity-ower{
    font-size: 10px;
    background: #FF2222;
    margin-left: 10px;
    padding: 2px 4px;
    line-height: 12px;
    color: #fff;
    border-radius: 2px;
}
.user-identity-admin{
    font-size: 10px;
    background: #FC7F1E;
    margin-left: 10px;
    padding: 2px 4px;
    line-height: 12px;
    color: #fff;
    border-radius: 2px;
}
.user-identity-other{
    font-size: 10px;
    background: #0CC86B;
    margin-left: 10px;
    padding: 2px 4px;
    line-height: 12px;
    color: #fff;
    border-radius: 2px;
}
.checkbox {
    margin-right: 10px;
}

.contact-item {
    display: flex;
    flex-direction: column;
    font-size: 14px;
    align-items: flex-start;
}

.contact-item .label {
    width: 100%;
    background-color: #fafafa;
}

.contact-item .label p {
    padding: 10px 5px 5px 10px;
    font-size: 14px;
    color: #BBBBBB;
    line-height: 16px;
}

.contact-item .label.sticky {
    position: sticky;
    top: 0;
}

.contact-item .content {
    padding: 10px 5px 10px 0;
    display: flex;
    width: 100%;
    align-items: center;
}

.contact-item .content span {
    margin-left: 10px;
}

.contact-item .content.active {
    background-color: #d6d6d6;
}

.contact-item .content:active {
    background-color: #d6d6d6;
}

.user-online-status {
    color: gray;
    font-size: 10px;
}
.add-friend-btn{
    width: 48px;
    height: 22px;
    background: #386BF6;
    border-radius: 5px;
    position: absolute;
    right: 50px;
    color: #fff;
    line-height: 22px;
    text-align: center;
    font-size: 12px;
}

/*.contact-item .content:hover {*/
/*  background-color: red;*/
/*}*/
</style>