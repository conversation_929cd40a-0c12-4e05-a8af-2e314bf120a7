<template>
  <view class="container">
    <view class="custom-navbar">
      <image :src="userInfo?.cover || '/static/image/me/pyq.png'" mode="aspectFill" class="header-bg-img"></image>
      <view class="back-btn" @click="handleBack">
        <i class="icon-ion-ios-arrow-back"></i>
      </view>
      <view class="change-cover-btn" @click="handleChangeCover">
        <i class="icon-ion-ios-camera"></i>
        <text class="change-cover-text">更换封面</text>
      </view>
    </view>
    <view class="profile-section">
      <view class="section">

        <view class="item-wrap">
          <view class="item item-multip" @click="handleCardClick">
            <text class="label">名片</text>
            <text class="right">{{ myCardsData && myCardsData.length > 0 ? '' : '完善名片获取更多关注' }}</text>
            <i class="icon-ion-ios-arrow-right"></i>
          </view>
        </view>

        <view class="item-wrap">
          <view class="item item-multip" @click="handleAvatarClick">
            <text class="label">头像</text>
            <image :src="userInfo.avatar || '/static/logo.png'" class="icon"></image>
            <i class="icon-ion-ios-arrow-right"></i>
          </view>
        </view>
        
        <view class="item-wrap">
          <view class="item item-multip bordernone" @click="handleNicknameClick">
            <text class="label">昵称</text>
            <text class="right">{{ userInfo.displayName || '' }}</text>
            <i class="icon-ion-ios-arrow-right"></i>
          </view>
        </view>
      </view>

      <view class="section">
        <view class="item-wrap">
          <view class="item item-multip" @click="handleSignatureClick">
            <text class="label">简介</text>
            <text class="right">{{ userInfo.signature || '' }}</text>
            <i class="icon-ion-ios-arrow-right"></i>
          </view>
        </view>
        <view class="item-wrap">
          <view class="item item-multip" @click="handleGenderClick">
            <text class="label">性别</text>
            <text class="right">{{ userInfo.gender ? (userInfo.gender === 1 ? '男' : '女') : '' }}</text>
            <i class="icon-ion-ios-arrow-right"></i>
          </view>
        </view>
        <view class="item-wrap">
          <view class="item item-multip bordernone" @click="handleRegionClick">
            <text class="label">地区</text>
            <text class="right">{{ userInfo.city || '' }}</text>
            <i class="icon-ion-ios-arrow-right"></i>
          </view>
        </view>
      </view>

      <view class="section">
        <view class="item-wrap">
          <view class="item item-multip" @click="handleEarthIdClick">
            <text class="label">地球号</text>
            <text class="right">{{ userInfo.earthId || ''}}</text>
            <i class="icon-ion-ios-arrow-right"></i>
          </view>
        </view>
        <view class="item-wrap">
          <view class="item item-multip bordernone" @click="handleQRCodeClick">
            <text class="label">二维码</text>
            <i class="icon-ion-ios-arrow-right"></i>
          </view>
        </view>
      </view>
      <view class="section">
        <view class="item-wrap">
          <view class="item item-multip" @click="handleSchoolClick">
            <text class="label">毕业院校</text>
            <text class="right">{{ userInfo.school || '' }}</text>
            <i class="icon-ion-ios-arrow-right"></i>
          </view>
        </view>
        <view class="item-wrap">
          <view class="item item-multip bordernone" @click="handleAddressClick">
            <text class="label">我的地址</text>
            <text class="right">{{ userInfo.address || '' }}</text>
            <i class="icon-ion-ios-arrow-right"></i>
          </view>
        </view>
      </view>
    </view>

    <!-- 性别选择弹窗 -->
    <uni-popup ref="genderPopup" type="bottom">
      <view class="popup-content">
        <view class="popup-item" @click="selectGender(1)">男</view>
        <view class="popup-item" @click="selectGender(2)">女</view>
        <view class="popup-item cancel" @click="closeGenderPopup">取消</view>
      </view>
    </uni-popup>

    <!-- 地区选择弹窗 -->
    <uni-popup ref="regionPopup" type="bottom">
      <view class="region-picker">
        <view class="location-button" @click="useCurrentLocation">
          <text>使用当前位置</text>
        </view>
        <picker-view
            :value="regionValue"
            @change="onRegionChange"
            :indicator-style="'height: 40px;'"
            :style="'height: 200px;'"
        >
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in provinces" :key="index">
              {{item.name}}
            </view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in cities" :key="index">
              {{item.name}}
            </view>
          </picker-view-column>
        </picker-view>
        <view class="picker-buttons">
          <text @click="closeRegionPopup">取消</text>
          <text @click="confirmRegion">确定</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import regionData from '@/utils/regionData.js'
import weatherApi from '@/api/weatherApi'
import store from '@/store'
import userStore from '@/store/userStore'
import appServerApi from '@/api/appServerApi'

export default {
  data() {
    return {
      userInfo: {},
      regionValue: [0, 0],
      provinces: regionData,
      cities: [],
      selectedRegion: {
        province: '',
        city: ''
      },
      myCardsData: null
    };
  },
  onLoad() {
    this.fetchUserInfo();
    this.fetchMyCards();
    this.cities = this.provinces[0].cities
  },

  onShow() {
    this.fetchUserInfo();
    this.fetchMyCards();
  },

  methods: {
    async fetchUserInfo() {
      try {
        const userInfo = await userStore.getUserInfo();
        this.userInfo = { ...this.userInfo, ...userInfo };
      } catch (error) {
        console.error('获取用户信息失败:', error);
        uni.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      }
    },
    async fetchMyCards() {
      try {
        const result = await appServerApi.getMyCard();
        console.log('获取到的我的名片数据:', result);
        if (result && result.data && Array.isArray(result.data)) {
          this.myCardsData = result.data;
        } else {
          this.myCardsData = [];
        }
      } catch (error) {
        console.error('获取我的名片失败:', error);
        this.myCardsData = [];
        uni.showToast({
          title: '获取名片信息失败',
          icon: 'none'
        });
      }
    },
    //名片点击
    handleCardClick() {
      uni.navigateTo({
        url: '../card/MyCard'
      })
    },
    // 头像点击
    handleAvatarClick() {
      uni.navigateTo({
        url: '/pages/me/AvatarUpload',
        fail: (err) => {
          console.error('跳转失败:', err)
        }
      })
    },

    // 昵称点击
    handleNicknameClick() {
      uni.navigateTo({
        url: '/pages/me/EditDisplayName'
      })
    },

    // 性别点击
    handleGenderClick() {
      this.$refs.genderPopup.open();
    },

    // 选择性别
    async selectGender(gender) {
      // 乐观更新：先立即更新UI并显示成功
      this.userInfo.gender = gender

      // 关闭弹窗
      this.$refs.genderPopup.close()

      // 触发用户信息更新事件
      uni.$emit('userInfoUpdated')

      // 立即显示成功提示
      uni.showToast({
        title: '修改成功',
        icon: 'success'
      })

      // 后台静默保存到服务器
      try {
        await userStore.completeUserInfo({
          ...this.userInfo,
          gender: gender
        })
        console.log('性别信息保存到服务器成功')
      } catch (error) {
        console.error('性别信息保存到服务器失败:', error)
        // 如果需要，可以在这里回滚UI状态
      }
    },

    // 地区点击
    handleRegionClick() {
      this.cities = this.provinces[this.regionValue[0]].cities
      console.log('handleRegionClick this.provinces:', this.provinces)
      console.log('handleRegionClick this.cities:', this.cities)
      this.$refs.regionPopup.open()
    },

    // 地球号点击
    handleEarthIdClick() {
      uni.navigateTo({
        url: '/pages/me/EarthId'
      })
    },

    // 二维码点击
    handleQRCodeClick() {
      uni.navigateTo({
        url: '/pages/me/QRCode'
      })
    },

    // 简介点击
    handleSignatureClick() {
      uni.navigateTo({
        url: '/pages/me/EditSignature',
        fail: (err) => {
          console.error('跳转失败:', err)
        }
      })
    },

    // 毕业院校点击
    handleSchoolClick() {
      uni.navigateTo({
        url: `/pages/me/EditSchool?school=${this.userInfo.school || ''}`
      })
    },

    // 地址点击
    handleAddressClick() {
      uni.navigateTo({
        url: `/pages/me/EditAddress`
      })
    },

    // 关闭性别弹窗
    closeGenderPopup() {
      this.$refs.genderPopup.close();
    },

    // 监听地区选择变化
    onRegionChange(e) {
      const values = e.detail.value
      console.log('onRegionChange values:', values)

      // 省份改变时更新城市列表
      if (values[0] !== this.regionValue[0]) {
        this.cities = this.provinces[values[0]].cities
        values[1] = 0
      }

      this.regionValue = values
      console.log('onRegionChange updated cities:', this.cities)
    },

    // 确认选择地区
    confirmRegion() {
      console.log('confirmRegion this.regionValue ', this.regionValue)
      const province = this.provinces[this.regionValue[0]]
      const city = this.cities[this.regionValue[1]]

      this.selectedRegion = {
        province: province.name,
        city: city.name
      }

      // 更新显示的地区文本
      this.userInfo.city = `${province.name} ${city.name}`
      console.log('confirmRegion this.userInfo ', this.userInfo)
      
      // 关闭弹窗
      this.closeRegionPopup()
      
      // 触发用户信息更新事件
      uni.$emit('userInfoUpdated')
      
      // 立即显示成功提示
      uni.showToast({
        title: '地区已更新',
        icon: 'success'
      })
      
      // 后台静默保存到服务器
      userStore.completeUserInfo(this.userInfo).then(() => {
        console.log('地区信息保存到服务器成功')
      }).catch(err => {
        console.error('地区信息保存到服务器失败:', err)
        // 如果需要，可以在这里回滚UI状态
      })
    },

    // 关闭地区选择器
    closeRegionPopup() {
      this.$refs.regionPopup.close()
    },

    // 使用当前位置
    useCurrentLocation() {
      uni.showLoading({
        title: '定位中...'
      });
      
      // 使用store中的_getLocation方法获取位置
      store._getLocation().then(async (location) => {
        try {
          // 调用后端天气接口获取城市信息
          const weatherResponse = await weatherApi.getWeatherInfo()
          
          if (weatherResponse && weatherResponse.city) {
            // 直接使用后端返回的城市名称
            this.userInfo.city = weatherResponse.city
            
            // 关闭弹窗
            this.closeRegionPopup()
            
            // 触发用户信息更新事件
            uni.$emit('userInfoUpdated')
            
            // 立即显示成功提示
            uni.showToast({
              title: '位置已更新',
              icon: 'success'
            })
            
            // 后台静默保存地区数据
            userStore.completeUserInfo(this.userInfo).then(() => {
              console.log('位置信息保存到服务器成功')
            }).catch(error => {
              console.error('位置信息保存到服务器失败:', error)
              // 如果需要，可以在这里回滚UI状态
            })
          } else {
            throw new Error('获取城市信息失败')
          }
        } catch (error) {
          console.error('获取位置信息失败:', error)
          uni.showToast({
            title: '获取位置信息失败',
            icon: 'none'
          })
        }
      }).catch(() => {
        uni.showToast({
          title: '获取位置失败，请检查定位权限',
          icon: 'none'
        })
      }).finally(() => {
        uni.hideLoading()
      })
    },

    handleBack() {
      uni.navigateBack();
    },

    onUnload() {
      // 移除事件监听
      uni.$off('userInfoUpdated');
    },

    handleChangeCover() {
      // TODO: 实现更换封面逻辑
      uni.navigateTo({
          url: '/pages/moments/ChangeCover'
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f8f8;
}

.custom-navbar {
  width: 100%;
  height: 220px;
  position: relative;
  margin-bottom: 5px;
  padding-top: env(safe-area-inset-top);
  background: #fff;
  overflow: hidden;
}

.header-bg-img {
  width: 100%;
  height: 260px;
  display: block;
  margin: 0;
  padding: 0;
}

.back-btn {
  position: absolute;
  top: calc(env(safe-area-inset-top) + 38px);
  left: 16px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-ion-ios-arrow-back {
  font-size: 26px;
  color: #fff;
}

.change-cover-btn {
  position: absolute;
  top: calc(env(safe-area-inset-top) + 38px);
  right: 16px;
  display: flex;
  align-items: center;
  background: rgba(255,255,255,0.7);
  border-radius: 12px;
  padding: 2px 10px 2px 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  z-index: 2;
  height: 32px;
}
.icon-ion-ios-camera {
  font-size: 20px;
  color: #222;
  margin-right: 4px;
}
.change-cover-text {
  font-size: 14px;
  color: #222;
}

.profile-section {
  flex: 1;
  padding: 12px;
}

.section {
  margin-bottom: 10px;
  border-radius: 10px;
  background: #fff;
  overflow: hidden;
}
.item-wrap{
  padding: 0 10px;
}
.item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #eee;
  background: #fff;
  /* border-radius: 10px; */
}
.item-multip {
  padding: 10px 0;
}
.icon {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}

.label {
  flex: 1;
  font-size: 16px;
}
.right{
  font-size: 14px;
  margin-right:10px;
  color: #999999;
  max-width: 60vw;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.arrow {
  width: 16px;
  height: 16px;
}
.bordernone {
  border: none;
}

.popup-content {
  background-color: #fff;
  border-radius: 10px 10px 0 0;
  padding: 20rpx;
  padding-bottom: env(safe-area-inset-bottom);
}

.popup-item {
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 16px;
  color: #333;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    left: 20rpx;
    right: 20rpx;
    bottom: 0;
    height: 1px;
    background-color: #EEEEEE;
  }

  &.cancel {
    margin-top: 8px;
    color: #999;

    &::after {
      display: none;
    }
  }

  &:active {
    background-color: #f5f5f5;
  }
}

.region-picker {
  background-color: #fff;

  picker-view {
    width: 100%;
    height: 400rpx;
  }

  .picker-item {
    line-height: 40px;
    text-align: center;
    padding: 0 10px;
  }

  .picker-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1px solid #eee;
    position: relative;
    width: 100%;

    text {
      font-size: 36rpx;

      &:first-child {
        position: absolute;
        left: 40rpx;
        color: #999;
      }

      &:last-child {
        position: absolute;
        right: 0;
        padding-right: 40rpx;
        color: #007AFF;
      }

      &:active {
        opacity: 0.7;
      }
    }
  }
}

.qr-code-container {
  width: 30px;
  height: 30px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 16px;
  color: #333;
  position: relative;
  background-color: #fff;
  
  &::after {
    content: '';
    position: absolute;
    left: 20rpx;
    right: 20rpx;
    bottom: 0;
    height: 1px;
    background-color: #EEEEEE;
  }
  
  &:active {
    background-color: #f5f5f5;
  }
  
  text {
    color: #333;
    font-size: 16px;
  }
}
</style>