<template>
  <view class="container">
    <canvas canvas-id="qrcodeCanvas" style="width: 300px; height: 500px; position: fixed; left: -9999px;"></canvas>
    <CustomHeader>
      <template v-slot:title>
        <text>二维码名片</text>
      </template>
    </CustomHeader>

    <view class="content-wrap">
      <!-- 用户信息区域 -->
      <view class="user-info">
        <image :src="userInfo.avatar || '/static/image/icon/pyqx.png'" class="avatar" mode="aspectFill" />
        <text class="displayName">{{ userInfo.displayName || '地球岛用户' }}</text>
        <text class="location_qr" v-if="userInfo.city">{{ userInfo.city }}</text>
      </view>

      <!-- 二维码区域 -->
      <view class="qr-section">
        <text class="section-title">我的二维码</text>
        <view class="refresh-btn" @click="showQrcode">
          <text>换个样式</text>
        </view>
        <view class="qr-wrap">
          <view class="qr-box">
            <uqrcode
                ref="uqrcode"
                canvas-id="qrcode-canvas"
                :value="qrCodeConfig.text"
                :options="{
                  margin: 10,
                  size: 200,
                  backgroundColor: qrCodeConfig.backgroundColor,
                  foregroundColor: qrCodeConfig.foregroundColor,
                  foregroundImageSrc: qrCodeConfig.logo,
                  foregroundImagePadding: 10,
                  logo: qrCodeConfig.logo,
                  logoSize: qrCodeConfig.logoSize
            }"
            ></uqrcode>

          </view>
          <text class="scan-tip">扫一扫上面的二维码图案，加我为好友</text>
        </view>
      </view>

      <!-- 底部按钮区域 -->
      <view class="action-buttons">
        <button class="action-btn" @click="handleScan">
          <text>扫一扫</text>
        </button>
        <button class="action-btn" @click="handleSave">
          <text>保存图片</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import uQRCode from '@/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue'
import ColorUtil from '@/utils/colorUtil'
import userStore from '@/store/userStore'

export default {
  components: {
    CustomHeader,
    uqrcode: uQRCode
  },
  data() {
    return {
      userInfo: {
        displayName: '',
        userId: '',
        avatar: '',
        gender: '',
        city: '',
        signature: '',
        school: '',
        address: '',
      },
      qrCodeConfig: {
        text: '',
        size: 200,
        margin: 10,
        backgroundColor: '#ffffff',
        foregroundColor: this.getRandomColor(),
        foregroundImageSrc: '/static/logo.png',
        foregroundImagePadding: 10,
        logo: '/static/logo.png',
        logoSize: 50,
      },
      options: {
        margin: 10
      }
    }
  },
  onLoad() {
    this.fetchUserInfo()
  },
  methods: {
    // 生成随机颜色
    getRandomColor() {
      return ColorUtil.getSmartColor()
    },
    
    // 如果需要获取特定色系的颜色，可以添加这个方法
    getColorByType(type) {
      return ColorUtil.getRandomColor(type)
    },
    
    // 如果需要获取渐变色，可以添加这个方法
    getGradientColor() {
      return ColorUtil.getGradientColor()
    },
    
    // 更新二维码中的头像
    updateQRCodeLogo() {
      if (this.userInfo.avatar) {
        console.log('userInfo.avatar:', this.userInfo.avatar)
        this.qrCodeConfig.foregroundImageSrc = this.userInfo.avatar
		this.qrCodeConfig.logo = this.userInfo.avatar
      } else {
        console.log('updateQRCodeLogo: 没有头像')
            // 直接使用静态logo
		this.qrCodeConfig.foregroundImageSrc = '/static/logo.png'
        this.qrCodeConfig.logo = '/static/logo.png'
      }
    },
    
    async fetchUserInfo() {
      try {
        const result = await userStore.getUserInfo();
        this.userInfo = {...this.userInfo, ...result};
        this.updateQRCodeLogo();
        console.log('fetchUserInfo :', this.userInfo)
        this.qrCodeConfig.text = `http://admin.ykjrhl.com/?UserID=${this.userInfo.userId}`;
        this.showQrcode();
      } catch (error) {
        console.error('获取用户信息失败:', error);
        uni.showToast({title: '获取用户信息失败', icon: 'none'});
      }
    },

    handleScan() {
      console.log('handleScan')
      uni.scanCode({
        success: (res) => {
          console.log('扫码结果：', res)
          const scanResult = res.result

          // 处理 URL 类型
          if (scanResult.startsWith('http://') || scanResult.startsWith('https://')) {
            // 处理新的群组URL格式
            if (scanResult.includes('?GroupID=')) {
              const groupIdPart = scanResult.split('GroupID=')[1]
              // 处理可能的额外URL参数
              const groupId = groupIdPart.split('&')[0]
              
              console.log('扫描到群组链接, 群ID:', groupId)
              // 跳转到群组详情页面
              uni.navigateTo({
                url: `/pages/group/GroupDetailPage?groupId=${groupId}`,
                fail: (err) => {
                  console.error('跳转失败:', err)
                  uni.showToast({
                    title: '跳转失败',
                    icon: 'none'
                  })
                }
              })
              return
            }

            // 处理新的用户URL格式
            if (scanResult.includes('?UserID=')) {
              const userIdPart = scanResult.split('UserID=')[1]
              // 处理可能的额外URL参数
              const userId = userIdPart.split('&')[0]
              
              console.log('扫描到用户链接, 用户ID:', userId)
              // 跳转到用户详情页面
              uni.navigateTo({
                url: `/pages/contact/UserDetailPage?userId=${userId}`,
                fail: (err) => {
                  console.error('跳转失败:', err)
                  uni.showToast({
                    title: '跳转失败',
                    icon: 'none'
                  })
                }
              })
              return
            }

            // 其他网址直接跳转到WebPage
            uni.navigateTo({
              url: `/pages/misc/WebPage?url=${encodeURIComponent(scanResult)}`,
              fail: (err) => {
                console.error('跳转失败:', err)
                uni.showToast({
                  title: '跳转失败',
                  icon: 'none'
                })
              }
            })
            return
          }

          // 处理地球号
          if (scanResult.startsWith('EARTHID:')) {
            const userId = scanResult.substring(8)
            console.log('userId:', userId)
            uni.navigateTo({
              url: `/pages/contact/UserDetailPage?userId=${userId}`,
              fail: (err) => {
                console.error('跳转失败:', err)
                uni.showToast({
                  title: '跳转失败',
                  icon: 'none'
                })
              }
            })
            return
          }

          // 处理其他字符串
          uni.navigateTo({
            url: `/pages/misc/StringPage?content=${encodeURIComponent(scanResult)}`,
            fail: (err) => {
              console.error('跳转失败:', err)
              uni.showToast({
                title: '跳转失败',
                icon: 'none'
              })
            }
          })
        },
        fail: (err) => {
          console.error('扫码失败:', err)
        }
      })
    },

    showQrcode() {
      console.log('showQrcode')
      this.qrCodeConfig.foregroundColor = this.getRandomColor();
      // this.$refs.uqrcode.make(this.qrCodeConfig.text, this.qrCodeConfig);
    },

    async handleSave() {

      uni.showLoading({title: '保存中...'});

      // 使用 uni-app 的 canvas API
      const ctx = uni.createCanvasContext('qrcodeCanvas', this);
      const dpr = uni.getSystemInfoSync().pixelRatio;
      const width = 300;
      const height = 500;

      // 绘制白色背景
      ctx.setFillStyle('#ffffff');
      ctx.fillRect(0, 0, width, height);

      // 绘制用户头像
      const avatarSize = 60;
      ctx.save();
      ctx.beginPath();
      ctx.arc(width / 2, 50 + avatarSize / 2, avatarSize / 2, 0, Math.PI * 2);
      ctx.clip();
      ctx.drawImage(this.userInfo.avatar || '/static/image/icon/pyqx.png',
          width / 2 - avatarSize / 2, 50, avatarSize, avatarSize);
      ctx.restore();

      // 绘制用户名和城市
      ctx.setTextAlign('center');
      ctx.setFillStyle('#333333');
      ctx.setFontSize(18);
      ctx.fillText(this.userInfo.displayName || '地球岛用户', width / 2, 50 + avatarSize + 30);

      if (this.userInfo.city) {
        ctx.setFillStyle('#666666');
        ctx.setFontSize(14);
        ctx.fillText(this.userInfo.city, width / 2, 50 + avatarSize + 55);
      }

      // 绘制二维码标题
      // ctx.setFillStyle('#333333');
      // ctx.setFontSize(16);
      // ctx.fillText('我的二维码', width / 2, 50 + avatarSize + 90);

      // 获取二维码图片并绘制
      const qrcode = await new Promise((resolve, reject) => {
        uni.canvasToTempFilePath({
          canvasId: 'qrcode-canvas',
          success: res => resolve(res.tempFilePath),
          fail: reject
        }, this.$refs.uqrcode);
      });

      const qrSize = 200;
      ctx.drawImage(qrcode,
          width / 2 - qrSize / 2, 50 + avatarSize + 110, qrSize, qrSize);

        // 绘制提示文字
        ctx.setFillStyle('#666666');
        ctx.setFontSize(12);
        ctx.fillText('扫一扫上面的二维码图案，加我为好友', 
          width / 2, 50 + avatarSize + 130 + qrSize);

        // 执行绘制
        ctx.draw(false, () => {
          // 将 canvas 转换为图片并保存
          uni.canvasToTempFilePath({
            canvasId: 'qrcodeCanvas',
            success: (res) => {
              uni.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  uni.showToast({
                    title: '保存成功',
                    icon: 'success'
                  });
                },
                fail: (err) => {
                  console.error('保存失败:', err);
                  uni.showToast({
                    title: '保存失败',
                    icon: 'none'
                  });
                }
              });
            },
            fail: (err) => {
              console.error('生成图片失败:', err);
              uni.showToast({
                title: '保存失败',
                icon: 'none'
              });
            }
          });
        });

        uni.hideLoading();
      
    },

    waitForImage(image) {
      return new Promise((resolve) => {
        if (image.complete) {
          resolve();
        } else {
          image.onload = resolve;
        }
      });
    },

    handleShare() {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      })
    },

    handleReport() {
      uni.showToast({
        title: '举报功能开发中',
        icon: 'none'
      })
    },

    // 设置二维码中央的logo
    setQRCodeLogo(logoPath) {
      this.qrCodeConfig.logo = logoPath
      this.qrCodeConfig.logoSize = 50  // 可以根据需要调整大小
      this.showQrcode()  // 重新生成二维码
    },
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.content-wrap {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: 10px;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }
  
  .displayName {
    font-size: 18px;
    color: #333;
    margin-bottom: 5px;
  }
  
  .location_qr {
    font-size: 14px;
    color: #666;
  }
}

.qr-section {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  position: relative;
  
  .section-title {
    font-size: 18px;
    color: #333;
    margin-bottom: 40px;
    text-align: center;
    font-weight: 500;
  }
  
  .refresh-btn {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 14px;
    color: #386BF6;
    padding: 5px 10px;
    
    &:active {
      opacity: 0.8;
    }
  }
}

.qr-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 15px;
  
  .qr-box {
    background: #fff;
    padding: 15px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    margin-bottom: 20px;
  }
  
  .scan-tip {
    font-size: 14px;
    color: #666;
    text-align: center;
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 20px;
  
  .action-btn {
    background: #386BF6;
    border: none;
    color: #fff;
    font-size: 16px;
    padding: 0 30px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
    
    &::after {
      border: none;
    }
  }
}
</style>
