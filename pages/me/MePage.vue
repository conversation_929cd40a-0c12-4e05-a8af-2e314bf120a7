<template>
    <div class="me-container rq-me" :style="{ backgroundColor: '#ffffff', minHeight: '100vh' }">
        <canvas canvas-id="qrcodeCanvas" :style="{
            width: width + 'px',
            height: height + 'px',
            position: 'fixed',
            left: '-9999px'
        }"></canvas>
        <view class="user-profile-container" @click="showUserInfo">
            <div class="user-info me2">
                <image class="portrait me2a" :src="userInfo.avatar || '/static/image/icon/150px.png'" mode="aspectFill"></image>
                <view class="me2b">
                    <view class="me2b1">
                        <view class="me2b1a">
                            {{ userInfo?.displayName || '地球岛会员' }} <i class="icon-ion-ios-arrow-right"></i>
                        </view>
                        <view class="me2b1b">
                            地球号：{{ userInfo?.earthId || '' }}
                            <view class="me2b2img" @click.stop="openQRCode">
                                <image class="img" src="../../static/image/me/erweima.png"></image>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="weather-card">
                    <view class="weather-icon">
                        <image class="img" :src="weatherIcon" mode="aspectFit"></image>
                    </view>
                    <view class="weather-info">
                        <text class="temperature">{{ weather.temp || '28' }}°C {{ weather.city || '义乌' }}</text>
                    </view>
                </view>
            </div>
        </view>
        <view class="rq-hine"></view>
        <view class="me3">
            <view class="me3a">
                <view class="me3a1">经常访问</view>
                <view class="me3a2" @click="showMorePeople">更多<i class="icon-ion-ios-arrow-right"></i></view>
            </view>
            <view class="me3b">
                <view class="me3bitem" v-for="(item, index) in recentVisitors" :key="index" @click="handleVisitorClick(item)">
                    <view class="me3b1">
                        <image class="img" :src="item.userInfo.avatar" mode="aspectFill"></image>
                    </view>
                    <view class="me3b2">{{ item.userInfo.displayName }}</view>
                </view>
                <view v-if="!recentVisitors.length" class="empty-tip">
                    暂无访客记录
                </view>
                <view style="clear: both;"></view>
            </view>
        </view>
        <view class="rq-hine"></view>
        <!-- 功能项列表 -->
        <div class="discovery-container me5">
            <div class="item" @click="goWallet">
                <view class="left-content" style="padding-left: 0px;">
                    <text>钱包</text>
                </view>
                <view class="right-content">
                    <i class="icon-ion-ios-arrow-right"></i>
                </view>
            </div>
            <view class="rq-hine2"></view>


            <div class="item" @click="goCard">
                <view class="left-content" style="padding-left: 0px;">
                    <text>名片</text>
                </view>
                <view class="right-content">
                    <i class="icon-ion-ios-arrow-right"></i>
                </view>
            </div>
            <view class="rq-hine2"></view>

            <div class="item" @click="goComment">
                <view class="left-content" style="padding-left: 0px;">
                    <text>评价 <span class="comment-point">{{ userScore }}</span></text>
                </view>
                <view class="right-content">
                    <i class="icon-ion-ios-arrow-right"></i>
                </view>
            </div>
            <view class="rq-hine2"></view>

            <!-- <div class="item" @click="goCollection">
                <view class="left-content" style="padding-left: 0px;">
                    <text>收藏</text>
                </view>
                <view class="right-content">
                    <text class="count" v-if="collectionCount > 0">{{ collectionCount }}</text>
                    <i class="icon-ion-ios-arrow-right"></i>
                </view>
            </div>
            <view class="rq-hine2"></view> -->

            <div class="item" @click="showDevGuide">
                <view class="left-content" style="padding-left: 0px;">
                    <text>动态</text>
                </view>
                <view class="right-content">
                    <!-- <text class="count" v-if="dynamicCount > 0">{{ dynamicCount }}</text> -->
                    <i class="icon-ion-ios-arrow-right"></i>
                </view>
            </div>
            
            <!-- 动态预览图片 -->
            <view class="moments-preview" v-if="momentImages && momentImages.length > 0">
                <image 
                    v-for="(image, index) in momentImages" 
                    :key="index"
                    :src="image"
                    class="preview-image"
                    mode="aspectFill"
                    @error="handleImageError(index)"
                />
            </view>
            <view class="rq-hine2"></view>

            <div class="item" @click="showSettings">
                <view class="left-content" style="padding-left: 0px;">
                    <text>设置</text>
                </view>
                <view class="right-content">
                    <i class="icon-ion-ios-arrow-right"></i>
                </view>
            </div>
            <view class="rq-hine2"></view>

            <div class="item" @click="goFeedback">
                <view class="left-content" style="padding-left: 0px;">
                    <text>意见反馈</text>
                </view>
                <view class="right-content">
                    <i class="icon-ion-ios-arrow-right"></i>
                </view>
            </div>
            <view class="rq-hine2"></view>

            <div class="item" @click="contactUs">
                <view class="left-content" style="padding-left: 0px;">
                    <text>联系我们</text>
                </view>
                <view class="right-content">
                    <i class="icon-ion-ios-arrow-right"></i>
                </view>
            </div>
        </div>
        <view style="height: 100px; width: 100%; background-color: transparent;"></view>
        <view class="rq-tc" v-if="iserweima" @click="closeQRCode">
            <view class="tc" @click.stop>
                <view class="tcc">
                    <image class="tcc1" :src="userInfo.avatar || '/static/image/icon/150px.png'"></image>
                </view>
                <view class="tcd">{{ userInfo.displayName || '地球岛用户' }}</view>
                <view v-if="userInfo.city" class="tce">{{ userInfo.city || '' }}</view>
                <!-- 二维码区域 -->
                <view>
                    <view class="qr-wrap">
                        <view class="qr-box">
                            <uqrcode ref="uqrcode" canvas-id="qrcode-canvas" :value="qrCodeConfig.text" :options="{
                                margin: 0,
                                size: 180,
                                backgroundColor: qrCodeConfig.backgroundColor,
                                foregroundColor: qrCodeConfig.foregroundColor,
                                foregroundImageSrc: qrCodeConfig.logo,
                                foregroundImagePadding: 10,
                                logo: qrCodeConfig.logo,
                                logoSize: qrCodeConfig.logoSize
                            }">
                            </uqrcode>
                        </view>
                        <text class="scan-tip">扫一扫上面的二维码图案，加我为好友</text>
                    </view>
                </view>

                <!-- 底部按钮区域 -->
                <view class="action-buttons">
                    <view class="action-btn" @click="handleScan">
                        <text>扫一扫</text>
                    </view>
                    <view class="refresh-btn" @click="showQrcode">
                        <text>换个样式</text>
                    </view>
                    <view class="action-btn" @click="handleSave">
                        <text>保存图片</text>
                    </view>

                </view>
            </view>
        </view>
    </div>
</template>

<script>
import wfc from '../../wfc/client/wfc'
import { clear, getItem, setItem } from '../util/storageHelper'
import store from '../../store'
import avengineKit from '../../wfc/av/engine/avengineKit'
import Config from '../../config'
import appServerApi from '@/api/appServerApi'
import weatherApi from '@/api/weatherApi'
import uQRCode from '@/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue'
import ColorUtil from '@/utils/colorUtil'
import topMessage from '@/common/topMessageView'
import util from '@/utils/util'
import checkUpdate from '@/uni_modules/uni-upgrade-center-app/utils/check-update'

export default {
    name: 'MePage',
    components: {
        uqrcode: uQRCode,
    },

    onLoad() {
        // 获取系统信息以获取屏幕宽度
        const systemInfo = uni.getSystemInfoSync()
        this.width = systemInfo.windowWidth * 0.95
        // 首次加载时初始化数据
        this.initPageData()
        this.checkLocationPermission()
    },
    onShow() {
        // 每次页面显示时更新数据
        this.hasLoadedImages = false; // 重置加载状态
        
        // 使用initPageData统一处理数据加载
        this.initPageData().catch(error => {
            console.error('页面数据加载失败:', error);
        });
    },
    onHide() {
        // 页面隐藏时,如果二维码弹窗是显示状态,则关闭它
        // if (this.iserweima) {
        //     this.iserweima = false
        // }
    },
    onReady() {
      checkUpdate();
    },

    data() {
        return {
            iserweima: false,
            userInfo: {},
            weather: {
                temp: '',
                text: '',
            },
            recentVisitors: [],
            imageLoadErrors: [false, false, false],
            defaultImages: Array.from(
                { length: 15 },
                (_, i) => `/static/image/me/tx${101 + i}.png`
            ),
            qrCodeConfig: {
                text: '',
                size: 200,
                margin: 10,
                backgroundColor: '#ffffff',
                foregroundColor: this.getRandomColor(),
                foregroundImageSrc: '/static/logo.png',
                foregroundImagePadding: 10,
                logo: '/static/logo.png',
                logoSize: 50,
            },
            options: {
                margin: 10,
            },
            systemInfo: uni.getSystemInfoSync(),
            width: 300,
            height: 500,
            userScore: 5.0,
            collectionCount: 0,
            walletAmount: 0,
            dynamicCount: 0,
            momentImages: [],
            hasLoadedImages: false
        }
    },
    mounted() {
        // 在mountied阶段先不调用这些方法，等onShow时处理
        // 因为onShow会自动调用这些方法
    },
    created() {},
    computed: {
        cacheKey() {
            return 'recent_visitors_cache'
        },
        momentImagesCacheKey() {
            return 'user_moments_images_cache'
        },
        weatherIcon() {
            const weatherText = this.weather.text || ''
            // 根据天气状态返回不同的图标路径
            if (weatherText.includes('暴风雨')) {
                return '/static/image/weather/thunderstorm.png'
            } else if (weatherText.includes('暴雨')) {
                return '/static/image/weather/heavyrain.png'
            } else if (weatherText.includes('中雨')) {
                return '/static/image/weather/moderaterain.png'
            } else if (weatherText.includes('小雨')) {
                return '/static/image/weather/smallrain.png'
            } else if (weatherText.includes('暴雪')) {
                return '/static/image/weather/stormsnow.png'
            } else if (weatherText.includes('大雪')) {
                return '/static/image/weather/heavysnow.png'
            } else if (weatherText.includes('中雪')) {
                return '/static/image/weather/moderatesnow.png'
            } else if (weatherText.includes('小雪')) {
                return '/static/image/weather/smallsnow.png'
            } else if (weatherText.includes('雾') || weatherText.includes('霾')) {
                return '/static/image/weather/fog.png'
            } else if (weatherText.includes('大风')) {
                return '/static/image/weather/strongwind.png'
            } else if (weatherText.includes('阴')) {
                return '/static/image/weather/overcast.png'
            } else if (weatherText.includes('多云')) {
                return '/static/image/weather/cloudy.png'
            } else if (weatherText.includes('晴')) {
                return '/static/image/weather/sunny.png'
            } else {
                // 默认返回晴天图标
                return '/static/image/weather/sunny.png'
            }
        },
        weatherBackgroundColor() {
            // 根据天气状态返回不同的背景颜色
            const weatherText = this.weather.text || ''
            if (weatherText.includes('雨')) {
                return '#B5D0DC' // 雨天
            } else if (weatherText.includes('雪')) {
                return '#9BAFCB' // 雪天
            } else if (
                weatherText.includes('雾') ||
                weatherText.includes('霾') ||
                weatherText.includes('阴') ||
                weatherText.includes('大风')
            ) {
                return '#ACCFE4' // 雾天、霾天、阴天、大风
            } else if (
                weatherText.includes('云') ||
                weatherText.includes('多云')
            ) {
                return '#7FD3FF' // 多云
            } else if (weatherText.includes('晴')) {
                return '#2FB4FF' // 晴天
            } else {
                return '#2FB4FF' // 默认使用晴天
            }
        },
    },
    methods: {
        async pay() {
            const response = await appServerApi.joinPayGroup({
                gid: '313ec890023047709ad49c0a966b7e50',
                payType: 2,
            })
            console.log('charge', response)

            util.wxpay(response.data, function (res) {
                console.log('1111111', res)
            })
        },
        async pay2() {
            const response = await appServerApi.joinPayGroup({
                gid: '313ec890023047709ad49c0a966b7e50',
                payType: 1,
            })
            util.alipay(response.data, function (res) {
                console.log('1111111', res)
            })
        },
        // 位置授权 注释
        async checkLocationPermission() {
            /*#ifdef APP-PLUS*/
            if (plus.os.name !== 'iOS') {
                var isPermission = plus.navigator.checkPermission(
                    'android.permission.ACCESS_FINE_LOCATION'
                )
                if (isPermission != 'authorized') {
                    // 检查是否已经显示过权限说明
                    const hasShownLocationPermissionTip = getItem('hasShownLocationPermissionTip')
                    
                    // 如果从未显示过权限说明，则显示并记录
                    if (!hasShownLocationPermissionTip) {
                        topMessage.createTopMessage(
                            '位置权限使用说明',
                            '用于向你推荐附近你可能感兴趣的内容、服务或用户等相关信息，当地天气,以展示与你的距离、提升浏览体验，或帮助你在发布的信息或互动中展示位置。'
                        )
                        // 设置标志，记录已经显示过权限说明
                        setItem('hasShownLocationPermissionTip', true)
                    }
                    
                    let res = await topMessage.requestPermissions(
                        'ACCESS_FINE_LOCATION',
                        '位置权限未获得，此权限为了给您查看附近的人，请前往设置中打开'
                    )
                    setTimeout(() => {
                        topMessage.hideTopMessage()
                    }, 300)
                    if (!res.granted[0]) {
                        // 无权限
                        return
                    }
                }
            }
            /*#endif*/
        },
        goWallet() {
            uni.navigateTo({
                url: '/pages/wallet/WalletHome',
            })
        },

        goCard() {
            uni.navigateTo({
                url: '/pages/card/MyCard',
            })
        },

        async handleScan() {
            console.log('handleScan')
            this.iserweima = false
            
            /*#ifdef APP-PLUS*/
            if (plus.os.name !== 'iOS') {
                var isPermission = plus.navigator.checkPermission(
                    'android.permission.CAMERA'
                )
                if (isPermission != 'authorized') {
                    topMessage.createTopMessage(
                        '相机、相册权限使用说明',
                        '用于扫描二维码、条形码等，以便快速访问相关内容或服务。我们不会将相机用于其他用途。'
                    )
                }
                let res = await topMessage.requestPermissions(
                    'CAMERA',
                    '相机权限未获得，此权限用于扫码功能，请前往设置中打开'
                )
                setTimeout(() => {
                    topMessage.hideTopMessage()
                }, 300)
                if (!res.granted[0]) {
                    // 无权限
                    return
                }
            }
            /*#endif*/

            uni.scanCode({
                success: (res) => {
                    console.log('扫码结果：', res)
                    const scanResult = res.result

                    // 处理 URL 类型
                    if (
                        scanResult.startsWith('http://') ||
                        scanResult.startsWith('https://')
                    ) {
                        // 处理新的群组URL格式
                        if (scanResult.includes('?GroupID=')) {
                            const groupIdPart = scanResult.split('GroupID=')[1];
                            const groupId = groupIdPart.split('&')[0];
                            
                            console.log('扫描到群组链接, 群ID:', groupId);
                            uni.navigateTo({
                                url: `/pages/group/GroupDetailPage?groupId=${groupId}`,
                                fail: (err) => {
                                    console.error('跳转失败:', err);
                                    uni.showToast({
                                        title: '跳转失败',
                                        icon: 'none'
                                    });
                                }
                            });
                            return;
                        }

                        // 处理新的用户URL格式
                        if (scanResult.includes('?UserID=')) {
                            const userIdPart = scanResult.split('UserID=')[1]
                            // 处理可能的额外URL参数
                            const userId = userIdPart.split('&')[0]
                            
                            console.log('扫描到用户链接, 用户ID:', userId)
                            // 跳转到用户详情页面
                            uni.navigateTo({
                                url: `/pages/contact/UserDetailPage?userId=${userId}`,
                                fail: (err) => {
                                    console.error('跳转失败:', err)
                                    uni.showToast({
                                        title: '跳转失败',
                                        icon: 'none'
                                    })
                                }
                            })
                            return
                        }

                        uni.navigateTo({
                            url: `/pages/misc/WebPage?url=${encodeURIComponent(
                                scanResult
                            )}`,
                            fail: (err) => {
                                console.error('跳转失败:', err)
                                uni.showToast({
                                    title: '跳转失败',
                                    icon: 'none',
                                })
                            },
                        })
                        return
                    }

                    // 处理地球号
                    if (scanResult.startsWith('EARTHID:')) {
                        const userId = scanResult.substring(8)
                        uni.navigateTo({
                            url: `/pages/contact/UserDetailPage?userId=${userId}`,
                            fail: (err) => {
                                console.error('跳转失败:', err)
                                uni.showToast({
                                    title: '跳转失败',
                                    icon: 'none'
                                })
                            }
                        })
                        return
                    }

                    // 处理其他字符串
                    uni.navigateTo({
                        url: `/pages/misc/StringPage?content=${encodeURIComponent(
                            scanResult
                        )}`,
                        fail: (err) => {
                            console.error('跳转失败:', err)
                            uni.showToast({
                                title: '跳转失败',
                                icon: 'none',
                            })
                        },
                    })
                },
                fail: (err) => {
                    console.error('扫码失败:', err)
                },
            })
        },

        async showQrcode() {
            console.log('showQrcode')
            return new Promise((resolve) => {
                this.qrCodeConfig.foregroundColor = this.getRandomColor()
                this.$nextTick(() => {
                    if (this.$refs.uqrcode) {
                        this.$refs.uqrcode.make();
                        setTimeout(() => {
                            resolve();
                        }, 500);
                    } else {
                        resolve();
                    }
                });
            });
        },

        showUserInfo() {
            store.setCurrentFriend(this.userInfo)
            uni.navigateTo({
                url: '/pages/me/UserInfo',
                success: () => {
                    console.log('nav to UserDetailPage success')
                },
                fail: (err) => {
                    console.log('nav to UserDetailPage err', err)
                },
            })
        },
        showService() {
            uni.navigateTo({
                url: '/pages/misc/WebViewPage?url=https://wildfirechat.cn/',
                fail: (e) => {
                    console.log(e)
                },
            })
        },
        showDevGuide() {
            uni.navigateTo({
                url: `/pages/moments/SelfMoments?userId=${this.userInfo.userId}`,
                fail: (e) => {
                    console.log(e)
                },
            })
        },
        showSettings() {
            uni.navigateTo({
                url: '/pages/settings/Settings',
                fail: (e) => {
                    console.log(e)
                },
            })
        },
        logout() {
            wfc.disconnect(true, false)
            clear()
            uni.reLaunch({
                url: '/pages/login/login',
            })
        },
        showAbout() {
            uni.navigateTo({
                url: '/pages/misc/WebViewPage?url=https://wildfirechat.cn/',
                fail: (e) => {
                    console.log(e)
                },
            })
        },
        showApiTest() {
            uni.navigateTo({
                url: '/pages/misc/ApiTestPage',
                fail: (e) => {
                    console.log(e)
                },
            })
        },
        async initPageData() {
            try {
                // 先获取用户信息
                await this.fetchUserInfo()
                
                // 用户信息加载完成后，再获取其他数据
                if (this.userInfo && this.userInfo.userId) {
                    // 获取其他数据
                    await Promise.all([
                        this.fetchRecentVisitors(),
                        this.getUserScore(),
                        this.fetchThreePic(),
                        // this.fetchMomentImages(),
                        // this.fetchWalletAmount(),
                        this.fetchWeatherInfo()
                    ])
                    
                    console.log('所有数据加载完成')
                } else {
                    console.log('用户未登录或用户信息不完整，跳过加载其他数据')
                }
            } catch (error) {
                console.error('初始化页面数据失败:', error)
            }
        },
        async fetchUserInfo() {
            try {
                // 从缓存中获取用户ID
                const userId = getItem('userId');
                if (!userId) {
                    console.log('未找到用户ID，用户可能未登录');
                    return;
                }
                
                // 尝试获取用户信息
                const userInfo = await appServerApi.getUserInfo();
                if (userInfo) {
                    this.userInfo = userInfo;
                    // 更新二维码内容
                    this.qrCodeConfig.text = `http://admin.ykjrhl.com/?UserID=${userInfo.userId}`;
                    console.log('用户信息获取成功');
                    return userInfo;
                } else {
                    console.error('获取到的用户信息为空');
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                // 尝试从缓存获取
                const cachedUserInfo = getItem('userInfo');
                if (cachedUserInfo) {
                    console.log('从缓存加载用户信息');
                    this.userInfo = cachedUserInfo;
                    this.qrCodeConfig.text = `http://admin.ykjrhl.com/?UserID=${cachedUserInfo.userId}`;
                    return cachedUserInfo;
                }
            }
        },
        async fetchRecentVisitors() {
            try {
                // 先加载缓存数据
                this.loadFromCache()

                // 检查用户ID是否可用
                if (!this.userInfo || !this.userInfo.userId) {
                    console.log('用户ID不可用，无法获取访客数据')
                    return
                }

                // 请求新数据
                const response = await appServerApi.getUserMomentList({
                    pageNo: 1,
                    pageSize: 30,
                    userId: this.userInfo.userId
                })

                if (response?.data?.result) {
                    // 处理访客列表
                    const uniqueVisitors = new Map()
                    const newVisitors = response.data.result
                        .filter((item) => item.userInfo)
                        .reduce((acc, item) => {
                            if (!uniqueVisitors.has(item.userInfo.avatar)) {
                                uniqueVisitors.set(item.userInfo.avatar, true)
                                acc.push({
                                    userInfo: {
                                        avatar:
                                            item.userInfo.avatar ||
                                            '/static/image/icon/150px.png',
                                        displayName:
                                            item.userInfo.displayName ||
                                            '地球岛用户',
                                        userId: item.userInfo.userId,
                                    },
                                })
                            }
                            return acc
                        }, [])
                        .slice(0, 5)

                    // 比对数据是否有变化
                    const visitorsChanged =
                        JSON.stringify(newVisitors) !==
                        JSON.stringify(this.recentVisitors)

                    if (visitorsChanged) {
                        // 更新数据
                        this.recentVisitors = newVisitors
                        // 更新缓存
                        this.updateCache()
                        console.log('访客数据已更新')
                    } else {
                        console.log('访客数据无变化')
                    }
                }
            } catch (error) {
                console.error('加载数据失败:', error)
                // 如果没有缓存数据，显示错误提示
                if (!this.recentVisitors.length) {
                    uni.showToast({
                        title: '加载失败',
                        icon: 'none',
                    })
                }
            }
        },
        handleVisitorClick(visitor) {
            if (visitor.userInfo.userId) {
                uni.navigateTo({
                    url: `/pages/moments/SelfMoments?userId=${visitor.userInfo.userId}`,
                    fail: (err) => {
                        console.error('跳转失败:', err)
                        uni.showToast({
                            title: '跳转失败',
                            icon: 'none',
                        })
                    },
                })
            }
        },
        handleImageError(index) {
            console.error('图片加载失败:', this.momentImages[index])
            // 移除加载失败的图片
            this.momentImages = this.momentImages.filter((_, i) => i !== index)
        },
        async handleSave() {
            try {
                /*#ifdef APP-PLUS*/
                if (plus.os.name !== 'iOS') {
                    // 获取Android版本
                    let androidVersion = parseInt(plus.os.version);
                    console.log('Android版本:', androidVersion);
                    
                    // 根据Android版本选择合适的权限
                    let permissionName = 'WRITE_EXTERNAL_STORAGE';
                    
                    // Android 13及以上使用细分的媒体权限
                    if (androidVersion >= 13) {
                        permissionName = 'READ_MEDIA_IMAGES';
                        console.log('使用READ_MEDIA_IMAGES权限');
                    } else {
                        console.log('使用WRITE_EXTERNAL_STORAGE权限');
                    }
                    
                    // 检查权限状态
                    var isPermission = plus.navigator.checkPermission('android.permission.' + permissionName);
                    console.log('存储权限状态:', isPermission);
                    
                    // 如果权限已授权，直接保存图片
                    if (isPermission === 'authorized') {
                        console.log('权限已授权，直接保存图片');
                        await this.saveQRCodeToAlbum();
                        return;
                    }
                    
                    // 显示权限使用说明
                    topMessage.createTopMessage(
                        '权限使用说明',
                        '需要您开启存储权限，以便将您的个人二维码保存到相册中，方便您分享给好友。我们不会访问您相册中的其他图片。'
                    );
                    
                    // 请求权限
                    let res = await topMessage.requestPermissions(
                        permissionName,
                        '存储权限未获得，此权限用于保存二维码到相册，请前往设置中打开'
                    );
                    
                    console.log('权限请求结果:', JSON.stringify(res));
                    
                    setTimeout(() => {
                        topMessage.hideTopMessage();
                    }, 300);
                    
                    // 检查权限结果中是否有重复前缀问题
                    const hasDuplicatePrefix = res.deniedAlways && res.deniedAlways.some(perm => 
                        perm.includes('android.permission.android.permission')
                    );
                    
                    if (hasDuplicatePrefix) {
                        console.log('检测到权限前缀重复问题，尝试直接保存');
                        await this.saveQRCodeToAlbum();
                        return;
                    }
                    
                    // 如果权限请求失败
                    if (!res.granted || res.granted.length === 0) {
                        // 检查真实权限状态再次确认
                        const realPermissionStatus = plus.navigator.checkPermission('android.permission.' + permissionName);
                        console.log('再次确认权限状态:', realPermissionStatus);
                        
                        if (realPermissionStatus === 'authorized') {
                            console.log('权限实际已授权，直接保存图片');
                            await this.saveQRCodeToAlbum();
                            return;
                        }
                        
                        // 检查是否被永久拒绝
                        if (res.deniedAlways && res.deniedAlways.includes('android.permission.' + permissionName)) {
                            uni.showModal({
                                title: '存储权限已被禁用',
                                content: '无法保存二维码到相册，请在系统设置中开启存储权限',
                                confirmText: '去设置',
                                cancelText: '取消',
                                success: (modalRes) => {
                                    if (modalRes.confirm) {
                                        // 跳转到应用权限设置页面
                                        if (plus.os.name === 'Android') {
                                            plus.runtime.openURL('package:' + plus.runtime.appid);
                                        }
                                    } else {
                                        // 尝试直接保存，某些设备可能不需要明确权限
                                        this.saveQRCodeToAlbum();
                                    }
                                }
                            });
                            return;
                        }
                        
                        uni.showToast({
                            title: '无法保存二维码，请授予存储权限',
                            icon: 'none',
                            duration: 2000
                        });
                        return;
                    }
                }
                await this.showQrcode();
                /*#endif*/
                
                // iOS或已获得权限，直接保存
                await this.saveQRCodeToAlbum();
                
            } catch (error) {
                uni.hideLoading();
                console.error('保存二维码失败:', error);
                uni.showToast({
                    title: '保存失败: ' + (error?.message || '未知错误'),
                    icon: 'none',
                    duration: 2000
                });
            }
        },
        
        // 将二维码保存到相册的具体实现
        async saveQRCodeToAlbum() {
            uni.showLoading({ title: '保存中...' });
            
            try {
                // 确保二维码生成完成
                await new Promise((resolve) => {
                    setTimeout(resolve, 800);
                });
                
                // 使用 uni-app 的 canvas API
                const ctx = uni.createCanvasContext('qrcodeCanvas', this);
                // 使用设备宽度的95%作为画布宽度
                const width = this.systemInfo.windowWidth * 0.95;
                const height = 500;

                // 绘制白色背景
                ctx.setFillStyle('#ffffff');
                ctx.fillRect(0, 0, width, height);

                // 绘制用户头像
                const avatarSize = 50;
                ctx.save();
                ctx.beginPath();
                ctx.arc(
                    width / 2,
                    50 + avatarSize / 2,
                    avatarSize / 2,
                    0,
                    Math.PI * 2
                );
                ctx.clip();
                ctx.drawImage(
                    this.userInfo.avatar || '/static/logo.png',
                    width / 2 - avatarSize / 2,
                    50,
                    avatarSize,
                    avatarSize
                );
                ctx.restore();

                // 绘制用户名和城市
                ctx.setTextAlign('center');
                ctx.setFillStyle('#333333');
                ctx.setFontSize(15);
                ctx.fillText(
                    this.userInfo.displayName || '地球岛用户',
                    width / 2,
                    50 + avatarSize + 30
                );

                if (this.userInfo.city) {
                    ctx.setFillStyle('#666666');
                    ctx.setFontSize(13);
                    ctx.fillText(
                        this.userInfo.city,
                        width / 2,
                        50 + avatarSize + 75
                    );
                }

                // 获取二维码图片并绘制
                const qrcode = await new Promise((resolve, reject) => {
                    uni.canvasToTempFilePath(
                        {
                            canvasId: 'qrcode-canvas',
                            success: (res) => resolve(res.tempFilePath),
                            fail: reject,
                        },
                        this.$refs.uqrcode
                    );
                });

                // 保持二维码尺寸与组件一致
                const qrSize = 180;
                // 在动态宽度的画布上居中显示二维码
                const qrX = (width / 2) - (qrSize / 2);
                // 垂直位置保持不变
                const qrY = 50 + avatarSize + 130;
                
                ctx.drawImage(
                    qrcode,
                    qrX,
                    qrY,
                    qrSize,
                    qrSize
                );

                // 绘制提示文字
                ctx.setFillStyle('#666666');
                ctx.setFontSize(12);
                ctx.fillText(
                    '扫一扫上面的二维码图案，加我为好友',
                    width / 2,
                    50 + avatarSize + 150 + qrSize
                );

                // 执行绘制
                await new Promise((resolve) => {
                    ctx.draw(false, resolve);
                });

                // 将 canvas 转换为图片并保存
                const tempFilePath = await new Promise((resolve, reject) => {
                    uni.canvasToTempFilePath({
                        canvasId: 'qrcodeCanvas',
                        success: (res) => resolve(res.tempFilePath),
                        fail: reject
                    });
                });

                // 保存到相册
                await new Promise((resolve, reject) => {
                    uni.saveImageToPhotosAlbum({
                        filePath: tempFilePath,
                        success: () => {
                            uni.hideLoading();
                            uni.showToast({
                                title: '保存成功',
                                icon: 'success',
                                duration: 2000
                            });
                            this.iserweima = false;
                            resolve();
                        },
                        fail: (err) => {
                            console.error('保存到相册失败:', err);
                            reject(new Error('保存到相册失败'));
                        }
                    });
                });
            } catch (error) {
                uni.hideLoading();
                throw error; // 向上传递错误
            }
        },
        async fetchWeatherInfo() {
            try {
                const weatherInfo = await weatherApi.getWeatherInfo()
                this.weather = weatherInfo
            } catch (error) {
                console.error('获取天气信息失败:', error)
            }
        },
        openQRCode() {
            this.iserweima = true
            this.qrCodeConfig.text = `http://admin.ykjrhl.com/?UserID=${this.userInfo.userId}`
            this.updateQRCodeLogo()
            this.showQrcode()
        },
        closeQRCode() {
            this.iserweima = false
        },
        showMorePeople() {
            uni.navigateTo({
                url: '/pages/me/MorePeople',
                fail: (err) => {
                    console.error('跳转失败:', err)
                    uni.showToast({
                        title: '跳转失败',
                        icon: 'none',
                    })
                },
            })
        },
        // 生成随机颜色
        getRandomColor() {
            return ColorUtil.getSmartColor()
        },
        // 如果需要多个颜色：
        getMultipleColors(count) {
            return ColorUtil.getMultiColors(count)
        },
        // 更新二维码中的头像
        updateQRCodeLogo() {
            if (this.userInfo.avatar) {
                console.log('userInfo.avatar:', this.userInfo.avatar)
                this.qrCodeConfig.foregroundImageSrc = this.userInfo.avatar
                this.qrCodeConfig.logo = this.userInfo.avatar
            } else {
                console.log('updateQRCodeLogo: 没有头像')
                this.qrCodeConfig.foregroundImageSrc = '/static/logo.png'
                this.qrCodeConfig.logo = '/static/logo.png'
            }
        },
        // 从缓存加载数据
        loadFromCache() {
            try {
                const cachedData = getItem(this.cacheKey)
                if (cachedData) {
                    this.recentVisitors = cachedData.visitors || []
                    console.log(
                        '从缓存加载访客数据:',
                        this.recentVisitors.length,
                        '条'
                    )
                }
            } catch (error) {
                console.error('加载缓存数据失败:', error)
            }
        },

        // 更新缓存
        updateCache() {
            setItem(this.cacheKey, {
                visitors: this.recentVisitors,
                timestamp: Date.now(),
            })
        },

        goComment() {
            uni.navigateTo({
                url: '/pages/user/Comment?userId=' + this.userInfo.userId + '&isComment=false&isMyFriend=true'
            })
        },

        goCollection() {
            uni.navigateTo({
                url: '/pages/me/Collection'
            })
        },

        async getUserScore() {
            try {
                if (!this.userInfo || !this.userInfo.userId) {
                    console.log('用户信息未加载，暂不获取评分');
                    return;
                }
                console.log('获取用户ID：', this.userInfo.userId);
                const response = await appServerApi.getUserScore(this.userInfo.userId)
                console.log('获取用户评分：', response.data?.score);
                this.userScore = response.data?.score || 5.0
            } catch (error) {
                console.error("获取用户评分失败:", error)
            }
        },

        async fetchMomentImages() {
            try {
                // 先尝试从缓存加载
                const cachedImages = getItem(this.momentImagesCacheKey);
                const now = Date.now();
                const cacheExpireTime = 5 * 60 * 1000; // 5分钟缓存过期时间
                
                if (cachedImages && 
                    cachedImages.userId === this.userInfo.userId && 
                    cachedImages.images.length > 0 &&
                    (now - cachedImages.timestamp) < cacheExpireTime) {
                    console.log('从缓存加载动态图片:', cachedImages.images.length, '张');
                    this.momentImages = cachedImages.images;
                    this.dynamicCount = cachedImages.count || 0;
                    
                    // 静默更新缓存
                    this.silentUpdateMomentImages();
                    return;
                }
                
                // 缓存不存在或已过期，重新请求
                await this.loadMomentImagesFromServer();
            } catch (error) {
                console.error('获取动态图片失败:', error);
                this.momentImages = [];
                this.dynamicCount = 0;
            }
        },
        
        // 静默更新动态图片缓存
        async silentUpdateMomentImages() {
            try {
                // 后台更新，不影响界面显示
                this.loadMomentImagesFromServer();
            } catch (error) {
                console.error('静默更新动态图片失败:', error);
            }
        },

        async fetchThreePic(){
            const response = await appServerApi.getThreePic(this.userInfo.userId)
            if(response?.data){
                this.momentImages = response.data;
            }
        },
        
        // 从服务器加载动态图片
        async loadMomentImagesFromServer() {
            const response = await appServerApi.getUserMomentList({
                pageNo: 1,
                pageSize: 10,
                userId: this.userInfo.userId
            });
            
            if (response?.data?.result) {
                // 设置动态数量
                this.dynamicCount = response.data.result.length;
                
                // 只收集图片URL(排除视频)
                const allImages = response.data.result
                    .filter(moment => moment.url && moment.url.length > 0)
                    .reduce((acc, moment) => {
                        // 只添加图片URL,排除视频URL
                        const imageUrls = moment.url.filter(url => {
                            const videoExtensions = ['.mp4', '.mov', '.m4v', '.3gp', '.avi', '.m3u8'];
                            return !videoExtensions.some(ext => url.toLowerCase().includes(ext));
                        });
                        return [...acc, ...imageUrls];
                    }, []);
                    
                // 只取前三张图片
                const newImages = allImages.slice(0, 3);
                
                // 更新界面
                this.momentImages = newImages;
                
                // 更新缓存
                setItem(this.momentImagesCacheKey, {
                    userId: this.userInfo.userId,
                    images: newImages,
                    count: this.dynamicCount,
                    timestamp: Date.now()
                });
            } else {
                this.momentImages = [];
                this.dynamicCount = 0;
                
                // 清除缓存
                setItem(this.momentImagesCacheKey, null);
            }
        },

        async fetchWalletAmount() {
            try {
                const response = await appServerApi.getWallet()
                if (response?.data) {
                    this.walletAmount = response.data.balance || 0
                }
            } catch (error) {
                console.error('获取钱包余额失败:', error)
            }
        },

        contactUs() {
            uni.showModal({
                title: '联系我们',
                content: '客服电话：15669611258\n客服邮箱：<EMAIL>\n工作时间：周一至周六 9:00-18:00',
                showCancel: false,
                confirmText: '确定'
            })
        },

        goFeedback() {
            uni.navigateTo({
                url: '/pages/me/Feedback'
            })
        },
    },
}
</script>

<style lang="scss" scoped>
.img {
    width: 100%;
    height: 100%;
}

.user-profile-container {
    position: relative;
    width: 100%;
    background-color: #ffffff;
    cursor: pointer;
    
    &:active {
        background-color: #f5f5f5;
    }
}

.rq-me {
    .me5 {
        padding: 0 15px;
        margin-bottom: 10px;
        background-color: #ffffff;
    }

    .me4 {
        padding: 15px 15px 10px;
        margin-bottom: 10px;
        background-color: #ffffff;

        .me4c {
            width: 100%;
            padding: 10rpx;

            .images-row {
                display: flex;
                justify-content: space-between;
                gap: 10rpx;
            }

            .image-item {
                width: 32%;
                height: 160rpx;
                border-radius: 8rpx;
                overflow: hidden;
                background-color: #f5f5f5;

                .dynamic-image {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }

        .me4b2 {
            font-size: 16px;
            color: #000000;
        }
    }

    .me3 {
        width: 100%;
        background-color: #ffffff;
        padding: 15px 15px 10px;
        margin-bottom: 10px;

        .me3a {
            display: flex;
            justify-content: space-between;
            width: 100%;
            align-items: center;

            .me3a1 {
                font-size: 16px;
                padding-left: 20rpx;
            }

            .me3a2 {
                font-size: 12px;
                color: #999999;
                display: flex;
                align-items: center;
                gap: 8px;
                
                i {
                    color: #c0c0c1;
                }
            }
        }

        .me3b {
            padding: 15px 0 5px;

            .me3bitem {
                float: left;
                width: 20%;
                display: flex;
                justify-content: space-between;
                flex-direction: column;
                align-items: center;

                .me3b1 {
                    width: 40px;
                    height: 40px;
                    border-radius: 8px;
                    overflow: hidden;
                }

                .me3b2 {
                    width: 50px;
                    font-size: 12px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }

        .empty-tip {
            text-align: center;
            color: #999;
            font-size: 12px;
            padding: 20px 0;
        }
    }

    .me2 {
        padding: 15px;
        padding-bottom: 30px;
        background-color: #ffffff;
        position: relative;
        display: flex;
        align-items: center;

        .me2a {
            float: left;
            width: 60px;
            height: 60px;
            border-radius: 8px;
            overflow: hidden;
        }

        .me2b {
            float: right;
            margin-left: 15px;
            width: calc(100% - 195px); /* 减少宽度，为天气卡片留出更多空间 */
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 60px;

            .me2b1 {
                display: flex;
                flex-direction: column;
                justify-content: center;
                width: 100%;
                gap: 8px;

                .me2b1a {
                    font-size: 18px;
                    color: #000000;

                    i {
                        margin-left: 10px;
                    }
                }

                .me2b1b {
                    font-size: 12px;
                    color: #969698;
                    display: flex;
                    align-items: center;
                    gap: 6px;

                    .me2b2img {
                        width: 16px;
                        height: 16px;
                        margin-left: 4px;
                    }
                }
            }
        }

        .weather-card {
            display: flex;
            align-items: center;
            background: #B8DEFF;
            padding: 8px 18px;
            border-radius: 100px;
            gap: 8px;
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            min-width: 140px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

            .weather-icon {
                width: 22px;
                height: 22px;
                display: flex;
                align-items: center;
                justify-content: center;

                .img {
                    width: 22px;
                    height: 22px;
                    display: block;
                }
            }

            .weather-info {
                display: flex;
                align-items: center;
                color: #4A7DB3;
                font-size: 16px;
                line-height: 22px;

                .temperature {
                    font-weight: normal;
                    display: flex;
                    align-items: center;
                    height: 22px;
                    white-space: nowrap;
                }
            }
        }
    }
}

.me-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: auto;
    background-color: #ffffff;
    min-height: 100vh;
    padding-top: 70px;
}

.user-info {
    width: 100%;
    padding: 15px;
    height: 90px;
    background: white;
    margin-bottom: 10px;
    position: relative;
}

.user-info:active {
    background: #d6d6d6;
}

.user-info .portrait {
    width: 60px;
    height: 60px;
    border-radius: 8px;
}

.about {
    width: 100%;
    padding: 15px 10px;
    background: white;
}

.about:active {
    background: #d6d6d6;
}

.discovery-container {
    width: 100%;
    background-color: #ffffff;
    margin-bottom: 10px;
    padding: 0;

    .item {
        display: flex;
        align-items: center;
        // padding: 0 15px;
        justify-content: space-between;
        height: 56px;
        width: 100%;
        box-sizing: border-box;
        margin: 0;
        position: relative;
        background: #fff;
        transition: background 0.2s;

        image {
            width: 26px;
            height: 26px;
            margin-right: 16px;
        }

        text {
            font-size: 16px;
            color: #000000;
        }

        .left-content {
            display: flex;
            align-items: center;

            image {
                width: 26px;
                height: 26px;
                margin-right: 16px;
            }

            text {
                font-size: 16px;
                color: #000000;
            }

            .comment-point {
                color: #FF2222;
                margin-left: 4px;
            }
        }

        .right-content {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #999;

            .count {
                font-size: 14px;
            }

            i {
                color: #c0c0c1;
            }
        }

        &:active {
            background: #d6d6d6;
        }
    }

    .moments-preview {
        padding: 12px 15px;
        display: flex;
        gap: 8px;
        margin: 0 15px;
        
        .preview-image {
            width: 104px;
            height: 104px;
            border-radius: 8px;
            object-fit: cover;
        }
    }
}

.rq-hine2 {
    height: 1px;
    background-color: #efefef;
    margin: 0 15px;
}

.rq-hine {
    height: 10px;
    background-color: #f5f5f5;
    width: 100%;
}

.moments-container {
    background-color: #ffffff;
    
    &:active {
        background-color: #f5f5f5;
    }
}

.logout-button {
    margin-top: 20px;
    width: 120px;
    height: 48px;
}

.item {
    display: flex;
    align-items: center;
    padding: 0 15px;
    height: 56px;
    width: 100%;
    box-sizing: border-box;
    margin: 0;
    position: relative;
    background: #fff;
    transition: background 0.2s;
}

.item:active {
    background: #d6d6d6;
}

.item image {
    width: 26px;
    height: 26px;
    margin-right: 16px;
}

.item text {
    font-size: 16px;
    color: #000000;
}

.item i {
    color: #c0c0c1;
}

.item:active {
    background: #d6d6d6;
}

.me3bitem {
    cursor: pointer;

    &:active {
        opacity: 0.8;
    }

    .me3b1 {
        display: flex;
        justify-content: center;
        align-items: center;

        .img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .me3b2 {
        text-align: center;
        margin-top: 10rpx;
        font-size: 26rpx;
        color: #333;
    }
}

.tch {
    margin-top: 5px;
    color: #386bf6;
    font-size: 16px;
    text-align: center;
    padding: 10px;

    &:active {
        opacity: 0.8;
    }
}

.rq-tc {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tc {
    position: relative;
    width: 80%;
    height: auto !important;
    background: #fff;
    border-radius: 12px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: center;
    .tcc {
        margin-top: 0 !important;
    }
    .tcc1 {
        width: 50px !important;
        height: 50px !important;
    }
}

.qr-section {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;

    .section-title {
        font-size: 16px;
        color: #333;
        margin-bottom: 20px;
        text-align: center;
    }
}

.qr-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;

    .qr-box {
        background: #fff;
        padding: 15px;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        margin-bottom: 15px;
    }

    .scan-tip {
        font-size: 12px;
        color: #999999;
        text-align: center;
    }
}

.action-buttons {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    .action-btn {
        color: #386bf6;
        font-size: 14px;
    }
    .refresh-btn {
        color: #386bf6;
        font-size: 14px;
        padding: 0 20px;
        margin: 0 20px;
        border-left: 1px solid #efefef;
        border-right: 1px solid #efefef;
    }
}

.moments-container {
    background-color: #ffffff;
    
    &:active {
        background-color: #f5f5f5;
    }
}

.wallet-text {
    padding-left: 0;
    margin-left: 0;
}

.icon-placeholder {
    width: 26px;
    height: 26px;
    margin-right: 16px;
    display: inline-block;
}

.no-icon {
    padding-left: 0;
}

.item .left-content.no-icon {
    margin-left: 15px;
}
</style>
