<template>
	<view class="feedback-reply">
		<CustomHeader background-color="#ffffff">
			<template v-slot:title>
				<text class="title">反馈记录</text>
			</template>
		</CustomHeader>

		<view class="content">
			<!-- 原始反馈 -->
			<view class="feedback-item">
				<view class="feedback-content">
					<text class="me">我：</text>
					<text class="text">{{ feedbackDetail.content }}</text>
				</view>
				<view class="images" v-if="feedbackDetail.images && feedbackDetail.images.length > 0">
					<image
						v-for="(img, idx) in feedbackDetail.images"
						:key="idx"
						:src="img"
						class="img"
						mode="aspectFill"
						@click="previewImage(idx, feedbackDetail.images)"
					/>
				</view>
				<view class="date">{{ feedbackDetail.date }}</view>
			</view>

			<!-- 回复列表 -->
			<view v-if="replyList.length === 0" class="no-replies">
				<text class="no-replies-text">暂无回复</text>
			</view>
			<view v-for="reply in replyList" :key="reply.id" class="reply-item">
				<view class="reply-content">
					<text class="developer" v-if="reply.isAdmin">开发者：</text>
					<text class="user" v-else>我</text>
					<text class="text">{{ reply.content }}</text>
				</view>
				<view class="images" v-if="reply.images && reply.images.length > 0">
					<image
						v-for="(img, idx) in reply.images"
						:key="idx"
						:src="img"
						class="img"
						mode="aspectFill"
						@click="previewImage(idx, reply.images)"
					/>
				</view>
				<view class="date">{{ reply.date }}</view>
			</view>

			<!-- 输入区域 - 移动到内容流中 -->
			<view class="input-area" v-if="canReply">
				<view class="input-container">
					<!-- 图片上传区域 -->
					<view class="image-picker">
						<view class="image-list">
							<view 
								v-for="(image, index) in replyImages" 
								:key="index" 
								class="image-item"
							>
								<image :src="image" mode="aspectFill" @click="previewImage(index, replyImages)"></image>
								<view class="delete-icon" @click.stop="deleteImage(index)">×</view>
							</view>
							
							<view class="image-add" @click="chooseImage" v-if="replyImages.length < 6">
								<text class="add-icon">+</text>
							</view>
						</view>
					</view>

					<textarea
						v-model="replyContent"
						placeholder="请输入回复内容"
						class="input"
						:maxlength="500"
					/>
				</view>
				
				<view class="submit-container">
					<button class="submit-btn" @click="submitReply" :disabled="!canSubmit">
						发送回复
					</button>
				</view>
			</view>

			<!-- 无法回复时的提示 -->
			<view class="no-reply-tip" v-else>
				<text class="tip-text" v-if="replyList.length === 0">请等待开发者回复</text>
				<text class="tip-text" v-else>请等待开发者回复后再继续留言</text>
			</view>
		</view>
	</view>
</template>

<script>
import appServerApi from '@/api/appServerApi'
import CustomHeader from '@/components/custom-header'
import Config from '@/config'
import { getItem } from '@/pages/util/storageHelper'
	
	export default {
		components: {
        	CustomHeader
    	},
		data() {
			return {
				feedbackId: '',
				feedbackDetail: {},
				replyList: [],
				replyContent: '',
				replyImages: [], // 新增：回复图片列表
				isSubmitting: false, // 新增：提交状态
			}
		},
		computed: {
			canSubmit() {
				return this.replyContent.trim().length > 0 || this.replyImages.length > 0
			},
			// 新增：判断是否可以回复
			canReply() {
				// 如果没有回复，不可以回复
				if (this.replyList.length === 0) {
					return false
				}
				// 只有最后一条回复是开发者回复时，才可以回复
				const lastReply = this.replyList[this.replyList.length - 1]
				return lastReply.isAdmin
			}
		},
		async onLoad(options) {
			this.feedbackId = options.feedbackId
			await this.loadFeedbackDetail()
			await this.loadReplyList()
		},
		methods: {
			async loadFeedbackDetail() {
				try {
					const res = await appServerApi.getFeedbackList(1, 100)
					const feedback = res.data?.result?.find(item => item.id == this.feedbackId)
					if (feedback) {
						// 处理图片URL - 可能是数组或字符串
						let images = []
						if (feedback.url) {
							if (Array.isArray(feedback.url)) {
								images = feedback.url.filter(url => url && url.trim())
							} else if (typeof feedback.url === 'string') {
								images = feedback.url.split(',').filter(url => url.trim())
							}
						}
						
						this.feedbackDetail = {
							id: feedback.id,
							content: feedback.content,
							images: images,
							date: feedback.createTime || ''
						}
					}
					console.log('处理后的反馈详情', this.feedbackDetail)
				} catch (error) {
					console.error('加载反馈详情失败', error)
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					})
				}
			},
			async loadReplyList() {
				try {
					const res = await appServerApi.getFeedbackReplyList(this.feedbackId, 1, 50)
					console.log('回复列表数据', res)
					this.replyList = (res.data || []).map(item => ({
						id: item.id,
						content: item.content,
						date: item.createTime || '未知时间',
						isAdmin: item.userId === 'admin' || item.isAdmin || false,
						images: Array.isArray(item.url) ? item.url : []
					}))
					console.log('处理后的回复列表', this.replyList)
				} catch (error) {
					console.error('加载回复列表失败', error)
				}
			},
			// 选择图片
			chooseImage() {
				uni.chooseImage({
					count: 6 - this.replyImages.length,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						// 过滤重复图片
						const newImages = res.tempFilePaths.filter(newPath => {
							return !this.replyImages.some(existingPath => existingPath === newPath)
						})
						
						if (newImages.length > 0) {
							this.replyImages = [...this.replyImages, ...newImages]
						}
					},
					fail: (err) => {
						console.log('选择图片失败', err);
						if (err.errMsg && err.errMsg.indexOf('permission') > -1) {
							uni.showToast({
								title: '无法访问相册，请在系统设置中授权',
								icon: 'none',
								duration: 3000
							});
						}
					}
				});
			},

			// 删除图片
			deleteImage(index) {
				this.replyImages.splice(index, 1);
			},

			async submitReply() {
				if (this.isSubmitting || !this.canSubmit) {
					uni.showToast({
						title: '请输入回复内容或上传图片',
						icon: 'none'
					})
					return
				}

				try {
					this.isSubmitting = true
					uni.showLoading({
						title: '提交中...'
					})
					
					// 上传图片（如果有）
					let imageUrls = []
					if (this.replyImages.length > 0) {
						try {
							imageUrls = await Promise.all(
								this.replyImages.map(async (image) => {
									return new Promise((resolve, reject) => {
										appServerApi.uploadimgFile(
											image,
											(res) => {
												if (res && res.data) {
													resolve(res.data)
												} else {
													reject(new Error('上传返回数据异常'))
												}
											},
											(error) => {
												reject(error)
											}
										)
									})
								})
							)
						} catch (error) {
							throw new Error('图片上传失败')
						}
					}
					
					const res = await appServerApi.submitFeedbackReply(
						this.feedbackId, 
						this.replyContent,
						imageUrls // Send the array directly instead of joining it
					)
					
					console.log('提交回复响应:', res)
					
					if (res.code === 200) {
						this.replyContent = ''
						this.replyImages = [] // 清空图片
						
						uni.hideLoading()
						uni.showToast({
							title: '提交成功',
							icon: 'success'
						})
						
						await this.loadReplyList()
					} else {
						throw new Error(res.msg || '提交失败')
					}
				} catch (error) {
					uni.hideLoading()
					console.error('提交回复失败', error)
					uni.showToast({
						title: error.message || '提交失败',
						icon: 'none'
					})
				} finally {
					this.isSubmitting = false
				}
			},
			// 预览图片
			previewImage(index, images) {
				uni.previewImage({
					urls: images,
					current: index,
					indicator: 'number',
					loop: true
				})
			},
		}
	}
</script>

<style scoped>
.feedback-reply {
	background: #f7f7f7;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.title {
	font-size: 18px;
	font-weight: bold;
}

.content {
	flex: 1;
	padding: 12px;
	padding-bottom: 20px;
}

.feedback-item {
	background: #fff;
	border-radius: 8px;
	padding: 12px;
	margin-bottom: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feedback-content {
	display: flex;
	align-items: flex-start;
	margin-bottom: 8px;
}

.me {
	color: #333;
	font-weight: bold;
	margin-right: 4px;
}

.developer {
	color: #007aff;
	font-weight: bold;
	margin-right: 4px;
}

.user {
	color: #28a745;
	font-weight: bold;
	margin-right: 4px;
}

.text {
	color: #333;
	line-height: 1.4;
	flex: 1;
}

.date {
	color: #888;
	font-size: 12px;
	text-align: right;
}

.reply-item {
	background: #fff;
	border-radius: 8px;
	padding: 12px;
	margin-bottom: 8px;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.reply-content {
	display: flex;
	align-items: flex-start;
	margin-bottom: 8px;
}

.input-area {
	background: #fff;
	border-radius: 8px;
	padding: 12px;
	margin-bottom: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.input-container {
	margin-bottom: 12px;
}

.image-picker {
	margin-bottom: 12px;
}

.image-list {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}

.image-item {
	width: 80px;
	height: 80px;
	position: relative;
}

.image-item image {
	width: 100%;
	height: 100%;
	border-radius: 4px;
}

.delete-icon {
	position: absolute;
	top: -8px;
	right: -8px;
	width: 20px;
	height: 20px;
	border-radius: 10px;
	background-color: rgba(0, 0, 0, 0.5);
	color: #fff;
	text-align: center;
	line-height: 20px;
	font-size: 16px;
}

.image-add {
	width: 80px;
	height: 80px;
	background-color: #f5f5f5;
	border-radius: 4px;
	display: flex;
	justify-content: center;
	align-items: center;
}

.add-icon {
	font-size: 24px;
	color: #ccc;
}

.image-count {
	margin-top: 4px;
	text-align: right;
	font-size: 12px;
	color: #999;
}

.input {
	margin-top: 8px;
	width: 100%;
	min-height: 80px;
	max-height: 120px;
	padding: 12px 16px;
	border: 1px solid #e5e5e5;
	border-radius: 12px;
	font-size: 15px;
	line-height: 1.5;
	resize: none;
	background: #f8f9fa;
	transition: all 0.3s ease;
	color: #333;
}

.input:focus {
	border-color: #4168e0;
	background: #fff;
	box-shadow: 0 0 0 3px rgba(65, 104, 224, 0.1);
}

.submit-container {
	margin: 0;
}

.submit-btn {
	width: 100%;
	height: 45px;
	background-color: #386BF6;
	border-radius: 22.5px;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 16px;
}

.submit-btn:active {
	opacity: 0.8;
}

.submit-btn:disabled {
	background-color: #cccccc;
}

.submit-btn:disabled:active {
	opacity: 1;
}

.no-replies {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 20px;
}

.no-replies-text {
	color: #888;
	font-size: 14px;
}

.images {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	margin: 8px 0;
}

.img {
	width: 80px;
	height: 80px;
	border-radius: 4px;
	background-color: #f0f0f0;
}

.no-reply-tip {
	background: #fff;
	border-radius: 8px;
	padding: 20px;
	margin-bottom: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	display: flex;
	justify-content: center;
	align-items: center;
}

.tip-text {
	color: #888;
	font-size: 14px;
}
</style>