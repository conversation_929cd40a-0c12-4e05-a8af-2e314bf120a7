<template>
	<view class="feedback-records">
		<CustomHeader background-color="#ffffff">
            <template v-slot:title>
                <text class="title">反馈记录</text>
            </template>
        </CustomHeader>

		<!-- 反馈列表 -->
		<view v-for="item in feedbackList" :key="item.id" class="feedback-item" @click="goToReply(item)">
			<view class="content">
				<text class="me">我：</text>
				<text class="text">{{ item.content }}</text>
			</view>
			<view class="images">
				<image
					v-for="(img, idx) in item.images"
					:key="idx"
					:src="img"
					class="img"
					mode="aspectFill"
				/>
			</view>
			<view class="footer">
				<text class="date">{{ item.date }}</text>
				<text class="reply">
					(回复+{{ item.replyCount }})
				</text>
			</view>
		</view>
	</view>
</template>

<script>
import appServerApi from '@/api/appServerApi'
import CustomHeader from '@/components/custom-header'

export default {
	components: {
        CustomHeader
    },
	data() {
		return {
			feedbackList: []
		}
	},
	async mounted() {
		const res = await appServerApi.getFeedbackList(1, 10)
		console.log('API返回数据', res)
		this.feedbackList = (res.data?.result || []).map(item => ({
			id: item.id,
			content: item.content,
			images: item.url || [],
			date: item.createTime || '',
			replyCount: item.replyCount || 0
		})).sort((a, b) => {
			// 按照时间升序排序，最早的在前面
			return new Date(a.date) - new Date(b.date)
		})
		console.log('处理后的反馈列表', this.feedbackList)
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},
		goToReply(item) {
			// 跳转到反馈回复页面，传递feedbackId
			uni.navigateTo({
				url: `/pages/me/FeedbackReply?feedbackId=${item.id}`
			})
		}
	}
}
</script>

<style scoped>
.feedback-records {
	background: #f7f7f7;
	min-height: 100vh;
}
.header {
	display: flex;
	align-items: center;
	height: 44px;
	background: #fff;
	border-bottom: 1px solid #eee;
	padding: 0 12px;
}
.back {
	font-size: 22px;
	margin-right: 12px;
	color: #333;
}
.title {
	font-size: 18px;
	font-weight: bold;
}
.feedback-item {
	background: #fff;
	margin: 12px;
	border-radius: 8px;
	padding: 12px;
	box-shadow: 0 2px 8px #eee;
	cursor: pointer;
}
.content {
	display: flex;
	align-items: flex-start;
	margin-bottom: 8px;
}
.me {
	color: #333;
	font-weight: bold;
}
.text {
	margin-left: 4px;
	color: #333;
}
.images {
	display: flex;
	gap: 8px;
	margin-bottom: 8px;
}
.img {
	width: 48px;
	height: 48px;
	background: #ccc;
	border-radius: 4px;
}
.footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.date {
	color: #888;
	font-size: 13px;
}
.reply {
	color: #f56c6c;
	font-size: 13px;
	cursor: pointer;
}
</style>
