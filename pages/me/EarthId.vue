<template>
  <view class="container">
    <CustomHeader>
      <template v-slot:title>
        <text>地球号名片</text>
      </template>
    </CustomHeader>
    <view class="content">
      <view class="earth-id-card">
        <view class="avatar-wrap">
          <image :src="userInfo.avatar || '/static/logo.png'" class="avatar" mode="aspectFill" />
          <text class="displayName">{{ userInfo.displayName || '用户' }}</text>
        </view>
        
        <view class="id-info">
          <text class="label">我的地球号</text>
          <view class="id-number">
            <text class="number">{{ userInfo.earthId }}</text>
            <button class="copy-btn" @click="copyEarthId">复制</button>
          </view>
          <text class="tips">地球号是您在平台的唯一标识</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import appServerApi from '@/api/appServerApi'

export default {
  components: {
    CustomHeader,
  },
  data() {
    return {
      userInfo: {
        displayName: '',
        userId: '',
        avatar: '',
        gender: '',
        city: '',
        signature: '',
        school: '',
        address: '',
      },
    }
  },
  onLoad() {
    this.fetchUserInfo()
  },
  methods: {
    async fetchUserInfo() {
      try {
        const result = await appServerApi.getUserInfo()
        this.userInfo = { ...this.userInfo, ...result }
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },

    copyEarthId() {
      uni.setClipboardData({
        data: this.userInfo.earthId,
        success: () => {
          uni.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          })
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.content {
  padding: 20px;
}

.earth-id-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 30px 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.avatar-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
  
  .avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-bottom: 10px;
  }
  
  .displayName {
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }
}

.id-info {
  text-align: center;
  margin-bottom: 30px;
  
  .label {
    font-size: 14px;
    color: #999;
    margin-bottom: 10px;
    display: block;
  }
  
  .id-number {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
    
    .number {
      font-size: 24px;
      font-weight: bold;
      color: #333;
    }
    
    .copy-btn {
      width: 120px;
      font-size: 14px;
      color: #386BF6;
      background: none;
      border: 1px solid #386BF6;
      border-radius: 20px;
      padding: 8px 0;
      line-height: 1.2;
      
      &:active {
        opacity: 0.8;
      }
      
      &::after {
        border: none;
      }
    }
  }
  
  .tips {
    font-size: 12px;
    color: #999;
  }
}
</style>
