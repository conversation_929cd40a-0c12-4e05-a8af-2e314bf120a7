<template>
    <div class="new-friend-item-container">
        <div v-for="(friendRequest,index) in sharedContactState.friendRequestList" :key="index"
            @click="navigateToUserDetail(friendRequest)">
            <view class="rq-hine" v-if="index > 0"></view>
            <view class="new-friend-item">
                <img class="avatar" :src="friendRequest._target.portrait">
                <div class="info">
                    <div class="name">
                        <span class="single-line rqname">{{ friendRequest._target.displayName }}</span>
                        <div class="reason-container">
                            <p class="reason single-line">
                                {{ friendRequest.reason ? friendRequest.reason : $t('friend_request.im') + friendRequest._target.displayName }}
                            </p>
                            <span class="rqhuifu" v-if="friendRequest.status !== 1" @click.stop="navigateToChat(friendRequest)">回复</span>
                        </div>
                    </div>
                    <div class="action-buttons">
                        <span v-if="friendRequest.status === 0" class="jujue rqyy" @click.stop="denied(friendRequest)">
                            拒绝
                        </span>
                        <span v-if="friendRequest.status === 1" class="status rqyy">
                            {{$t('friend_request.accepted')}}
                        </span>
                        <span v-else-if="friendRequest.status === 0" class="accept rqyy" @click.stop="acceptAndNavigate(friendRequest)">
                            {{ $t('common.add') }}
                        </span>
                        <span v-else-if="friendRequest.status === 3" class="status rqyy">
                            {{ $t('friend_request.denied') }}
                        </span>
                    </div>
                </div>

            </view>
        </div>
    </div>

</template>

<script>
    import store from "../../store";
    import wfc from "../../wfc/client/wfc";
    import EventType from "../../wfc/client/wfcEvent";
    import Conversation from "../../wfc/model/conversation";
    import ConversationType from "../../wfc/model/conversationType";

    export default {
        name: "NewFriendListView",
        props: {
            newFriends: null,
        },
        data() {
            return {
                sharedContactState: store.state.contact,
                isActive: false,
                isaccept: false,
            };
        },
        methods: {
            navigateToUserDetail(friendRequest) {
                uni.navigateTo({
                    url: `/pages/contact/UserDetailPage?userId=${friendRequest.target}`,
                    success: () => {
                        console.log('nav to UserDetailPage success');
                    },
                    fail: (err) => {
                        console.log('nav to UserDetailPage err', err);
                    }
                });
            },
            navigateToChat(friendRequest) {
                let conversation = new Conversation(ConversationType.Single, friendRequest.target, 0);
                store.setCurrentConversation(conversation);
                uni.navigateTo({
                    url: '/pages/conversation/ConversationPage',
                    success: () => {
                        console.log('nav to ConversationPage success');
                    },
                    fail: (err) => {
                        console.log('nav to ConversationPage err', err);
                    }
                });
            },
            showFriendRequest(friendRequest) {
                store.setCurrentFriendRequest(friendRequest);
                uni.navigateTo({
                    url: '/pages/contact/FriendRequestDetailPage',
                    success: () => {
                        console.log('nav to FriendRequestDetailPage success');
                    },
                    fail: (err) => {
                        console.log('nav to FriendRequestDetailPage err', err);
                    }
                });
            },
            denied(friendRequest) {
                wfc.handleFriendRequest(friendRequest.target, false, "", () => {
                    friendRequest.status = 3;
                }, (err) => {
                    uni.showToast({
                        title: '拒绝添加好友失败 ' + err,
                        icon: 'none',
                    });
                    console.log('accept friend request error', err)
                })
            },
            acceptAndNavigate(friendRequest) {
                wfc.handleFriendRequest(friendRequest.target, true, "", async () => {
                    friendRequest.status = 1;
                    
                    // 添加好友成功后直接跳转到用户详情页
                    uni.navigateTo({
                        url: `/pages/contact/UserDetailPage?userId=${friendRequest.target}`,
                        success: () => {
                            console.log('nav to UserDetailPage success');
                        },
                        fail: (err) => {
                            console.log('nav to UserDetailPage err', err);
                        }
                    });
                }, (err) => {
                    uni.showToast({
                        title: '添加好友失败 ' + err,
                        icon: 'none',
                    });
                    console.log('accept friend request error', err)
                })
            },
            
            accept(friendRequest) {
                wfc.handleFriendRequest(friendRequest.target, true, "", async () => {
                    friendRequest.status = 1;
                }, (err) => {
                    uni.showToast({
                        title: '添加好友失败 ' + err,
                        icon: 'none',
                    });
                    console.log('accept friend request error', err)
                })
            },
            onFriendRequestUpdate() {
                if (this.isActive) {
                    wfc.clearUnreadFriendRequestStatus();
                }
            },
        },

        activated() {
            this.isActive = true;
            wfc.clearUnreadFriendRequestStatus();
        },

        deactivated() {
            this.isActive = false;
        },

        mounted() {
            this.isActive = true;
            wfc.clearUnreadFriendRequestStatus();
            wfc.eventEmitter.on(EventType.FriendRequestUpdate, this.onFriendRequestUpdate);
        },

        beforeUnmount() {
            this.isActive = false;
            wfc.eventEmitter.removeListener(EventType.FriendRequestUpdate, this.onFriendRequestUpdate);
        }
    }
</script>

<style lang="css" scoped>
    .rqyy {
        color: #FFF;
        font-family: "PingFang SC";
        font-size: 12px;
        padding: 5px 10px;
        border-radius: 100px;
        background: #F5F5F5;
        display:inline-block;
        margin-left:5px;
        white-space: nowrap;
    }


    .new-friend-item-container {
        padding-left: 10px;
    }

    .avatar {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        flex-shrink: 0;
    }

    .new-friend-item {
        display: flex;
        width: 100%;
        padding: 10px 10px 10px 0;
        align-items: center;
        font-size: 13px;
        position: relative;
    }


    .new-friend-item .info {
        margin-left: 10px;
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        min-width: 0;
    }

    .new-friend-item .info .name {
        flex: 1;
        min-width: 0;
        overflow: hidden;
    }

    .new-friend-item .info .name .rqname {
        font-size: 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: block;
    }

    .new-friend-item .info .name .reason-container {
        display: flex;
        align-items: center;
        min-width: 0;
        margin: 2px 0 0 0;
    }

    .new-friend-item .info .name .reason {
        font-size: 12px;
        color: #b2b2b2;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        margin: 0;
        padding: 0;
        min-width: 0;
    }

    .new-friend-item .info .name .rqhuifu {
        padding-left: 10px;
        font-size: 12px;
        color: #386BF6;
        white-space: nowrap;
        flex-shrink: 0;
    }

    .new-friend-item .info .action-buttons {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        gap: 5px;
    }

    .new-friend-item .info .accept {
        text-align: center;
        color: white;
        background: #4168e0;
        border: solid 1px #4168e0;
    }

    .new-friend-item .info .jujue {
        color: #000000;
    }
    
    .new-friend-item .info .status {
        color: #b2b2b2;
    }

    .single-line {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>