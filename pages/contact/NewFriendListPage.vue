<template>

    <view class="friend-request-list">
        <view>
            <uni-search-bar bgColor="#fff" :radius="100" v-model="keyword" placeholder="搜索"
                @input="searchUser"></uni-search-bar>
        </view>
        <view class="rqstq">三天前</view>
        <view class="conversation-rq">
            <uni-list>
                <FriendRequestListView />
            </uni-list>
        </view>
    </view>
</template>
<script>
    import FriendRequestListView from "./FriendRequestListView";
    import store from "../../store";
    import UniList from "../../components/uni-list/uni-list.vue";
    import wfc from "../../wfc/client/wfc";

    export default {
        name: "ContactListPage",
        components: {
            UniList,
            FriendRequestListView
        },
        data() {
            return {
                sharedContactState: store.state.contact,
                keyword: ''
            }
        },
        onReady() {
            uni.setNavigationBarColor({
                frontColor: '#000000',
                backgroundColor: '#F5F5F5',
            })
        },
        onNavigationBarButtonTap(e) {
            console.log('onNavigationBarButtonTap')
            switch (e.index) {
                case 0:
                    // 跳转到添加朋友页面
                    uni.navigateTo({
                        url: '/pages/contact/SearchUserPage'
                    });
                    break;
                case 1:
                    uni.navigateTo({
                        url: '/pages/search/SearchPortalPage?user=true&contact=false&group=false&conversation=false'
                    });
                    break;
                default:
                    break;
            }
        },
        methods: {
            searchUser() {

            }
        }
    }
</script>

<style lang="css" scoped>
    .rqstq {
        color: #BBB;
        font-family: MiSans;
        font-size: 14px;
        margin:16px 10px 10px;
    }

    .friend-request-list {
        height: var(--page-full-height-without-header);
        overflow: auto;
        background: #F5F5F5;
    }
</style>