// TODO 仅聊天 接口对接
<template>
    <div class="contact-list">
        <view>
            <uni-search-bar bgColor="#fff" :radius="100" @confirm="search" clearButton="auto" cancelButton="none" placeholder="搜索" @click="onsearch" @input="onsearch"></uni-search-bar>
        </view>
        <uni-list>
            <view>
                <view class="conversation-rq2">
                    <UserListView :enable-pick="false" :istxl="false" :users="sharedContactState.favContactList.concat(sharedContactState.friendList)" :click-user-item-func="setCurrentUser" :padding-left="'10px'" />
                </view>
            </view>
        </uni-list>
        <main-action-menu ref="mainActionMenu"></main-action-menu>
    </div>
</template>
<script>
import store from '../../store'
import UserListView from '../user/UserListView.vue'
import UniList from '../../components/uni-list/uni-list.vue'
export default {
    name: 'JustChattingFriendListPage',
    components: {
        UniList,
        UserListView,
    },
    data() {
        return {
            sharedContactState: store.state.contact,
        }
    },
    onReady() {
        //动态修改状态栏的颜色
        uni.setNavigationBarColor({
            frontColor: '#000000',
            backgroundColor: '#fafafa',
        })
    },
    onHide() {
        console.log('contactList onHide')
        this.$refs.mainActionMenu.hide()
    },

    onNavigationBarButtonTap(e) {
        console.log('onNavigationBarButtonTap')
        switch (e.index) {
            case 0:
                this.$refs.mainActionMenu.toggle()
                break
            case 1:
                this.$refs.mainActionMenu.hide()
                uni.navigateTo({
                    url: '/pages/search/SearchPortalPage',
                })
                break
            default:
                break
        }
    },
    methods: {
        onsearch(res) {
            this.$refs.mainActionMenu.hide()
            uni.navigateTo({
                url: '/pages/search/SearchPortalPage',
            })
        },
        setCurrentUser(userInfo) {
            store.setCurrentFriend(userInfo)
            uni.navigateTo({
                url: './UserDetailPage',
                success: () => {
                    console.log('nav to UserDetailPage success')
                },
                fail: (err) => {
                    console.log('nav to UserDetailPage err', err)
                },
            })
        },
        showNewFriends() {
            uni.navigateTo({
                url: './NewFriendListPage',
                success: () => {
                    console.log('nav to NewFriendListPage success')
                },
                fail: (err) => {
                    console.log('nav to NewFriendListPage err', err)
                },
            })
        },
        showGroups() {
            uni.navigateTo({
                url: './GroupListPage',
                success: () => {
                    console.log('nav to GroupListPage success')
                },
                fail: (err) => {
                    console.log('nav to GroupListPage err', err)
                },
            })
        },
        showChannels() {
            uni.navigateTo({
                url: './ChannelListPage',
                success: () => {
                    console.log('nav to ChannelListPage success')
                },
                fail: (err) => {
                    console.log('nav to ChannelListPage err', err)
                },
            })
        },
        showContacts() {
            store.toggleFriendList()
        },
    },
}
</script>

<style lang="css" scoped>
.conversation-rq2 {
    /* margin: 10px; */
}

.contact-list {
    height: var(--page-full-height-without-header-and-tabbar);
    overflow: auto;
    background-color: #fafafa;
}

.category-item-container {
    height: 50px;
    display: flex;
    flex-direction: row;
    align-items: center;
    z-index: 1000;
    padding-left: 10px;
    color: #262626;
    font-size: 14px;
    position: sticky;
    /* background-color: #fafafa; */
    position: relative;
}

.category-item-container image {
    max-width: 40px;
    max-height: 40px;
    margin-right: 10px;
}

.category-item {
    display: flex;
    width: 100%;
    align-items: center;
}

.category-item .title {
    font-size: 16px;
}

.category-item .desc {
    color: #ffffff;
    width: 20px;
    height: 20px;
    font-size: 12px;
    border-radius: 50%;
    background-color: red;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 15px;
}

.category-item span:last-of-type {
    margin-right: 15px;
}
</style>