<template>
	<div class="user-detail-container" v-if="sharedStateContact.currentFriend">
		<view class="rqud">
			<view class="ud1">
				<view class="ud1a">发送添加好友申请</view>
				<textarea class="ud1b udfs" v-model="sqtext"/>
			</view>
			<view class="ud1">
				<view class="ud1a">设置备注</view>
				<input class="ud1b udbz" />
			</view>
			<view class="ud1">
				<view class="ud1a">设置分组</view>
				<view class="ud1b udfz" @click="goToSelectGroup">
					<view style="flex: 1;">{{groupNames[fzindex]}}</view>
					<i class="icon-ion-ios-arrow-right"></i>
				</view>
			</view>
			<view class="ud1 ud2">
				<view class="ud1a">设置动态权限</view>
				<view class="ud1b udfz uddt1" @click="isdtauth=0">
					<view>公开</view>
					<i class="icon-ion-checkmark-round" v-if="isdtauth===0"></i>
					<view v-else></view>
				</view>
				<view class="ud1b udfz uddt2" @click="isdtauth=1">
					<view>仅聊天</view>
					<i class="icon-ion-checkmark-round" v-if="isdtauth===1"></i>
					<view v-else></view>
				</view>
			</view>
			<!-- <view class="ud1">
				<view class="ud1b udfz uddt1">
					<view>不让她看我</view>
					<switch />
				</view>
				<view class="ud1b udfz uddt2">
					<view>我不看他</view>
					<switch />
				</view>
			</view> -->
			<view class="ud1">
				<view class="ud1b udbat" @click="addFriend">发送</view>
			</view>

		</view>
		<!-- <div>
			<div class="header">
				<div>
					<h2>{{ name }}</h2>
					<p>你好，野火哈哈</p>
				</div>
				<div>
					<img class="avatar" :src="sharedStateContact.currentFriend.portrait">
				</div>
			</div>
			<div class="content">
				<ul>
					<li>
						<label>{{ $t('common.alias') }}</label>
						<div class="alias">
							<input type="text" ref="input" :value="sharedStateContact.currentFriend.friendAlias" placeholder="备注名"
								@keyup.enter="updateFriendAlias" />
						</div>
					</li>
					<li>
						<label>{{ $t('common.wfc_id') }}</label>
						<p class="single-line">{{ user.name }}</p>
					</li>
					<li>
						<label>{{ $t('common.area') }}</label>
						<p>{{ $t('common.unknown') }}</p>
					</li>
					<li>
						<label>{{ $t('common.label') }}</label>
						<p>{{ $t('misc.test_user') }}</p>
					</li>
				</ul>
			</div>
			<div class="footer">
				<div class="action" @click="chat">
					<i class="icon-ion-ios-chatboxes-outline"></i>
					<a>{{ $t('message.send_message') }}</a>
				</div>
				<div class="action" @click="startAudioCall">
					<i class="icon-ion-ios-telephone-outline"></i>
					<a>语音通话</a>
				</div>
				<div class="action" @click="startVideoCall">
					<i class="icon-ion-ios-videocam-outline"></i>
					<a>视频通话</a>
				</div>
				<div v-if="!isFriend && !isSelf" class="action" @click="addFriend">
					<i class="icon-ion-person-add"></i>
					<a>添加好友</a>
				</div>
			</div>
		</div> -->
	</div>
</template>

<script>
	import store from "../../store";
	import ConversationType from "../../wfc/model/conversationType";
	import Conversation from "../../wfc/model/conversation";
	import wfc from "../../wfc/client/wfc";
	import avengineKit from "../../wfc/av/engine/avengineKit";
	import friendGroupService from "../user/friendGroupService";

	export default {
		name: "UserDetailPage",
		data() {
			return {
				sharedStateContact: store.state.contact,
				user: store.state.contact.currentFriend,
				isdtauth: 0,
				groupNames: [],
				fzindex: 0,
				sqtext:''
			}
		},
		onLoad(options) {
			if (options.userId) {
				// 如果从URL参数中获取到userId，则获取用户信息并设置到store中
				const userInfo = wfc.getUserInfo(options.userId, false);
				if (userInfo) {
					store.setCurrentFriend(userInfo);
					this.user = userInfo;
				} else {
					uni.showToast({
						title: '获取用户信息失败',
						icon: 'none'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}
			}
		},
		onReady() {
			uni.setNavigationBarColor({
				frontColor: '#000000',
				backgroundColor: '#F5F5F5',
			})
		},
		mounted() {
			wfc.getUserInfo(this.user.uid, true);
			// uni.setNavigationBarTitle({
			//     title:this.user._displayName,
			// });
			
			// 加载好友分组
			this.loadFriendGroups();
		},
		onUnload() {
			store.setCurrentFriend(null);
		},
		methods: {
			// 加载好友分组
			loadFriendGroups() {
				// 获取分组信息
				const groupsStr = wfc.getUserSetting(friendGroupService.USER_SETTING_SCOPE, friendGroupService.USER_SETTING_KEY);
				let groupNames = [friendGroupService.DEFAULT_GROUP_NAME];
				
				try {
					if (groupsStr) {
						const groups = JSON.parse(groupsStr);
						groupNames = [friendGroupService.DEFAULT_GROUP_NAME, ...groups.filter(name => name !== friendGroupService.DEFAULT_GROUP_NAME)];
					}
				} catch (e) {
					console.error('解析好友分组信息失败', e);
				}
				
				this.groupNames = groupNames;
				
				// 设置当前好友所在分组的索引
				const currentGroup = friendGroupService.getFriendGroup(this.user.uid);
				const index = this.groupNames.indexOf(currentGroup);
				this.fzindex = index >= 0 ? index : 0;
			},
			
			// 跳转到选择分组页面
			goToSelectGroup() {
				// 跳转到选择分组页面
				uni.navigateTo({
					url: `/pages/contact/SelectGroupPage?userId=${this.user.uid}`
				});
			},
			
			// 设置选中的分组（由SelectGroupPage调用）
			setSelectedGroup(groupName) {
				if (groupName && this.groupNames.includes(groupName)) {
					this.fzindex = this.groupNames.indexOf(groupName);
				}
			},
			
			bindPickerChange: function(e) {
				console.log('picker发送选择改变，携带值为', e.detail.value)
				this.fzindex = e.detail.value;
				
				// 更新好友分组
				const groupName = this.groupNames[this.fzindex];
				friendGroupService.changeFriendGroup(this.user.uid, groupName);
			},
			chat() {
				let conversation = new Conversation(ConversationType.Single, this.user.uid, 0);
				store.setCurrentConversation(conversation);
				this.$go2ConversationPage();
			},
			updateFriendAlias() {
				let friendAlias = this.$refs.input.value;
				if (friendAlias.trim() && friendAlias !== this.sharedStateContact.currentFriend.friendAlias) {
					wfc.setFriendAlias(this.user.uid, friendAlias, () => {
							// do nothing
							console.log('setFriendAlias success', this.user, friendAlias);
						},
						(error) => {
							// do nothing
						})
				}
			},
			addFriend() {
				let target = this.friend || this.user;
				if (!target) {
					return;
				}
				
				// 检查是否已经是好友
				if (wfc.isMyFriend(target.uid)) {
					uni.showToast({
						title: '已经是好友',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				
				let reason = this.sqtext;
				
				// 显示加载提示
				uni.showLoading({
					title: '发送中...'
				});
				
				// 发送好友请求
				wfc.sendFriendRequest(target.uid, reason, '', () => {
					console.log('发送好友请求成功');
					
					// 如果选择了分组（不是默认分组），设置好友分组
					if (this.fzindex !== 0) {
						const groupName = this.groupNames[this.fzindex];
						
						// 设置好友分组
						friendGroupService.changeFriendGroup(
							target.uid, 
							groupName,
							// 成功回调
							() => {
								console.log('设置好友分组成功');
								uni.hideLoading();
								uni.showToast({
									title: '已发送好友请求',
									icon: 'success',
									duration: 2000
								});
								// 返回上一页
								setTimeout(() => {
									uni.navigateBack({
										delta: 1
									});
								}, 2000);
							},
							// 失败回调
							(errorCode) => {
								console.error('设置好友分组失败', errorCode);
								uni.hideLoading();
								uni.showToast({
									title: '已发送好友请求',
									icon: 'success',
									duration: 2000
								});
								// 返回上一页
								setTimeout(() => {
									uni.navigateBack({
										delta: 1
									});
								}, 2000);
							}
						);
					} else {
						uni.hideLoading();
						uni.showToast({
							title: '已发送好友请求',
							icon: 'success',
							duration: 2000
						});
						// 返回上一页
						setTimeout(() => {
							uni.navigateBack({
								delta: 1
							});
						}, 2000);
					}
				}, (error) => {
					console.log('发送好友请求失败', error);
					uni.hideLoading();
					// 检查错误码是否为重复添加好友
					if (error === 16) {
						uni.showToast({
							title: '已发送过好友请求',
							icon: 'none',
							duration: 2000
						});
					} else {
						uni.showToast({
							title: '发送好友请求失败：' + error,
							icon: 'none',
							duration: 2000
						});
					}
				});
			},

		},
		computed: {
			name: function() {
				let name;
				let friend = this.sharedStateContact.currentFriend;
				if (!friend) {
					return null;
				}
				if (friend.displayName) {
					name = friend.displayName;
				} else {
					name = friend.name;
				}
				console.log('sharedStateContact', this.sharedStateContact)
				return name;
			},
			isFriend() {
				return wfc.isMyFriend(this.user.uid);
			},
			isSelf() {
				return this.user.uid === wfc.getUserId();
			}
		}
	}
</script>

<style lang="scss" scoped>
	.user-detail-container {
		.rqud {
			.ud1 {
				.ud1a {
					color: #BBB;
					font-family: MiSans;
					font-size: 14px;
					font-style: normal;
					font-weight: 400;
					line-height: normal;
					margin-bottom: 10px;
				}

				.ud1b {
					width: 100%;
					border-radius: 10px;
					background: #FFF;
					color: #000;
					font-family: MiSans;
					font-size: 16px;
					font-style: normal;
					font-weight: 400;
					line-height: normal;
					padding: 14px 24px;
					display: flex;
					align-items: center;
					height: 50px;

					i {
						color: #C0C0C1;
						margin-right: -10px;
					}

					switch {
						transform: scale(0.7);
						margin-right: -20px;
					}
				}

				.udbat {
					background: #386BF6;
					height: 46px;
					color: #ffffff;
					justify-content: center;
				}

				.uddt1 {
					border-radius: 10px 10px 0px 0px;
				}

				.uddt2 {
					border-radius: 0px 0px 10px 10px;
				}

				.udfz {
					justify-content: space-between;
				}

				.udfs {
					height: 100px;
				}

				margin-bottom: 24px;
			}

			.ud2 {
				margin-bottom: 10px;
			}
		}

		background: #F5F5F5;
		padding: 15px 12px;
	}
</style> 