<template>
  <view class="activity-main-bg act-home">
    <CustomHeader>
      <template v-slot:title>
        <text class="title">资讯</text>
      </template>
    </CustomHeader>
    <view class="list-container">
      <view
          class="list-item"
          v-for="(item, index) in list"
          :key="index"
          @click="navigateToNewsDetail(item, index)"
      >
        <image class="item-image" :src="item.image"/>
        <view class="item-content">
          <view class="tag">创业</view>
          <view class="title">{{ item.title }}</view>
          <view class="subtitle">{{ item.subtitle }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import appServerApi from '@/api/appServerApi';

export default {
  data() {
    return {
      list: []
    };
  },
  mounted() {
    this.fetchNewsList();
  },
  methods: {
    async fetchNewsList() {
      try {
        const response = await appServerApi.getArticleDetail();
        console.log(response); // 添加日志输出
        if (Array.isArray(response)) {
          this.list = response.map(item => ({
            image: item.image || "https://via.placeholder.com/50",
            title: item.title,
            subtitle: item.subtitle
          }));
        } else if (response && Array.isArray(response.articles)) {
          this.list = response.articles.map(item => ({
            image: item.image || "https://via.placeholder.com/50",
            title: item.title,
            subtitle: item.subtitle
          }));
        } else {
          console.error('Unexpected response structure:', response);
        }
      } catch (error) {
        console.error('Failed to fetch news list:', error);
      }
    },
    navigateToNewsDetail(item, index) {
      uni.navigateTo({
        url: `/pages/news/NewsDetail?index=${index}`
      });
    }
  }

};
</script>

<style scoped>
.container {
  padding: 10px;
}

.list-container {
  display: flex;
  flex-direction: column;
}

.list-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.item-image {
  width: 50px;
  height: 50px;
  border-radius: 5px;
  margin-right: 10px;
}

.item-content {
  flex: 1;
}

.tag {
  background-color: #ff4c4c;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
  margin-bottom: 4px;
}

.title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.subtitle {
  font-size: 14px;
  color: #666;
}
</style>
