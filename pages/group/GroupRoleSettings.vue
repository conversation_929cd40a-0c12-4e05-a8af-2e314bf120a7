<template>
    <view class="group-role-settings">
        <!-- 自定义头部 -->
        <CustomHeader backgroundColor="#fff">
            <template v-slot:title>
                <text>群内身份设置</text>
            </template>
        </CustomHeader>

        <!-- 角色列表 -->
        <view class="role-list">
            <view class="role-item" v-for="role in allRoles" :key="role.id" @tap="showRoleMembers(role.id, role.name)">
                <view class="role-info">
                    <text class="role-name">{{ role.name }}</text>
                    <text class="member-count">({{ getRoleMemberCount(role.id) }})</text>
                </view>
                <view class="action-buttons" v-if="!role.fixed">
                    <text class="action-btn edit" @click.stop="handleEdit(role.id)">修改</text>
                    <text class="action-btn delete" @click.stop="handleDelete(role.id)">删除</text>
                </view>
            </view>
        </view>

        <!-- 新增按钮 -->
        <view class="add-role-btn" @click="handleAddRole">
            <text>新增</text>
        </view>

        <!-- 新增身份弹窗 -->
        <view class="popup-mask" v-if="showAddPopup" @click="closeAddPopup"></view>
        <view class="add-role-popup" v-if="showAddPopup">
            <view class="popup-title">新增群内身份</view>
            <view class="input-container">
                <input 
                    type="text" 
                    v-model="newRoleName"
                    placeholder="请输入身份名称"
                    maxlength="6"
                />
                <text class="input-counter">{{newRoleName.length}}/6</text>
            </view>
            <view class="popup-buttons">
                <view class="btn cancel" @click="closeAddPopup">取消</view>
                <view class="btn confirm" @click="confirmAddRole">确认</view>
            </view>
        </view>

        <!-- 修改身份弹窗 -->
        <view class="popup-mask" v-if="showEditPopup" @click="closeEditPopup"></view>
        <view class="add-role-popup" v-if="showEditPopup">
            <view class="popup-title">修改群内身份</view>
            <view class="input-container">
                <input 
                    type="text" 
                    v-model="newRoleName"
                    placeholder="请输入身份名称"
                    maxlength="6"
                />
                <text class="input-counter">{{newRoleName.length}}/6</text>
            </view>
            <view class="popup-buttons">
                <view class="btn cancel" @click="closeEditPopup">取消</view>
                <view class="btn confirm" @click="confirmEdit">确认</view>
            </view>
        </view>
    </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import appServerApi from '@/api/appServerApi'
import wfc from '@/wfc/client/wfc'
import GroupIdentityCache from '@/utils/GroupIdentityCache'

export default {
    name: 'GroupRoleSettings',
    components: {
        CustomHeader
    },
    data() {
        return {
            roles: [],  // 自定义角色
            fixedRoles: [
                { id: 'admin', name: '群主/群管理', fixed: true, count: 0 },
                { id: 'member', name: '成员', fixed: true, count: 0 }
            ],
            groupId: '',
            showAddPopup: false,
            showEditPopup: false,
            newRoleName: '',
            editingRole: {
                id: '',
                identity: ''
            },
            memberIdentities: [], // 成员身份映射
            isNavigating: false, // 添加导航状态标记
            groupMembers: [], // 群成员列表
            updateTimer: null, // 定时器
        }
    },
    computed: {
        // 合并固定角色和自定义角色
        allRoles() {
            return [...this.fixedRoles, ...this.roles]
        }
    },
    watch: {
        // 监听成员身份变化
        memberIdentities: {
            handler(newVal) {
                console.log('成员身份发生变化:', newVal)
                this.$forceUpdate() // 强制更新视图
            },
            deep: true
        },
        // 监听群成员变化
        groupMembers: {
            handler(newVal) {
                console.log('群成员发生变化:', newVal)
                this.$forceUpdate() // 强制更新视图
            },
            deep: true
        }
    },
    onLoad(option) {
        try {
            console.log('GroupRoleSettings onLoad 开始', option)
            if (!option) {
                console.error('onLoad: option 为空')
                uni.showToast({
                    title: '参数错误',
                    icon: 'none'
                })
                return
            }

            if (option.groupId) {
                console.log('Setting groupId from option:', option.groupId)
                this.groupId = option.groupId
                // 添加延迟，确保页面完全加载
                setTimeout(() => {
                    this.loadRoleList()
                    this.startUpdateTimer() // 启动定时器
                }, 100)
            } else {
                console.log('No groupId in option, trying to get from previous page')
                // 从上一个页面获取groupId
                const pages = getCurrentPages()
                if (!pages || pages.length < 2) {
                    console.error('获取页面栈失败')
                    uni.showToast({
                        title: '获取群组ID失败',
                        icon: 'none'
                    })
                    return
                }
                
                const prevPage = pages[pages.length - 2]
                if (prevPage && prevPage.$vm && prevPage.$vm.groupId) {
                    console.log('Found groupId from previous page:', prevPage.$vm.groupId)
                    this.groupId = prevPage.$vm.groupId
                    setTimeout(() => {
                        this.loadRoleList()
                    }, 100)
                } else {
                    console.error('Failed to get groupId from both option and previous page')
                    uni.showToast({
                        title: '获取群组ID失败',
                        icon: 'none'
                    })
                }
            }
            this.loadMemberIdentities()

            // 添加页面显示事件监听
            uni.$on('updateRoleMemberCount', this.handleRoleMemberCountUpdate)
        } catch (error) {
            console.error('onLoad error:', error)
            uni.showToast({
                title: '页面加载失败',
                icon: 'none'
            })
        }
    },
    onShow() {
        // 每次页面显示时重新加载数据并启动定时器
        this.loadRoleList()
        this.loadMemberIdentities()
        this.startUpdateTimer()
    },
    onHide() {
        // 页面隐藏时停止定时器
        this.stopUpdateTimer()
    },
    onUnload() {
        // 页面卸载时清理
        this.stopUpdateTimer()
        uni.$off('updateRoleMemberCount', this.handleRoleMemberCountUpdate)
    },
    methods: {
        handleEdit(roleId) {
            const role = this.roles.find(r => r.id === roleId)
            if (role) {
                this.editingRole = {
                    id: roleId,
                    identity: role.name
                }
                this.newRoleName = role.name
                this.showEditPopup = true
            }
        },
        
        async handleDelete(roleId) {
            try {
                // 获取当前要删除的身份名称
                const role = this.roles.find(r => r.id === roleId)
                if (!role) {
                    uni.showToast({
                        title: '身份不存在',
                        icon: 'none'
                    })
                    return
                }

                const result = await uni.showModal({
                    title: '删除群内身份',
                    content: `是否删除群内身份"${role.name}"？\n提示：如身份内有用户，该身份删除后用户全部变成默认成员身份`,
                    cancelText: '取消',
                    confirmText: '确认',
                    confirmColor: '#386bf6',
                    cancelColor: '#333333'
                })
                
                if (!result.confirm) return
                
                // 乐观更新：先从本地移除
                this.roles = this.roles.filter(role => role.id !== roleId)
                
                const response = await appServerApi._post(`/group/identity/delete/${roleId}`)
                console.log('删除身份响应:', response)
                
                // 从日志可以看到，success res.data 的结构是：
                // {"code":200,"msg":null,"data":null}
                if (response?.code === 200) {
                    uni.showToast({
                        title: '删除成功',
                        icon: 'success'
                    })
                    // 减少延迟时间
                    await this.loadRoleList()
                    return
                } else if (response === null) {
                    // 如果 response 是 null，说明请求成功但是返回 null
                    uni.showToast({
                        title: '删除成功',
                        icon: 'success'
                    })
                    // 减少延迟时间
                    await this.loadRoleList()
                    return
                }
                
                // 如果删除失败，恢复本地数据
                await this.loadRoleList()
                throw new Error(response?.msg || '删除失败')
            } catch (error) {
                console.error('删除身份失败:', error)
                uni.showToast({
                    title: error.message || '删除失败，请重试',
                    icon: 'none'
                })
            }
        },
        
        handleAddRole() {
            this.showAddPopup = true
            this.newRoleName = ''
        },
        
        closeAddPopup() {
            this.showAddPopup = false
            this.newRoleName = ''
        },
        
        async confirmAddRole() {
            if (!this.newRoleName.trim()) {
                uni.showToast({
                    title: '请输入身份名称',
                    icon: 'none'
                })
                return
            }

            // 检查是否存在相同名称的身份
            const existingRole = this.allRoles.find(role => role.name === this.newRoleName.trim())
            if (existingRole) {
                uni.showToast({
                    title: '该身份名称已存在',
                    icon: 'none'
                })
                return
            }

            if (!this.groupId) {
                console.error('No groupId available')
                uni.showToast({
                    title: '群组ID不存在',
                    icon: 'none'
                })
                return
            }
            
            console.log('Attempting to add role with groupId:', this.groupId)
            try {
                const response = await appServerApi._post('/group/identity', {
                    groupId: this.groupId,
                    identity: this.newRoleName.trim(),
                    override: false
                })
                
                console.log('Add role response:', response)
                
                if (response === true || response?.data?.code === 200) {
                    uni.showToast({
                        title: '添加成功',
                        icon: 'success'
                    })
                    this.closeAddPopup()
                    setTimeout(() => {
                        this.loadRoleList()
                    }, 500)
                } else {
                    uni.showToast({
                        title: (response?.data?.msg || response?.msg || '添加失败'),
                        icon: 'none'
                    })
                }
            } catch (error) {
                console.error('添加身份失败:', error)
                uni.showToast({
                    title: '添加失败，请重试',
                    icon: 'none'
                })
            }
        },
        
        closeEditPopup() {
            this.showEditPopup = false
            this.newRoleName = ''
            this.editingRole = {
                id: '',
                identity: ''
            }
        },
        
        async confirmEdit() {
            if (!this.newRoleName.trim()) {
                uni.showToast({
                    title: '请输入身份名称',
                    icon: 'none'
                })
                return
            }

            try {
                const response = await appServerApi._post('/group/identity/update', {
                    id: this.editingRole.id,
                    groupId: this.groupId,
                    identity: this.newRoleName.trim()
                })

                console.log('Edit role response:', response)
                
                if (response === true || response?.data?.code === 200) {
                    uni.showToast({
                        title: '修改成功',
                        icon: 'success'
                    })
                    this.closeEditPopup()
                    setTimeout(() => {
                        this.loadRoleList()
                    }, 500)
                } else {
                    uni.showToast({
                        title: (response?.data?.msg || response?.msg || '修改失败'),
                        icon: 'none'
                    })
                }
            } catch (error) {
                console.error('修改身份失败:', error)
                uni.showToast({
                    title: '修改失败，请重试',
                    icon: 'none'
                })
            }
        },
        
        async loadRoleList() {
            try {
                if (!this.groupId) {
                    console.error('loadRoleList: groupId为空')
                    return
                }
                
                console.log('开始获取角色列表，groupId:', this.groupId)
                const response = await appServerApi._get(`/group/identity/${this.groupId}`)
                
                console.log('获取角色列表原始响应:', response)
                
                if (response?.code === 200) {
                    console.log('处理返回的数据:', response.data)
                    if (Array.isArray(response.data)) {
                        this.roles = response.data.map(role => ({
                            id: role.id,
                            name: role.identity,
                            count: 0
                        }))
                        
                        // 立即加载成员身份数据来更新数量
                        await this.loadMemberIdentities()
                        
                        // 计算每个角色的成员数量
                        await this.updateAllRoleCounts()
                    }
                } else {
                    console.error('获取角色列表失败:', response)
                    if (response?.msg) {
                        uni.showToast({
                            title: response.msg,
                            icon: 'none'
                        })
                    }
                }
                
                console.log('最终角色列表:', this.roles)
                
            } catch (error) {
                console.error('加载角色列表失败:', error)
                this.roles = []
                uni.showToast({
                    title: '加载失败，请重试',
                    icon: 'none'
                })
            }
        },
        handleRoleClick(role) {
            const url = `/pages/group/GroupMemberList?groupId=${this.groupId}&roleId=${role.id}&roleName=${role.name}`
            console.log('Navigating to:', url)
            
            // 使用完整路径
            uni.navigateTo({
                url: url,
                success: () => {
                    console.log('Navigation successful')
                },
                fail: (error) => {
                    console.error('Navigation failed:', error)
                    uni.showToast({
                        title: '页面跳转失败',
                        icon: 'none'
                    })
                }
            })
        },
        // 加载成员身份映射
        async loadMemberIdentities() {
            try {
                if (!this.groupId) {
                    console.error('loadMemberIdentities: groupId为空')
                    return
                }
                
                console.log('开始获取成员身份列表，groupId:', this.groupId)
                
                // 使用全局缓存管理器获取群成员身份信息原始数据
                const rawIdentities = await GroupIdentityCache.getGroupIdentitiesRaw(this.groupId);
                
                // 保存原始数据格式，以便后续使用
                this.memberIdentities = rawIdentities;
                
                console.log('更新后的成员身份列表:', this.memberIdentities)
                
                // 更新群成员信息
                this.groupMembers = wfc.getGroupMembers(this.groupId)
                console.log('更新后的群成员列表:', this.groupMembers)
                
                // 强制更新视图
                this.$forceUpdate()
                
            } catch (error) {
                console.error('加载成员身份失败:', error)
                uni.showToast({
                    title: '加载失败，请重试',
                    icon: 'none'
                })
            }
        },
        // 更新所有角色的成员数量
        async updateAllRoleCounts() {
            try {
                // 更新固定角色的数量
                for (let fixedRole of this.fixedRoles) {
                    fixedRole.count = await this.calculateRoleMemberCount(fixedRole.id);
                }
                
                // 更新自定义角色的数量
                for (let role of this.roles) {
                    role.count = await this.calculateRoleMemberCount(role.id);
                }
                
                console.log('所有角色成员数量更新完成:', {
                    fixedRoles: this.fixedRoles,
                    customRoles: this.roles
                });
                
                // 强制更新视图
                this.$forceUpdate();
                
            } catch (error) {
                console.error('更新角色成员数量失败:', error);
            }
        },
        // 获取某个身份下的成员数量
        async calculateRoleMemberCount(roleId) {
            console.log('Calculating member count for roleId:', roleId, {
                groupMembers: this.groupMembers,
                memberIdentities: this.memberIdentities
            })

            if (roleId === 'admin') {
                // 群主和管理员数量
                const adminCount = this.groupMembers.filter(member => 
                    member.type === 1 || member.type === 2
                ).length
                console.log('Admin count:', adminCount)
                return adminCount
            } else if (roleId === 'member') {
                // 普通成员数量（不包括群主/管理员和有自定义身份的成员）
                const memberCount = this.groupMembers.filter(member => {
                    // 排除群主和管理员
                    if (member.type === 1 || member.type === 2) {
                        return false
                    }
                    // 排除有自定义身份的成员
                    return !this.memberIdentities.some(mi => 
                        String(mi.memberId) === String(member.memberId)
                    )
                }).length
                console.log('Member count:', memberCount)
                return memberCount
            } else {
                // 自定义身份成员数量 - 通过角色名称来计算
                try {
                    // 首先找到对应的角色名称
                    const role = this.roles.find(r => r.id === roleId);
                    if (!role) {
                        console.log('找不到角色，roleId:', roleId);
                        return 0;
                    }
                    
                    const roleName = role.name;
                    console.log('使用角色名称匹配:', roleName, 'for roleId:', roleId);
                    
                    // 通过角色名称获取成员
                    const customMembers = await GroupIdentityCache.getMembersByIdentityName(this.groupId, roleName);
                    const customCount = customMembers.length;
                    console.log('Custom role count:', customCount, 'for roleName:', roleName, 'roleId:', roleId);
                    return customCount;
                } catch (error) {
                    console.error('获取自定义身份成员数量失败:', error);
                    return 0;
                }
            }
        },
        // 获取某个身份下的成员数量（同步方法，用于模板调用）
        getRoleMemberCount(roleId) {
            // 查找对应的角色
            const fixedRole = this.fixedRoles.find(role => role.id === roleId);
            if (fixedRole) {
                return fixedRole.count || 0;
            }
            
            const customRole = this.roles.find(role => role.id === roleId);
            if (customRole) {
                return customRole.count || 0;
            }
            
            return 0;
        },
        // 处理成员数量更新事件
        async handleRoleMemberCountUpdate() {
            console.log('Received member count update event')
            // 先加载成员身份数据
            await this.loadMemberIdentities()
            // 然后重新加载角色列表
            await this.loadRoleList()
            // 更新所有角色的成员数量
            await this.updateAllRoleCounts()
        },
        // 启动定时器
        startUpdateTimer() {
            if (!this.updateTimer) {
                console.log('启动群成员更新定时器')
                this.updateTimer = setInterval(() => {
                    if (this.groupId) {
                        console.log('定时更新群成员信息')
                        this.groupMembers = wfc.getGroupMembers(this.groupId)
                        this.$forceUpdate()
                    }
                }, 30000)
            }
        },
        // 停止定时器
        stopUpdateTimer() {
            if (this.updateTimer) {
                console.log('停止群成员更新定时器')
                clearInterval(this.updateTimer)
                this.updateTimer = null
            }
        },
        showRoleMembers(roleId, roleName) {
            // 添加防重复点击标记
            if (this.isNavigating) {
                return
            }
            
            if (!this.groupId || !roleId) {
                uni.showToast({
                    title: '参数错误',
                    icon: 'none'
                })
                return
            }

            this.isNavigating = true // 设置导航标记
            this.stopUpdateTimer() // 停止定时器

            uni.navigateTo({
                url: `/pages/group/GroupMemberList?groupId=${this.groupId}&roleId=${roleId}&roleName=${encodeURIComponent(roleName)}`,
                success: (res) => {
                    console.log('Navigation successful')
                    res.eventChannel.emit('roleInfo', {
                        groupId: this.groupId,
                        roleId: roleId,
                        roleName: roleName,
                        fromPage: 'GroupRoleSettings'
                    })
                },
                fail: (e) => {
                    console.error('跳转到身份成员页面失败:', e)
                    uni.showToast({
                        title: '页面跳转失败',
                        icon: 'none',
                        duration: 2000
                    })
                    this.startUpdateTimer() // 如果跳转失败，重新启动定时器
                },
                complete: () => {
                    // 导航完成后重置标记
                    setTimeout(() => {
                        this.isNavigating = false
                    }, 500)
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.group-role-settings {
    min-height: 100vh;
    background-color: #fff;
    padding: 0 0 85px;
    position: relative;
}

.role-list {
    margin: 12px 15px;
    background-color: #fff;
    padding: 4px 16px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.role-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    
    &:last-child {
        border-bottom: none;
    }

    &:active {
        background-color: #fafafa;
    }
    
    .role-info {
        display: flex;
        align-items: center;
        
        .role-name {
            font-size: 16px;
            color: #333;
        }
        
        .member-count {
            font-size: 14px;
            color: #999;
            margin-left: 8px;
        }
    }
}

.action-buttons {
    display: flex;
    gap: 16px;
}

.action-btn {
    font-size: 14px;
    padding: 6px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    
    &.edit {
        color: #386bf6;
        background-color: rgba(56, 107, 246, 0.1);

        &:active {
            background-color: rgba(56, 107, 246, 0.2);
        }
    }
    
    &.delete {
        color: #ff4d4f;
        background-color: rgba(255, 77, 79, 0.1);

        &:active {
            background-color: rgba(255, 77, 79, 0.2);
        }
    }
}

.add-role-btn {
    position: fixed;
    left: 15px;
    right: 15px;
    bottom: 20px;
    height: 50px;
    background: linear-gradient(135deg, #4e7cf7, #386bf6);
    border-radius: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    z-index: 100;
    box-shadow: 0 4px 12px rgba(56, 107, 246, 0.25);
    transition: all 0.2s ease;
    
    &:active {
        transform: scale(0.98);
        box-shadow: 0 2px 8px rgba(56, 107, 246, 0.2);
    }
}

/* 弹窗样式优化 */
.popup-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 999;
    backdrop-filter: blur(4px);
}

.add-role-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    background-color: #fff;
    border-radius: 16px;
    z-index: 1000;
    padding: 24px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);

    .popup-title {
        font-size: 18px;
        text-align: center;
        margin-bottom: 24px;
        font-weight: 600;
        color: #333;
    }

    .input-container {
        position: relative;
        margin-bottom: 24px;

        input {
            width: 100%;
            height: 44px;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 0 15px;
            font-size: 15px;
            transition: all 0.3s ease;
            
            &:focus {
                border-color: #386bf6;
                box-shadow: 0 0 0 2px rgba(56, 107, 246, 0.1);
            }
        }

        .input-counter {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 13px;
            color: #999;
            background: #f7f7f7;
            padding: 2px 6px;
            border-radius: 4px;
        }
    }

    .popup-buttons {
        display: flex;
        margin: 0 -24px -24px;
        border-top: 1px solid #f0f0f0;

        .btn {
            flex: 1;
            height: 50px;
            line-height: 50px;
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.2s ease;

            &.cancel {
                color: #666;
                border-right: 1px solid #f0f0f0;
                border-bottom-left-radius: 16px;

                &:active {
                    background-color: #f7f7f7;
                }
            }

            &.confirm {
                color: #386bf6;
                border-bottom-right-radius: 16px;

                &:active {
                    background-color: rgba(56, 107, 246, 0.05);
                }
            }
        }
    }
}
</style>