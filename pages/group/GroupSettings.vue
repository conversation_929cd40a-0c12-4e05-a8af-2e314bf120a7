<template>
    <div class="group-settings">
        <CustomHeader>
            <template v-slot:title>
                <text class="title">群管理</text>
            </template>
        </CustomHeader>
        <div class="group-settings-content">
            <view class="group-info-container">
                <view class="info-item no-border">
                    <view class="item-label">群聊邀请确认</view>
                    <view class="item-content">
                        <switch 
                            :checked="isJoinNeedConfirm" 
                            @change="toggleNeedConfirm" 
                            color="#4168e0" 
                            :disabled="isPaidGroup"
                        />
                    </view>
                </view>
                <view class="info-description">
                    <text v-if="isPaidGroup" class="paid-group-notice">
                        付费群不支持开启邀请确认功能
                    </text>
                    <text v-else>
                        启用后，群成员需群主或者群管理员确认才能邀请朋友进群。扫描二维码进群将同时停用。
                    </text>
                </view>
            </view>
            
            <!-- 待审批申请入口 -->
            <view class="group-info-container" v-if="isJoinNeedConfirm && false">
                <view class="info-item" @click="goToAuditRequests">
                    <view class="item-label">待审批申请</view>
                    <view class="item-content">
                        <text class="pending-count" v-if="pendingRequestCount > 0">{{ pendingRequestCount }}</text>
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
            </view>
            <view class="group-info-container">
                <view class="info-item">
                    <view class="item-label">仅群主/管理员可修改群名称</view>
                    <view class="item-content">
                        <switch :checked="isLimitChangeName" @change="toggleChangeName" color="#4168e0" />
                    </view>
                </view>
            </view>
            <view class="group-info-container">
                <view class="info-item" v-if="isGroupOwner" @click="goToTransferOwner">
                    <view class="item-label">群主管理权转让</view>
                    <view class="item-content">
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
                <view class="info-item" v-if="isGroupOwner" @click="goToGroupAdmins">
                    <view class="item-label">群管理员</view>
                    <view class="item-content">
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
                <view class="info-item" @click="goToExpandCapacity" v-if="false">
                    <view class="item-label">群人数扩容</view>
                    <view class="item-content">
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
                <view class="info-item" @click="goToRoleSettings">
                    <view class="item-label">群内身份</view>
                    <view class="item-content">
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
            </view>
        </div>
    </div>
</template>
<script>
import store from '@/store'
import wfc from '@/wfc/client/wfc'
import appServerApi from '../../api/appServerApi'
import CustomHeader from '../../components/custom-header'
import { getItem } from '@/pages/util/storageHelper'
import ModifyGroupInfoType from '@/wfc/model/modifyGroupInfoType'

/**
 * 群设置页面
 * 
 * 功能说明：
 * 1. "仅群主/管理员可修改群名称" 开关控制群名称修改权限
 * 2. 开关状态保存在群信息的extra字段中
 * 3. 开关开启时（默认），只有群主和管理员可以修改群名称
 * 4. 开关关闭时，所有群成员都可以修改群名称
 * 5. 权限检查在GroupConversationManagePage.vue和GroupConversationInfoPage.vue的enableEditGroupNameOrAnnouncement方法中实现
 * 6. 群聊邀请确认功能通过/group/detail接口的audit字段判断（1开启，0关闭）
 */
export default {
    name: 'GroupSettings',
    data() {
        return {
            isJoinNeedConfirm: false,
            isLimitChangeName: false, // 群名称修改限制开关
            groupId: '',
            groupInfo: null,
            groupExtra: {}, // 群扩展信息
            groupDetail: null, // 群详情信息（来自接口）
            pendingRequestCount: 0 // 待审批申请数量
        }
    },
    computed: {
        isGroupOwner() {
            if (!this.groupId || !this.groupInfo) return false
            const currentUserId = getItem('userId')
            return this.groupInfo.owner === currentUserId
        },
        isGroupAdmin() {
            if (!this.groupId || !this.groupInfo) return false
            const currentUserId = getItem('userId')
            return this.groupInfo.admins && this.groupInfo.admins.includes(currentUserId)
        },
        // 判断是否为付费群
        isPaidGroup() {
            // 优先使用接口返回的群详情信息
            if (this.groupDetail) {
                return this.groupDetail.price && this.groupDetail.price > 0
            }
            // 兜底使用本地群信息
            if (!this.groupInfo) return false
            return this.groupInfo.price && this.groupInfo.price > 0
        }
    },
    onLoad(option) {
        console.log('GroupSettings onLoad', option.groupId)
        this.groupId = option.groupId
        if (this.groupId) {
            this.groupInfo = wfc.getGroupInfo(this.groupId)
            this.loadGroupSettings()
        }
    },
    onShow() {
        // 每次页面显示时刷新待审批请求数量
        if (this.isJoinNeedConfirm) {
            this.loadPendingRequestCount()
        } else {
            // 如果审核关闭，清零待审批数量
            this.pendingRequestCount = 0
        }
    },
    methods: {
        // 加载群设置
        async loadGroupSettings() {
            if (!this.groupInfo) return
            
            try {
                // 解析群extra信息（仅用于群名称修改限制）
                if (this.groupInfo.extra) {
                    this.groupExtra = JSON.parse(this.groupInfo.extra)
                } else {
                    this.groupExtra = {}
                }
                
                // 读取群名称修改限制设置状态
                this.isLimitChangeName = this.groupExtra.limitChangeName !== false // 默认为true
                
                // 通过接口获取群详情来判断审核状态
                await this.loadGroupDetail()
                
                console.log('群设置已加载:', {
                    limitChangeName: this.isLimitChangeName,
                    joinNeedConfirm: this.isJoinNeedConfirm,
                    isPaidGroup: this.isPaidGroup
                })
            } catch (error) {
                console.error('解析群设置失败:', error)
                // 使用默认值
                this.groupExtra = {}
                this.isJoinNeedConfirm = false
                this.isLimitChangeName = true // 默认开启限制
            }
        },
        
        // 通过接口获取群详情
        async loadGroupDetail() {
            try {
                console.log('正在获取群详情...')
                const result = await appServerApi.getGroupDetail(this.groupId)
                
                if (result && result.code === 200 && result.data) {
                    this.groupDetail = result.data
                    
                    // 通过audit字段判断审核状态（1开启，0关闭）
                    this.isJoinNeedConfirm = this.groupDetail.audit === 1
                    
                    console.log('群详情获取成功:', {
                        audit: this.groupDetail.audit,
                        isJoinNeedConfirm: this.isJoinNeedConfirm,
                        price: this.groupDetail.price
                    })
                    
                    // 如果开启了审核，加载待审批请求数量
                    if (this.isJoinNeedConfirm) {
                        this.loadPendingRequestCount()
                    }
                } else {
                    console.error('获取群详情失败:', result)
                    this.isJoinNeedConfirm = false
                }
            } catch (error) {
                console.error('获取群详情出错:', error)
                this.isJoinNeedConfirm = false
            }
        },
        
        // 获取待审批请求数量
        async loadPendingRequestCount() {
            try {
                console.log('正在获取待审批请求数量...')
                const result = await appServerApi.getGroupAuditRequestList()
                
                if (result && result.code === 200 && result.data) {
                    this.pendingRequestCount = result.data.length || 0
                    console.log('待审批请求数量:', this.pendingRequestCount)
                } else {
                    this.pendingRequestCount = 0
                }
            } catch (error) {
                console.error('获取待审批请求数量出错:', error)
                this.pendingRequestCount = 0
            }
        },
        
        // 保存群设置到extra字段（仅保存群名称修改限制）
        saveGroupSettings() {
            // 更新extra对象（移除joinNeedConfirm，只保存limitChangeName）
            this.groupExtra.limitChangeName = this.isLimitChangeName
            
            // 保存到服务器
            wfc.modifyGroupInfo(
                this.groupId,
                ModifyGroupInfoType.Modify_Group_Extra,
                JSON.stringify(this.groupExtra),
                [0],
                null,
                () => {
                    console.log('群设置保存成功')
                    // 更新本地groupInfo
                    this.groupInfo.extra = JSON.stringify(this.groupExtra)
                },
                (err) => {
                    console.error('群设置保存失败:', err)
                    uni.showToast({
                        title: '设置保存失败',
                        icon: 'none'
                    })
                }
            )
        },
        
        // 切换进群邀请确认状态
        async toggleNeedConfirm(e) {
            const newValue = e.detail.value
            console.log('切换进群邀请确认状态:', newValue)
            
            // 如果是付费群且尝试开启邀请确认，则阻止操作
            if (this.isPaidGroup && newValue) {
                console.log('付费群不允许开启邀请确认功能')
                uni.showToast({
                    title: '付费群不支持邀请确认功能',
                    icon: 'none'
                })
                // 强制保持开关为关闭状态
                this.isJoinNeedConfirm = false
                return
            }
            
            try {
                // 调用后端API设置群聊审核状态
                const audit = newValue ? '1' : '0' // 1开启0关闭
                const result = await appServerApi.setGroupAudit(this.groupId, audit)
                
                if (result && result.code === 200) {
                    // API调用成功，更新本地状态
                    this.isJoinNeedConfirm = newValue
                    console.log('进群审核设置成功:', newValue ? '开启' : '关闭')
                    
                    // 更新群详情中的audit字段
                    if (this.groupDetail) {
                        this.groupDetail.audit = newValue ? 1 : 0
                    }
                    
                    // 更新待审批请求数量
                    if (newValue) {
                        // 开启审核时，加载待审批请求数量
                        await this.loadPendingRequestCount()
                    } else {
                        // 关闭审核时，自动通过所有待审批申请
                        await this.autoApproveAllPendingRequests()
                        this.pendingRequestCount = 0
                    }
                    
                    uni.showToast({
                        title: newValue ? '已开启进群审核' : '已关闭进群审核',
                        icon: 'success'
                    })
                } else {
                    // API调用失败，恢复开关状态
                    this.isJoinNeedConfirm = !newValue
                    const errorMsg = result?.msg || '设置失败'
                    console.error('进群审核设置失败:', errorMsg)
                    uni.showToast({
                        title: errorMsg,
                        icon: 'none'
                    })
                }
            } catch (error) {
                // 网络错误，恢复开关状态
                this.isJoinNeedConfirm = !newValue
                console.error('进群审核设置出错:', error)
                uni.showToast({
                    title: '网络错误，请稍后重试',
                    icon: 'none'
                })
            }
        },
        
        // 自动通过所有待审批申请
        async autoApproveAllPendingRequests() {
            try {
                console.log('关闭审核时自动通过所有待审批申请')
                
                // 先获取所有待审批申请
                const result = await appServerApi.getGroupAuditRequestList()
                
                if (result && result.code === 200 && result.data && result.data.length > 0) {
                    console.log('发现', result.data.length, '个待审批申请，开始自动通过')
                    
                    // 遍历所有申请，逐个通过
                    const approvePromises = result.data.map(request => {
                        return appServerApi.auditJoinGroupRequest(request.id, '1', '群主关闭审核，自动通过')
                    })
                    
                    // 等待所有申请处理完成
                    const approveResults = await Promise.allSettled(approvePromises)
                    
                    // 统计处理结果
                    let successCount = 0
                    let failCount = 0
                    
                    approveResults.forEach((result, index) => {
                        if (result.status === 'fulfilled' && result.value?.code === 200) {
                            successCount++
                            console.log(`申请 ${index + 1} 自动通过成功`)
                        } else {
                            failCount++
                            console.error(`申请 ${index + 1} 自动通过失败:`, result.reason)
                        }
                    })
                    
                    console.log(`自动审批完成: 成功 ${successCount} 个，失败 ${failCount} 个`)
                    
                    if (successCount > 0) {
                        uni.showToast({
                            title: `已自动通过 ${successCount} 个申请`,
                            icon: 'success',
                            duration: 2000
                        })
                    }
                    
                    if (failCount > 0) {
                        uni.showToast({
                            title: `${failCount} 个申请处理失败`,
                            icon: 'none',
                            duration: 2000
                        })
                    }
                } else {
                    console.log('没有待审批申请需要处理')
                }
            } catch (error) {
                console.error('自动通过待审批申请时出错:', error)
                uni.showToast({
                    title: '自动处理申请时出错',
                    icon: 'none'
                })
            }
        },
        
        toggleChangeName(e) {
            this.isLimitChangeName = e.detail.value
            this.saveGroupSettings()
        },
        goToTransferOwner() {
            if (!this.groupId) {
                uni.showToast({
                    title: '群组ID不存在',
                    icon: 'none'
                })
                return
            }
            uni.navigateTo({
                url: `./GroupMemberList?groupId=${this.groupId}&roleId=transfer&roleName=${encodeURIComponent('选择新群主')}`,
                fail: (err) => {
                    console.error('跳转失败:', err)
                    uni.showToast({
                        title: '页面跳转失败',
                        icon: 'none'
                    })
                }
            })
        },
        goToGroupAdmins() {
            if (!this.groupId) {
                uni.showToast({
                    title: '群组ID不存在',
                    icon: 'none'
                })
                return
            }
            
            // 跳转到群管理员页面
            uni.navigateTo({
                url: `./GroupAdmins?groupId=${this.groupId}`,
                fail: (err) => {
                    console.error('跳转失败:', err)
                    uni.showToast({
                        title: '页面跳转失败',
                        icon: 'none'
                    })
                }
            })
        },
        goToExpandCapacity() {
            uni.showToast({
                title: '功能开发中',
                icon: 'none'
            })
        },
        goToRoleSettings() {
            if (!this.groupId) {
                uni.showToast({
                    title: '群组ID不存在',
                    icon: 'none'
                })
                return
            }
            uni.navigateTo({
                url: `./GroupRoleSettings?groupId=${this.groupId}`,
                fail: (err) => {
                    console.error('跳转失败:', err)
                    uni.showToast({
                        title: '页面跳转失败',
                        icon: 'none'
                    })
                }
            })
        },
        goToAuditRequests() {
            if (!this.groupId) {
                uni.showToast({
                    title: '群组ID不存在',
                    icon: 'none'
                })
                return
            }
            uni.navigateTo({
                url: `./GroupAuditRequests?groupId=${this.groupId}`,
                fail: (err) => {
                    console.error('跳转失败:', err)
                    uni.showToast({
                        title: '页面跳转失败',
                        icon: 'none'
                    })
                }
            })
        }
    },

    components: {
        CustomHeader,
    }
}
</script>
<style lang="scss" scoped>
.group-settings {
    height: 100vh;
    overflow: hidden;
    background-color: #f7f7f7;

    .group-settings-content {
        display: flex;
        flex-direction: column;
        position: relative;
        justify-content: flex-start;
        overflow: auto;
        padding: 12px 15px 50px;
        height: calc(100vh - var(--status-bar-height));

        .group-info-container {
            width: 100%;
            background-color: #ffffff;
            padding: 4px 16px;
            margin-bottom: 12px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
            
            &:last-child {
                margin-bottom: 10px;
            }

            .info-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px 0;
                border-bottom: 1px solid #f0f0f0;

                &.no-border {
                    border-bottom: none;
                }

                &:last-child {
                    border-bottom: none;
                }

                &:active {
                    background-color: #fafafa;
                }

                .item-label {
                    font-size: 16px;
                    color: #333;
                    font-weight: 500;
                    white-space: nowrap;
                }

                .item-content {
                    display: flex;
                    align-items: center;
                    color: #999;
                    font-size: 14px;

                    .item-text {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        max-width: 200px;
                    }

                    .pending-count {
                        background-color: #ff4757;
                        color: #ffffff;
                        font-size: 12px;
                        padding: 2px 6px;
                        border-radius: 10px;
                        margin-right: 8px;
                        min-width: 18px;
                        text-align: center;
                        line-height: 1.2;
                    }

                    .icon-ion-ios-arrow-right {
                        margin-left: 8px;
                        font-size: 16px;
                        color: #bbb;
                        transition: all 0.2s ease;
                    }

                    switch {
                        transform: scale(0.9);
                        margin-right: -6px;
                    }
                }

                &:active .item-content .icon-ion-ios-arrow-right {
                    transform: translateX(3px);
                    color: #999;
                }
            }
            
            .info-description {
                font-size: 12px;
                color: #999;
                padding: 0 0 16px 0;
                line-height: 1.5;
                margin-top: -8px;
                
                .paid-group-notice {
                    color: #ff6b35;
                    font-weight: 500;
                }
            }
        }
    }
}
</style>