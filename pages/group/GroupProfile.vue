<template>
    <view class="group-profile-container">
        <!-- 使用CustomHeader组件，添加固定样式以防止输入法顶起 -->
        <view class="fixed-header">
            <CustomHeader leftIcon="back" backgroundColor="#ffffff">
                <template v-slot:title>
                    <view class="title-container">
                        <text class="title" v-if="!showSearch">{{ groupInfo ? groupInfo.name : '群聊' }}({{ memberCount }})</text>
                        <view class="search-input-container" v-if="showSearch">
                            <input class="search-input" v-model="searchKeyword" placeholder="搜索群成员" focus @input="onSearchInput" @blur="closeSearchIfEmpty" />
                        </view>
                        <view class="search-icon" @click="toggleSearch">
                            <text class="iconfont" v-if="!showSearch">&#xe604;</text>
                            <text class="iconfont" v-else>&#xe605;</text>
                        </view>
                    </view>
                </template>
                <template v-slot:right>
                    <!-- <text class="right-edit-btn" @click="isEdit ? saveProfile() : startEdit()">{{ isEdit ? '提交' : '编辑' }}</text> -->
                    <text class="right-edit-btn" @click="goToMyCard">{{ '名片' }}</text>
                </template>
            </CustomHeader>
        </view>
        
        <!-- 添加导航栏占位元素，防止内容被遮挡 -->
        <view class="header-placeholder" :style="{ height: (statusBarHeight + 44) + 'px' }"></view>
        
        <!-- 内容区域，修改滚动区域样式 -->
        <scroll-view class="scroll-content" scroll-y :style="{ 
            height: 'calc(100vh - ' + (statusBarHeight + 44) + 'px)',
            paddingBottom: keyboardHeight > 0 ? '10px' : '0'
        }">
            <!-- 搜索结果 -->
            <view class="search-results" v-if="showSearch && searchResults.length > 0">
                <view class="search-item" v-for="(item, index) in searchResults" :key="index" @click="viewMemberProfile(item)">
                    <view class="search-user-avatar">
                        <image class="avatar-image" :src="getUserPortrait(item.userId)" mode="aspectFill"></image>
                    </view>
                    <view class="search-user-info">
                        <text class="search-user-name">{{ item.name || item.name }}</text>
                        <text class="search-user-title">{{ getFirstTextContent(item.description) }}</text>
                    </view>
                </view>
                <view class="search-results-end">
                    <text class="search-results-end-text">- 搜索结果结束 -</text>
                </view>
            </view>
            
            <!-- 无搜索结果提示 -->
            <view class="no-results" v-if="showSearch && searchKeyword && !searchResults.length">
                <text class="no-results-text">没有找到匹配的群成员</text>
            </view>
            
            <!-- 缺省页 - 当没有任何群名片时显示 -->
            <view class="empty-state" v-if="!showSearch && !isEdit && !hasMyProfile && otherMembersProfiles.length === 0">
                <image class="empty-state-image" src="/static/image/icon/empty-profile.png" mode="aspectFit"></image>
                <text class="empty-state-text" @click="startEdit">点击编辑名片获得更多资源对接</text>
            </view>
            
            <!-- 群成员名片信息列表 -->
            <view class="profile-list" v-if="(!showSearch || !searchResults.length) && (hasMyProfile || otherMembersProfiles.length > 0)">
                <!-- 当前用户名片 -->
                <view class="profile-card" v-if="!isEdit && hasMyProfile" @click="goToCardDetails(profile)">
                    <view class="preview-card-box">
                        <view class="preview-info-row">
                            <image class="preview-avatar" :src="getCurrentUserPortrait()" />
                            <view class="preview-info-main">
                                <view class="name-row">
                                    <text class="preview-info-name">{{ profile.name || '未设置姓名' }}</text>
                                    <text v-if="profile.roleName" class="role-badge">{{ profile.roleName }}</text>
                                </view>
                                <text class="preview-info-title">{{ profile.company || '未设置公司/职位' }}</text>
                            </view>
                        </view>

                        <view class="preview-section-row flex-align">
                            <view class="preview-section-bar"></view>
                            <text class="preview-section-label">简介</text>
                            <view class="preview-section-content-block">
                                <text class="preview-section-content-inline">{{ getFirstTextContent(profile.description) || '未设置简介' }}</text>
                            </view>
                        </view>
                        <view class="preview-section-row flex-align">
                            <view class="preview-section-bar"></view>
                            <text class="preview-section-label">资源</text>
                            <view class="preview-section-content-block">
                                <text class="preview-section-content-inline">{{ getFirstTextContent(profile.advantage) || '未设置资源' }}</text>
                            </view>
                        </view>
                        <view class="preview-section-row flex-align">
                            <view class="preview-section-bar"></view>
                            <text class="preview-section-label">需求</text>
                            <view class="preview-section-content-block">
                                <text class="preview-section-content-inline">{{ getFirstTextContent(profile.defect) || '未设置需求' }}</text>
                            </view>
                        </view>
                    </view>
                    
                </view>
                
                <!-- 其他群成员名片列表 -->
                <view class="profile-card" v-for="(member, index) in otherMembersProfiles" :key="index" v-if="!isEdit" @click="goToCardDetails(member)">
                    <view class="preview-card-box">
                        <view class="preview-info-row">
                            <image class="preview-avatar" :src="getUserPortrait(member.userId)" />
                            <view class="preview-info-main">
                                <view class="name-row">
                                    <text class="preview-info-name">{{ member.name || '未设置姓名' }}</text>
                                    <text v-if="member.roleName" class="role-badge">{{ member.roleName }}</text>
                                </view>
                                <text class="preview-info-title">{{ member.company || '未设置公司/职位' }}</text>
                            </view>
                        </view>
                        <view class="preview-section-row flex-align">
                            <view class="preview-section-bar"></view>
                            <text class="preview-section-label">简介</text>
                            <view class="preview-section-content-block">
                                <text class="preview-section-content-inline">{{ getFirstTextContent(member.description) || '未设置简介' }}</text>
                            </view>
                        </view>
                        <view class="preview-section-row flex-align">
                            <view class="preview-section-bar"></view>
                            <text class="preview-section-label">资源</text>
                            <view class="preview-section-content-block">
                                <text class="preview-section-content-inline">{{ getFirstTextContent(member.advantage) || '未设置资源' }}</text>
                            </view>
                        </view>
                        <view class="preview-section-row flex-align">
                            <view class="preview-section-bar"></view>
                            <text class="preview-section-label">需求</text>
                            <view class="preview-section-content-block">
                                <text class="preview-section-content-inline">{{ getFirstTextContent(member.defect) || '未设置需求' }}</text>
                            </view>
                        </view>
                    </view>
                
                </view>
            </view>

            <!-- 滚筒区域悬浮在页面上，带遮罩 -->
            <view v-if="!hasMyProfile && userCards.length && showEmptyGroupCardModal" class="empty-group-card-modal-mask" @click="dismissShowCardModal">
                <view class="empty-group-card-modal-box" @click.stop>
                    <view class="empty-group-card-modal-content">
                        该群未展示名片，请选择想展示的名片信息吧~
                    </view>
                    <view class="empty-group-card-modal-actions">
                        <button class="empty-modal-btn cancel" @click="dismissShowCardModal">暂不展示</button>
                        <button class="empty-modal-btn confirm" @click="goToMyCard">立即前往</button>
                    </view>
                </view>
            </view>
        </scroll-view>
    </view>
</template>

<script>
import wfc from '@/wfc/client/wfc';
import CustomHeader from '@/components/custom-header/index';
import appServerApi from '@/api/appServerApi';

export default {
    components: {
        CustomHeader
    },
    data() {
        return {
            groupId: '',
            groupInfo: null,
            memberInfo: null,
            memberCount: 0,
            currentUserId: '',  // 添加当前用户ID属性
            profile: { //存储自己已发布到该群的群名片信息
                id: '',
                name: '',
                company: '',
                roleName: '',
                description: '',
                advantage: '',
                defect: '',
                url:[],
                phone: '',
                wechatId: '',
            },
            otherMembersProfiles: [],//存储其他成员的名片
            isEdit: false,
            isSaving: false,
            showSearch: false,
            searchKeyword: '',
            searchResults: [],
            allMembersProfiles: [], // 存储所有成员的名片信息
            hasMyProfile: false,    // 添加标记，表示当前用户是否已有群名片
            keyboardHeight: 0,      // 添加键盘高度状态
            statusBarHeight: 0,      // 添加状态栏高度
            userCards: [], // 当前用户所有名片
            allGroupCards: [],//当前群中展示的所有名片
            currentCardIndex: 0, // 当前滚筒展示的名片索引
            showUserCardsRoller: true, // 滚筒开关，默认显示
            showEmptyGroupCardModal: false, // 新增弹窗开关
            hasDismissedShowCardModal: false, // 新增：标记用户是否主动关闭过弹窗
        }
    },
    watch: {
        hasMyProfile(val) {
            if (!val && this.userCards.length && !this.hasDismissedShowCardModal) {
                this.showEmptyGroupCardModal = true;
            } else {
                this.showEmptyGroupCardModal = false;
            }
        },
        userCards(val) {
            if (!this.hasMyProfile && val.length && !this.hasDismissedShowCardModal) {
                this.showEmptyGroupCardModal = true;
            } else {
                this.showEmptyGroupCardModal = false;
            }
        }
    },
    // 添加onBackPress生命周期方法
    onBackPress() {
        // 如果当前在编辑模式，拦截返回事件并关闭编辑模式
        if (this.isEdit) {
            this.isEdit = false;
            return true; // 返回true表示自己处理返回事件
        }
        
        // 如果搜索栏打开，先关闭搜索栏
        if (this.showSearch) {
            this.showSearch = false;
            this.searchKeyword = '';
            this.searchResults = [];
            return true;
        }
        
        // 返回false表示不拦截，由系统处理返回事件
        return false;
    },
    onLoad(options) {
        // 获取状态栏高度
        const sysInfo = uni.getSystemInfoSync();
        this.statusBarHeight = sysInfo.statusBarHeight;
        
        // 接收参数
        this.groupId = options.groupId;
        
        // 存储当前用户ID
        this.currentUserId = wfc.getUserId();

        this.getUserCard();

        this.fetchMyCards();
        
        // 获取群组信息
        if (this.groupId) {
            this.groupInfo = wfc.getGroupInfo(this.groupId);
            
            // 获取群成员数量
            const members = wfc.getGroupMembers(this.groupId);
            this.memberCount = members ? members.length : 0;
            
            // 获取当前用户在群中的信息
            this.memberInfo = wfc.getGroupMember(this.groupId, this.currentUserId);
            
            // 加载现有的群名片信息
            // this.loadExistingProfile();
            
            // 加载其他群成员的名片信息
            // this.loadOtherMembersProfiles();
        }
        
        // 监听键盘高度变化
        uni.onKeyboardHeightChange(res => {
            console.log('键盘高度变化：', res.height);
            this.keyboardHeight = res.height;
        });

        uni.$on('refreshGroupCard', this.refreshGroupCardHandler);
    },

    onShow() {
            this.getUserCard();
            this.fetchMyCards();
        },
    // 在页面卸载时移除键盘监听
    onUnload() {
        try {
            uni.offKeyboardHeightChange();
            console.log('移除键盘高度监听');
        } catch (error) {
            console.error('移除键盘监听失败:', error);
        }
        uni.$off('refreshGroupCard', this.refreshGroupCardHandler);
    },
    methods: {
        refreshGroupCardHandler() {
            this.getUserCard();
        },
        // 返回上一页
        goBack() {
            // 如果当前在编辑模式，点击返回应该退出编辑模式而不是返回上一页
            if (this.isEdit) {
                this.isEdit = false;
                return;
            }
            // 否则正常返回上一页
            uni.navigateBack();
        },
        
        // 开始编辑
        startEdit() {
            console.log("点击编辑，传递的名片数据:", this.profile);
            // 保存当前的 global 值
            // const currentGlobal = this.profile.global;
            // this.isEdit = true;
            // 确保 global 值不被重置
            // this.profile.global = currentGlobal;
            // console.log('进入编辑模式，当前展示范围:', this.profile.global);
            // 跳转到编辑名片页面
            // 将 profile 数据序列化并编码后传递给 EditCard.vue
            const cardDataStr = encodeURIComponent(JSON.stringify(this.profile));
            uni.navigateTo({
                //带着群id和名片信息跳转页面并
                url: `/pages/card/EditCard?groupId=${this.groupId}&card=${cardDataStr}`
            });
        },

        // 添加新方法：获取第一段文本内容
        getFirstTextContent(content) {
            if (!content) return '';
            
            // 按换行符分割内容
            const lines = content.split('\\n');
            
            // 查找第一个不是链接的文本
            const firstText = lines.find(line => {
                const trimmed = line.trim();
                // 排除OSS链接和本地文件链接
                return trimmed && 
                    !trimmed.includes('oss-cn-hangzhou.aliyuncs.com') && 
                    !trimmed.startsWith('file:///');
            });
            
            return firstText || '';
        },
        //获取当前用户名片夹里的所有名片
        async fetchMyCards() {
            try {
                // 调用后端接口获取我的名片数据
                const result = await appServerApi.getMyCard();
                console.log('GroupProfile.vue页面获取到的我的名片数据:', result);
                
                if (result && result.data && Array.isArray(result.data)) {
                    this.userCards = result.data; // 存储原始数据
                    console.log("获取到的userCards:", this.userCards);
                    
                    // 如果有名片数据，设置当前索引为0
                    if (this.userCards.length > 0) {
                        this.currentCardIndex = 0;
                    }
                } else {
                    console.log('未获取到名片数据或数据格式不正确');
                    this.userCards = [];
                }
            } catch (error) {
                console.error('获取我的名片数据失败:', error);
                this.userCards = [];
                uni.showToast({
                    title: '获取名片数据失败',
                    icon: 'none'
                });
            }
        },
        
        // 获取紧急程度文本
        getUrgencyText() {
            const urgencyMap = {
                'urgent': '急需(1周内)',
                'normal': '中度(1-3个月)',
                'longterm': '长期规划'
            };
            return urgencyMap[this.profile.urgency] || '急需(1周内)';
        },
        
        // 获取当前用户头像
        getCurrentUserPortrait() {
            return this.getUserPortrait(this.currentUserId);
        },
        
        // 获取用户头像
        getUserPortrait(userId) {
            console.log('获取用户头像，userId:', userId);
            // 确保用户ID存在
            if (!userId) {
                // 尝试使用当前登录用户ID
                userId = this.currentUserId;
                console.log('用户ID为空，使用当前用户ID:', userId);
                if (!userId) {
                    console.log('无法获取有效用户ID，使用默认头像');
                    return '/static/images/user.png';
                }
            }
            
            const userInfo = wfc.getUserInfo(userId);
            // console.log('获取到的用户信息:', userInfo);
            
            if (!userInfo || !userInfo.portrait) {
                console.log('用户头像为空，使用默认头像');
                return '/static/images/user.png';
            }
            
            // 确保portrait是有效URL
            if (!userInfo.portrait.startsWith('http') && !userInfo.portrait.startsWith('/')) {
                console.log('头像路径格式不正确，使用默认头像');
                return '/static/images/user.png';
            }
            
            // console.log('使用用户头像:', userInfo.portrait);
            return userInfo.portrait;
        },
        
        // 选择紧急程度
        selectUrgency(type) {
            console.log('用户选择了紧急程度:', type);
            this.profile.urgency = type;
            
            // 立即输出确认更改生效
            console.log('更新后的紧急程度:', this.profile.urgency);
        },
        
        // 选择展示范围
        selectGlobal(value) {
            console.log('用户选择了展示范围:', value);
            this.profile.global = Number(value); // 强制转为数字
            console.log('更新后的展示范围:', this.profile.global, typeof this.profile.global);
        },
        
        // 获取展示范围文本
        getGlobalText() {
            return this.profile.global === 0 ? '全平台展示' : '只在该群展示';
        },
        
        // 将紧急程度字符串转换为字符串key
        getUrgencyKey(hurryLevel) {
            console.log('获取紧急程度key，输入值:', hurryLevel, '类型:', typeof hurryLevel);
            // 兼容数字和字符串类型
            const urgencyMap = {
                1: 'urgent',    // 数字类型
                2: 'normal',
                3: 'longterm',
                '1': 'urgent',  // 字符串类型 
                '2': 'normal',
                '3': 'longterm'
            };
            const result = urgencyMap[hurryLevel] || 'urgent';
            console.log('紧急程度映射结果:', result);
            return result;
        },
        
        // 使用用户基本信息作为默认值
        useUserInfoAsDefault() {
            const userInfo = wfc.getUserInfo(this.currentUserId);
            if (userInfo) {
                this.profile.displayName = userInfo.displayName || userInfo.name;
            }
            this.memberInfo.alias = this.profile.displayName;
            this.memberInfo.extra = JSON.stringify({
                company: this.profile.company,
                title: this.profile.title,
                advantage: this.profile.advantage,
                defect: this.profile.defect,
                // urgency: this.profile.urgency,
                // global: this.profile.global
            });
        },
        
        // 加载现有的群名片信息
        loadExistingProfile() {
            if (!this.groupId) return;

            console.log('开始加载现有群名片');
            
            // 使用getGroupCards接口来获取所有群名片
            appServerApi.getGroupCards({
                gid: this.groupId,  // 群组ID
                pageNo: 1,
                pageSize: 50  // 增大页面大小，确保能获取到所有群成员名片
            }).then(res => {
                console.log('获取群名片API返回数据:', JSON.stringify(res));
                // 正确解析API返回的结构
                if (res && res.data && res.data.result && res.data.result.length > 0) {
                    // 找到当前用户的群名片
                    const myCard = res.data.result.find(card => card.userId === this.currentUserId);
                    console.log('找到当前用户群名片:', JSON.stringify(myCard));
                    if (myCard) {
                        // 输出卡片中的紧急程度
                        // console.log('群名片中的hurryLevel值:', myCard.hurryLevel, '类型:', typeof myCard.hurryLevel);
                        
                        // 确保紧急程度字段有效
                        // if (myCard.hurryLevel === undefined || myCard.hurryLevel === null) {
                        //     console.warn('服务器返回的hurryLevel无效，使用默认值');
                        //     myCard.hurryLevel = 1; // 默认为急需
                        // }
                        
                        // 更新表单数据
                        this.profile.displayName = myCard.name || '';
                        this.profile.title = myCard.job || '';
                        this.profile.company = myCard.company || '';
                        this.profile.advantage = myCard.advantage || '';
                        this.profile.defect = myCard.defect || '';
                        
                        // 不使用String转换，直接使用数字类型
                        const urgencyKey = this.getUrgencyKey(myCard.hurryLevel);
                        console.log('转换后的紧急程度key:', urgencyKey);
                        this.profile.urgency = urgencyKey;
                        
                        // 处理展示范围
                        this.profile.global = (myCard.global !== undefined && myCard.global !== null) ? Number(myCard.global) : 1;
                        console.log('群名片中的展示范围:', this.profile.global, typeof this.profile.global);

                        // 更新本地显示
                        this.memberInfo.alias = this.profile.displayName;
                        this.memberInfo.extra = JSON.stringify({
                            company: this.profile.company,
                            title: this.profile.title,
                            advantage: this.profile.advantage,
                            defect: this.profile.defect,
                            urgency: this.profile.urgency,
                            global: this.profile.global
                        });
                        
                        // 设置标记，表示当前用户已有群名片
                        this.hasMyProfile = true;
                    } else {
                        console.log('未找到当前用户群名片，尝试使用用户基本信息');
                        this.useUserInfoAsDefault();
                        // 设置标记，表示当前用户没有群名片
                        this.hasMyProfile = false;
                    }
                } else {
                    console.log('群名片列表为空或未正确返回，尝试使用用户基本信息');
                    this.useUserInfoAsDefault();
                    // 设置标记，表示当前用户没有群名片
                    this.hasMyProfile = false;
                }
            }).catch(err => {
                console.error('获取群名片失败:', err);
                // 使用用户信息作为默认值
                this.useUserInfoAsDefault();
                // 设置标记，表示当前用户没有群名片
                this.hasMyProfile = false;
            });
        },
        
        // 切换搜索状态
        toggleSearch() {
            this.showSearch = !this.showSearch;
            if (this.showSearch) {
                this.searchKeyword = '';
                this.searchResults = [];
                // 合并当前用户和其他成员的名片信息
                this.updateAllMembersProfiles();
            }
        },
        
        // 如果搜索框为空并且失焦，关闭搜索
        closeSearchIfEmpty() {
            if (!this.searchKeyword.trim()) {
                setTimeout(() => {
                    this.showSearch = false;
                }, 200);
            }
        },
        
        // 关闭搜索
        closeSearchPopup() {
            this.showSearch = false;
            this.searchKeyword = '';
            this.searchResults = [];
        },
        
        // 更新所有成员名片列表
        updateAllMembersProfiles() {
            this.allMembersProfiles = [...this.otherMembersProfiles];
            
            // 只有当用户有群名片时，才添加到搜索列表中
            if (this.hasMyProfile) {
                // 添加当前用户的信息
                const currentUserProfile = {
                    userId: this.profile.userId,
                    name: this.profile.name,
                    avatar: this.profile.avatar,
                    displayName: this.profile.displayName,
                    description: this.profile.description,
                    company: this.profile.company,
                    advantage: this.profile.advantage,
                    defect: this.profile.defect,
                };
                
                this.allMembersProfiles.push(currentUserProfile);
            }
            console.log("allMembersProfiles:",this.allMembersProfiles)
        },
        
        // 处理搜索输入
        onSearchInput() {
            if (!this.searchKeyword.trim()) {
                this.searchResults = [];
                return;
            }
            
            const keyword = this.searchKeyword.toLowerCase().trim();
            this.searchResults = this.allMembersProfiles.filter(member => {
                return (
                    (member.name && member.name.toLowerCase().includes(keyword)) ||
                    (member.description && member.description.toLowerCase().includes(keyword)) ||
                    (member.company && member.company.toLowerCase().includes(keyword)) ||
                    (member.advantage && member.advantage.toLowerCase().includes(keyword)) ||
                    (member.defect && member.defect.toLowerCase().includes(keyword))
                );
            });
        },
        
        // 查看成员详情
        viewMemberProfile(member) {
            console.log('查看成员详情:', member);
            // 如果是当前用户，切换到编辑模式
            if (member.userId === this.currentUserId) {
                this.closeSearchPopup();
                this.startEdit();
                return;
            }
            
            // 如果是其他成员，可以滚动到该成员的卡片
            this.closeSearchPopup();
            // TODO: 可以实现滚动到对应成员卡片的功能
            uni.showToast({
                title: '查看成员: ' + member.name,
                icon: 'none'
            });
        },
        
        // 加载其他群成员的名片信息
        loadOtherMembersProfiles() {
            // 使用API获取群成员名片
            if (!this.groupId) return;
            
            appServerApi.getGroupCards({
                gid: this.groupId,  // 群组ID
                pageNo: 1,          // 页码
                pageSize: 50        // 增大页面大小
            }).then(res => {
                console.log('获取群成员名片成功:', res.data);
                // 正确解析API返回的结构
                if (res && res.data && res.data.result) {
                    // 筛选出已编辑过群名片的成员
                    const editedCards = res.data.result.filter(card => {
                        // 判断卡片是否有有效内容（至少有一个字段不为空）
                        return card.userId !== this.currentUserId && 
                               (card.name || card.job || card.company || 
                                card.advantage || card.defect);
                    });
                    
                    this.otherMembersProfiles = editedCards.map(card => {
                        // 转换数据结构为界面显示格式
                        const urgencyMap = {
                            '1': '急需(1周内)',    // hurryLevel是字符串类型
                            '2': '中度(1-3个月)',
                            '3': '长期规划'
                        };
                        
                        return {
                            userId: card.userId,
                            displayName: card.name || '',
                            title: card.job || '',
                            company: card.company || '',
                            advantage: card.advantage || '',
                            defect: card.defect || '',
                            urgency: this.getUrgencyKey(card.hurryLevel) || 'urgent',
                            urgencyText: urgencyMap[card.hurryLevel] || '急需(1周内)',
                            global: card.global !== undefined ? card.global : 1,
                            globalText: card.global === 1 ? '只在该群展示' : '全平台展示'
                        };
                    });
                    
                    // 更新所有成员名片列表
                    this.updateAllMembersProfiles();
                }
            }).catch(err => {
                console.error('获取群成员名片失败:', err);
                // 加载失败时显示提示
                uni.showToast({
                    title: '加载群成员名片失败',
                    icon: 'none'
                });
            });
        },

        // 查询群内所有名片
        async getUserCard() {
            try {
                const result = await appServerApi.getUserCard({
                    gid: this.groupId,
                    pageNo: 1,
                    pageSize: 20
                });
                console.log("能获取到群ID吗？", this.groupId);
                // 正确取 result.data.result
                if (result && result.data && Array.isArray(result.data.result)) {
                    const allCards = result.data.result;
                    const myUserId = this.currentUserId || (this.memberInfo && this.memberInfo.userId);

                    // 找到自己的名片
                    const myCard = allCards.find(card => card.userId === myUserId);
                    // 其余成员名片
                    const otherCards = allCards.filter(card => card.userId !== myUserId);

                    // 渲染自己的名片
                    if (myCard) {
                        this.profile.id = myCard.id || '';
                        this.profile.userId = myCard.userId || '';
                        this.profile.name = myCard.name || '';
                        this.profile.avatar = myCard.avatar || '';
                        this.profile.company = myCard.company || '';
                        this.profile.roleName = myCard.roleName || '';
                        this.profile.description = myCard.description || '';           
                        this.profile.advantage = myCard.advantage || '';
                        this.profile.defect = myCard.defect || '';
                        this.profile.url = myCard.url;
                        this.profile.phone = myCard.phone || '';
                        this.profile.wechatId = myCard.wechatId || '';
                        this.profile.platformIndex = myCard.platformIndex
                        // 你可以根据需要补充其它字段
                        this.hasMyProfile = true;
                    } else {
                        this.hasMyProfile = false;
                    }
                    console.log("我的名片信息：",this.profile)
                    // 渲染其他成员名片
                    this.otherMembersProfiles = otherCards.map(card => ({
                        id: card.id,
                        userId: card.userId,
                        name: card.name || '',
                        avatar: card.avatar,
                        company: card.company || '',
                        roleName: card.roleName || '',
                        description: card.description || '',
                        advantage: card.advantage || '',
                        defect: card.defect || '',
                        url: card.url,
                        phone: card.phone || '',
                        wechatId: card.wechatId || '',
                        platformIndex : card.platformIndex
                    
                        // 你可以根据需要补充其它字段
                    }));

                    this.allGroupCards = allCards; // 如果页面其它地方还用到全部名片
                    this.currentCardIndex = 0;
                } else {
                    this.allGroupCards = [];
                    this.otherMembersProfiles = [];
                    this.hasMyProfile = false;
                }
                console.log('查询群内所有名片(this.allGroupCards):', this.allGroupCards);
            } catch (e) {
                console.error('查询群内名片失败:', e);
                this.allGroupCards = [];
                this.otherMembersProfiles = [];
                this.hasMyProfile = false;
            }
        },
        

        goToCardDetail(card) {
            // 跳转到名片详情页，传递完整名片数据（原始card对象）
            const cardStr = encodeURIComponent(JSON.stringify(card));
            uni.navigateTo({
                url: `/pages/card/CardDetail?card=${cardStr}`
            });
        },

        async saveProfile() {
            if (!this.groupId || !this.memberInfo) return;
            
            if (this.isSaving) return; // 防止重复提交
            
            // 如果用户选择了全平台展示，先弹出确认对话框
            // if (this.profile.global === 0) {
            //     // 使用uni-app的showModal方法弹出确认对话框
            //     const confirmResult = await new Promise((resolve) => {
            //         uni.showModal({
            //             title: '提示',
            //             content: '由于您的名片已在全平台展示，此次编辑将进行覆盖，是否确认？',
            //             cancelText: '取消',
            //             confirmText: '确认',
            //             success: function(res) {
            //                 resolve(res.confirm);
            //             }
            //         });
            //     });
                
            //     // 如果用户点击取消，则中断保存操作
            //     if (!confirmResult) {
            //         return;
            //     }
            // }
            
            // uni.showLoading({
            //     title: '保存中...'
            // });
            this.isSaving = true;

            try {
                const extraInfo = {};
                if (this.profile.company) extraInfo.company = this.profile.company;
                if (this.profile.title) extraInfo.title = this.profile.title;
                if (this.profile.advantage) extraInfo.advantage = this.profile.advantage;
                if (this.profile.defect) extraInfo.defect = this.profile.defect;
                if (this.profile.urgency) extraInfo.urgency = this.profile.urgency;
                if (this.profile.global !== undefined) extraInfo.global = this.profile.global;
                
                // 获取之前保存的id，以便更新而不是创建
                let cardId = null;
                try {
                    const existingCards = await appServerApi.getGroupCards({
                        gid: this.groupId,
                        pageNo: 1,
                        pageSize: 50
                    });
                    console.log('查询现有名片:', JSON.stringify(existingCards));
                    
                    if (existingCards && existingCards.data && existingCards.data.result) {
                        const myExistingCard = existingCards.data.result.find(card => card.userId === this.currentUserId);
                        if (myExistingCard) {
                            cardId = myExistingCard.id;
                            console.log('找到现有名片ID:', cardId);
                        }
                    }
                } catch (err) {
                    console.error('获取现有名片信息失败:', err);
                }
                
                // 将紧急程度字符串转换为对应的数字（移除引号）
                const urgencyMap = {
                    'urgent': 1,     // 改为数字类型
                    'normal': 2,
                    'longterm': 3
                };
                
                console.log('当前选择的紧急程度:', this.profile.urgency);
                const hurryLevel = urgencyMap[this.profile.urgency] || 1;
                console.log('提交的hurryLevel值:', hurryLevel, '类型:', typeof hurryLevel);

                // 使用API保存群名片（一个用户在一个群中只能有一张群名片）
                const cardInfo = {
                    gid: this.groupId,         // 群组ID
                    userId: this.currentUserId, // 用户ID
                    name: this.profile.displayName,  // 姓名
                    company: this.profile.company || '',
                    job: this.profile.title || '',
                    advantage: this.profile.advantage || '',
                    defect: this.profile.defect || '',
                    hurryLevel: hurryLevel,      // 数字类型
                    global: this.profile.global  // 添加展示范围参数
                };
                
                // 如果有ID就添加到请求中，用于更新而不是创建
                if (cardId) {
                    cardInfo.id = cardId;
                    console.log('使用现有ID更新群名片:', cardId);
                }
                
                console.log('提交前的profile.global:', this.profile.global);
                console.log('保存群名片:', JSON.stringify(cardInfo));
                // 根据appServerApi.js中_post方法，如果成功返回的是res.data.data
                // 但由于接口返回的data是null，所以saveResult为null是正常的
                const saveResult = await appServerApi.saveGroupCard(cardInfo);
                console.log('保存结果:', JSON.stringify(saveResult));
                
                // 修改判断逻辑，null也表示成功
                console.log('群名片保存成功');
                
                // 更新本地显示
                this.memberInfo.alias = this.profile.displayName;
                this.memberInfo.extra = JSON.stringify(extraInfo);
                
                // 设置标记，表示当前用户已有群名片
                this.hasMyProfile = true;
                
                uni.hideLoading();
                uni.showToast({
                    title: '保存成功',
                    icon: 'success'
                });

                // 只退出编辑模式，不退出整个页面
                this.isEdit = false;
                
                // 重新加载数据，确保显示最新保存的内容
                setTimeout(() => {
                    console.log('开始重新加载数据');
                    this.loadExistingProfile();
                    this.loadOtherMembersProfiles();
                }, 1500); // 延长到1.5秒，确保服务器数据更新
            } catch (err) {
                console.error('保存群名片失败:', err);
                uni.hideLoading();
                uni.showToast({
                    title: '保存失败，请重试',
                    icon: 'none'
                });
            } finally {
                this.isSaving = false;
            }
        },
        onCardSwiperChange(e) {
            this.currentCardIndex = e.detail.current;
        },
        closeUserCardsRoller() {
            this.showUserCardsRoller = false;
        },
        onAddCard() {
            uni.navigateTo({
                //带着群id跳转页面
                url: `/pages/card/EditCard?groupId=${this.groupId}`
            });
        },
        onDeleteCard(card) {
            // 实现删除逻辑
            // TODO: 删除名片逻辑
            console.log('点击删除名片:', card);
            uni.showModal({
                title: '提示',
                content: '确定要删除这张名片吗？',
                success: async (res) => {
                if (res.confirm) {
                    try {
                    // 调用删除名片接口
                    const result = await appServerApi.deleteMyCard(card.id);
                    console.log('删除名片结果:', result);

                    // 根据接口返回判断是否成功，这里假设成功code为200
                    if (result && result.code === 200) {
                        uni.showToast({
                        title: '删除成功',
                        icon: 'success'
                        });
                        // 删除成功后刷新列表
                        this.fetchMyCards();
                    } else {
                        // 删除失败，显示错误信息
                        uni.showToast({
                        title: result?.msg || '删除失败', // 使用可选链操作符更安全
                        icon: 'none'
                        });
                    }
                    } catch (error) {
                    console.error('删除名片接口调用失败:', error);
                    uni.showToast({
                        title: '删除失败，请重试',
                        icon: 'none'
                    });
                    }
                }
                }
            });
        },
        onEditCard(card) {
            // 实现编辑逻辑
            uni.navigateTo({
                url: '/pages/card/EditCard?card=' + encodeURIComponent(JSON.stringify(card))
        });
        },
        onShowCard(card) {
            // 实现展示逻辑
            // TODO: 立即展示名片逻辑
            console.log('立即展示名片:', card);
            
            // 检查是否有 groupId
            if (!this.groupId) {
                uni.showToast({ title: '当前不在群聊中，无法立即展示', icon: 'none' });
                return;
            }

            // 调用设置名片到群组的API
            appServerApi.setCardIntoGroup(card.id, this.groupId)
                .then(result => {
                    console.log('设置名片到群组成功:', result);
                    uni.showToast({ title: '已在群内展示该名片', icon: 'success' });

                    // 设置成功后，返回群名片展示页并刷新
                    // 使用 uni.$emit 触发事件通知 GroupProfile 页面刷新
                    uni.$emit('refreshGroupCard');
                    // uni.navigateBack({ delta: 1 }); // 返回上一页
                    this.closeUserCardsRoller();
                })
                .catch(error => {
                    console.error('设置名片到群组失败:', error);
                    uni.showToast({ title: error.message || '设置失败，请重试', icon: 'none' });
                });
        },
        goToCardDetails(card) {
            // 跳转到名片详情页，传递完整名片数据（原始card对象）
            const cardStr = encodeURIComponent(JSON.stringify(card));
            uni.navigateTo({
                url: `/pages/card/CardDetails?card=${cardStr}`
            });
        },
        
        beforeDestroy() {
            uni.$off('refreshGroupCard'); // 页面销毁时解绑
        },
        fetchCardData() {
            // 拉取群名片数据的逻辑
        },
        goToMyCard() {
            // 跳转到我的名片页面，并传递群组ID和用户名片ID（如果有）
            let url = `/pages/card/MyCard?groupId=${this.groupId}`;
            if (this.hasMyProfile && this.profile && this.profile.id) {
                url += `&groupCardId=${this.profile.id}`;
            }
            uni.navigateTo({
                url: url
            });
        },
        dismissShowCardModal() {
            this.showEmptyGroupCardModal = false;
            this.hasDismissedShowCardModal = true;
        }
    }
}
</script>

<style lang="scss" scoped>
/* 定义字体 */
@font-face {
    font-family: "iconfont1";
    src: url('/static/iconfonts/iconfont1.ttf') format('truetype');
}

.group-profile-container {
    min-height: 100vh;
    background-color: #f5f5f5;
    position: relative; /* 添加相对定位 */
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
}

.header-placeholder {
    width: 100%;
}

.scroll-content {
    width: 100%;
    box-sizing: border-box;
    position: relative;
    padding-top: 5px; /* 减小顶部内边距 */
    z-index: 1;
}

.title-container {
    display: flex;
    align-items: center;
    max-width: 220px;
}

.title {
    font-size: 18px;
    color: #333;
    font-weight: 500;
    margin-right: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
}

.iconfont {
    font-family: "iconfont1";
    font-size: 20px;
    color: #666;
}

.search-input-container {
    flex: 1;
    height: 36px;
    background-color: #f5f5f5;
    border-radius: 18px;
    display: flex;
    align-items: center;
    padding: 0 12px;
    margin-right: 5px;
    border: 1px solid #e6e6e6;
    max-width: 180px;
}

.search-input {
    flex: 1;
    height: 36px;
    font-size: 14px;
    width: 100%;
}

.search-results {
    background-color: #ffffff;
    padding: 0 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.search-item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.search-item:last-child {
    border-bottom: none;
}

.search-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 12px;
    background-color: #f0f0f0;
}

.search-user-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.search-user-name {
    font-size: 16px;
    color: #333;
    margin-bottom: 4px;
}

.search-user-title {
    font-size: 14px;
    color: #666;
}

.no-results {
    display: flex;
    justify-content: center;
    padding: 20px 0;
    background-color: #ffffff;
    margin-bottom: 10px;
}

.no-results-text {
    font-size: 14px;
    color: #999;
}

.search-results-end {
    text-align: center;
    padding: 15px 0;
}

.search-results-end-text {
    font-size: 12px;
    color: #999;
}

.right-edit-btn {
    font-size: 15px;
    color: #4E7CF7;
    padding-right: 5px;
    writing-mode: horizontal-tb;
    text-orientation: mixed;
    white-space: nowrap;
    display: inline-block;
}

.profile-list {
    padding: 5px 0;
}

.profile-card {
    margin: 10px;
    background-color: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e6e6e6;
    // padding: 16px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 10px;
}

.preview-card-box {
    // margin: 16px 16px 0 16px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    padding: 16px;
}

.preview-info-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.preview-avatar {
    width: 42px;
    height: 42px;
    border-radius: 8px;
    background: #e6e6e6;
    margin-right: 5px;
}

.preview-info-main {
    padding-left: 12px; /* 增加左侧内边距 */
    display: flex;
    flex-direction: column;
    flex: 1; /* 使其填充剩余空间 */
    min-width: 0; /* 允许内容收缩 */
}

.preview-info-name {
    font-size: 16px;
    font-weight: bold;
    color: #222;
    padding-bottom: 5px;
}

.preview-info-title {
    font-size: 13px;
    color: #888;
    margin-top: 2px;
}

.preview-section-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.preview-section-bar {
    width: 4px;
    height: 16px;
    background: #fd3c3c;
    border-radius: 2px;
    margin-right: 6px;
}

.preview-section-content {
    font-size: 14px;
    color: #888;
    background: #fff;
    border-radius: 6px;
    min-height: 22px;
    padding: 2px 0 8px 0;
    word-break: break-all;
}

.preview-contact-box {
    margin-top: 16px;
}

.preview-contact-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.preview-contact-label {
    font-size: 14px;
    color: #333;
    margin-right: 8px;
}

.preview-contact-value {
    font-size: 14px;
    color: #666;
}

.profile-content {
    padding: 0px 16px 30px 16px; /* 减小顶部内边距 */
    margin-bottom: 10px; /* 减小底部边距 */
}

.form-item {
    margin-bottom: 20px;
}

.form-label {
    font-size: 15px;
    color: #333;
    margin-bottom: 8px;
    display: block;
}

.form-input {
    width: 100%;
    height: 44px;
    background: #ffffff;
    border-radius: 8px;
    padding: 0 12px;
    font-size: 15px;
    color: #333;
    border: 1px solid #e6e6e6;
}

.form-textarea {
    width: 100%;
    height: 120px;
    background: #ffffff;
    border-radius: 8px;
    padding: 12px;
    font-size: 15px;
    color: #333;
    border: 1px solid #e6e6e6;
}

.form-item-radio {
    margin-bottom: 20px;
}

.radio-group {
    margin-top: 8px;
}

.radio-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.radio-circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid #cccccc;
    margin-right: 8px;
    position: relative;
    
    &.checked {
        border-color: #4E7CF7;
        background-color: #ffffff;
        
        &::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #4E7CF7;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
}

.radio-text {
    font-size: 15px;
    color: #333;
}

/* 缺省页样式 - 修正位置不再基于绝对定位 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 44px - var(--status-bar-height));
    width: 100%;
    background-color: #f5f5f5;
    margin: 0;
    padding: 20px 0;
    padding-top: 0;  /* 移除顶部内边距 */
    margin-top: -50px; /* 向上移动整体内容，调整为更合适的值 */
}

.empty-state-image {
    width: 200px;
    height: 200px;
    margin-bottom: 30px;
}

.empty-state-text {
    font-size: 16px;
    color: #4E7CF7;
    text-align: center;
}

.char-count {
    font-size: 12px;
    color: #999;
    text-align: right;
    margin-top: 8px;
}

/* 新增样式 */
.flex-align {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
}
.preview-section-bar {
    flex-shrink: 0;
    width: 4px;
    height: 16px;
    background: #fd3c3c;
    border-radius: 2px;
    margin-right: 6px;
    margin-top: 4px;
}
.preview-section-label {
    flex-shrink: 0;
    font-size: 14px;
    color: #222;
    font-weight: bold;
    // margin-right: 0px;
    margin-top: 2px;
    min-width: 40px;
    text-align: left;
    // font-size: 14px;
}
.preview-section-content-block {
    flex: 1;
    min-width: 0;
    word-break: break-all;
}
.preview-section-content-inline {
    font-size: 14px;
    color: #888;
    background: #fff;
    border-radius: 6px;
    min-height: 22px;
    padding: 0 3px;
    display: -webkit-box;
    -webkit-line-clamp: 2;           /* 最多显示2行 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-line;
    line-height: 1.7;
    word-break: break-all;
    max-height: calc(1.7em * 2);     /* 限制最大高度为2行 */
}

.empty-group-card-modal-mask {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.15);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.role-badge {
    margin-left: 8px;
    // background-color: #e7edff; /* 淡蓝色背景 */
    // color: #256dff; /* 蓝色文字 */
    // font-size: 12px;
    // padding: 2px 6px;
    // border-radius: 4px;
    font-weight: normal;
    margin-bottom: 5px;

    padding: 2px 8px;
    background: #007AFF;
    border-radius: 8px;
    font-size: 12px;
    color: #fff;
}

.empty-group-card-modal-box {
    background: #fff;
    border-radius: 10px;
    // border: 1.5px solid #4E7CF7;
    box-shadow: 0 2px 16px rgba(0,0,0,0.08);
    padding: 28px 18px 18px 18px;
    min-width: 280px;
    max-width: 90vw;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.empty-group-card-modal-content {
    font-size: 16px;
    color: #222;
    margin-bottom: 28px;
    margin-left: 20px;
    margin-right: 20px;
    text-align: center;
    line-height: 1.6;
}

.empty-group-card-modal-actions {
    display: flex;
    width: 100%;
    justify-content: space-between;
}

.empty-modal-btn {
    flex: 1;
    height: 38px;
    margin: 0 8px;
    border-radius: 6px;
    font-size: 18px;
    font-weight: 500;
    border: none;
    outline: none;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 38px;
}

.empty-modal-btn.cancel {
    background: #e0e0e0;
    color: #aaa;
}

.empty-modal-btn.confirm {
    background: #4E7CF7;
    color: #fff;
}

/* 新增样式 for role badge */
.name-row {
    display: flex;
    align-items: center;
}

// .role-badge {
//     margin-left: 8px;
//     background-color: #e7edff; /* 淡蓝色背景 */
//     color: #256dff; /* 蓝色文字 */
//     font-size: 12px;
//     padding: 2px 6px;
//     border-radius: 4px;
//     font-weight: normal;
//     margin-bottom: 5px;
// }

.avatar-image {
    width: 42px;
    height: 42px;
    border-radius: 8px;
    background: #e6e6e6;
    margin-right: 5px;
}
</style> 