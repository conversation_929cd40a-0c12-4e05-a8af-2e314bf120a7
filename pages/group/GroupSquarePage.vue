<template>
  <view class="group-square-container">
    <CustomHeader title="群广场" color="#000">
      <template v-slot:right>
        <view class="op-btns">
          <image class="op-btns-icon" @click="toggleSearch" src="../../assets/images/bar/search.png" mode="aspectFit"></image>
        </view>
      </template>
    </CustomHeader>

    <!-- 搜索框 -->
    <view class="search-container" v-if="showSearch">
      <view class="search-box">
        <image class="search-icon" src="../../assets/images/bar/search.png"></image>
        <input 
          class="search-input" 
          type="text" 
          v-model="searchKeyword" 
          placeholder="搜索群名称" 
          @input="handleSearchInput" 
          confirm-type="search"
          @confirm="handleSearch"
        />
        <image 
          v-if="searchKeyword.length > 0" 
          class="clear-icon" 
          src="../../assets/images/bar/closesearch.png" 
          @click="clearSearch"
        ></image>
      </view>
    </view>

    <!-- 主内容区域 - 两栏布局 -->
    <view class="main-content">
      <!-- 左侧分类导航 -->
      <view class="category-sidebar">
        <scroll-view scroll-y class="category-scroll">
          <view 
            v-for="(item, index) in categories" 
            :key="index" 
            class="category-item" 
            :class="{ active: currentCategory === index }"
            @click="handleCategoryClick(index)"
          >
            {{ item.name }}
          </view>
        </scroll-view>
      </view>

      <!-- 右侧群列表 -->
      <view class="group-list-container">
        <scroll-view 
          class="group-list-scroll" 
          scroll-y 
          @scrolltolower="loadMoreGroups"
          refresher-enabled
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onRefresh"
        >
          <view class="group-list">
            <view 
              v-for="(group, index) in groupList" 
              :key="index" 
              class="group-item"
              @click="handleGroupClick(group)"
            >
              <view class="group-avatar">
                <view v-if="group.price" class="price-badge">
                  <text class="badge-text">付费</text>
                </view>
                <image :src="group.avatar" mode="aspectFill" class="avatar"></image>
              </view>
              <view class="group-info">
                <view class="info-title">
                  <text class="group-name">{{ group.name }}</text>
                  <text class="tag" v-if="group.categoryName && group.categoryName.trim()">{{ group.categoryName }}</text>
                </view>
                <text v-if="false" class="group-desc">{{ group.description || "该群主很懒，什么都没留下" }}</text>
              </view>
              <view class="group-status">
                <button v-if="group.joined == 0" class="join-button" @click.stop="handleJoin(group)">加入</button>
                <view v-else disabled class="has-button">已加入</view>
              </view>
            </view>
          </view>
          
          <!-- 加载状态 -->
          <view v-if="isLoading" class="loading-text">加载中...</view>
          <view v-if="!isLoading && !hasMore && groupList.length > 0" class="loading-text">没有更多了</view>
          <view v-if="!isLoading && groupList.length === 0" class="empty-state">
            <image src="/static/image/icon/empty.png" mode="aspectFit"></image>
            <text>暂无数据</text>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 群详情弹窗 -->
    <uni-popup ref="popup" borderRadius="10px 10px 0 0" background-color="#fff">
      <view class="popup-content">
        <view class="top-line"></view>
        <view class="group-avatar">
          <view v-if="selectedGroup.price" class="price-badge">
            <text class="badge-text">付费</text>
          </view>
          <image :src="selectedGroup.avatar" class="group-avatar-img" />
        </view>
        <view class="pop-title">
          <text class="group-name">{{ selectedGroup.name }}</text>
          <text class="tag" v-if="selectedGroup.categoryName && selectedGroup.categoryName.trim()">{{ selectedGroup.categoryName }}</text>
        </view>
        <view class="pop-desc">
          <view class="desc-line">
            <view class="desc-label">群介绍</view>
            <view class="desc-content">{{ selectedGroup.description || "该群主很懒，什么都没留下" }}</view>
          </view>
          <view class="desc-line">
            <view class="desc-label">群主</view>
            <view class="desc-content">
              <image :src="creatorInfo.portrait" class="avatar-small" />
              <view class="desc-name">{{ selectedGroup.CreatorName }}</view>
            </view>
            <view class="header-right" @click="checkCreatorHome">查看主页<i class="icon-ion-ios-arrow-right"></i></view>
          </view>
        </view>

        <view class="op-block">
          <view v-if="selectedGroup.price > 0" class="price-header">
            <text class="validity">有效期：{{ getValidityPeriod(selectedGroup.duration) }}</text>
            <text class="daily-price">低至 {{ (selectedGroup.price / getDurationDays(selectedGroup.duration)).toFixed(3) }} 元/天</text>
          </view>
          <button v-if="selectedGroup.price > 0" type="primary" @click="handlePayJoin(selectedGroup)">
            立即加入 ¥{{ selectedGroup.price }}
          </button>
          <button v-else type="primary" @click="joinGroup(selectedGroup)">
            立即加入
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import appServerApi from '@/api/appServerApi'
import wfc from '@/wfc/client/wfc'
import { getItem } from '@/pages/util/storageHelper'
import store from '@/store'
import Conversation from '@/wfc/model/conversation'
import ConversationType from '@/wfc/model/conversationType'

export default {
  name: 'GroupSquarePage',
  components: {
    CustomHeader
  },
  data() {
    return {
      showSearch: false,
      searchKeyword: '',
      categories: [
        { name: '全部', id: '' }
      ],
      currentCategory: 0,
      groupList: [],
      pageNo: 1,
      pageSize: 20,
      hasMore: true,
      isLoading: false,
      isRefreshing: false,
      selectedGroup: {},
      creatorInfo: {}
    }
  },
  onLoad() {
    // 获取所有群分类
    this.getAllGroupCategories()
    // 默认加载全部群
    this.fetchGroupList()
  },
  methods: {
    // 获取所有群分类
    getAllGroupCategories() {
      appServerApi
        .getAllGroupCategories()
        .then((response) => {
          console.log('getAllGroupCategories', response)
          var arr1 = [
            {
              name: '全部',
              id: '',
            }
          ]
          var arr2 = response.data || []
          this.categories = [...arr1, ...arr2]
        })
        .catch((error) => {
          console.error('获取群分类失败:', error)
        })
    },
    
    // 切换搜索框显示状态
    toggleSearch() {
      this.showSearch = !this.showSearch
      if (!this.showSearch) {
        this.searchKeyword = ''
        this.fetchGroupList()
      }
    },
    
    // 清除搜索内容
    clearSearch() {
      this.searchKeyword = ''
      this.fetchGroupList()
    },
    
    // 处理搜索输入
    handleSearchInput() {
      if (this.searchKeyword.trim() === '') {
        this.fetchGroupList()
      }
    },
    
    // 处理搜索确认
    handleSearch() {
      this.pageNo = 1
      this.fetchGroupList()
    },
    
    // 处理分类点击
    handleCategoryClick(index) {
      if (this.currentCategory === index) return
      
      // 更新当前选中的分类
      this.currentCategory = index
      
      // 重置页码和列表数据
      this.pageNo = 1
      this.groupList = []
      
      // 显示加载中状态
      this.isLoading = true
      
      // 获取新分类的数据
      this.fetchGroupList()
      
      // 滚动到顶部
      this.$nextTick(() => {
        const scrollView = uni.createSelectorQuery().in(this).select('.group-list-scroll')
        if (scrollView) {
          scrollView.node && scrollView.node.scrollTo({ top: 0 })
        }
      })
    },
    
    // 获取群列表
    async fetchGroupList(loadMore = false) {
      if (this.isLoading && loadMore) return
      
      this.isLoading = true
      
      try {
        // 如果不是加载更多，重置页码
        if (!loadMore) {
          this.pageNo = 1
          this.groupList = []
        }
        
        // 获取当前分类ID
        const categoryId = this.categories[this.currentCategory].id
        
        // 调用API获取群列表
        const params = {
          categoryId: categoryId == 0 ? '' : categoryId,
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          keyword: this.searchKeyword || ''
        }
        
        console.log('获取群列表参数:', params)
        
        const response = await appServerApi.searchGroups(params)
        console.log('获取群列表响应:', response)
        
        if (response?.data?.result) {
          let newGroups = response.data.result
          
          // // 对群组进行排序，已加入的排在后面
          // newGroups.sort((a, b) => {
          //   if (a.joined === b.joined) return 0
          //   return a.joined === 0 ? -1 : 1
          // })
          
          // if (loadMore) {
          //   // 加载更多时，过滤掉已存在的群组，避免重复
          //   const existingGids = this.groupList.map(group => group.gid)
          //   newGroups = newGroups.filter(group => !existingGids.includes(group.gid))
            
            // 追加新数据
            this.groupList = [...this.groupList, ...newGroups]
          // } else {
          //   // 首次加载或刷新时，替换数据
          //   this.groupList = newGroups
          // }
          
          // 判断是否还有更多数据
          this.hasMore = newGroups.length >= this.pageSize
          
          // 如果有数据，页码加1
          if (newGroups.length > 0) {
            this.pageNo++
          }
        } else {
          this.hasMore = false
          if (!loadMore && (!this.groupList || this.groupList.length === 0)) {
            console.log('没有获取到群列表数据')
          }
        }
      } catch (error) {
        console.error('获取群列表失败:', error)
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        })
        this.hasMore = false
      } finally {
        this.isLoading = false
        this.isRefreshing = false
      }
    },
    
    // 加载更多群
    loadMoreGroups() {
      if (this.hasMore && !this.isLoading) {
        this.fetchGroupList(true)
      }
    },
    
    // 下拉刷新
    onRefresh() {
      this.isRefreshing = true
      this.pageNo = 1
      this.fetchGroupList()
    },
    
    // 处理群点击
    handleGroupClick(group) {
      console.log('current group', group)
      if (group.joined == 0) {
        this.handleJoin(group)
        return
      }
      
      // 跳转到群聊天页面
      let conversation = new Conversation(
        ConversationType.Group,
        group.gid,
        0
      )
      store.setCurrentConversation(conversation)
      this.$go2ConversationPage()
    },
    
    // 处理加入群
    async handleJoin(group) {
      try {
        // 获取群组详情
        const detailResponse = await appServerApi.getGroupDetail(group.gid);
        console.log('群组详情:', detailResponse.data);
        
        this.selectedGroup = {
          ...group,
          duration: detailResponse.data?.duration || 30  // 使用接口返回的 duration，如果没有则默认 30
        }
        
        // 使用本地WFC获取最新的群信息，确保群主信息是最新的
        const localGroupInfo = wfc.getGroupInfo(group.gid, true); // refresh设为true强制刷新
        const actualOwner = localGroupInfo.owner || group.creator; // 使用最新的群主信息，如果没有则回退到原始数据
        
        // 注意：这里显示的是当前群主，不是创建者
        this.selectedGroup.CreatorName = wfc.getUserDisplayName(actualOwner)
        // 获取当前群主的完整信息
        this.creatorInfo = wfc.getUserInfo(actualOwner, true)
        // 保存当前群主信息用于显示，但不修改原始creator字段
        this.selectedGroup.currentOwner = actualOwner
        
        console.log('群主信息更新:', {
          originalCreator: group.creator,
          currentOwner: actualOwner,
          ownerName: this.selectedGroup.CreatorName
        });
        
        this.$refs.popup.open('bottom')
      } catch (error) {
        console.error('获取群组详情失败:', error);
        uni.showToast({
          title: '获取群组信息失败',
          icon: 'none'
        });
      }
    },
    
    // 加入群
    async joinGroup(group) {
      let userId = getItem('userId')
      console.log('准备加入群聊:', userId, group)
      
      try {
        // 获取群详情，判断是否需要审核
        const detailResponse = await appServerApi.getGroupDetail(group.gid);
        console.log('群详情audit状态:', detailResponse.data?.audit);
        
        const needAudit = detailResponse.data?.audit === 1; // 1表示需要审核，0表示不需要审核
        
        if (needAudit) {
          // 需要审核，使用申请进群API
          console.log('群开启审核，提交申请')
          const result = await appServerApi.requestJoinGroup(group.gid, '申请加入群聊')
          
          if (result && result.code === 200) {
            console.log('申请提交成功')
            uni.showToast({
              icon: 'success', 
              title: '申请已提交，等待审核',
              duration: 2000,
            })
            this.$refs.popup.close('bottom')
          } else {
            const errorMsg = result?.msg || '申请失败'
            console.error('申请进群失败:', errorMsg)
            uni.showToast({
              title: errorMsg,
              icon: 'none',
              duration: 2000
            })
            // 如果是业务错误（如重复申请），也要关闭弹窗
            this.$refs.popup.close('bottom')
          }
        } else {
          // 不需要审核，直接加入群聊
          console.log('群未开启审核，直接加入')
          this.directJoinGroup(group)
        }
      } catch (error) {
        console.error('加入群聊出错:', error)
        
        // 检查是否是业务错误（后端返回的错误信息）
        if (error && typeof error === 'string') {
          // 如果error是字符串，说明是业务错误，直接显示错误信息
          uni.showToast({
            title: error,
            icon: 'none',
            duration: 2000
          })
          this.$refs.popup.close('bottom')
          return
        }
        
        // 只有在真正的网络错误或接口异常时，才回退到申请加群的方式
        console.log('获取群详情失败，回退到申请加群方式')
        try {
          const result = await appServerApi.requestJoinGroup(group.gid, '申请加入群聊')
          
          if (result && result.code === 200) {
            console.log('申请提交成功')
            uni.showToast({
              icon: 'success', 
              title: '申请已提交，等待审核',
              duration: 2000,
            })
            this.$refs.popup.close('bottom')
          } else {
            const errorMsg = result?.msg || '申请失败'
            console.error('申请进群失败:', errorMsg)
            uni.showToast({
              title: errorMsg,
              icon: 'none',
              duration: 2000
            })
            this.$refs.popup.close('bottom')
          }
        } catch (fallbackError) {
          console.error('申请加群也失败:', fallbackError)
          
          // 检查是否是业务错误
          if (fallbackError && typeof fallbackError === 'string') {
            uni.showToast({
              title: fallbackError,
              icon: 'none',
              duration: 2000
            })
          } else {
            uni.showToast({
              title: '网络错误，请稍后重试',
              icon: 'none'
            })
          }
          this.$refs.popup.close('bottom')
        }
      }
    },

    // 直接加入群聊的方法（作为备用方案）
    directJoinGroup(group) {
      let userId = getItem('userId')
      console.log('直接加入群聊:', userId, group)
      
      wfc.addGroupMembers(
        group.gid,
        [userId],
        null,
        [0],
        null,
        () => {
          console.log('直接加入群聊成功')
          uni.showToast({
            icon: 'success',
            title: '加入成功',
            duration: 1000,
          })
          this.$refs.popup.close('bottom')
          this.fetchGroupList()
        },
        (err) => {
          uni.showModal({
            title: '提示',
            content: '加入失败，请联系管理员',
            confirmText: '确定',
            showCancel: false,
            success: (res) => {
              // 处理确认后的操作
            }
          })
          console.log('直接加入群聊失败', err)
        }
      )
    },
    
    // 处理付费加入
    handlePayJoin(group) {
      uni.showToast({
        title: '付费功能暂未实现',
        icon: 'none'
      })
    },
    
    // 查看创建者主页
    checkCreatorHome() {
      // 跳转到当前群主的主页，而不是创建者
      const targetUserId = this.selectedGroup.currentOwner || this.selectedGroup.creator;
      uni.navigateTo({
        url: '/pages/contact/UserDetailPage?userId=' + targetUserId,
      })
    },

    getValidityPeriod(duration) {
      if (!duration) return '永久有效';
      switch (parseInt(duration, 10)) {
        case 30:
          return '一个月';
        case 90:
          return '三个月';
        case 180:
          return '半年';
        case 365:
          return '一年';
        default:
          return '一个月';
      }
    },

    getDurationDays(duration) {
      if (!duration) return 365;
      return parseInt(duration, 10);
    }
  }
}
</script>

<style lang="scss" scoped>
.group-square-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.op-btns {
  display: flex;
  align-items: center;
}

.op-btns-icon {
  width: 20px;
  height: 20px;
}

/* 搜索框样式 */
.search-container {
  padding: 10px 15px;
  background-color: #fff;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 0 15px;
  height: 36px;
}

.search-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.search-input {
  flex: 1;
  height: 36px;
  font-size: 14px;
  background-color: transparent;
}

.clear-icon {
  width: 16px;
  height: 16px;
}

/* 主内容区域 - 两栏布局 */
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧分类导航 */
.category-sidebar {
  width: 80px;
  background-color: #f8f8f8;
  border-right: 1px solid #eee;
}

.category-scroll {
  height: 100%;
}

.category-item {
  padding: 15px 0;
  text-align: center;
  font-size: 14px;
  color: #666;
  position: relative;
}

.category-item.active {
  color: #386BF6;
  font-weight: bold;
  background-color: #fff;
}

.category-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background-color: #386BF6;
  border-radius: 0 1.5px 1.5px 0;
}

/* 右侧群列表 */
.group-list-container {
  flex: 1;
  overflow: hidden;
}

.group-list-scroll {
  height: 100%;
}

.group-list {
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
}

.group-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  margin-bottom: 10px;
  padding: 12px 10px;
  border-radius: 10px;
  width: 100%;
  box-sizing: border-box;
  justify-content: space-between;
}

.group-avatar {
  min-width: 50px;
  width: 50px;
  height: 50px;
  border-radius: 6px;
  overflow: hidden;
  margin-right: 10px;
  position: relative;
  background: #f9fafc;
  box-shadow: inset 0px 0px 4px 0px rgba(23, 104, 139, 0.12);
  flex-shrink: 0;
  
  .price-badge {
    position: absolute;
    top: -12px;
    left: -30px;
    width: 80px;
    height: 24px;
    background-color: #FF2222;
    transform: rotate(-45deg);
    transform-origin: center;
    z-index: 1;
    
    .badge-text {
      position: absolute;
      width: 100%;
      text-align: center;
      color: #FFFFFF;
      font-size: 10px;
      bottom: 1px;
      left: -7px;
      transform: translateY(2px);
    }
  }
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.group-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 4px 0;
  overflow: hidden;
  min-width: 0;
}

.info-title {
  display: flex;
  align-items: center;
  width: 100%;
}

.group-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  max-width: 75%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.group-desc {
  font-size: 12px;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

.group-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 50px;
  width: 50px;
  flex-shrink: 0;
}

.tag {
  background: #0cc86b;
  border-radius: 2px;
  color: #fff;
  font-size: 10px;
  margin-left: 5px;
  padding: 2px;
}

.join-button {
  min-width: 50px;
  width: 50px;
  height: 22px;
  font-size: 12px;
  line-height: 22px;
  background: #ffffff;
  border-radius: 5px;
  border: 1px solid #386bf6;
  color: #386bf6;
  padding: 0 5px;
  white-space: nowrap;
  text-align: center;
}

.has-button {
  min-width: 50px;
  width: 50px;
  height: 22px;
  font-size: 12px;
  line-height: 22px;
  border-radius: 5px;
  border: none;
  color: #666;
  text-align: center;
  white-space: nowrap;
}

/* 加载状态 */
.loading-text {
  text-align: center;
  padding: 15px;
  color: #999;
  font-size: 14px;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.empty-state image {
  width: 100px;
  height: 100px;
  margin-bottom: 10px;
}

.empty-state text {
  font-size: 14px;
  color: #999;
}

/* 弹窗样式 */
.popup-content {
  display: flex;
  flex-direction: column;
  padding: 23px;
  width: 100%;
  height: auto;
  align-items: center;

  .top-line {
    width: 38px;
    height: 4px;
    margin: 0 auto 17px auto;
    border-radius: 10px;
    background: #e7e7e7;
  }

  .group-avatar-img {
  width: 60px;
  height: 60px;
  border-radius: 6px;
 }

  .pop-title {
    padding-top: 18px;
  }

  .pop-desc {
    border-top: 1px solid #efefef;
    padding: 18px 0;
    margin-top: 20px;
    margin-bottom: 20px;
    width: 100%;

    .desc-line {
      display: flex;
      position: relative;
      padding-bottom: 20px;
      align-items: center;

      &:last-child {
        padding-bottom: 0;
      }

      .desc-label {
        height: 15px;
        font-size: 11px;
        color: #000000;
        line-height: 15px;
        text-align: left;
        padding: 2px 0;
        margin-right: 10px;
        width: 44px;
        flex-shrink: 0;
      }

      .desc-content {
        font-size: 14px;
        color: #999999;
        display: flex;
        align-items: center;
        padding-left: 0;
        flex: 1;
      }

      .desc-name {
        color: #000000;
      }

      .header-right {
        position: absolute;
        right: 0;
        font-size: 14px;
        color: #999999;
        line-height: 16px;

        i {
          margin-left: 10px;
        }
      }

      .avatar-small {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        border-radius: 4px;
      }
    }
  }

  .op-block {
    margin-bottom: 20px;
    width: 100%;

    .price-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0;
      margin-bottom: 8px;
      
      .validity {
        font-size: 8px;
        color: #666666;
        font-weight: 400;
      }
      
      .daily-price {
        font-size: 8px;
        color: #386bf6;
        font-weight: 500;
      }
    }
    
    button[type="primary"] {
      width: 100%;
      height: 46px;
      background: #386bf6;
      border-radius: 4px;
      color: #fff;
      font-size: 16px;
      border: none;
      margin-top: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: 1;
      padding: 0;
      
      &:active {
        opacity: 0.9;
      }
    }
  }
}
</style> 