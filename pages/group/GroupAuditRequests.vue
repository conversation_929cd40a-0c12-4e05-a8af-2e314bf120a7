<template>
    <div class="group-audit-requests">
        <CustomHeader>
            <template v-slot:title>
                <text class="title">进群申请</text>
            </template>
        </CustomHeader>
        
        <div class="audit-content">
            <!-- 加载状态 -->
            <view v-if="loading" class="loading-container">
                <text class="loading-text">加载中...</text>
            </view>
            
            <!-- 空状态 -->
            <view v-else-if="requestList.length === 0" class="empty-container">
                <text class="empty-text">暂无待审批申请</text>
            </view>
            
            <!-- 申请列表 -->
            <view v-else class="request-list">
                <view v-for="request in requestList" :key="request.id" class="request-item">
                    <view class="user-info">
                        <image class="avatar" :src="request.userAvatar || '/static/default-avatar.png'" mode="aspectFill" />
                        <view class="user-details">
                            <text class="username">{{ request.userName || '未知用户' }}</text>
                            <text class="group-name">申请加入：{{ request.groupName || '未知群聊' }}</text>
                            <text class="apply-reason">申请理由：{{ request.reason || '无' }}</text>
                            <text class="apply-time">申请时间：{{ formatTime(request.createTime) }}</text>
                        </view>
                    </view>
                    
                    <view class="action-buttons">
                        <button class="reject-btn" @click="handleAudit(request.id, '2', '')">拒绝</button>
                        <button class="approve-btn" @click="handleAudit(request.id, '1', '')">通过</button>
                    </view>
                </view>
            </view>
        </div>
    </div>
</template>

<script>
import appServerApi from '../../api/appServerApi'
import CustomHeader from '../../components/custom-header'
import wfc from '../../wfc/client/wfc'

export default {
    name: 'GroupAuditRequests',
    data() {
        return {
            loading: true,
            requestList: [],
            groupId: ''
        }
    },
    
    onLoad(option) {
        console.log('GroupAuditRequests onLoad', option)
        this.groupId = option.groupId || ''
        this.loadAuditRequests()
    },
    
    onShow() {
        // 每次页面显示时刷新数据
        this.loadAuditRequests()
    },
    
    methods: {
        // 加载待审批请求列表
        async loadAuditRequests() {
            try {
                this.loading = true
                console.log('正在加载待审批请求...')
                
                const result = await appServerApi.getGroupAuditRequestList()
                
                if (result && result.code === 200) {
                    const rawList = result.data || []
                    console.log('待审批请求原始数据:', rawList)
                    
                    // 打印第一个申请的详细字段信息，便于调试
                    if (rawList.length > 0) {
                        console.log('第一个申请的字段:', Object.keys(rawList[0]))
                        console.log('第一个申请的完整数据:', rawList[0])
                    }
                    
                    // 处理数据，确保字段名正确
                    const processedList = []
                    
                    for (const request of rawList) {
                        // 基础字段映射
                        const processedRequest = {
                            id: request.id,
                            reason: request.reason || request.content || request.message || '申请加入群聊',
                            createTime: request.createTime || request.createDate || request.timestamp,
                            // 用户信息字段可能的名称
                            userName: request.userName || request.nickname || request.displayName || request.name || request.applicantName,
                            userAvatar: request.userAvatar || request.avatar || request.headImg || request.profilePhoto,
                            userId: request.userId || request.uid || request.applicantId,
                            // 群组信息字段可能的名称
                            groupId: request.groupId || request.gid || request.targetGroupId,
                            groupName: request.groupName || request.groupTitle || request.targetGroupName,
                            // 保留原始数据便于调试
                            ...request
                        }
                        
                        // 如果缺少用户名或头像，尝试通过userId获取
                        if ((!processedRequest.userName || !processedRequest.userAvatar) && processedRequest.userId) {
                            try {
                                console.log('尝试获取用户信息:', processedRequest.userId)
                                
                                // 优先从wfc获取用户信息
                                let userInfo = null
                                try {
                                    userInfo = wfc.getUserInfo(processedRequest.userId)
                                    console.log('从wfc获取到用户信息:', userInfo)
                                } catch (wfcError) {
                                    console.warn('wfc获取用户信息失败:', wfcError)
                                }
                                
                                // 如果wfc没有获取到，尝试API
                                if (!userInfo || (!userInfo.displayName && !userInfo.portrait)) {
                                    try {
                                        const apiUserInfo = await appServerApi.getOtherUserInfo(processedRequest.userId)
                                        if (apiUserInfo) {
                                            console.log('从API获取到用户信息:', apiUserInfo)
                                            userInfo = apiUserInfo
                                        }
                                    } catch (apiError) {
                                        console.warn('API获取用户信息失败:', apiError)
                                    }
                                }
                                
                                if (userInfo) {
                                    processedRequest.userName = processedRequest.userName || 
                                        userInfo.displayName || userInfo.nickname || userInfo.name || '未知用户'
                                    processedRequest.userAvatar = processedRequest.userAvatar || 
                                        userInfo.portrait || userInfo.avatar || userInfo.headImg || '/static/default-avatar.png'
                                }
                            } catch (error) {
                                console.warn('获取用户信息失败:', error)
                            }
                        }
                        
                        // 如果缺少群组名称，尝试通过groupId获取
                        if (!processedRequest.groupName && processedRequest.groupId) {
                            try {
                                console.log('尝试获取群组信息:', processedRequest.groupId)
                                
                                // 从wfc获取群组信息
                                const groupInfo = wfc.getGroupInfo(processedRequest.groupId)
                                if (groupInfo && groupInfo.name) {
                                    processedRequest.groupName = groupInfo.name
                                    console.log('从wfc获取到群组名称:', groupInfo.name)
                                }
                            } catch (error) {
                                console.warn('获取群组信息失败:', error)
                            }
                        }
                        
                        // 确保有默认值
                        processedRequest.userName = processedRequest.userName || '未知用户'
                        processedRequest.userAvatar = processedRequest.userAvatar || '/static/default-avatar.png'
                        processedRequest.groupName = processedRequest.groupName || '未知群聊'
                        
                        console.log('处理后的申请数据:', processedRequest)
                        processedList.push(processedRequest)
                    }
                    
                    this.requestList = processedList
                    console.log('待审批请求加载成功:', this.requestList.length, '条')
                } else {
                    console.warn('待审批请求加载失败:', result?.msg)
                    this.requestList = []
                    uni.showToast({
                        title: result?.msg || '加载失败',
                        icon: 'none'
                    })
                }
            } catch (error) {
                console.error('加载待审批请求出错:', error)
                this.requestList = []
                uni.showToast({
                    title: '网络错误，请稍后重试',
                    icon: 'none'
                })
            } finally {
                this.loading = false
            }
        },
        
        // 处理审批操作
        async handleAudit(requestId, status, remark = '') {
            try {
                console.log('处理审批:', { requestId, status, remark })
                
                // 如果是拒绝操作，可以弹出输入框让用户填写拒绝理由
                if (status === '2' && !remark) {
                    const res = await uni.showModal({
                        title: '拒绝申请',
                        content: '是否要填写拒绝理由？',
                        showCancel: true,
                        confirmText: '填写理由',
                        cancelText: '直接拒绝'
                    })
                    
                    if (res.confirm) {
                        // 用户选择填写理由，这里可以扩展为输入框
                        remark = '群主拒绝了您的申请'
                    }
                }
                
                const result = await appServerApi.auditJoinGroupRequest(requestId, status, remark)
                
                if (result && result.code === 200) {
                    const actionText = status === '1' ? '通过' : '拒绝'
                    console.log(`审批${actionText}成功`)
                    
                    uni.showToast({
                        title: `已${actionText}申请`,
                        icon: 'success'
                    })
                    
                    // 刷新列表
                    this.loadAuditRequests()
                    
                    // 通知父页面更新红点状态
                    uni.$emit('refreshGroupListBadge')
                } else {
                    const errorMsg = result?.msg || '操作失败'
                    console.error('审批操作失败:', errorMsg)
                    uni.showToast({
                        title: errorMsg,
                        icon: 'none'
                    })
                }
            } catch (error) {
                console.error('审批操作出错:', error)
                uni.showToast({
                    title: '网络错误，请稍后重试',
                    icon: 'none'
                })
            }
        },
        
        // 格式化时间
        formatTime(timestamp) {
            if (!timestamp) return '未知时间'
            
            const date = new Date(timestamp)
            const now = new Date()
            const diff = now - date
            
            // 如果是今天
            if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
                return date.toLocaleTimeString('zh-CN', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                })
            }
            
            // 如果是昨天
            const yesterday = new Date(now)
            yesterday.setDate(yesterday.getDate() - 1)
            if (date.getDate() === yesterday.getDate() && 
                date.getMonth() === yesterday.getMonth() && 
                date.getFullYear() === yesterday.getFullYear()) {
                return '昨天 ' + date.toLocaleTimeString('zh-CN', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                })
            }
            
            // 其他时间显示完整的年月日和星期
            const year = date.getFullYear()
            const month = date.getMonth() + 1
            const day = date.getDate()
            const hours = date.getHours().toString().padStart(2, '0')
            const minutes = date.getMinutes().toString().padStart(2, '0')
            
            // 获取星期几
            const weekdays = ['日', '一', '二', '三', '四', '五', '六']
            const weekday = weekdays[date.getDay()]
            
            return `${year}年${month}月${day}日 星期${weekday} ${hours}:${minutes}`
        }
    },
    
    components: {
        CustomHeader
    }
}
</script>

<style lang="scss" scoped>
.group-audit-requests {
    height: 100vh;
    overflow: hidden;
    background-color: #f7f7f7;
    
    .audit-content {
        height: calc(100vh - var(--status-bar-height));
        overflow: auto;
        padding: 12px 15px;
        
        .loading-container, .empty-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 300px;
            
            .loading-text, .empty-text {
                font-size: 16px;
                color: #999;
            }
        }
        
        .request-list {
            .request-item {
                background-color: #ffffff;
                padding: 16px;
                margin-bottom: 12px;
                border-radius: 12px;
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
                
                .user-info {
                    display: flex;
                    align-items: flex-start;
                    margin-bottom: 16px;
                    
                    .avatar {
                        width: 50px;
                        height: 50px;
                        border-radius: 25px;
                        margin-right: 12px;
                        background-color: #f0f0f0;
                    }
                    
                    .user-details {
                        flex: 1;
                        
                        .username {
                            display: block;
                            font-size: 16px;
                            font-weight: 500;
                            color: #333;
                            margin-bottom: 4px;
                        }
                        
                        .group-name {
                            display: block;
                            font-size: 14px;
                            color: #4168e0;
                            margin-bottom: 4px;
                            font-weight: 500;
                        }
                        
                        .apply-reason {
                            display: block;
                            font-size: 14px;
                            color: #666;
                            margin-bottom: 4px;
                            line-height: 1.4;
                        }
                        
                        .apply-time {
                            display: block;
                            font-size: 12px;
                            color: #999;
                        }
                    }
                }
                
                .action-buttons {
                    display: flex;
                    justify-content: flex-end;
                    gap: 12px;
                    
                    button {
                        padding: 8px 20px;
                        border-radius: 20px;
                        font-size: 14px;
                        border: none;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        
                        &.reject-btn {
                            background-color: #f5f5f5;
                            color: #666;
                            
                            &:active {
                                background-color: #e8e8e8;
                            }
                        }
                        
                        &.approve-btn {
                            background-color: #4168e0;
                            color: #ffffff;
                            
                            &:active {
                                background-color: #3557c5;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style> 