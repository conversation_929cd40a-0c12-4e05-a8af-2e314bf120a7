<template>
    <view class="group-detail-container">
        <CustomHeader>
            <template v-slot:title>
                <text class="title">群介绍</text>
            </template>
        </CustomHeader>
        
        <view v-if="loading" class="loading-container">
            <text class="loading-text">加载中...</text>
        </view>
        
        <view v-else-if="error" class="error-container">
            <text class="error-text">{{ error }}</text>
            <button class="retry-button" @click="fetchGroupDetail">重试</button>
        </view>
        
        <view v-else class="group-info-container">
            <view class="group-header">
                <view class="group-avatar">
                    <view v-if="groupDetail.price" class="price-badge">
                        <text class="badge-text">付费</text>
                    </view>
                    <image :src="groupDetail.avatar" class="group-avatar-img" />
                </view>
                <view class="group-basic-info">
                    <view class="info-title">
                        <text class="group-name">{{ groupDetail.name }}</text>
                        <text v-if="groupDetail.categoryName" class="tag">{{ groupDetail.categoryName }}</text>
                    </view>
                    <!-- <text class="group-id">群号：{{ groupId }}</text> -->
                    <!-- <text class="group-member-count">成员：{{ groupDetail.memberCount || 0 }}人</text> -->
                </view>
            </view>
            
            <view class="creator-info">
                <text class="creator-title">创建者</text>
                <view class="creator-content">
                    <image :src="creatorInfo.portrait" class="creator-avatar" />
                    <text class="creator-name">{{ groupDetail.CreatorName }}</text>
                    <view class="view-profile" @click="checkCreatorHome">
                        <text class="view-profile-text">查看主页</text>
                        <text class="arrow-icon">></text>
                    </view>
                </view>
            </view>
            
            <view class="group-description">
                <text class="description-title">群介绍  </text>
                <view class="description-content-wrapper">
                    <view v-for="(block, index) in descriptionBlocks" :key="index" class="block-wrapper">
                        <!-- 文本块 -->
                        <text v-if="block.type === 'text'" class="description-content">{{ block.content }}</text>
                        
                        <!-- 图片块 -->
                        <view v-else-if="block.type === 'image'" class="image-wrapper">
                            <image :src="block.content" mode="widthFix" class="description-image" @click="previewImage([block.content], 0)" />
                        </view>
                    </view>
                    
                    <!-- 无内容提示 -->
                    <view v-if="descriptionBlocks.length === 0" class="no-content">
                        <text class="description-content">该群主很懒，什么都没留下</text>
                    </view>
                </view>
            </view>
        </view>
        
        <!-- 固定在底部的加入/进入按钮 -->
        <view v-if="!loading && !error" class="fixed-bottom-bar">
            <!-- 添加价格信息到底部固定区域 -->
            <view v-if="groupDetail.price > 0" class="bottom-price-info">
                <view class="price-left">
                    <text class="validity">有效期：{{groupDetail.duration/30}}个月</text>
                    <text class="daily-price">低至 {{ (groupDetail.price / groupDetail.duration).toFixed(3) }} 元/天</text>
                </view>
            </view>
            
            <button 
                v-if="!isCurrentUserGroupMember" 
                class="join-button" 
                :class="{'pay-button': groupDetail.price > 0}"
                @click="handleJoin"
            >
                {{ groupDetail.price > 0 ? '立即加入 ¥' + groupDetail.price : '立即加入' }}
            </button>
            <button v-else class="join-button" @click="enterGroupChat">进入聊天</button>
        </view>
        
        <payment-selector 
            ref="paymentselector" 
            v-model:visible="paymentVisible" 
            :amount="payAmount" 
            :balance="userBalance" 
            @close="handlePaymentClose" 
            @confirm="handlePaymentConfirm" 
        />
        
        <ios-payment-selector 
            v-model:visible="iosPaymentVisible" 
            @close="handleIosPaymentClose" 
            @confirm="handleIosPaymentConfirm" 
        />
    </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import PaymentSelector from '@/components/payment/PaymentSelector2'
import IosPaymentSelector from '@/components/payment/IosPaymentSelector'
import appServerApi from '@/api/appServerApi'
import wfc from '@/wfc/client/wfc'
import { getItem } from '@/pages/util/storageHelper'
import store from '@/store'
import Conversation from '@/wfc/model/conversation'
import ConversationType from '@/wfc/model/conversationType'
import util from '@/utils/util'

export default {
    name: 'GroupDetailPage',
    components: {
        CustomHeader,
        PaymentSelector,
        IosPaymentSelector
    },
    data() {
        return {
            groupId: '',
            groupDetail: {},
            creatorInfo: {},
            loading: true,
            error: null,
            paymentVisible: false,
            iosPaymentVisible: false,
            payAmount: '0',
            userBalance: '0',
            isCurrentUserGroupMember: false,
            descriptionBlocks: [] // 存储解析后的群介绍内容块
        }
    },
    onLoad(options) {
        if (options.groupId) {
            this.groupId = options.groupId;
            console.log('加载群组详情，群ID:', this.groupId);
            
            // 获取群组信息
            this.fetchGroupDetail();
            
            // 检查当前用户是否是群成员
            this.checkGroupMemberStatus();
        } else {
            this.loading = false;
            this.error = '未找到群组信息';
        }
    },
    created() {
        // 监听充值事件
        uni.$on('triggerRecharge', this.handleRecharge);
    },
    beforeDestroy() {
        // 移除充值事件监听
        uni.$off('triggerRecharge', this.handleRecharge);
    },
    methods: {
        // 检查当前用户是否是群成员
        checkGroupMemberStatus() {
            try {
                const userId = getItem('userId');
                console.log('检查用户是否是群成员:', userId, this.groupId);
                
                // 获取群成员列表
                const groupMembers = wfc.getGroupMembers(this.groupId);
                console.log('群成员列表:', groupMembers);
                
                // 检查当前用户是否在群成员列表中
                this.isCurrentUserGroupMember = groupMembers.some(member => member.memberId === userId);
                console.log('用户是否是群成员:', this.isCurrentUserGroupMember);
            } catch (error) {
                console.error('检查群成员状态失败:', error);
                this.isCurrentUserGroupMember = false;
            }
        },
        
        async fetchGroupDetail() {
            this.loading = true;
            this.error = null;
            
            try {
                // 获取群组详情
                const response = await appServerApi.getGroupDetail(this.groupId);
                console.log('群组详情:', response);
                
                if (response && response.data) {
                    this.groupDetail = response.data;
                    
                    // 确保使用API返回的categoryName字段
                    console.log('群组分类:', this.groupDetail.categoryName);
                    
                    // 获取创建者信息 - 使用本地WFC获取最新的群主信息
                    if (this.groupDetail.creator) {
                        // 使用本地WFC获取最新的群信息，确保群主信息是最新的
                        const localGroupInfo = wfc.getGroupInfo(this.groupId, true); // refresh设为true强制刷新
                        const actualOwner = localGroupInfo.owner || this.groupDetail.creator; // 使用最新的群主信息，如果没有则回退到原始数据
                        
                        // 注意：这里显示的是当前群主，不是创建者
                        this.groupDetail.CreatorName = wfc.getUserDisplayName(actualOwner);
                        this.creatorInfo = wfc.getUserInfo(actualOwner, true);
                        // 保存当前群主信息用于显示，但不修改原始creator字段
                        this.groupDetail.currentOwner = actualOwner;
                        
                        console.log('群主信息更新:', {
                            originalCreator: response.data.creator,
                            currentOwner: actualOwner,
                            ownerName: this.groupDetail.CreatorName
                        });
                    }
                    
                    // 解析群介绍内容，处理可能包含的图片
                    this.parseGroupDescription(this.groupDetail.description || "");
                    
                    // 再次检查群成员状态
                    this.checkGroupMemberStatus();
                    
                    // 获取钱包余额信息（如果是付费群）
                    if (this.groupDetail.price > 0) {
                        try {
                            const walletResponse = await appServerApi.getWallet();
                            if (walletResponse && walletResponse.data) {
                                this.userBalance = walletResponse.data.balance || '0';
                            }
                        } catch (error) {
                            console.error('获取钱包信息失败:', error);
                        }
                    }
                    
                    // 如果已经是群成员，直接进入聊天
                    if (this.isCurrentUserGroupMember) {
                        console.log('用户已是群成员，准备进入聊天');
                    }
                } else {
                    this.error = '获取群组信息失败';
                }
            } catch (error) {
                console.error('获取群组详情失败:', error);
                this.error = '获取群组信息失败，请稍后重试';
            } finally {
                this.loading = false;
            }
        },
        
        // 解析群介绍中的文本和图片
        parseGroupDescription(description) {
            if (!description) {
                this.descriptionBlocks = [];
                return;
            }
            
            const blocks = [];
            let lastIndex = 0;
            
            // 匹配图片标记：[图片:URL]
            const imgRegex = /\[图片:(.*?)\]/g;
            let match;
            
            while ((match = imgRegex.exec(description)) !== null) {
                // 添加图片前的文本
                const textBefore = description.substring(lastIndex, match.index);
                if (textBefore) {
                    blocks.push({ type: 'text', content: textBefore });
                }
                
                // 添加图片
                blocks.push({ type: 'image', content: match[1] });
                
                lastIndex = match.index + match[0].length;
            }
            
            // 添加最后一段文本（如果有）
            const textAfter = description.substring(lastIndex);
            if (textAfter) {
                blocks.push({ type: 'text', content: textAfter });
            }
            
            this.descriptionBlocks = blocks;
        },
        
        // 预览图片
        previewImage(urls, current) {
            uni.previewImage({
                urls: urls,
                current: current,
                indicator: 'number',
                loop: true
            });
        },
        
        // 查看创建者主页
        checkCreatorHome() {
            // 跳转到当前群主的主页，而不是创建者
            const targetUserId = this.groupDetail.currentOwner || this.groupDetail.creator;
            if (targetUserId) {
                uni.navigateTo({
                    url: '/pages/contact/UserDetailPage?userId=' + targetUserId,
                });
            }
        },
        
        // 进入群聊聊天页面
        enterGroupChat() {
            let conversation = new Conversation(
                ConversationType.Group,
                this.groupId,
                0
            );
            store.setCurrentConversation(conversation);
            uni.redirectTo({
                url: '/pages/conversation/ConversationPage',
            });
        },
        
        // 处理加入群组
        handleJoin() {
            if (this.groupDetail.price > 0) {
                // 付费群组
                this.payAmount = this.groupDetail.price;
                this.$refs.paymentselector.init();
                this.paymentVisible = true;
            } else {
                // 免费群组
                this.joinGroup();
            }
        },
        
        // 加入群组
        async joinGroup() {
            let userId = getItem('userId');
            console.log('准备加入群组:', userId, this.groupDetail);
            
            try {
                // 获取群详情，判断是否需要审核
                const detailResponse = await appServerApi.getGroupDetail(this.groupId);
                console.log('群详情audit状态:', detailResponse.data?.audit);
                
                const needAudit = detailResponse.data?.audit === 1; // 1表示需要审核，0表示不需要审核
                
                if (needAudit) {
                    // 需要审核，使用申请进群API
                    console.log('群开启审核，提交申请')
                    const result = await appServerApi.requestJoinGroup(this.groupId, '申请加入群聊')
                    
                    if (result && result.code === 200) {
                        console.log('申请提交成功')
                        uni.showToast({
                            icon: 'success', 
                            title: '申请已提交，等待审核',
                            duration: 2000,
                        })
                    } else {
                        const errorMsg = result?.msg || '申请失败'
                        console.error('申请进群失败:', errorMsg)
                        uni.showToast({
                            title: errorMsg,
                            icon: 'none',
                            duration: 2000
                        })
                    }
                } else {
                    // 不需要审核，直接加入群聊
                    console.log('群未开启审核，直接加入')
                    this.directJoinGroup()
                }
            } catch (error) {
                console.error('加入群聊出错:', error)
                
                // 检查是否是业务错误（后端返回的错误信息）
                if (error && typeof error === 'string') {
                    // 如果error是字符串，说明是业务错误，直接显示错误信息
                    uni.showToast({
                        title: error,
                        icon: 'none',
                        duration: 2000
                    })
                    return
                }
                
                // 只有在真正的网络错误或接口异常时，才回退到申请加群的方式
                console.log('获取群详情失败，回退到申请加群方式')
                try {
                    const result = await appServerApi.requestJoinGroup(this.groupId, '申请加入群聊')
                    
                    if (result && result.code === 200) {
                        console.log('申请提交成功')
                        uni.showToast({
                            icon: 'success', 
                            title: '申请已提交，等待审核',
                            duration: 2000,
                        })
                    } else {
                        const errorMsg = result?.msg || '申请失败'
                        console.error('申请进群失败:', errorMsg)
                        uni.showToast({
                            title: errorMsg,
                            icon: 'none',
                            duration: 2000
                        })
                    }
                } catch (fallbackError) {
                    console.error('申请加群也失败:', fallbackError)
                    
                    // 检查是否是业务错误
                    if (fallbackError && typeof fallbackError === 'string') {
                        uni.showToast({
                            title: fallbackError,
                            icon: 'none',
                            duration: 2000
                        })
                    } else {
                        uni.showToast({
                            title: '网络错误，请稍后重试',
                            icon: 'none'
                        })
                    }
                }
            }
        },

        // 直接加入群聊的方法（作为备用方案）
        directJoinGroup() {
            let userId = getItem('userId');
            console.log('直接加入群组:', userId, this.groupDetail);
            
            wfc.addGroupMembers(
                this.groupId,
                [userId],
                null,
                [0],
                null,
                () => {
                    console.log('直接加入群组成功');
                    uni.showToast({
                        icon: 'success',
                        title: '加入成功',
                        duration: 1000,
                    });
                    
                    // 更新状态
                    this.isCurrentUserGroupMember = true;
                    
                    // 可以选择跳转到会话页面
                    setTimeout(() => {
                        let conversation = new Conversation(
                            ConversationType.Group,
                            this.groupId,
                            0
                        );
                        store.setCurrentConversation(conversation);
                        uni.navigateBack();
                        setTimeout(() => {
                            uni.navigateTo({
                                url: '/pages/conversation/ConversationPage',
                            });
                        }, 500);
                    }, 1000);
                },
                (err) => {
                    uni.showModal({
                        title: '提示',
                        content: '加入失败，请联系管理员',
                        confirmText: '确定',
                        showCancel: false,
                        success: (res) => {
                            // 处理确认后的操作
                        }
                    });
                    console.log('直接加入群组失败:', err);
                }
            );
        },
        
        // 支付弹窗关闭
        handlePaymentClose() {
            console.log('支付弹窗关闭');
        },
        
        // 支付确认
        async handlePaymentConfirm({ method, amount, password, coinAmount }) {
            var that = this;
            try {
                console.log('支付确认', method, amount, coinAmount);
                switch (method) {
                    case 'wechat':
                        const response = await appServerApi.joinPayGroup({
                            gid: that.groupId,
                            payType: 2,
                        });
                        util.wxpay(
                            response.data,
                            function (res) {
                                console.log('支付成功', res);
                                that.groupDetail.joined = 1;
                                that.paymentVisible = false;
                                uni.showToast({
                                    icon: 'success',
                                    title: '加入成功',
                                    duration: 1000,
                                });
                                
                                // 可以选择跳转到会话页面
                                setTimeout(() => {
                                    let conversation = new Conversation(
                                        ConversationType.Group,
                                        that.groupId,
                                        0
                                    );
                                    store.setCurrentConversation(conversation);
                                    uni.navigateBack();
                                    setTimeout(() => {
                                        uni.navigateTo({
                                            url: '/pages/conversation/ConversationPage',
                                        });
                                    }, 500);
                                }, 1000);
                            },
                            function (res) {
                                console.log('支付失败', res);
                                uni.showToast({
                                    title: '支付失败',
                                    icon: 'none',
                                });
                            }
                        );
                        break;
                    case 'alipay':
                        const response2 = await appServerApi.joinPayGroup({
                            gid: that.groupId,
                            payType: 1,
                        });
                        util.alipay(
                            response2.data,
                            function (res) {
                                console.log('支付成功', res);
                                that.groupDetail.joined = 1;
                                that.paymentVisible = false;
                                uni.showToast({
                                    icon: 'success',
                                    title: '加入成功',
                                    duration: 1000,
                                });
                                
                                // 可以选择跳转到会话页面
                                setTimeout(() => {
                                    let conversation = new Conversation(
                                        ConversationType.Group,
                                        that.groupId,
                                        0
                                    );
                                    store.setCurrentConversation(conversation);
                                    uni.navigateBack();
                                    setTimeout(() => {
                                        uni.navigateTo({
                                            url: '/pages/conversation/ConversationPage',
                                        });
                                    }, 500);
                                }, 1000);
                            },
                            function (res) {
                                console.log('支付失败', res);
                                uni.showToast({
                                    title: '支付失败',
                                    icon: 'none',
                                });
                            }
                        );
                        break;
                    case 'balance':
                        uni.showLoading({
                            title: '正在校验密码',
                        });
                        const response3 = await appServerApi.joinPayGroup({
                            gid: that.groupId,
                            payType: 0,
                        });
                        uni.hideLoading();
                        uni.showToast({
                            icon: 'success',
                            title: '加入成功',
                            duration: 1000,
                        });
                        
                        // 更新状态并跳转
                        that.groupDetail.joined = 1;
                        that.paymentVisible = false;
                        
                        // 可以选择跳转到会话页面
                        setTimeout(() => {
                            let conversation = new Conversation(
                                ConversationType.Group,
                                that.groupId,
                                0
                            );
                            store.setCurrentConversation(conversation);
                            uni.navigateBack();
                            setTimeout(() => {
                                uni.navigateTo({
                                    url: '/pages/conversation/ConversationPage',
                                });
                            }, 1000);
                        }, 1000);
                        break;
                    case 'coin':
                        uni.showLoading({
                            title: '处理中',
                        });
                        const response4 = await appServerApi.joinPayGroup({
                            gid: that.groupId,
                            payType: 5,
                            coinAmount: coinAmount // 传递金币数量到接口
                        });
                        uni.hideLoading();
                        uni.showToast({
                            icon: 'success',
                            title: '加入成功',
                            duration: 1000,
                        });
                        
                        // 更新状态并跳转
                        that.groupDetail.joined = 1;
                        that.paymentVisible = false;
                        
                        // 可以选择跳转到会话页面
                        setTimeout(() => {
                            let conversation = new Conversation(
                                ConversationType.Group,
                                that.groupId,
                                0
                            );
                            store.setCurrentConversation(conversation);
                            uni.navigateBack();
                            setTimeout(() => {
                                uni.navigateTo({
                                    url: '/pages/conversation/ConversationPage',
                                });
                            }, 500);
                        }, 1000);
                        break;
                    default:
                        break;
                }
            } catch (error) {
                console.error('支付处理失败:', error);
                uni.showToast({
                    title: '支付失败',
                    icon: 'none',
                });
            }
        },
        
        // 生成群二维码
        generateGroupQRCode() {
            // 使用新的URL格式生成二维码内容
            const qrCodeContent = `http://admin.ykjrhl.com/?GroupID=${this.groupId}`;
            
            // 复制链接到剪贴板
            uni.setClipboardData({
                data: qrCodeContent,
                success: () => {
                    // 显示弹窗提示
                    uni.showModal({
                        title: '群邀请链接',
                        content: '群邀请链接已复制到剪贴板。扫描此链接的二维码可直接加入该群。',
                        showCancel: false,
                        confirmText: '确定'
                    });
                }
            });
        },
        
        // 处理充值
        handleRecharge() {
            // 根据平台选择支付方式
            if (uni.getSystemInfoSync().platform === 'ios') {
                this.iosPaymentVisible = true;
            } else {
                this.paymentVisible = true;
            }
        },
        
        // 处理iOS支付
        handleIosPaymentClose() {
            console.log('iOS支付弹窗关闭');
        },
        
        // 处理iOS支付确认
        async handleIosPaymentConfirm({ method, amount, password, coinAmount }) {
            var that = this;
            try {
                console.log('iOS支付确认', method, amount, coinAmount);
                switch (method) {
                    case 'wechat':
                        const response = await appServerApi.joinPayGroup({
                            gid: that.groupId,
                            payType: 2,
                        });
                        util.wxpay(
                            response.data,
                            function (res) {
                                console.log('iOS支付成功', res);
                                that.groupDetail.joined = 1;
                                that.iosPaymentVisible = false;
                                uni.showToast({
                                    icon: 'success',
                                    title: '加入成功',
                                    duration: 1000,
                                });
                                
                                // 可以选择跳转到会话页面
                                setTimeout(() => {
                                    let conversation = new Conversation(
                                        ConversationType.Group,
                                        that.groupId,
                                        0
                                    );
                                    store.setCurrentConversation(conversation);
                                    uni.navigateBack();
                                    setTimeout(() => {
                                        uni.navigateTo({
                                            url: '/pages/conversation/ConversationPage',
                                        });
                                    }, 500);
                                }, 1000);
                            },
                            function (res) {
                                console.log('iOS支付失败', res);
                                uni.showToast({
                                    title: 'iOS支付失败',
                                    icon: 'none',
                                });
                            }
                        );
                        break;
                    case 'alipay':
                        const response2 = await appServerApi.joinPayGroup({
                            gid: that.groupId,
                            payType: 1,
                        });
                        util.alipay(
                            response2.data,
                            function (res) {
                                console.log('iOS支付成功', res);
                                that.groupDetail.joined = 1;
                                that.iosPaymentVisible = false;
                                uni.showToast({
                                    icon: 'success',
                                    title: '加入成功',
                                    duration: 1000,
                                });
                                
                                // 可以选择跳转到会话页面
                                setTimeout(() => {
                                    let conversation = new Conversation(
                                        ConversationType.Group,
                                        that.groupId,
                                        0
                                    );
                                    store.setCurrentConversation(conversation);
                                    uni.navigateBack();
                                    setTimeout(() => {
                                        uni.navigateTo({
                                            url: '/pages/conversation/ConversationPage',
                                        });
                                    }, 500);
                                }, 1000);
                            },
                            function (res) {
                                console.log('iOS支付失败', res);
                                uni.showToast({
                                    title: 'iOS支付失败',
                                    icon: 'none',
                                });
                            }
                        );
                        break;
                    case 'balance':
                        uni.showLoading({
                            title: '正在校验密码',
                        });
                        const response3 = await appServerApi.joinPayGroup({
                            gid: that.groupId,
                            payType: 0,
                        });
                        uni.hideLoading();
                        uni.showToast({
                            icon: 'success',
                            title: '加入成功',
                            duration: 1000,
                        });
                        
                        // 更新状态并跳转
                        that.groupDetail.joined = 1;
                        that.iosPaymentVisible = false;
                        
                        // 可以选择跳转到会话页面
                        setTimeout(() => {
                            let conversation = new Conversation(
                                ConversationType.Group,
                                that.groupId,
                                0
                            );
                            store.setCurrentConversation(conversation);
                            uni.navigateBack();
                            setTimeout(() => {
                                uni.navigateTo({
                                    url: '/pages/conversation/ConversationPage',
                                });
                            }, 1000);
                        }, 1000);
                        break;
                    case 'coin':
                        uni.showLoading({
                            title: '处理中',
                        });
                        const response4 = await appServerApi.joinPayGroup({
                            gid: that.groupId,
                            payType: 5,
                            coinAmount: coinAmount // 传递金币数量到接口
                        });
                        uni.hideLoading();
                        uni.showToast({
                            icon: 'success',
                            title: '加入成功',
                            duration: 1000,
                        });
                        
                        // 更新状态并跳转
                        that.groupDetail.joined = 1;
                        that.iosPaymentVisible = false;
                        
                        // 可以选择跳转到会话页面
                        setTimeout(() => {
                            let conversation = new Conversation(
                                ConversationType.Group,
                                that.groupId,
                                0
                            );
                            store.setCurrentConversation(conversation);
                            uni.navigateBack();
                            setTimeout(() => {
                                uni.navigateTo({
                                    url: '/pages/conversation/ConversationPage',
                                });
                            }, 500);
                        }, 1000);
                        break;
                    default:
                        break;
                }
            } catch (error) {
                console.error('iOS支付处理失败:', error);
                uni.showToast({
                    title: 'iOS支付失败',
                    icon: 'none',
                });
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.group-detail-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f5f5f5;
}

.loading-container, .error-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.loading-text, .error-text {
    font-size: 16px;
    color: #666;
}

.retry-button {
    margin-top: 20px;
    padding: 8px 20px;
    background-color: #386BF6;
    color: #fff;
    border-radius: 4px;
    font-size: 14px;
}

.group-info-container {
    flex: 1;
    padding: 15px;
    display: flex;
    flex-direction: column;
    margin-bottom: 76px; /* 为底部固定按钮留出空间 */
    overflow-y: auto; /* 允许内容滚动 */
}

.group-header {
    display: flex;
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 15px;
}

.group-avatar {
    width: 70px;
    height: 70px;
    position: relative;
    margin-right: 15px;
    overflow: hidden;
    
    .price-badge {
        position: absolute;
        top: -12px;
        left: -30px;
        width: 80px;
        height: 24px;
        background-color: #FF2222;
        transform: rotate(-45deg);
        transform-origin: center;
        z-index: 1;
        border-radius: 12px;
        
        .badge-text {
            position: absolute;
            width: 100%;
            text-align: center;
            color: #FFFFFF;
            font-size: 10px;
            bottom: 1px;
            left: -7px;
            transform: translateY(2px);
        }
    }
}

.group-avatar-img {
    width: 70px;
    height: 70px;
    border-radius: 8px;
}

.group-basic-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.info-title {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.group-name {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-right: 8px;
}

.tag {
    display: inline-block;
    background-color: #0cc86b;
    color: #fff;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 2px;
    height: 18px;
    line-height: 14px;
}

.entrepreneurship, .school, .tech, .biz {
    display: none;
}

.group-id, .group-member-count {
    font-size: 14px;
    color: #666;
    margin-top: 4px;
}

.group-description, .creator-info {
    background-color: #fff;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
}

.description-title, .creator-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 15px; /* 增加标题和内容之间的间距 */
}

.description-content-wrapper {
    margin-top: 5px;
}

.block-wrapper {
    margin-bottom: 10px;
}

.description-content {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

.image-wrapper {
    margin: 10px 0;
    border-radius: 6px;
    overflow: hidden;
    
    .description-image {
        width: 100%;
        border-radius: 6px;
        background-color: #f5f5f5;
    }
}

.no-content {
    color: #999;
}

.creator-content {
    display: flex;
    align-items: center;
    margin-top: 15px; /* 从内容顶部增加间距 */
}

.creator-avatar {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    margin-right: 10px;
}

.creator-name {
    flex: 1;
    font-size: 14px;
    color: #333;
}

.view-profile {
    display: flex;
    align-items: center;
}

.view-profile-text {
    font-size: 14px;
    color: #666;
}

.arrow-icon {
    margin-left: 5px;
    color: #999;
}

/* 移除原来的join-section样式 */
.join-section {
    display: none;
}

/* 固定在底部的按钮栏 */
.fixed-bottom-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    padding: 15px;
    box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.05);
    z-index: 100;
    border-top: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
}

/* 底部价格信息样式 */
.bottom-price-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.price-left {
    display: flex;
    flex-direction: column;
}

.validity {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.daily-price {
    font-size: 14px;
    color: #386BF6;
    font-weight: 500;
}

.join-button, .joined-button {
    width: 100%;
    height: 46px;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
}

.join-button {
    background-color: #386BF6;
    color: #fff;
}

.pay-button {
    background-color: #FF6B00;
}

.joined-button {
    background-color: #f5f5f5;
    color: #999;
}

.creator-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 30px; /* 特别为创建者标题增加更多间距 */
}

/* 二维码按钮样式 */
.qrcode-button {
    margin-top: 10px;
    height: 40px;
    border-radius: 4px;
    background-color: #f5f5f5;
    color: #333;
    font-size: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style> 