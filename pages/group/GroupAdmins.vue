<template>
    <view class="group-admins">
        <CustomHeader title="群管理员">
            <template v-slot:left>
                <view class="header-left" @click="goBack">
                    <text class="back-icon">&#xe600;</text>
                </view>
            </template>
        </CustomHeader>

        <view class="description-container">
            <view class="description-list">
                <view class="description-item">
                    <text class="bullet">•</text>
                    <text class="description-text">管理员可协助群主管理群聊，拥有发布群公告、移除群成员能力。</text>
                </view>
                <view class="description-item">
                    <text class="bullet">•</text>
                    <text class="description-text">只有群主具备设管理员、解散群聊的能力。</text>
                </view>
                <view class="description-item">
                    <text class="bullet">•</text>
                    <text class="description-text">最多可设置10个管理员。</text>
                </view>
            </view>
        </view>

        <!-- 管理员列表 -->
        <view class="admin-list">
            <view class="admin-item" v-for="admin in admins" :key="admin.userId">
                <view class="admin-info">
                    <image class="avatar" :src="admin.portrait || '/static/images/default-avatar.png'" mode="aspectFill"/>
                    <text class="nickname">{{ admin.displayName }}</text>
                </view>
                <view class="admin-action" @click="removeAdmin(admin)">
                    <text class="remove-text">移除</text>
                </view>
            </view>
        </view>

        <!-- 添加管理员按钮 -->
        <view class="add-admin-btn" @click="goToAddAdmin" v-if="canAddMoreAdmins && isGroupOwner">
            <view class="add-icon-container">
                <text class="add-icon">+</text>
            </view>
            <text class="add-text">增加管理员</text>
        </view>
    </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import wfc from '@/wfc/client/wfc'
import { getItem } from '@/pages/util/storageHelper'

export default {
    name: 'GroupAdmins',
    components: {
        CustomHeader
    },
    data() {
        return {
            groupId: '',
            groupInfo: null,
            admins: [], // 管理员列表
        }
    },
    computed: {
        isGroupOwner() {
            if (!this.groupId || !this.groupInfo) return false
            const currentUserId = getItem('userId')
            return this.groupInfo.owner === currentUserId
        },
        canAddMoreAdmins() {
            return this.admins.length < 10
        }
    },
    onLoad(option) {
        console.log('GroupAdmins onLoad', option.groupId)
        this.groupId = option.groupId
        if (this.groupId) {
            this.groupInfo = wfc.getGroupInfo(this.groupId)
            this.loadAdmins()
        }
    },
    onShow() {
        // 每次页面显示时重新加载管理员列表，确保数据最新
        if (this.groupId) {
            this.loadAdmins()
        }
    },
    methods: {
        loadAdmins() {
            if (!this.groupId) return
            
            // 获取群成员
            const members = wfc.getGroupMembers(this.groupId)
            
            // 筛选出管理员（type = 1）
            const adminMembers = members.filter(member => member.type === 1)
            
            // 获取用户信息
            this.admins = adminMembers.map(member => {
                const userInfo = wfc.getUserInfo(member.memberId)
                return {
                    userId: member.memberId,
                    portrait: userInfo?.portrait || '/static/images/default-avatar.png',
                    displayName: userInfo?.displayName || member.alias || member.nickName || member.memberId
                }
            })
            
            console.log('加载管理员成功，数量:', this.admins.length)
        },
        
        removeAdmin(admin) {
            if (!this.isGroupOwner) {
                uni.showToast({
                    title: '只有群主才能移除管理员',
                    icon: 'none'
                })
                return
            }
            
            uni.showModal({
                title: '移除管理员',
                content: `确定要移除管理员 "${admin.displayName}" 吗？`,
                success: res => {
                    if (res.confirm) {
                        this.doRemoveAdmin(admin.userId)
                    }
                }
            })
        },
        
        doRemoveAdmin(userId) {
            try {
                uni.showLoading({
                    title: '处理中...',
                    mask: true
                })
                
                // 调用 wfc 的取消管理员 API
                wfc.setGroupManager(
                    this.groupId,
                    false, // false表示取消管理员
                    [userId], // 需要是数组
                    [0], // 默认线路
                    null, // 通知内容为null
                    () => {
                        // 成功回调
                        uni.hideLoading()
                        uni.showToast({
                            title: '移除成功',
                            icon: 'success'
                        })
                        
                        // 重新加载管理员列表
                        this.loadAdmins()
                    },
                    (error) => {
                        // 失败回调
                        uni.hideLoading()
                        uni.showToast({
                            title: '移除失败: ' + error,
                            icon: 'none'
                        })
                    }
                )
            } catch (error) {
                uni.hideLoading()
                console.error('移除管理员失败:', error)
                uni.showToast({
                    title: '移除失败，请重试',
                    icon: 'none'
                })
            }
        },
        
        goToAddAdmin() {
            uni.navigateTo({
                url: `./GroupMemberList?groupId=${this.groupId}&roleId=admin&roleName=${encodeURIComponent('设置管理员')}`,
                fail: (err) => {
                    console.error('跳转失败:', err)
                    uni.showToast({
                        title: '页面跳转失败',
                        icon: 'none'
                    })
                }
            })
        },
        
        goBack() {
            uni.navigateBack()
        }
    }
}
</script>

<style lang="scss" scoped>
.group-admins {
    min-height: 100vh;
    background-color: #f7f7f7;
    padding-bottom: 40px;
}

.header-left {
    padding: 10px;
    margin-left: -10px;
}

.back-icon {
    font-family: "iconfont";
    font-size: 20px;
    color: #333;
}

.description-container {
    margin: 12px 15px;
    background-color: #ffffff;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.description-list {
    .description-item {
        display: flex;
        margin-bottom: 10px;
        align-items: flex-start;
        
        &:last-child {
            margin-bottom: 0;
        }
        
        .bullet {
            margin-right: 6px;
            color: #333;
            font-size: 15px;
            line-height: 22px;
        }
        
        .description-text {
            flex: 1;
            font-size: 14px;
            color: #333;
            line-height: 22px;
        }
    }
}

.admin-list {
    margin: 12px 15px;
    background-color: #ffffff;
    border-radius: 12px;
    padding: 4px 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.admin-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
        border-bottom: none;
    }
    
    .admin-info {
        display: flex;
        align-items: center;
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            margin-right: 12px;
            background-color: #f5f5f5;
        }
        
        .nickname {
            font-size: 16px;
            color: #333;
        }
    }
    
    .admin-action {
        .remove-text {
            color: #4168e0;
            font-size: 14px;
            padding: 4px 8px;
        }
    }
}

.add-admin-btn {
    margin: 12px 15px;
    background-color: #fff;
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    
    .add-icon-container {
        width: 40px;
        height: 40px;
        border-radius: 20px;
        background-color: #4168e0;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        
        .add-icon {
            color: #fff;
            font-size: 24px;
            font-weight: bold;
        }
    }
    
    .add-text {
        font-size: 16px;
        color: #333;
    }
}
</style> 