<template>
    <view class="group-square-container">
        <view class="category-tabs-container">
            <view class="tabs-wrapper">
                <scroll-view scroll-x class="tabs-scroll-view" show-scrollbar="false">
                    <view class="tabs-container">
                        <view
                            v-for="(item, index) in tabs"
                            :key="index"
                            class="tab-item"
                            :class="{ active: activeCategory === item.id }"
                            @click="handleTabClick(item)"
                        >
                            {{ item.name }}
                        </view>
                    </view>
                </scroll-view>
                <view class="more-tabs-btn">
                    <text class="more-icon" @click="goToGroupSquare">≡</text>
                </view>
            </view>
        </view>

        <!-- 群列表 -->
        <scroll-view class="group-list" scroll-y @scrolltolower="onScrollToLower">
            <view v-for="(group, index) in groups" :key="index" class="group-item" @click="handleGroupClick(group)">
                <view class="group-avatar">
                    <view v-if="group.price" class="price-badge">
                        <text class="badge-text">付费</text>
                    </view>
                    <image :src="group.avatar" class="group-avatar-img" />
                </view>
                <view class="group-info">
                    <view class="info-title">
                        <text class="group-name">{{ group.name }}</text>
                        <text class="tag" v-if="group.categoryName && group.categoryName.trim()">{{ group.categoryName }}</text>
                    </view>
                    <text v-if="false" class="group-desc">{{ group.description || "该群主很懒，什么都没留下" }}</text>
                </view>
                <view class="group-status">
                    <button v-if="group.joined == 0" class="join-button" @click.stop="handleJoin(group)">加入</button>
                    <view v-else disabled class="has-button">已加入</view>
                </view>
            </view>
            <view v-if="isLoading" class="loading-text">加载中...</view>
            <view v-if="!hasMore && groups.length > 0" class="loading-text">没有更多了</view>
            <view v-if="!hasMore && groups.length === 0" class="loading-text">暂无数据</view>
        </scroll-view>

        <!-- 底部按钮
		<view class="create-group">
			<button class="create-group-button" @click="createGroup">+ 创建群聊</button>
		</view> -->

        <uni-popup ref="popup" borderRadius="10px 10px 0 0" background-color="#fff">
            <view class="popup-content">
                <view class="top-line"></view>
                <view class="group-avatar">
                    <view v-if="selectedGroup.price" class="price-badge">
                        <text class="badge-text">付费</text>
                    </view>
                    <image :src="selectedGroup.avatar" class="group-avatar-img" />
                </view>
                <view class="pop-title">
                    <text class="group-name">{{ selectedGroup.name }}</text>
                    <text class="tag" v-if="selectedGroup.categoryName && selectedGroup.categoryName.trim()">{{ selectedGroup.categoryName }}</text>
                </view>
                <view class="pop-desc">
                    <view class="desc-line" v-if="false">
                        <view class="desc-label">群介绍</view>
                        <view class="desc-content">{{ selectedGroup.description || "该群主很懒，什么都没留下" }}</view>
                    </view>
                    <view class="desc-line">
                        <view class="desc-label">群主</view>
                        <view class="desc-content">
                            <image :src="creatorInfo.portrait" class="avatar-small" />
                            <view class="desc-name">{{ selectedGroup.CreatorName }}</view>
                        </view>
                        <view class="header-right" @click="checkCreatorHome">查看主页<i class="icon-ion-ios-arrow-right"></i></view>
                    </view>
                    <!--
                    <view class="desc-line" v-if="selectedGroup.price > 0">
                        <view class="desc-label">收费</view>
                        <view class="desc-content">
                            <view class="desc-name">¥{{ selectedGroup.price }}/天</view>
                        <view class="header-right" >¥365</view>
                        </view>
                    </view>
                    -->
                </view>

                <view class="op-block">
                    <view v-if="selectedGroup.price > 0" class="price-header">
                        <text class="validity">有效期：{{ getValidityPeriod(selectedGroup.duration) }}</text>
                        <text class="daily-price">低至 {{ (selectedGroup.price / getDurationDays(selectedGroup.duration)).toFixed(3) }} 元/天</text>
                    </view>
                    <button v-if="selectedGroup.price > 0" type="primary" @click="handlePayJoin(selectedGroup)">
                        立即加入 ¥{{ selectedGroup.price }}
                    </button>
                    <button v-else type="primary" @click="joinGroup(selectedGroup)">
                        立即加入
                    </button>
                </view>
            </view>
        </uni-popup>
        <payment-selector ref="paymentselector" v-model:visible="paymentVisible" :amount="payAmount" :balance="userBalance" @close="handlePaymentClose" @confirm="handlePaymentConfirm" />
        <ios-payment-selector v-model:visible="iosPaymentVisible" @close="handleIosPaymentClose" @confirm="handleIosPaymentConfirm" />
    </view>
</template>

<script>
import CustomHeader from '../../components/custom-header'
import PaymentSelector from '@/components/payment/PaymentSelector2'
import IosPaymentSelector from '@/components/payment/IosPaymentSelector'
import appServerApi from '@/api/appServerApi'
import Config from '../../config'
import wfc from '@/wfc/client/wfc'

import { getItem } from '@/pages/util/storageHelper'

import store from '@/store'
// import store from '../../store'
import Conversation from '@/wfc/model/conversation'
import ConversationType from '@/wfc/model/conversationType'
import ConversationInfo from '@/wfc/model/conversationInfo'
import util from '@/utils/util'
export default {
    name: 'GroupMainPage',
    components: {
        CustomHeader,
        PaymentSelector,
        IosPaymentSelector
    },
    data() {
        return {
            tabs: [
                {
                    name: '全部',
                    id: 0,
                },
            ],
            keyword: '',
            isTriggered: false,
            activeCategory: '0',
            groups: [],
            selectedGroup: {},
            creatorInfo: {},
            pageNo: 1,
            pageSize: 20,
            hasMore: true, // 是否还有更多数据
            isLoading: false, // 是否正在加载
            paymentVisible: false,
            iosPaymentVisible: false,
            payAmount: '299.00',
            userBalance: '0',
            originalGroups: null,
            isSearching: false,
            total: 0,
            emptyPages: undefined,
        }
    },
    mounted() {
        this.initData()
        // 监听充值事件
        uni.$on('triggerRecharge', this.handleRecharge)
    },
    beforeDestroy() {
        // 移除充值事件监听
        uni.$off('triggerRecharge', this.handleRecharge)
    },
    watch: {
        '$parent.topCurrent'(newVal) {
            if (newVal === 2) {
                // 2 表示社群 tab
                this.initData()
            }
        },
    },
    methods: {
        // 跳转群消息页面
        handleGroupClick(group) {
            console.log('current group', group)
            if (group.joined == 0) {
                this.handleJoin(group)
                return
            }
            //   let conversation = wfc.getConversationInfo(1, group.gid, 0);
            //   let conv = new Conversation(ConversationType.Group, group.target, 0);
            //             this._reloadConversation(conv, false);
            //   uni.navigateTo({
            //     url: `/pages/conversation/ConversationPage?conversation=${encodeURIComponent(JSON.stringify(conversation))}&name=${encodeURIComponent(group.name)}`,
            //   });
            let conversation = new Conversation(
                ConversationType.Group,
                group.gid,
                0
            )
            store.setCurrentConversation(conversation)
            this.$go2ConversationPage()
        },
        // 查看主页
        checkCreatorHome() {
            // 跳转到当前群主的主页，而不是创建者
            const targetUserId = this.selectedGroup.currentOwner || this.selectedGroup.creator;
            uni.navigateTo({
                url: '/pages/contact/UserDetailPage?userId=' + targetUserId,
            })
        },
        async fetchAllTabs() {
            const res = await appServerApi.getAllGroupCategories()
            if (res) {
                this.tabs = [
                    {
                        name: '全部',
                        id: 0,
                    },
                ]
                res?.data.forEach((v) => {
                    this.tabs.push({
                        name: v.name,
                        value: v.name,
                        id: v.id,
                    })
                })
            }
            // 确保设置默认选中的分类
            this.activeCategory = 0
        },
        handleSearch(event) {
            console.log('handleSearch', event)
            this.keyword = event.detail.value
            this.fetchGroups()
        },
        handleTabClick(item) {
            console.log('点击标签:', item);
            this.activeCategory = item.id
            this.pageNo = 1;
            this.hasMore = true;
            this.fetchGroups()
        },
        // fetchGroups() {
        //   console.log("参数", this.activeCategory, this.keyword);
        //   appServerApi
        //     .searchGroups({
        //       categoryId: this.activeCategory == 0 ? "" : this.activeCategory,
        //       pageNo: "1",
        //       pageSize: "20",
        //       keyword: this.keyword || "",
        //     })
        //     .then((response) => {
        //       if (response.data && response.data) {
        //         this.groups = response.data?.result;
        //       } else {
        //         this.groups = [];
        //         console.warn("API 返回的数据格式不正确:", response);
        //       }
        //     })
        //     .catch((error) => {
        //       console.error("Error fetching groups:", error);
        //       this.groups = [];
        //     });
        // },
        // 修改 fetchGroups 方法
        async fetchGroups(isLoadMore = false) {
            if (this.isLoading) return
            this.isLoading = true

            if (!isLoadMore) {
                this.pageNo = 1
                this.hasMore = true
                this.groups = []
                this.emptyPages = 0
            }
            try{
                const response = await appServerApi.searchGroups({
                        categoryId: this.activeCategory == 0 ? '' : this.activeCategory,
                        pageNo: this.pageNo,
                        pageSize: this.pageSize,
                        keyword: this.keyword || '',
                        joined: 0
                })

                console.log('群广场响应数据:', this.pageNo, response.data)
                console.log('群组数据示例:', response.data?.result?.[0])

                    // 检查是否有结果
                if (response.data && response.data.result && response.data.result.length > 0) {
                        // 保存总数，用于判断是否还有更多数据
                        if (response.data.total) {
                            this.total = response.data.total
                        }

                            // 添加到当前列表
                        this.groups = [...this.groups, ...response.data.result]

                        // 判断是否还有更多数据
                        this.hasMore = this.pageNo * this.pageSize < this.total

                        // 准备加载下一页
                        this.pageNo++

                } else {
                        // 没有更多数据了
                      this.hasMore = false
                }
            } catch (error) {
                console.error('获取群组失败:', error)
                this.hasMore = false
            } finally {
                this.isLoading = false
            }
        },

        // 添加滚动到底部的处理方法
        async onScrollToLower() {
            console.log('滚动到底部，加载更多数据')
            if (!this.hasMore || this.isLoading) {
                console.log('没有更多数据或正在加载中，不再请求')
                return
            }
            await this.fetchGroups(true)
        },
        createGroup() {
            let sharedContactState = store.state.contact
            let users = sharedContactState.favContactList.concat(
                sharedContactState.friendList
            )
            users = users.filter((u) => {
                return u.uid !== Config.FILE_HELPER_ID
            })
            this.$pickUsers({
                users: users,
                successCB: (users) => {
                    store.createConversation(
                        users,
                        (conversation) => {
                            setTimeout(() => {
                                this.$go2ConversationPage()
                            }, 50)
                        },
                        (err) => {
                            console.error('Error creating group:', err)
                        }
                    )
                },
            })
        },
        async handleJoin(group) {
            try {
                // 获取群组详情
                const detailResponse = await appServerApi.getGroupDetail(group.gid);
                console.log('群组详情:', detailResponse.data);

                this.selectedGroup = {
                    ...group,
                    duration: detailResponse.data?.duration || 30  // 使用接口返回的 duration，如果没有则默认 30
                }
                
                // 使用本地WFC获取最新的群信息，确保群主信息是最新的
                try {
                    const localGroupInfo = wfc.getGroupInfo(group.gid, true); // refresh设为true强制刷新
                    const actualOwner = localGroupInfo?.owner || group.creator; // 使用最新的群主信息，如果没有则回退到原始数据
                    
                    if (actualOwner) {
                        // 注意：这里显示的是当前群主，不是创建者
                        const ownerDisplayName = wfc.getUserDisplayName(actualOwner);
                        this.selectedGroup.CreatorName = ownerDisplayName || '未知用户';
                        
                        // 获取当前群主的完整信息
                        const ownerInfo = wfc.getUserInfo(actualOwner, true);
                        this.creatorInfo = ownerInfo || { portrait: '/static/image/icon/150px.png' };
                        
                        // 保存当前群主信息用于显示，但不修改原始creator字段
                        this.selectedGroup.currentOwner = actualOwner;
                        
                        console.log('群主信息设置完成:', {
                            actualOwner,
                            ownerDisplayName,
                            creatorInfo: this.creatorInfo
                        });
                    } else {
                        console.warn('无法获取群主信息，使用默认值');
                        this.selectedGroup.CreatorName = '未知群主';
                        this.creatorInfo = { portrait: '/static/image/icon/150px.png' };
                    }
                } catch (wfcError) {
                    console.error('获取WFC群信息失败:', wfcError);
                    // 使用原始数据作为后备
                    this.selectedGroup.CreatorName = '群主';
                    this.creatorInfo = { portrait: '/static/image/icon/150px.png' };
                }

                this.$refs.popup.open('bottom')
            } catch (error) {
                console.error('获取群组详情失败:', error);
                uni.showToast({
                    title: '获取群组信息失败',
                    icon: 'none'
                });
            }
        },
        // memberIds, extra, notifyLines, notifyMessageContent, successCB, failCB
        async joinGroup(group) {
            let userId = getItem('userId')
            console.log('准备加入群聊:', userId, group)

            try {
                // 获取群详情，判断是否需要审核
                const detailResponse = await appServerApi.getGroupDetail(group.gid);
                console.log('群详情audit状态:', detailResponse.data?.audit);

                const needAudit = detailResponse.data?.audit === 1; // 1表示需要审核，0表示不需要审核

                if (needAudit) {
                    // 需要审核，使用申请进群API
                    console.log('群开启审核，提交申请')
                    const result = await appServerApi.requestJoinGroup(group.gid, '申请加入群聊')

                    if (result && result.code === 200) {
                        console.log('申请提交成功')
                        uni.showToast({
                            icon: 'success',
                            title: '申请已提交，等待审核',
                            duration: 2000,
                        })
                        this.$refs.popup.close('bottom')
                    } else {
                        const errorMsg = result?.msg || '申请失败'
                        console.error('申请进群失败:', errorMsg)
                        uni.showToast({
                            title: errorMsg,
                            icon: 'none',
                            duration: 2000
                        })
                        // 如果是业务错误（如重复申请），也要关闭弹窗
                        this.$refs.popup.close('bottom')
                    }
                } else {
                    // 不需要审核，直接加入群聊
                    console.log('群未开启审核，直接加入')
                    this.directJoinGroup(group)
                }
            } catch (error) {
                console.error('加入群聊出错:', error)

                // 检查是否是业务错误（后端返回的错误信息）
                if (error && typeof error === 'string') {
                    // 如果error是字符串，说明是业务错误，直接显示错误信息
                    uni.showToast({
                        title: error,
                        icon: 'none',
                        duration: 2000
                    })
                    this.$refs.popup.close('bottom')
                    return
                }

                // 只有在真正的网络错误或接口异常时，才回退到申请加群的方式
                console.log('获取群详情失败，回退到申请加群方式')
                try {
                    const result = await appServerApi.requestJoinGroup(group.gid, '申请加入群聊')

                    if (result && result.code === 200) {
                        console.log('申请提交成功')
                        uni.showToast({
                            icon: 'success',
                            title: '申请已提交，等待审核',
                            duration: 2000,
                        })
                        this.$refs.popup.close('bottom')
                    } else {
                        const errorMsg = result?.msg || '申请失败'
                        console.error('申请进群失败:', errorMsg)
                        uni.showToast({
                            title: errorMsg,
                            icon: 'none',
                            duration: 2000
                        })
                        this.$refs.popup.close('bottom')
                    }
                } catch (fallbackError) {
                    console.error('申请加群也失败:', fallbackError)

                    // 检查是否是业务错误
                    if (fallbackError && typeof fallbackError === 'string') {
                        uni.showToast({
                            title: fallbackError,
                            icon: 'none',
                            duration: 2000
                        })
                    } else {
                        uni.showToast({
                            title: '网络错误，请稍后重试',
                            icon: 'none'
                        })
                    }
                    this.$refs.popup.close('bottom')
                }
            }
        },

        // 直接加入群聊的方法（作为备用方案）
        directJoinGroup(group) {
            let userId = getItem('userId')
            console.log('直接加入群聊:', userId, group)

            wfc.addGroupMembers(
                group.gid,
                [userId],
                null,
                [0],
                null,
                () => {
                    console.log('直接加入群聊成功')
                    uni.showToast({
                        icon: 'success',
                        title: '加入成功',
                        duration: 1000,
                    })
                    this.$refs.popup.close('bottom')

                    // 优化：减少等待时间并改进群信息获取逻辑
                    const attemptNavigation = (attempt = 0) => {
                        try {
                            // 尝试获取群信息，最多重试3次
                            const groupInfo = wfc.getGroupInfo(group.gid, true);
                            console.log('获取到的群信息:', groupInfo);
                            
                            if (groupInfo && groupInfo.target) {
                                // 群信息获取成功，立即跳转
                                let conversation = new Conversation(ConversationType.Group, group.gid, 0)
                                let conversationInfo = new ConversationInfo()
                                conversationInfo.conversation = conversation
                                
                                // 确保_displayName字段正确设置
                                if (!groupInfo._displayName && groupInfo.name) {
                                    groupInfo._displayName = groupInfo.name
                                }
                                
                                conversationInfo.conversation._target = groupInfo;
                                this.showConversation(conversationInfo)
                                
                                // 更新群组状态
                                const targetGroup = this.groups.find(g => g.gid === group.gid);
                                if (targetGroup) {
                                    targetGroup.joined = 1;
                                    console.log('更新群组加入状态:', targetGroup.name);
                                }
                                return; // 成功跳转，退出重试
                            }
                            
                            // 群信息还未同步完成，进行重试
                            if (attempt < 3) {
                                console.log(`群信息未同步完成，进行第${attempt + 1}次重试`);
                                setTimeout(() => attemptNavigation(attempt + 1), 300);
                            } else {
                                // 重试3次后仍失败，使用原始数据跳转
                                console.warn('群信息获取失败，使用原始数据跳转');
                                let conversation = new Conversation(ConversationType.Group, group.gid, 0)
                                let conversationInfo = new ConversationInfo()
                                conversationInfo.conversation = conversation
                                
                                // 使用原始群信息作为后备
                                const fallbackGroupInfo = {
                                    target: group.gid,
                                    name: group.name || '群聊',
                                    _displayName: group.name || '群聊',
                                    memberCount: 1
                                };
                                conversationInfo.conversation._target = fallbackGroupInfo;
                                this.showConversation(conversationInfo)
                            }
                        } catch (error) {
                            console.error('处理群信息时出错:', error)
                            // 如果出错，仍然尝试跳转
                            let conversation = new Conversation(ConversationType.Group, group.gid, 0)
                            let conversationInfo = new ConversationInfo()
                            conversationInfo.conversation = conversation
                            this.showConversation(conversationInfo)
                        }
                    };
                    
                    // 立即尝试第一次导航
                    attemptNavigation();
                },
                (err) => {
                    console.error('直接加入群聊失败:', err)
                    uni.showModal({
                        title: '提示',
                        content: '加入失败，请联系管理员',
                        confirmText: '确定',
                        showCancel: false,
                        success: (res) => {
                            // 处理确认后的操作
                        }
                    })
                }
            )
        },

        showConversation(conversationInfo) {
            console.log('准备显示会话:', conversationInfo)
            
            try {
                // 确保conversationInfo和conversation都存在
                if (!conversationInfo || !conversationInfo.conversation) {
                    console.error('conversationInfo 或 conversation 为空')
                    return
                }
                
                const conversation = conversationInfo.conversation
                const groupId = conversation.target
                
                // 尝试获取最新的群信息
                if (groupId) {
                    const latestGroupInfo = wfc.getGroupInfo(groupId, false)
                    if (latestGroupInfo && latestGroupInfo.target) {
                        console.log('使用最新群信息:', latestGroupInfo)
                        
                        // 确保_displayName字段正确设置
                        if (!latestGroupInfo._displayName && latestGroupInfo.name) {
                            latestGroupInfo._displayName = latestGroupInfo.name
                        }
                        
                        conversation._target = latestGroupInfo
                        conversationInfo.conversation = conversation
                    }
                }
                
                store.setCurrentConversationInfo(conversationInfo)
                this.$go2ConversationPage()
            } catch (error) {
                console.error('显示会话时出错:', error)
                // 即使出错也尝试跳转，避免用户卡住
                if (conversationInfo && conversationInfo.conversation) {
                    store.setCurrentConversationInfo(conversationInfo)
                    this.$go2ConversationPage()
                }
            }
        },

        handlePayJoin(group) {
            console.log(group)
            this.payAmount = group.price
            // this.$refs.paymentselector.init()
            this.paymentVisible = true
        },
        handlePaymentClose() {
            console.log('支付弹窗关闭')
            // 确保重置支付选择器状态
            this.$refs.paymentselector.resetState()
            this.$refs.popup.close('bottom')
        },
        handleIosPaymentClose() {
            console.log('iOS支付弹窗关闭')
            this.$refs.popup.close('bottom')
        },

        // 处理充值
        handleRecharge() {
            // 根据平台选择支付方式
            if (uni.getSystemInfoSync().platform === 'ios') {
                this.iosPaymentVisible = true
            } else {
                this.paymentVisible = true
            }
        },

        async handlePaymentConfirm({ method, amount, password, coinAmount }) {
            var that = this
            try {
                console.log('支付确认', method, amount, coinAmount)
                switch (method) {
                    case 'wechat':
                        const response = await appServerApi.joinPayGroup({
                            gid: that.selectedGroup.gid,
                            payType: 2,
                        })
                        util.wxpay(
                            response.data,
                            function (res) {
                                console.log('成功', res)
                                that.selectedGroup.joined = 1
                                that.paymentVisible = false
                                uni.showToast({
                                    icon: 'success',
                                    title: '加入成功',
                                    duration: 1000,
                                })
                                that.$refs.popup.close('bottom')
                                that.fetchGroups() // 刷新群列表
                            },
                            function (res) {
                                console.log('失败', res)
                                uni.showToast({
                                    title: '支付失败',
                                    icon: 'none',
                                })
                            }
                        )
                        break
                    case 'alipay':
                        const response2 = await appServerApi.joinPayGroup({
                            gid: that.selectedGroup.gid,
                            payType: 1,
                        })
                        util.alipay(
                            response2.data,
                            function (res) {
                                console.log('成功', res)
                                uni.showToast({
                                    title: '支付成功',
                                    icon: 'none',
                                })
                                that.selectedGroup.joined = 1
                                that.paymentVisible = false
                                uni.showToast({
                                    icon: 'success',
                                    title: '加入成功',
                                    duration: 1000,
                                })
                                that.$refs.popup.close('bottom')
                                that.fetchGroups() // 刷新群列表
                            },
                            function (res) {
                                console.log('失败', res)
                                uni.showToast({
                                    title: '支付失败',
                                    icon: 'none',
                                })
                            }
                        )
                        break
                    case 'balance':
                        // TODO: 处理密码校验
                        // password
                        uni.showLoading({
                            title: '正在校验密码',
                        })
                        const response3 = await appServerApi.joinPayGroup({
                            gid: that.selectedGroup.gid,
                            payType: 0,
                        })
                        uni.hideLoading()
                        uni.showToast({
                            icon: 'success',
                            title: '加入成功',
                            duration: 1000,
                        })
                        that.fetchGroups() // 刷新群列表
                        break
                    case 'coin':
                        uni.showLoading({
                            title: '处理中',
                        })
                        const response4 = await appServerApi.joinPayGroup({
                            gid: that.selectedGroup.gid,
                            payType: 5,
                            coinAmount: coinAmount // 传递金币数量到接口
                        })
                        uni.hideLoading()
                        uni.showToast({
                            icon: 'success',
                            title: '加入成功',
                            duration: 1000,
                        })
                        that.selectedGroup.joined = 1
                        that.paymentVisible = false
                        that.$refs.popup.close('bottom')
                        that.fetchGroups() // 刷新群列表
                        break
                    default:
                        break
                }
                // 处理支付逻辑
                // const result = await this.processPayment(method, amount)
                // if (result.success) {
                //     this.$message.success('支付成功')
                //     this.paymentVisible = false
                // }
            } catch (error) {
                this.$message.error('支付失败')
            }
        },
        processPayment(method, amount) {
            // 实现具体的支付处理逻辑
            return Promise.resolve({ success: true })
        },
        async initData() {
            await this.fetchAllTabs()
            await this.fetchGroups()
        },

        // 跳转到群广场页面
        goToGroupSquare() {
            uni.navigateTo({
                url: '/pages/group/GroupSquarePage',
                success: () => {
                    console.log('导航到群广场页面成功')
                },
                fail: (err) => {
                    console.error('导航到群广场页面失败:', err)
                }
            })
        },
        // 添加更新搜索结果的方法
        updateSearchResults(results) {
            // 保存原始数据，以便重置
            if (!this.originalGroups) {
                this.originalGroups = [...this.groups];
            }

            // 更新为搜索结果
            this.groups = results || [];
            this.isSearching = true;
            this.hasMore = false; // 搜索结果不支持加载更多
        },

        // 添加重置搜索的方法
        resetSearch() {
            if (this.isSearching && this.originalGroups) {
                // 恢复原始数据
                this.groups = [...this.originalGroups];
                this.originalGroups = null;
                this.isSearching = false;
                this.hasMore = this.groups.length < this.total;
            }
        },

        getValidityPeriod(duration) {
            if (!duration) return '永久有效';
            switch (parseInt(duration, 10)) {
                case 30:
                    return '一个月';
                case 90:
                    return '三个月';
                case 180:
                    return '半年';
                case 365:
                    return '一年';
                default:
                    return '一个月';
            }
        },

        getDurationDays(duration) {
            if (!duration) return 365;
            return parseInt(duration, 10);
        },

        async handleIosPaymentConfirm({ status }) {
            try {
                if (status === 'success') {
                    console.log('iOS支付确认成功')
                    appServerApi.getWallet().then(response => {
                        this.userBalance = response?.data?.balance || '0'
                        this.gold = response?.data?.gold || 0
                        this.iosPaymentVisible = false
                    })
                }
            } catch (error) {
                console.error('iOS支付确认失败:', error)
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.loading-text {
    text-align: center;
    padding: 16px;
    color: #999;
    font-size: 14px;
}

.group-square-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.op-btns,
.op-btns-icon,
.uni-navbar__header-btns,
.search-bar,
.search-input {
    display: none;
}

/* 分类标签导航 */
.category-tabs-container {
    background-color: #fff;
    border-radius: 10px;
    margin: 10px;
    padding: 5px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.tabs-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.tabs-scroll-view {
    width: calc(100% - 40px); /* 减去更多按钮的宽度 */
    white-space: nowrap;
}

.tabs-container {
    display: inline-flex;
    padding: 0 5px; /* 从原来的10px减小到5px */
    align-items: center;
}

.tab-item {
    position: relative;
    padding: 8px 12px;
    font-size: 14px;
    color: #313131;
    display: inline-block;
    text-align: center;
    transition: all 0.3s;
}

.tab-item.active {
    color: #386BF6;
    font-weight: bold;
}

.tab-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 3px;
    background-color: #386BF6;
    border-radius: 1.5px;
}

.more-tabs-btn {
    position: absolute;
    right: 6px;
    top: 0;
    bottom: 0;
    width: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    z-index: 1;
}

.more-icon {
    font-size: 24px;
    padding: 0 10px;
    color: #333;
}

/* 群列表 */
.group-list {
    flex: 1;
    padding: 10px 12px;
    overflow-y: auto;
}

.group-item {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    margin-bottom: 10px;
    padding: 10px 13px;
    border-radius: 10px;
}

.group-avatar {
    width: 60px;
    height: 60px;
    background: #f9fafc;
    box-shadow: inset 0px 0px 4px 0px rgba(23, 104, 139, 0.12);
    border-radius: 6px;
    flex-shrink: 0;
    margin-right: 2px;
    justify-content: space-between;
    position: relative;
    overflow: hidden;

    .price-badge {
        position: absolute;
        top: -12px;
        left: -30px;
        width: 80px;
        height: 24px;
        background-color: #FF2222;
        transform: rotate(-45deg);
        transform-origin: center;
        z-index: 1;

        .badge-text {
            position: absolute;
            width: 100%;
            text-align: center;
            color: #FFFFFF;
            font-size: 10px;
            bottom: 1px;
            left: -7px;     /* 文字向左移动 */
            transform: translateY(2px);  /* 文字向下移动 */
        }
    }
}

.group-avatar-img {
    width: 60px;
    height: 60px;
    border-radius: 6px;
}

.group-info {
    height: 60px;
    position: relative;
    flex: 1;
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding: 6px 12px;
    width: 50%;
}

.info-title {
    display: flex;
    align-items: center;
}

.group-name {
    font-weight: 500;
    font-size: 14px;
    max-width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.group-desc {
    color: #999;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.group-status {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.tag {
    // height: 16px;
    background: #0cc86b;
    border-radius: 2px 2px 2px 2px;
    // line-height: 16px;
    color: #fff;
    font-size: 10px;
    margin-left: 5px;
    padding: 2px;
}

.join-button {
    height: 22px;
    font-size: 12px;
    line-height: 22px;
    background: #ffffff;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #386bf6;
    color: #386bf6;
}

.has-button {
    height: 22px;
    font-size: 12px;
    line-height: 22px;
    // background: #FFFFFF;
    border-radius: 5px;
    border: none;
    color: #666;
    width: 52px;
    text-align: center;
}
/* 底部按钮 */
.create-group {
    padding: 10px;
}

.create-group-button {
    width: 158px;
    height: 45px;
    line-height: 45px;
    text-align: center;
    color: #ffffff;
    background-color: #007aff;
    border-radius: 10px;
    font-size: 16px;
}

.popup-content {
    display: flex;
    flex-direction: column;
    padding: 23px;
    width: 100%;
    height: auto;
    align-items: center;

    .top-line {
        width: 38px;
        height: 4px;
        margin: 0 auto 17px auto;
        border-radius: 10px;
        background: #e7e7e7;
    }

    .pop-title {
        padding-top: 18px;
    }

    .pop-desc {
        border-top: 1px solid #efefef;
        padding: 18px 0;
        margin-top: 20px;
        margin-bottom: 20px;
        width: 100%;

        .desc-line {
            display: flex;
            position: relative;
            padding-bottom: 20px;
            align-items: center;

            &:last-child {
                padding-bottom: 0;
            }

            .desc-label {
                height: 15px;
                font-size: 11px;
                color: #000000;
                line-height: 15px;
                text-align: left;
                padding: 2px 0;
                margin-right: 10px;
                width: 44px;
                flex-shrink: 0;
            }

            .desc-content {
                font-size: 14px;
                color: #999999;
                display: flex;
                align-items: center;
                padding-left: 0;
                flex: 1;
            }

            .desc-name {
                color: #000000;
            }

            .header-right {
                position: absolute;
                right: 0;
                font-size: 14px;
                color: #999999;
                line-height: 16px;

                i {
                    margin-left: 10px;
                }
            }

            .avatar-small {
                width: 20px;
                height: 20px;
                margin-right: 10px;
                border-radius: 4px;
            }
        }
    }

    .op-block {
        margin-bottom: 20px;
        width: 100%;

        .price-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0;
            margin-bottom: 8px;

            .validity {
                font-size: 8px;
                color: #666666;
                font-weight: 400;
            }

            .daily-price {
                font-size: 8px;
                color: #386bf6;
                font-weight: 500;
            }
        }

        button[type="primary"] {
            width: 100%;
            height: 46px;
            background: #386bf6;
            border-radius: 4px;
            color: #fff;
            font-size: 16px;
            border: none;
            margin-top: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            line-height: 1;
            padding: 0;

            &:active {
                opacity: 0.9;
            }
        }
    }
}
.pay-method-popup {
    background-color: #fff;
    border-radius: 16px 16px 0 0;
    padding: 20px;

    .pay-method-title {
        text-align: center;
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 20px;
    }

    .pay-method-list {
        .pay-method-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f5f5f5;

            .pay-icon {
                margin-right: 10px;
                font-size: 16px;
            }

            .pay-name {
                font-size: 14px;
                color: #333;
            }
        }
    }

    .pay-cancel {
        margin-top: 10px;
        text-align: center;
        padding: 15px 0;
        color: #666;
        font-size: 14px;
    }
}
</style>