<template>
	<view class="activity-main-bg group-square-container">
		<!-- 群列表 -->
		<scroll-view class="group-list" 
			scroll-y 
			@scrolltolower="onScrollToLower"
			:refresher-enabled="true"
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresherRefresh"
			@refresherrestore="onRefresherRestore">
			
			<!-- 分类展示 -->
            <view v-if="filteredGroups.length > 0">
                <!-- 我创建的群聊 -->
                <view class="group-category">
                    <view class="category-header" @click="toggleCategory('created')">
                        <view class="category-title">我创建的群聊 ({{createdGroups.length}})</view>
                        <view class="category-icon">
                            <text class="iconfont" :class="categoryExpanded.created ? 'icon-arrow-up' : 'icon-arrow-down'"></text>
                        </view>
                    </view>
                    <view v-if="categoryExpanded.created" class="category-content">
                        <view class="group-item" v-for="(group, index) in createdGroups" :key="'created-'+index" @click="handleGroupClick(group)">
                            <view class="group-avatar">
                                <image :src="group.avatar" class="avatar" />
                            </view>
                            <view class="group-info">
                                <view class="info-title">
                                    <text class="group-name">{{ group.name }}</text>
                                    <text class="tag" v-if="group.categoryName">{{ group.categoryName }}</text>
                                </view>
                                <text class="group-desc">{{ group.description || "暂无介绍" }}</text>
                            </view>
                            <view class="group-status">
                                <button class="quit-button" @click.stop="handleQuit(group)">退出</button>
                            </view>
                        </view>
                        <view v-if="createdGroups.length === 0" class="empty-category">暂无创建的群聊</view>
                    </view>
                </view>
                
                <!-- 我管理的群聊 -->
                <view class="group-category">
                    <view class="category-header" @click="toggleCategory('managed')">
                        <view class="category-title">我管理的群聊 ({{managedGroups.length}})</view>
                        <view class="category-icon">
                            <text class="iconfont" :class="categoryExpanded.managed ? 'icon-arrow-up' : 'icon-arrow-down'"></text>
                        </view>
                    </view>
                    <view v-if="categoryExpanded.managed" class="category-content">
                        <view class="group-item" v-for="(group, index) in managedGroups" :key="'managed-'+index" @click="handleGroupClick(group)">
                            <view class="group-avatar">
                                <image :src="group.avatar" class="avatar" />
                            </view>
                            <view class="group-info">
                                <view class="info-title">
                                    <text class="group-name">{{ group.name }}</text>
                                    <text class="tag" v-if="group.categoryName">{{ group.categoryName }}</text>
                                </view>
                                <text class="group-desc">{{ group.description || "暂无介绍" }}</text>
                            </view>
                            <view class="group-status">
                                <button class="quit-button" @click.stop="handleQuit(group)">退出</button>
                            </view>
                        </view>
                        <view v-if="managedGroups.length === 0" class="empty-category">暂无管理的群聊</view>
                    </view>
                </view>
                
                <!-- 我加入的群聊 -->
                <view class="group-category">
                    <view class="category-header" @click="toggleCategory('joined')">
                        <view class="category-title">我加入的群聊 ({{joinedGroups.length}})</view>
                        <view class="category-icon">
                            <text class="iconfont" :class="categoryExpanded.joined ? 'icon-arrow-up' : 'icon-arrow-down'"></text>
                        </view>
                    </view>
                    <view v-if="categoryExpanded.joined" class="category-content">
                        <view class="group-item" v-for="(group, index) in joinedGroups" :key="'joined-'+index" @click="handleGroupClick(group)">
                            <view class="group-avatar">
                                <image :src="group.avatar" class="avatar" />
                            </view>
                            <view class="group-info">
                                <view class="info-title">
                                    <text class="group-name">{{ group.name }}</text>
                                    <text class="tag" v-if="group.categoryName">{{ group.categoryName }}</text>
                                </view>
                                <text class="group-desc">{{ group.description || "暂无介绍" }}</text>
                            </view>
                            <view class="group-status">
                                <button class="quit-button" @click.stop="handleQuit(group)">退出</button>
                            </view>
                        </view>
                        <view v-if="joinedGroups.length === 0" class="empty-category">暂无加入的群聊</view>
                    </view>
                </view>
            </view>
            <view v-else class="empty-state">
                <view class="empty-title">
                    暂无群组
                </view>
                <view class="empty-desc">
                    请在群聊设置中选择"保存到通讯录"
                </view>
            </view>

			<!-- 底部状态提示 -->
			<view v-if="!hasMore && groups.length > 0" class="loading-text">没有更多了</view>
			<view v-if="!hasMore && groups.length === 0" class="loading-text">暂无数据</view>
		</scroll-view>
	</view>
</template>

<script>
import appServerApi from "@/api/appServerApi";
import wfc from "@/wfc/client/wfc";
import store from "@/store";
import Conversation from "@/wfc/model/conversation";
import ConversationType from "@/wfc/model/conversationType";
import { getItem } from "@/pages/util/storageHelper";

export default {
	name: 'MyGroupPage',
	data() {
		return {
			groups: [],
			pageNo: 1,
			pageSize: 100, // 增大页面大小以一次性加载更多数据
			hasMore: true,
			isRefreshing: false,
            categoryExpanded: {
                created: false,
                managed: false,
                joined: false
            }
		};
	},
    computed: {
        filteredGroups() {
            return this.groups;
        },
        createdGroups() {
            // 根据群组成员类型定义，type 2 表示群主（Owner）
            return this.filteredGroups.filter(group => group.memberType === 2 || group.type === 2 || group.role === 2);
        },
        managedGroups() {
            // 根据群组成员类型定义，type 1 表示管理员（Manager）
            return this.filteredGroups.filter(group => group.memberType === 1 || group.type === 1 || group.role === 1);
        },
        joinedGroups() {
            // 根据群组成员类型定义，type 0 表示普通成员（Normal）
            return this.filteredGroups.filter(group => group.memberType === 0 || group.type === 0 || group.role === 0);
        }
    },
	mounted() {
		this.fetchGroups();
		this.addGroupChangeListener();
	},
	
	onShow() {
		// 每次页面显示时刷新数据
		this.fetchGroups();
	},
	
	onUnload() {
		// 页面卸载时移除监听器
		this.removeGroupChangeListener();
	},
	
	methods: {
        // 添加监听群组信息变更的方法
        addGroupChangeListener() {
            // 监听群组信息更新事件
            wfc.eventEmitter.on('groupInfoUpdated', this.onGroupInfoUpdated);
            // 监听群成员更新事件
            wfc.eventEmitter.on('groupMemberUpdated', this.onGroupInfoUpdated);
            // 监听用户加入或退出群聊事件
            wfc.eventEmitter.on('groupMembersUpdated', this.onGroupInfoUpdated);
        },
        
        // 移除群组变更监听
        removeGroupChangeListener() {
            wfc.eventEmitter.off('groupInfoUpdated', this.onGroupInfoUpdated);
            wfc.eventEmitter.off('groupMemberUpdated', this.onGroupInfoUpdated);
            wfc.eventEmitter.off('groupMembersUpdated', this.onGroupInfoUpdated);
        },
        
        // 处理群组信息更新的回调
        onGroupInfoUpdated(groupId) {
            console.log('群组信息已更新:', groupId);
            this.fetchGroups();
        },
        
		toggleCategory(category) {
            this.categoryExpanded[category] = !this.categoryExpanded[category];
        },
        
		// 获取已加入的群组列表
		async fetchGroups(isLoadMore = false) {
			if (!isLoadMore) {
				uni.showLoading({
					title: '加载中...'
				})
				this.pageNo = 1
				this.hasMore = true
			}

			try {
				const response = await appServerApi.searchGroups({
						pageNo: this.pageNo,
						pageSize: this.pageSize,
						joined: 1, // 只获取已加入的群
					})

				if (response.data && response.data.result) {
                    let groupList = response.data.result;
                    
                    // 为每个群组获取用户在该群的角色
                    for (let i = 0; i < groupList.length; i++) {
                        const group = groupList[i];
                        const groupId = group.gid;
                        if (groupId) {
                            try {
                                // 获取当前用户ID
                                const currentUserId = uni.getStorageSync('userId');
                                // 获取用户在群组中的成员信息
                                const memberInfo = wfc.getGroupMember(groupId, currentUserId);
                                if (memberInfo) {
                                    group.memberType = memberInfo.type;
                                }
                            } catch (err) {
                                console.error("Error getting group member info:", err);
                            }
                        }
                    }
                    
					if (!isLoadMore) {
						this.groups = groupList;
					} else {
						this.groups = [...this.groups, ...groupList];
					}

					this.hasMore = response.data.result.length >= this.pageSize
					if (this.hasMore) {
						this.pageNo++
					}
                    
                    // 默认所有分类都折叠
                    this.categoryExpanded.created = false;
                    this.categoryExpanded.managed = false;
                    this.categoryExpanded.joined = false;
				} else {
					if (!isLoadMore) {
						this.groups = []
					}
					this.hasMore = false
				}
			} catch (error) {
				console.error("Error fetching groups:", error)
				if (!isLoadMore) {
					this.groups = []
				}
				this.hasMore = false
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				if (!isLoadMore) {
					uni.hideLoading()
				}
			}
		},

		// 处理群组点击
		handleGroupClick(group) {
			let conversation = new Conversation(ConversationType.Group, group.gid, 0);
			store.setCurrentConversation(conversation);
			this.$go2ConversationPage();
		},

		// 处理退出群组
		handleQuit(group) {
			uni.showModal({
				title: '提示',
				content: '确定要退出该群组吗？',
				success: (res) => {
					if (res.confirm) {
                        store.quitGroup(group.gid)
                        setTimeout(() => {
                            this.fetchGroups();
                        }, 1000);
					}
				}
			});
		},

		// 处理下拉刷新
		async onRefresherRefresh() {
			this.isRefreshing = true
			await this.fetchGroups()
			this.isRefreshing = false
		},

		// 下拉刷新恢复
		onRefresherRestore() {
			console.log('refresher restore')
		},

		// 处理滚动到底部
		onScrollToLower() {
			if (!this.hasMore) return
			this.fetchGroups(true)
		},
	},
};
</script>
<style lang="scss" scoped>
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 20px 0;

		.loading-icon {
			width: 32px;
			height: 32px;
			margin-bottom: 8px;
		}

		.loading-text {
			font-size: 14px;
			color: #999;
		}
	}

	.loading-text {
		text-align: center;
		padding: 16px;
		color: #999;
		font-size: 14px;
	}

	.group-square-container {
		display: flex;
		flex-direction: column;
		height: 100%;
	}

	.op-btns,
	.op-btns-icon,
	.uni-navbar__header-btns,
	.search-bar,
	.search-input {
		display: none;
	}

	/* 分类标签导航 */
	.category-tabs {
		display: flex;
		background-color: #ffffff;
		padding: 10px 0;
		border-bottom: 1px solid #e6e6e6;
	}

	.category-tab {
		padding: 5px 15px;
		font-size: 14px;
		color: #666;
	}

	.category-tab.active {
		color: #007aff;
		font-weight: bold;
		border-bottom: 2px solid #007aff;
	}

	/* 群列表 */
	.group-list {
		flex: 1;
		padding: 10px 12px;
		overflow-y: auto;
	}

	.group-item {
		display: flex;
		align-items: center;
		background-color: #ffffff;
		margin-bottom: 10px;
		padding: 10px 13px;
		border-radius: 10px;
	}

	.group-avatar {
		width: 60px;
		height: 60px;
		background: #f9fafc;
		box-shadow: inset 0px 0px 4px 0px rgba(23, 104, 139, 0.12);
		border-radius: 6px 6px 6px 6px;
		flex-shrink: 0;
		padding: 2px;
		justify-content: space-between;
	}

	.avatar {
		width: 60px;
		height: 60px;
		border-radius: 4px 4px 4px 4px;
		margin-right: 1px;
	}

	.group-info {
		height: 60px;
		position: relative;
		flex: 1;
		display: flex;
		justify-content: space-between;
		flex-direction: column;
		padding: 6px 12px;
		width: 50%;
	}

	.info-title {
		display: flex;
		align-items: center;
	}

	.group-name {
		font-weight: 500;
		font-size: 14px;
		max-width: 70%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.group-desc {
		color: #999;
		font-size: 12px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.group-status {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.tag {
		// height: 16px;
		background: #0cc86b;
		border-radius: 2px 2px 2px 2px;
		// line-height: 16px;
		color: #fff;
		font-size: 10px;
		margin-left: 5px;
		padding: 2px;
	}
 
    .quit-button {
        height: 22px;
        font-size: 12px;
        line-height: 22px;
        background: #ffffff;
        border-radius: 5px;
        border: 1px solid #ff4d4f;
        color: #ff4d4f;
    }
    
    .group-category {
        margin-bottom: 10px;
    }

    .category-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 20px;
        background-color: #f5f5f5;
        border-radius: 8px;
        margin: 0 0 5px;
    }

    .category-title {
        font-size: 14px;
        font-weight: 500;
        color: #333;
    }

    .category-icon {
        color: #999;
        font-size: 14px;
    }

    .category-content {
        transition: all 0.3s ease;
    }

    .empty-category {
        text-align: center;
        padding: 15px;
        color: #999;
        font-size: 12px;
    }
    
    .empty-state {
        width: 100%;
        margin-top: 120px;
        text-align: center;
    }
    
    .empty-title {
        color: #000;
        text-align: center;
        font-family: MiSans;
        font-size: 20px;
        font-style: normal;
        font-weight: 500;
        line-height: 28px;
        margin-top: 15px;
    }
    
    .empty-desc {
        color: #999;
        text-align: center;
        font-family: MiSans;
        font-size: 14px;
        font-style: normal;
        font-weight: 330;
        line-height: 22px;
        margin-top: 10px;
    }

    .icon-arrow-down:before {
        content: "\e6b9";
    }

    .icon-arrow-up:before {
        content: "\e6ba";
    }

    .iconfont {
        font-family: "iconfont";
    }
</style>