<template>
  <view class="container">
    <text class="footer-text">高效社交用地球岛</text>
  </view>
</template>
<script>
import {getItem} from "@/pages/util/storageHelper";

export default {
  onInit() {
    let im_token = getItem('im_token');
    let userId = getItem('userId');
    if (im_token && userId) {
      this.go2ConversationList()
    } else {
        this.go2Login()
    }

  },
  methods: {
    go2ConversationList() {
      console.log('SplashPage: 准备跳转到会话列表页面')
      try {
        // 检查页面是否在tabBar中
        const tabBarPages = ['/pages/conversationList/ConversationListPage'];
        const isTabBarPage = tabBarPages.includes('/pages/conversationList/ConversationListPage');
        console.log('SplashPage: 是否为TabBar页面:', isTabBarPage);

        // 先尝试使用switchTab
        uni.switchTab({
          url: '/pages/conversationList/ConversationListPage',
          success: () => {
            console.log('SplashPage: 跳转到会话列表页成功 (switchTab)')
          },
          fail: (error) => {
            console.error('SplashPage: switchTab跳转失败:', error)
            console.log('SplashPage: 尝试使用reLaunch方式跳转')

            // 如果switchTab失败，尝试使用reLaunch
            uni.reLaunch({
              url: '/pages/conversationList/ConversationListPage',
              success: () => {
                console.log('SplashPage: 跳转到会话列表页成功 (reLaunch)')
              },
              fail: (err) => {
                console.error('SplashPage: reLaunch跳转也失败:', err)

                // 尝试使用navigateTo
                console.log('SplashPage: 尝试使用navigateTo方式跳转')
                uni.navigateTo({
                  url: '/pages/conversationList/ConversationListPage',
                  success: () => {
                    console.log('SplashPage: 跳转到会话列表页成功 (navigateTo)')
                  },
                  fail: (navErr) => {
                    console.error('SplashPage: navigateTo跳转也失败:', navErr)
                    this.clearLoginInfo()
                    uni.reLaunch({
                      url: '/pages/login/LoginPage'
                    })
                  }
                });
              }
            })
          },
          complete: () => {
            console.log('SplashPage: switchTab操作完成')
          }
        })
      } catch (error) {
        console.error('SplashPage: 跳转过程中发生异常:', error)
        if (error.stack) {
          console.error('错误堆栈:', error.stack)
        }

        // 尝试使用reLaunch作为最后的备选方案
        try {
          console.log('SplashPage: 尝试在异常处理中使用reLaunch')
          uni.reLaunch({
            url: '/pages/conversationList/ConversationListPage',
            fail: () => {
              this.clearLoginInfo()
              uni.reLaunch({
                url: '/pages/login/LoginPage'
              })
            }
          });
        } catch (e) {
          console.error('SplashPage: 在异常处理中reLaunch也失败:', e)
          this.clearLoginInfo()
          uni.reLaunch({
            url: '/pages/login/LoginPage'
          })
        }
      }
    },
    go2Login(){
      uni.reLaunch({
        url: '/pages/login/LoginPage'
      })
    }
  }
}
</script>
<style>
.container {
  background: #0074f8 url('/static/image/login/backgroud.png') no-repeat center center;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 90% auto;
  width: 100%;
  height: 100%;
}

.footer-text {
  position: absolute;
  bottom: 60rpx; /* Adjust this value to change distance from bottom */
  left: 0;
  right: 0; /* 左右撑满 */
  text-align: center;
  width: auto; /* 宽度自适应内容 */
  color: #ffffff;
  font-size: 40rpx;
  /* Optional: add text shadow for better visibility */
  lines: 1;
  text-overflow: ellipsis;
}
</style>
