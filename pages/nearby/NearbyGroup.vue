<template>
  <view class="nearby-people-wrap">
    <CustomHeader>
      <template v-slot:title>
        <text>附近的群</text>
      </template>
      <template v-slot:right>
        <uni-icons color="#333" type="more-filled" size="20" />
      </template>
    </CustomHeader>
    <view class="filter-block row-flex">
      <uv-tabs lineColor="#386BF6" :activeStyle="{
						color: '#000',
						fontSize: '18px',
						fontWeight: 'bold',
			    	}" :list="tabs" @click="handleTabClick"></uv-tabs>
      <text class="more" @click="handleFilter">筛选</text>
    </view>
    <!-- <nearbyListVue :type="type" :groups="groups" /> -->
	
	<nearbyPeople :list="people" v-if="type==='people'"/>
	<nearbyGroup  :list="people" v-if="type==='group'"/>

    <button @click="toHelp">帮助与反馈</button>

    <uni-popup ref="morePopup" borderRadius='20px' background-color="transparent" @change="()=>{}">
      <view class="cover-filter">
        <view class="filter-item col-flex">
          <view class="">
            只看老板
          </view>
          <view class="">
            只看员工
          </view>
          <view class="">
            查看全部
          </view>
          <view class="">
            清除位置并退出
          </view>
        </view>
        <view class="cancel-btn">
          取消
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'

	import nearbyPeople from '../../components/nearby/nearbyPeople.vue'
	import nearbyGroup from '../../components/nearby/nearbyGroup.vue'
import appServerApi from '@/api/appServerApi'

export default {
  components: {
    CustomHeader,nearbyPeople,nearbyGroup,
  },
  data() {
    return {
      type: 'people', // 人/群
      tabs: [{
        name: '找人',
        value: 'people'
      },
        {
          name: '找群',
          value: 'group'
        },
      ],
      groups: []
    }
  },
  methods: {
    handleTabClick(v) {
      this.type = v.value;
      if (this.type === 'group') {
        this.searchGroups();
      }
    },
    handleFilter() {
      this.$refs.morePopup.open('bottom');
    },
    toHelp() {
      uni.navigateTo({
        url: "/pages/HelpFeedback/index"
      })
    },
    async searchGroups() {
      try {
        const response = await appServerApi.searchGroups({
          pageNo: 1,
          pageSize: 10,
          keyword: '',
          categoryId: ''
        });
        this.groups = response.data;
      } catch (error) {
        console.error('搜索群组失败:', error);
      }
    }
  },
  mounted() {
    if (this.type === 'group') {
      this.searchGroups();
    }
  }
}
</script>

<style lang="scss" scoped>
.nearby-people-wrap {
  height: 100%;
  padding: 0 15px;
  background: #F5F5F5;
}

.filter-block {
  .more {
    font-size: 16px;
    font-weight: 400;
  }
}

.cover-filter {
  width: 100%;
  padding: 0 22px 32px;
}

.filter-item {
  overflow: hidden;
  border-radius: 12px;
  font-size: 16px;
  padding: 16px;
  box-sizing: border-box;
  background: #FFFFFF;

  >view {

    width: 100%;
    height: 50px;
    line-height: 50px;
    text-align: center;

    &:not(:last-of-type) {
      border-bottom: 1px solid #EFEFEF;
    }
  }

}

.cancel-btn {
  width: 100%;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 12px;
  background: #FFFFFF;
  margin-top: 10px;
}
</style>
