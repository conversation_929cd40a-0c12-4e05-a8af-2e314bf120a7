<template>
    <view class="nearby-people-wrap">
        <CustomHeader>
            <template v-slot:title>
                <text>附近的人</text>
            </template>
            <template v-slot:right>
                <uni-icons color="#333" type="more-filled" size="20" />
            </template>
        </CustomHeader>
        <view class="filter-block row-flex">
            <uv-tabs lineColor="#386BF6" :activeStyle="{
          color: '#000',
          fontSize: '18px',
          fontWeight: 'bold',
        }" :list="tabs" @click="handleTabClick"></uv-tabs>
            <text class="more" @click="handleFilter">筛选</text>
        </view>
        <nearbyPeople :list="listData" v-if="type === 'people'" />
        <nearbyGroup :list="listData" v-if="type === 'group'" />

        <uni-popup ref="morePopup" borderRadius="20px" background-color="transparent" @change="() => {}">
            <view class="cover-filter">
                <view class="filter-item col-flex">
                    <view class=""> 只看男生 </view>
                    <view class=""> 只看女生 </view>
                    <view class=""> 查看全部 </view>
                    <view class=""> 清除位置并退出 </view>
                </view>
                <view class="cancel-btn"> 取消 </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import nearbyPeople from '../../components/nearby/nearbyPeople.vue'
import nearbyGroup from '../../components/nearby/nearbyGroup.vue'
import appServerApi from '@/api/appServerApi'
import topMessage from '@/common/topMessageView'
export default {
    components: {
        CustomHeader,
        nearbyPeople,
        nearbyGroup,
    },
    data() {
        return {
            type: 'people', // 人/群
            tabs: [
                {
                    name: '找人',
                    value: 'people',
                },
                {
                    name: '找群',
                    value: 'group',
                },
            ],
            listData: [],
        }
    },
    onLoad() {
        this.searchPeople()
    },
    methods: {
        handleTabClick(v) {
            this.type = v.value
            if (this.type === 'people') {
                this.searchPeople()
            } else {
                this.searchGroup()
            }
        },
        handleFilter() {
            this.$refs.morePopup.open('bottom')
        },
        // toHelp() {
        //   uni.navigateTo({
        //     url: "/pages/HelpFeedback/index"
        //   })
        // },
        // 附近的人
        async searchPeople() {
            /*#ifdef APP-PLUS*/
            if (plus.os.name !== 'iOS') {
                var isPermission = plus.navigator.checkPermission(
                    'android.permission.ACCESS_FINE_LOCATION'
                )
                if (isPermission != 'authorized') {
                    topMessage.createTopMessage(
                        '位置权限使用说明',
                        '用于向你推荐附近你可能感兴趣的内容、服务或用户等相关信息，当地天气,以展示与你的距离、提升浏览体验，或帮助你在发布的信息或互动中展示位置。'
                    )
                }
                let res = await topMessage.requestPermissions(
                    'ACCESS_FINE_LOCATION',
                    '位置权限未获得，此权限为了给您查看附近的人，请前往设置中打开'
                )
                setTimeout(() => {
                    topMessage.hideTopMessage()
                }, 300)
                if (!res.granted[0]) {
                    // 无权限
                    return
                }
            }
            /*#endif*/

            try {
                // 获取位置信息
                const location = await this.getCurrentLocation()

                const response = await appServerApi.fetchNearbyPeople({
                    longitude: location.longitude,
                    latitude: location.latitude,
                    distance: 1000,
                })
                this.listData = response.data || []
            } catch (error) {
                console.error('搜索人失败:', error)
                uni.showToast({
                    title: '获取位置信息失败',
                    icon: 'none',
                })
            }
        },
        // 附近的群
        async searchGroup() {
            try {
                // 获取位置信息
                const location = await this.getCurrentLocation()

                const response = await appServerApi.fetchNearbyGroup({
                    longitude: location.longitude,
                    latitude: location.latitude,
                    distance: 1000,
                })
                this.listData = response.data || []
            } catch (error) {
                console.error('搜索群失败:', error)
                uni.showToast({
                    title: '获取位置信息失败',
                    icon: 'none',
                })
            }
        },
        // 添加获取位置的方法
        getCurrentLocation() {
            return new Promise((resolve, reject) => {
                uni.getLocation({
                    type: 'wgs84',
                    success: (res) => {
                        resolve({
                            latitude: res.latitude,
                            longitude: res.longitude,
                        })
                    },
                    fail: (err) => {
                        console.error('获取位置失败:', err)
                        uni.showToast({
                            title: '请授权位置权限',
                            icon: 'none',
                        })
                        reject(err)
                    },
                })
            })
        },
    },
}
</script>

<style lang="scss" scoped>
.nearby-people-wrap {
    height: 100%;
    padding: 0 15px;
    background: #f5f5f5;
}

.filter-block {
    .more {
        font-size: 16px;
        font-weight: 400;
    }
}

.cover-filter {
    width: 100%;
    padding: 0 22px 32px;
}

.filter-item {
    overflow: hidden;
    border-radius: 12px;
    font-size: 16px;
    padding: 16px;
    box-sizing: border-box;
    background: #ffffff;

    > view {
        width: 100%;
        height: 50px;
        line-height: 50px;
        text-align: center;

        &:not(:last-of-type) {
            border-bottom: 1px solid #efefef;
        }
    }
}

.cancel-btn {
    width: 100%;
    height: 50px;
    line-height: 50px;
    text-align: center;
    border-radius: 12px;
    background: #ffffff;
    margin-top: 10px;
    font-size: 16px;
}
</style>
