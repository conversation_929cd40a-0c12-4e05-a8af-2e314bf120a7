<template>
    <view class="search-portal-container">
        <view>
            <uni-search-bar bgColor="#fff" :radius="100" v-model="keyword" placeholder="搜索备注、昵称或者聊天记录"
                @confirm="search" :focus="true"
                @input="debounceSearch"></uni-search-bar>
        </view>

        <view class="conversation-rq" v-if="keyword && keyword.trim()">
            <SearchResultView :query="keyword" :options="options" v-if="keyword && keyword.trim()" />
        </view>
    </view>
</template>


<script>
    import SearchResultView from "./SearchResultView";
    import store from "../../store";

    export default {
        name: "SearchPortalPage",
        components: {
            SearchResultView
        },
        onReady() {
            uni.setNavigationBarColor({
                frontColor: '#000000',
                backgroundColor: '#F5F5F5',
            })
        },
        data() {
            return {
                keyword: '',
                type: 'all',
                options: {
                    user: false,
                    contact: true,
                    conversation: true,
                    group: true,
                }
            }
        },
        onLoad(option) {
            console.log('option', option)
            let queryOption = (query) => option[query] ? option[query] === 'true' : true;
            this.options.user = false  // 强制禁用用户搜索，不搜索未加好友的用户
            this.options.group = queryOption('group')
            this.options.contact = queryOption('contact')
            this.options.conversation = queryOption('conversation')
        },
        methods: {
            search() {
                store.setSearchQuery(this.keyword, this.options)
            },
            debounceSearch(value) {
                // value 就是搜索框的当前值
                if (this.searchTimer) {
                    clearTimeout(this.searchTimer)
                }
                this.searchTimer = setTimeout(() => {
                    store.setSearchQuery(value, this.options)
                }, 300)
            }
        }
    }
</script>

<style scoped>

    .search-portal-container {
        height: var(--page-full-height-without-header);
        overflow: auto;
        position: relative;
        background: #F5F5F5;
    }

    .search-portal-container input {
        padding: 5px 10px;
        height: 35px;
        box-sizing: border-box;
        border-bottom: 1px solid lightgrey;
        position: fixed;
        width: 100%;
        top: var(--uni-page-header-height);
        left: 0;
        background-color: white;
        z-index: 99;
    }

    .search-portal-container .tip-container {
        display: flex;
        height: 100%;
        justify-content: center;
        align-items: center;
        color: grey;
    }
</style>