<template>
    <view class="page-body page-body1">
        <view class="loginpage1" v-if="typeid==1">
            <view class="rq-l1">地球岛</view>
            <view class="rq-l2">开启你的新世界</view>
            <view class="rq-l3">首次登录自动创建新账号</view>
            <view class="rq-l4">
                <image class="rq-l4img" src="/static/image/login/login1.gif"></image>
            </view>
            <view class="rq-l5"><button class="confirm-button " @tap="logintypeid(2)">手机号登录</button></view>
            <view class="rq-l6">
                <view class="rqla rqlb" v-if="ischecked" @click="onischecked">
                    <image class="rqla1" src="/static/image/login/g2.png"></image>
                </view>
                <view class="rqla" v-if="!ischecked" @click="onischecked"></view>
                阅读并同意<text class="rq-l6xy" @tap="xieyi('用户服务协议')">用户服务协议</text>、<text class="rq-l6xy" @tap="xieyi('隐私协议')">隐私协议</text>
            </view>
        </view>
        <view class="loginpage2" v-if="typeid==2">

            <!-- <view class="rq-back">开启你的新世界</view> -->
            <view class="rq-l99"></view>
            <view style="height: 80rpx;"></view>
            <view class="rq-l8">开启你的新世界</view>
            <view class="rq-l9">首次登录自动创建新账号</view>

            <view class="rq-l12">
                <view class="confirm-button" @tap="logintypeid(99)">本机号码一键登录</view>
            </view>
            <view class="rq-l13">
                <view class="confirm-button rqbaise" @tap="logintypeid(3)">其他手机号登录</view>
            </view>
            <view class="rq-l14">
                <view class="confirm-button rqbaise" @tap="logintypeid(5)">账号密码登录</view>
            </view>
            <view class="rq-l18">
                <view class="rqla rqlb" v-if="ischecked == true" @click="onischecked">
                    <image class="rqla1" src="/static/image/login/g2.png"></image>
                </view>
                <view class="rqla" v-if="ischecked == false" @click="onischecked"></view>
                我已阅读并同意<text class="rq-l6xy" @tap="xieyi('用户服务协议')">用户服务协议</text>、<text class="rq-l6xy" @tap="xieyi('隐私协议')">隐私协议</text>
            </view>

            <!-- 添加一个提示 -->
            <view class="login-tip" v-if="showSdkTip">
                <text>如果一键登录页面无法操作，请点击下方按钮</text>
                <view class="tip-button" @tap="handleSdkIssue">切换到手机号登录</view>
            </view>

            <view v-if="false" class="rq-l6-a">
                <titles titlesx="其他登录方式"></titles>
                <view class="rq-l6-2"  @tap="handleWechatLogin">
                    <image class="rq-l6-2img" src="/static/image/login/wxtb.png"></image>
                </view>
                <view class="rq-l6-3" @tap="handleWechatLogin">微信登录</view>
            </view>
        </view>
        <view class="loginpage3" v-if="typeid==3">
            <view class="rq-back" @tap="backToAuthPage"></view>
            <view class="rq-l99"></view>
            <view style="height: 80rpx;"></view>
            <view class="rq-l8">开启你的新世界</view>
            <view class="rq-l9">首次登录自动创建新账号</view>
            <view class="rq-3a">
                <view class="rq-3a1">+86</view>
                <input :value="phone" class="input rq-3a2" @input="bindPhoneInput" type="number" placeholder="请输入手机号码" />
                <view :class="`rq-3a3 `+ (phone.length == 11&&countdown==0?'rq-3a3x':'')" @tap="bindAuthCodeTap">获取验证码<span v-if="countdown>0">({{countdown}})</span> </view>
            </view>
            <input class="cinput" adjust-position="false" auto-blur="true" @blur="blur" @input="codenum" :focus="focus" value="code" v-model="code" type="number" maxlength="4" />
            <view class="rq-3b">
                <view v-for="(item,index) in 4" :key="index" @click="codefocus(index)" :style='((index == code.length || code.length==4 )&&focus ? "border: 2rpx solid #1195db;":("color: " + codeclolor + ";" ))'>
                    {{code[index] && code[index] || ''}}
                </view>
            </view>
            
            <!-- 添加密码登录按钮 - 新位置：验证码输入的左下方 -->
            <view class="password-login-btn-new" @tap="logintypeid(5)">
                密码登录
            </view>
            
            <view :class="'confirm-button '+ (phone.length==11 && code.length==4 ?'':'rqqlanse') " @tap="bindLoginTap">
                登录</view>
            
            <view class="rq-l18">
                <view class="rqla rqlb" v-if="ischecked" @click="onischecked">
                    <image class="rqla1" src="/static/image/login/g2.png"></image>
                </view>
                <view class="rqla" v-if="!ischecked" @click="onischecked"></view>
                阅读并同意<text class="rq-l6xy" @tap="xieyi('用户服务协议')">用户服务协议</text>、<text class="rq-l6xy" @tap="xieyi('隐私协议')">隐私协议</text>
            </view>
            <view  v-if="false" class="rq-l6-a">
                <titles titlesx="其他登录方式"></titles>
                <view class="rq-l6-2" @tap="handleWechatLogin">
                    <image class="rq-l6-2img" src="/static/image/login/wxtb.png"></image>
                </view>
                <view class="rq-l6-3" @tap="handleWechatLogin">微信登录</view>
            </view>
        </view>
        <view class="loginpage4" v-if="typeid==4">
            <view class="rq-back" @tap="logintypeid(98)"></view>
            <view class="rq-4a rq-l97">完善你的资料 <image class="rq-l98" src="/static/image/login/bg2.png"></image>
            </view>
            <view class="rq-4a">让大家更好的了解你</view>
            <view style="height: 30rpx;"></view>
            <view class="rq-4b">
                <uni-file-picker limit="1" :del-icon="false" disable-preview :imageStyles="imageStyles" file-mediatype="image" @select="onuploadimg">
                    <!-- <image :src="avatarUrl" class="avatar"></image> -->
                </uni-file-picker>
            </view>
            <view style="height: 30rpx;"></view>
            <view class="rq-4c">
                <view class="rq-4c1">昵称</view>
                <view class="rq-4c2"><input class="rq-4cinp" type="text" v-model="formData.displayName" /></view>
            </view>
            <view class="rq-4c">
                <view class="rq-4c1">生日</view>
                <view class="rq-4c2">
                    <picker mode="date" :value="formData.birthday" start="1900-01-01" end="2100-12-31" @change="bindDateChange">
                        <view>{{formData.birthday}}</view>
                    </picker>
                </view>
            </view>
            <view class="rq-4c">
                <view class="rq-4c1">性别</view>
                <view class="rq-4c3">
                    <view :class="`rq-4c3m ` + (formData.gender==1?'rq-4c3xnn':'rq-4c3mnn') " @tap="formData.gender=1">
                        <view class="rqicon-nn"></view>
                    </view>
                    <view :class="`rq-4c3m ` + (formData.gender==2?'rq-4c3xnv':'rq-4c3mnv') " @tap="formData.gender=2">
                        <view class="rqicon-nv"></view>
                    </view>
                </view>
            </view>
            <view class="rq-4c">
                <view class="rq-4c1">城市</view>
                <view class="rq-4c2">
                    <input class="rq-4cinp" type="text" v-model="formData.city" />
                </view>
            </view>
            <view class="rq-4c">
                <view class="rq-4c1">学校</view>
                <view class="rq-4c2">
                    <input class="rq-4cinp" type="text" v-model="formData.school" placeholder="填写学校，发现校友" />
                </view>
            </view>
            <view class="complete-btn">
                <view class="confirm-button" @tap="userupload()">确认</view>
            </view>
            <view style="height: 180rpx;"></view>
        </view>
        <view class="loginpage5" v-if="typeid==5">
            <view class="rq-l99"></view>
            <view style="height: 80rpx;"></view>
            <view class="rq-l8">账号密码登录</view>
            <view class="rq-l9">使用手机号和密码登录</view>
            
            <!-- 手机号输入框 -->
            <view class="input-container">
                <view class="input-label">手机号</view>
                <input :value="phone" 
                       class="input-field" 
                       @input="bindPhoneInput" 
                       type="number" 
                       placeholder="请输入手机号码" 
                       maxlength="11" />
            </view>

            <!-- 密码输入框 -->
            <view class="input-container">
                <view class="input-label">密码</view>
                <input :value="password" 
                       class="input-field" 
                       @input="bindPasswordInput" 
                       type="password" 
                       placeholder="请输入密码" />
            </view>

            <!-- 忘记密码链接 -->
            <view class="forget-password">忘记密码?</view>

            <!-- 登录按钮 -->
            <view :class="'login-btn '+ (phone.length==11 && password.length>=6 ?'':'disabled')" 
                  @tap="bindPasswordLoginTap">
                登录
            </view>

            <!-- 验证码登录按钮 -->
            <view class="code-login-btn" @tap="logintypeid(3)">
                验证码登录
            </view>

            <!-- 协议同意部分 -->
            <view class="agreement-section">
                <view class="rqla rqlb" v-if="ischecked" @click="onischecked">
                    <image class="rqla1" src="/static/image/login/g2.png"></image>
                </view>
                <view class="rqla" v-if="!ischecked" @click="onischecked"></view>
                阅读并同意<text class="agreement-link" @tap="xieyi('用户服务协议')">《用户服务协议》</text>和<text class="agreement-link" @tap="xieyi('隐私协议')">《隐私协议》</text>
            </view>
        </view>
    </view>
</template>

<script>
import wfc from '../../wfc/client/wfc'
import titles from '../../components/titles.vue'
import Config from '../../config'

import { getItem, setItem, removeItem } from '../util/storageHelper'
import appServerApi from '../../api/appServerApi'
import uiConfig from '../../common/ui-config.js'
import ConnectionStatus from "@/wfc/client/connectionStatus";
export default {
    name: 'LoginPage',
    data() {
        return {
            focus: false,
            phone: '',
            code: '',
            password: '',
            ischecked: false,
            typeid: 2,
            verificationCode: ['', '', '', ''],
            currentIndex: 0,
            uploadimg: {},
            codeclolor: '#313131',
            imageStyles: {
                width: 120,
                height: 120,
                border: {
                    color: '#ffffff',
                    width: 0,
                    style: 'dashed',
                    radius: '0px',
                },
            },
            formData: {
                displayName: '',
                birthday: '选择你的出生日期',
                gender: 1,
                city: '',
                school: '',
            },
            avatarUrl: '/static/image/login/Ellipse2302.png',
            // 验证码获取秒数
            sec: '20', //这是重新获取验证码的倒计时(可根据需求修改)
            codeCorrect: '6666', //正确的验证码
            verifyShow: false, //是否禁用按钮,
            countdown: 0, // 倒计时时间
            timer: null, // 计时器
            countdownSeconds: 60, // 倒计时总时间
            tempMessage: '',
            showSdkTip: false, // 是否显示SDK提示
            userChooseOtherLogin: false, // 用户是否已选择其他登录方式
            lastChooseOtherTime: 0, // 用户最后一次选择其他登录方式的时间戳
        }
    },

    components: {
        titles,
    },
    props: {},

    onShow() {
        console.log('login onShow')
        
        // 检查是否已经登录
        const token = getItem('token');
        const userId = getItem('userId');
        const im_token = getItem('im_token');
        
        // 如果已经登录，检查是否需要跳转
        if (token && userId && im_token) {
            // 检查当前连接状态
            const currentStatus = wfc.getConnectionStatus();
            console.log('LoginPage onShow - 当前连接状态:', currentStatus);
            
            // 检查最后连接时间
            const lastConnectionTime = getItem('last_connection_time');
            const currentTime = new Date().getTime();
            
            if (lastConnectionTime) {
                const timeDiff = currentTime - parseInt(lastConnectionTime);
                const thirtyMinutes = 30 * 60 * 1000; // 30分钟
                
                if (timeDiff < thirtyMinutes || currentStatus === 1) {
                    console.log('已登录且连接正常，跳转到会话列表页');
                    
                    // 确保WFC连接状态
                    if (currentStatus !== ConnectionStatus.ConnectionStatusConnected) {
                        console.log('连接状态不是已连接，尝试重新连接');
                        wfc.connect(userId, im_token);
                    }
                    
                    // 直接跳转到会话列表页，不需要延迟
                    uni.switchTab({
                        url: '/pages/conversationList/ConversationListPage'
                    });
                    
                    return;
                }
            }
        }
        
        if (
            (Config.APP_SERVER.indexOf('wildfirechat') >= 0 &&
                Config.IM_SERVER_HOST.indexOf('wildfirechat') === -1) ||
            (Config.APP_SERVER.indexOf('wildfirechat') === -1 &&
                Config.IM_SERVER_HOST.indexOf('wildfirechat') >= 0)
        ) {
            console.error(
                '!!!! 严重错误!!!! Config.APP_SERVER 和 Config.IM_SERVER_HOST要一起修改，不能一个用官方服务，一个用自己部署的服务'
            )
        } else if (Config.IM_SERVER_HOST.indexOf(':') >= 0) {
            console.error(
                '!!!! 严重错误!!!! Config.IM_SERVER_HOST 不能包含端口，只需要 HOST 即可'
            )
        } else if (Config.IM_SERVER_HOST.indexOf('http') >= 0) {
            console.error(
                '!!!! 严重错误!!!! Config.IM_SERVER_HOST 不能包含http，只需要 HOST 即可'
            )
        }
    },
    created() {
        // 初始化时设置为验证码登录页面，随后唤起一键登录授权页
        this.typeid = 3
        
        // 检查隐私政策同意状态
        const privacyAgreed = getItem('privacy_agreement')
        if (privacyAgreed === 'true') {
            this.ischecked = true
            console.log('已同意隐私政策')
        } else {
            this.ischecked = false
            console.log('未同意隐私政策')
        }
        
        // 检查应用启动标志
        const appJustLaunched = getItem('app_just_launched') === 'true';
        console.log('检查应用启动标志:', appJustLaunched);
        
        if (appJustLaunched) {
            // 应用刚启动，清除用户选择状态，确保新会话能正常唤起授权页
            this.userChooseOtherLogin = false;
            this.lastChooseOtherTime = 0;
            removeItem('userChooseOtherLogin')
            removeItem('lastChooseOtherTime')
            // 清除启动标志，避免重复处理
            removeItem('app_just_launched')
            console.log('应用刚启动，清除用户选择状态，确保新会话能正常唤起授权页');
        } else {
            // 不是应用启动（页面切换），从localStorage恢复状态
            const userChooseOther = getItem('userChooseOtherLogin');
            const lastChooseTime = getItem('lastChooseOtherTime');
            if (userChooseOther === 'true') {
                this.userChooseOtherLogin = true;
                this.lastChooseOtherTime = parseInt(lastChooseTime || '0');
                console.log('页面切换，从localStorage恢复用户选择状态:', this.userChooseOtherLogin, this.lastChooseOtherTime);
            }
        }
    },
    onReady() {
        console.log('onReady 被调用，userChooseOtherLogin:', this.userChooseOtherLogin);
        
        // 检查是否已经登录
        const token = getItem('token');
        const userId = getItem('userId');
        const im_token = getItem('im_token');
        
        // 如果已经登录，并且当前连接状态正常，不调用一键登录
        if (token && userId && im_token) {
            // 检查当前连接状态
            const currentStatus = wfc.getConnectionStatus();
            console.log('LoginPage onReady - 当前连接状态:', currentStatus);
            
            // 检查最后连接时间
            const lastConnectionTime = getItem('last_connection_time');
            const currentTime = new Date().getTime();
            
            if (lastConnectionTime) {
                const timeDiff = currentTime - parseInt(lastConnectionTime);
                const thirtyMinutes = 30 * 60 * 1000; // 30分钟
                
                if (timeDiff < thirtyMinutes || currentStatus === 1) {
                    console.log('已登录且连接正常，不调用一键登录');
                    
                    // 如果当前页面是登录页，但用户已登录，直接跳转到会话列表页
                    const pages = getCurrentPages();
                    const currentPage = pages[pages.length - 1];
                    
                    if (currentPage && currentPage.route && currentPage.route.indexOf('LoginPage') !== -1) {
                        console.log('已登录但在登录页，直接跳转到会话列表页');
                        
                        // 确保WFC连接状态
                        if (currentStatus !== 1) {
                            console.log('连接状态不是已连接，尝试重新连接');
                            wfc.connect(userId, im_token);
                        }
                        
                        uni.switchTab({
                            url: '/pages/conversationList/ConversationListPage'
                        });
                    }
                    
                    return;
                }
            }
        }
        
        // 如果用户已经选择了其他登录方式，不再自动唤起授权页
        const userChooseOther = getItem('userChooseOtherLogin') === 'true';
        const lastChooseTime = parseInt(getItem('lastChooseOtherTime') || '0');
        const currentTime = Date.now();
        const timeSinceLastChoice = currentTime - lastChooseTime;
        
        console.log('onReady localStorage检查 - userChooseOther:', userChooseOther, 'timeSinceLastChoice:', timeSinceLastChoice);
        
        if (userChooseOther || timeSinceLastChoice < 3000) {
            console.log('onReady - 用户已选择其他登录方式或最近刚选择过，不再唤起授权页');
            console.log('userChooseOther:', userChooseOther, 'timeSinceLastChoice:', timeSinceLastChoice);
            return;
        }
        
        // 删除不正确的require语句
        // 延迟执行一键登录，确保页面已完全加载
        setTimeout(() => {
            console.log('setTimeout 中检查 userChooseOtherLogin:', this.userChooseOtherLogin);
            
            // 再次检查标志和时间戳，因为在setTimeout期间标志可能已经被设置
            const userChooseOther = getItem('userChooseOtherLogin') === 'true';
            const lastChooseTime = parseInt(getItem('lastChooseOtherTime') || '0');
            const currentTime = Date.now();
            const timeSinceLastChoice = currentTime - lastChooseTime;
            
            console.log('setTimeout localStorage检查 - userChooseOther:', userChooseOther, 'timeSinceLastChoice:', timeSinceLastChoice);
            
            if (userChooseOther || timeSinceLastChoice < 3000) {
                console.log('setTimeout - 用户已选择其他登录方式或最近刚选择过，不调用_presentLoginController');
                console.log('userChooseOther:', userChooseOther, 'timeSinceLastChoice:', timeSinceLastChoice);
                return;
            }
            // 直接使用_presentLoginController方法，不使用handleOneKeyLogin
            this._presentLoginController();
        }, 500); // 增加延迟时间，给用户操作留出足够时间
    },
    methods: {
        // 返回到授权页面
        backToAuthPage() {
            console.log('返回到授权页面');
            
            // 重置用户选择状态
            this.userChooseOtherLogin = false;
            this.lastChooseOtherTime = 0;
            
            // 同时清除localStorage
            removeItem('userChooseOtherLogin');
            removeItem('lastChooseOtherTime');
            
            // 切换到选择登录方式页面
            //this.typeid = 2;
            
            // 延迟一下再拉起授权页，确保状态已更新
            setTimeout(() => {
                // 拉起授权页
                this._presentLoginController();
            }, 300);
        },
        
        startCountdown() {
            this.countdown = this.countdownSeconds
            if (this.timer) {
                clearInterval(this.timer)
            }
            this.timer = setInterval(() => {
                if (this.countdown > 0) {
                    this.countdown--
                } else {
                    clearInterval(this.timer)
                }
            }, 1000)
        },
        // 输入验证码
        codenum: function (event) {
            console.log('输入的值', event)
            var that = this
            var code = event.detail.value
            that.code = code
            if (code.length == 4) {
                that.focus = false
                console.log('验证码正确：', that.code)
                this.bindLoginTap()
                // if (code == that.codeCorrect) {
                // } else {
                //     that.codeclolor = '#ff0000'
                //     uni.showToast({
                //         title: '验证码错误',
                //         icon: 'none',
                //     })
                //     setTimeout(function () {
                //         that.code = []
                //         event.detail.value = ''
                //         that.codeclolor = '#313131'
                //     }, 1500)
                // }
            }
        },
        // 键盘隐藏后设置失去焦点
        blur: function () {
            var that = this
            that.focus = false
        },
        // 点击自定义光标显示键盘
        codefocus: function (e) {
            var that = this
            //if (e == that.code.length) {
            that.focus = true
            //}
        },
        bindDateChange(e) {
            this.formData.birthday = e.detail.value
        },
        onuploadimg(e) {
            this.uploadimg = e.tempFilePaths[0]
            console.log(this.uploadimg)
            this.avatarUrl = this.uploadimg.path
        },
        async userupload() {
            try {
                // 获取登录 token
                const result = await aLiSDKModule.getLoginToken()
                console.log('获取登录token结果:', result)
                
                if (result.resultCode === '700002') {
                    // 登录成功,获取到 token
                    const token = result.token
                    
                    // 解析用户ID
                    const userId = this.parseUserIdFromToken(token)
                    
                    // 保存登录状态
                    setItem('token', token)
                    setItem('userId', userId)
                    setItem('last_login_time', new Date().getTime().toString())
                    
                    // 确保WFC已初始化
                    if (!wfc.isInitialized) {
                        console.warn('WFC未初始化，尝试主动初始化...')
                        try {
                            const initResult = wfc.init()
                            if (initResult) {
                                console.log('WFC主动初始化成功')
                            } else {
                                console.error('WFC主动初始化失败')
                            }
                        } catch (error) {
                            console.error('WFC主动初始化失败:', error)
                        }
                        
                        // 如果主动初始化失败，继续等待检查
                        return new Promise((resolve) => {
                            let checkCount = 0
                            const maxChecks = 10
                            const checkInterval = setInterval(() => {
                                checkCount++
                                if (wfc.isInitialized || checkCount >= maxChecks) {
                                    clearInterval(checkInterval)
                                    resolve()
                                }
                            }, 500)
                        }).then(() => {
                            if (!wfc.isInitialized) {
                                throw new Error('WFC初始化超时')
                            }
                        })
                    }
                    
                    // 定义连接状态监听函数
                    const handleConnectionStatus = (status) => {
                        console.log('连接状态变化:', status)
                        
                        if (status === 1) { // 连接成功
                            console.log('连接成功，跳转到会话列表')
                            
                            // 保存最后连接时间
                            setItem('last_connection_time', new Date().getTime().toString())
                            
                            // 移除监听器
                            wfc.eventEmitter.off('connectionStatusChanged', handleConnectionStatus)
                            
                            // 直接跳转到会话列表页,不关闭授权页
                            uni.switchTab({
                                url: '/pages/conversationList/ConversationListPage',
                                success: () => {
                                    console.log('跳转到会话列表页成功')
                                },
                                fail: (error) => {
                                    console.error('跳转到会话列表页失败:', error)
                                    uni.showToast({
                                        title: '跳转失败，请重试',
                                        icon: 'none'
                                    })
                                }
                            })
                        } else if (status === -1) { // 连接失败
                            console.error('连接失败')
                            
                            // 移除监听器
                            wfc.eventEmitter.off('connectionStatusChanged', handleConnectionStatus)
                            
                            uni.showToast({
                                title: '连接服务器失败，请重试',
                                icon: 'none',
                            })
                        }
                    }
                    
                    // 监听连接状态
                    wfc.eventEmitter.on('connectionStatusChanged', handleConnectionStatus)
                    
                    // 检查当前连接状态
                    const currentStatus = wfc.getConnectionStatus()
                    console.log('当前连接状态:', currentStatus)
                    
                    // 如果已经连接，直接跳转
                    if (currentStatus === 1) {
                        console.log('已经连接，直接跳转')
                        
                        // 保存最后连接时间
                        setItem('last_connection_time', new Date().getTime().toString())
                        
                        // 设置设备推送标识
                        try {
                            // 获取设备推送标识
                            plus.push.getClientInfoAsync((info) => {
                                if (info && info.clientid) {
                                    console.log('设置设备推送标识:', info.clientid)
                                    wfc.setDeviceToken(7, info.clientid)
                                } else {
                                    console.error('获取设备推送标识失败')
                                }
                            })
                        } catch (error) {
                            console.error('设置设备推送标识失败:', error)
                        }
                        
                        this.go2ConversationList()
                        return
                    } else {
                        // 连接 WFC 服务器
                        console.log('尝试连接WFC服务器...')
                        wfc.connect(userId, token)
                    }
                } else {
                    uni.showToast({
                        title: '登录失败',
                        icon: 'none'
                    })
                }
            } catch (error) {
                console.error('登录过程出错:', error)
                uni.showToast({
                    title: '登录失败',
                    icon: 'none'
                })
            }
        },
        // 从token中解析userId
        parseUserIdFromToken(token) {
            try {
                const base64Url = token.split('.')[1];
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));
                return JSON.parse(jsonPayload).user_id;
            } catch (error) {
                console.error('解析token失败:', error);
                return null;
            }
        },
        onFocus(index) {
            // 当输入框获得焦点时，更新当前聚焦索引
            this.currentIndex = index
        },
        handleInput(index, event) {
            const value = event.detail.value
            if (value.length > 1) {
                this.verificationCode[index] = value.slice(-1)
            } else {
                this.verificationCode[index] = value
            }
            this.code = this.verificationCode.join('')
            if (this.code.length == 4) {
                this.bindLoginTap()
            }
        },
        onischecked: function () {
            this.ischecked = !this.ischecked
            
            // 保存隐私政策同意状态
            if (this.ischecked) {
                setItem('privacy_agreement', 'true')
                // 通知App.vue隐私政策已同意
                uni.$emit('privacy_agreement_changed', true)
                
                // 设置SDK复选框为选中状态
                try {
                    if (aLiSDKModule && aLiSDKModule.setCheckboxIsChecked) {
                        aLiSDKModule.setCheckboxIsChecked(true)
                        console.log('设置SDK复选框为选中状态')
                    }
                } catch (e) {
                    console.error('设置SDK复选框状态失败:', e)
                }
            } else {
                setItem('privacy_agreement', 'false')
                // 通知App.vue隐私政策未同意
                uni.$emit('privacy_agreement_changed', false)
                
                // 设置SDK复选框为未选中状态
                try {
                    if (aLiSDKModule && aLiSDKModule.setCheckboxIsChecked) {
                        aLiSDKModule.setCheckboxIsChecked(false)
                        console.log('设置SDK复选框为未选中状态')
                    }
                } catch (e) {
                    console.error('设置SDK复选框状态失败:', e)
                }
            }
        },
        xieyi: function (e) {
            // uni.showToast({
            //     title: e,
            //     icon: 'none',
            // })
            console.log('e', e)
            var url = 'https://www.baidu.com'
            if (e == '用户服务协议') {
                var url = 'https://web.ykjrhl.com/serviceAgreement.html'
            }
            if (e == '隐私协议') {
                var url = 'https://web.ykjrhl.com/agreement.html'
            }
            uni.navigateTo({
                url: '/pages/login/webview?to=' + url,
                fail: (err) => {
                    console.error('跳转失败:', err)
                    uni.showToast({
                        title: '跳转失败',
                        icon: 'none',
                    })
                },
            })
            // /pages/subPack/webview?to=' + url
        },
        logintypeid: function (e) {
            // 从其他页面返回到选择登录方式界面时，只切换页面类型
            if (e == 2 && (this.typeid == 3 || this.typeid == 5)) {
                // 用户从其他登录方式返回到选择登录方式页面，重置标志
                this.userChooseOtherLogin = false
                this.lastChooseOtherTime = 0
                
                // 同时清除localStorage
                removeItem('userChooseOtherLogin')
                removeItem('lastChooseOtherTime')
                
                console.log('用户返回选择登录方式页面，重置标志')
                this.typeid = e
                return
            }
            
            // 如果用户选择其他手机号登录或密码登录，设置标志
            if (e == 3 || e == 5) {
                this.userChooseOtherLogin = true
                this.lastChooseOtherTime = Date.now()
                
                // 同时保存到localStorage
                setItem('userChooseOtherLogin', 'true')
                setItem('lastChooseOtherTime', this.lastChooseOtherTime.toString())
                
                console.log('用户选择其他登录方式，设置标志避免重新唤起授权页, time:', this.lastChooseOtherTime)
            }
            
            // 如果用户选择一键登录相关选项，重置标志
            if (e == 2 || e == 99) {
                this.userChooseOtherLogin = false
                this.lastChooseOtherTime = 0
                
                // 同时清除localStorage
                removeItem('userChooseOtherLogin')
                removeItem('lastChooseOtherTime')
                
                console.log('用户选择一键登录，重置标志')
            }
            
            // 其他情况保持原有逻辑
            if (e == 98) {
                this.go2ConversationList()
            }
            if (e == 2 || e == 99) {
                this._presentLoginController()
            }
            this.typeid = e
        },
        phonelogin: function (e) {
            this.typeid = e
        },
        bindPhoneInput: function (e) {
            this.phone = e.detail.value
        },
        bindCodeInput: function (e) {
            this.code = e.detail.value
        },
        bindLoginTap: function (e) {
            // 检查用户是否同意隐私政策
            if (!this.ischecked) {
                return uni.showToast({
                    title: '请阅读并同意用户服务协议、隐私协议',
                    icon: 'none',
                })
            }
            
            // 保存隐私政策同意状态
            setItem('privacy_agreement', 'true')
            
            // 通知App.vue隐私政策已同意
            uni.$emit('privacy_agreement_changed', true)
            
            // 登录
            this.login(this.phone, this.code)
        },

        login(phone, code) {
            if (!this.ischecked) {
                return uni.showToast({
                    title: '阅读并同意用户服务协议、隐私协议',
                    icon: 'none',
                })
            }
            
            console.log('login', wfc.getClientId(), Config.getWFCPlatform())
            
            // 显示加载提示
            uni.showLoading({
                title: '登录中...',
                mask: true
            })
            
            appServerApi.loginWithAuthCode(phone, code)
                .then((result) => {
                    console.log('登录成功，返回数据:', result)
                    
                    // 检查登录返回数据完整性
                    if (!result || !result.userId || !result.access_token || !result.im_token) {
                        console.error('登录返回数据不完整:', result)
                        throw new Error('登录返回数据不完整')
                    }
                    
                    // 存储登录信息
                    let userId = result.userId
                    let access_token = result.access_token
                    let im_token = result.im_token
                    
                    console.log('保存登录信息:', { userId, access_token, im_token })
                    setItem('userId', userId)
                    setItem('token', access_token)
                    setItem('im_token', im_token)
                    setItem('last_login_time', new Date().getTime().toString())
                    
                    // 设置最大等待时间
                    const maxWaitTime = 10000 // 10秒
                    const startTime = Date.now()
                    
                    // 定义检查连接状态的函数
                    const checkConnectionStatus = () => {
                        const currentStatus = wfc.getConnectionStatus()
                        console.log('当前连接状态:', currentStatus)
                        
                        // 如果已连接，直接跳转
                        if (currentStatus === 1) {
                            uni.hideLoading()
                            
                            // 设置设备推送标识
                            try {
                                // 获取设备推送标识
                                plus.push.getClientInfoAsync((info) => {
                                    if (info && info.clientid) {
                                        console.log('设置设备推送标识:', info.clientid)
                                        wfc.setDeviceToken(7, info.clientid)
                                    } else {
                                        console.error('获取设备推送标识失败')
                                    }
                                })
                            } catch (error) {
                                console.error('设置设备推送标识失败:', error)
                            }
                            
                            this.go2ConversationList()
                            return
                        }
                        
                        // 如果超时，也跳转
                        if (Date.now() - startTime > maxWaitTime) {
                            console.log('等待连接超时，直接跳转')
                            uni.hideLoading()
                            this.go2ConversationList()
                            return
                        }
                        
                        // 否则继续尝试连接
                        if (!wfc.isInitialized) {
                            try {
                                wfc.init()
                            } catch (error) {
                                console.error('WFC初始化失败:', error)
                            }
                        }
                        
                        // 如果未连接，尝试连接
                        if (currentStatus !== 1 && currentStatus !== 2) {
                            try {
                                wfc.connect(userId, im_token)
                            } catch (error) {
                                console.error('WFC连接失败:', error)
                            }
                        }
                        
                        // 继续检查
                        setTimeout(checkConnectionStatus, 1000)
                    }
                    
                    // 开始检查连接状态
                    checkConnectionStatus()
                })
                .catch((error) => {
                    console.error('登录过程出错:', error)
                    uni.hideLoading()
                    uni.showToast({
                        title: error.message || '登录失败',
                        icon: 'none',
                        duration: 2000
                    })
                })
        },
        
        _presentLoginController() {
            console.log('_presentLoginController 被调用，userChooseOtherLogin:', this.userChooseOtherLogin);
            
            // 从localStorage中获取用户选择状态
            const userChooseOther = getItem('userChooseOtherLogin') === 'true';
            const lastChooseTime = parseInt(getItem('lastChooseOtherTime') || '0');
            
            // 检查用户是否在最近3秒内选择了其他登录方式
            const currentTime = Date.now();
            const timeSinceLastChoice = currentTime - lastChooseTime;
            
            console.log('localStorage检查 - userChooseOther:', userChooseOther, 'timeSinceLastChoice:', timeSinceLastChoice);
            
            if (userChooseOther || timeSinceLastChoice < 3000) {
                console.log('_presentLoginController - 用户已选择其他登录方式或最近刚选择过，不唤起授权页');
                console.log('userChooseOther:', userChooseOther, 'timeSinceLastChoice:', timeSinceLastChoice);
                return;
            }
            
            const config = uiConfig.buildFullscreenAndWebviewBg()
            
            // 检查隐私政策同意状态
            const privacyAgreed = getItem('privacy_agreement')
            if (privacyAgreed === 'true') {
                this.ischecked = true
                console.log('_presentLoginController - 已同意隐私政策')
                
                // 设置SDK复选框为选中状态
                try {
                    if (aLiSDKModule && aLiSDKModule.setCheckboxIsChecked) {
                        aLiSDKModule.setCheckboxIsChecked(true)
                    }
                } catch (e) {
                    console.error('设置SDK复选框状态失败:', e)
                }
            } else {
                this.ischecked = false
                console.log('_presentLoginController - 未同意隐私政策')
            }
            
            uni.showLoading({
                mask: true,
            })
            // 调用该接口首先会弹起授权页，点击授权页的登录按钮获取Token
            aLiSDKModule.getLoginToken(
                5000,
                config,
                (tokenResult) => {
                    uni.hideLoading()
                    this.tempMessage = JSON.stringify(tokenResult)
                    console.log(JSON.stringify(tokenResult))
                    if ('600001' == tokenResult.resultCode) {
                        console.log('授权页拉起成功')
                        // 设置复选框状态
                        aLiSDKModule.setCheckboxIsChecked(false)
                        
                        // 显示提示
                        this.showSdkTip = true
                    } else if ('600000' == tokenResult.resultCode) {
                        console.log(
                            '获取Token成功，接下来拿着结果里面的Token去服务端换取手机号码，SDK服务到此结束',
                            tokenResult.token
                        )
                        this.loginByaliSDK(tokenResult.token)
                    } else {
                        //手动关闭授权页
                        aLiSDKModule.quitLoginPage()
                        console.log('授权页拉起失败或出错，错误码：' + tokenResult.resultCode + ' ' + tokenResult.msg)
                        
                        // 静默处理错误，不显示提示，直接设置typeid=3保持其他手机号登录状态
                        this.hideLogins = true
                        this.typeid = 3
                    }
                },
                (clickResult) => {
                    console.log(JSON.stringify(clickResult))
                    switch (clickResult.resultCode) {
                        case '700000':
                            console.log('用户点击了返回')
                            break
                        case '700001':
                            console.log('用户切换其他登录方式')
                            break
                        case '700002':
                            console.log('用户点击登录按钮')
                            if (!clickResult.result.isChecked) {
                                //Toast样式可参考：https://www.html5plus.org/doc/zh_cn/nativeui.html#plus.nativeUI.toast
                                if (
                                    !config.uiConfig.setPrivacyAlertIsNeedShow
                                ) {
                                    plus.nativeUI.toast('请同意服务条款')
                                }
                            }
                            break
                        case '700003':
                            console.log('用户点击checkBox')
                            this.ischecked  = clickResult.result.isChecked
                            break
                        case '700004':
                            console.log('用户点击协议')
                            break
                        case '700010':
                            //调用userControlAuthPageCancel后方可使用
                            console.log('用户点击返回按钮，Android专用')
                            aLiSDKModule.quitLoginPage()
                            break
                        case '700011':
                            //调用userControlAuthPageCancel后方可使用
                            console.log('用户使用物理返回键，Android专用')
                            aLiSDKModule.quitLoginPage()
                            break
                        case '700006':
                            console.log('弹出二次授权弹窗')
                            break
                        case '700007':
                            console.log('关闭二次授权弹窗')
                            break
                        case '700008':
                            console.log('点击二次授权弹窗确认按钮')
                            // 更新ischecked状态为true
                            this.ischecked = true
                            // 保存隐私政策同意状态
                            setItem('privacy_agreement', 'true')
                            // 通知App.vue隐私政策已同意
                            uni.$emit('privacy_agreement_changed', true)
                            // 设置SDK复选框为选中状态
                            try {
                                if (aLiSDKModule && aLiSDKModule.setCheckboxIsChecked) {
                                    aLiSDKModule.setCheckboxIsChecked(true)
                                    console.log('设置SDK复选框为选中状态')
                                }
                            } catch (e) {
                                console.error('设置SDK复选框状态失败:', e)
                            }
                            break
                        case '700009':
                            console.log('点击二次授权弹窗协议')
                            break
                    }
                },
                (customUiResult) => {
                    console.log(
                        '点击了自定义控件 ' + JSON.stringify(customUiResult)
                    )
                    if ('close' == customUiResult.widgetId) {
                        //点击了自定义的关闭授权页按钮
                        aLiSDKModule.quitLoginPage()
                    } else if ('closePrivacyAlert' == customUiResult.widgetId) {
                        aLiSDKModule.closePrivactAlertView()
                    } else if ('otherPhone' == customUiResult.widgetId) {
                        console.log('点击了其他手机号登录按钮')
                        console.log('设置标志前 userChooseOtherLogin:', this.userChooseOtherLogin)
                        // 设置标志，表示用户已选择其他登录方式
                        this.userChooseOtherLogin = true
                        this.lastChooseOtherTime = Date.now()
                        
                        // 同时保存到localStorage
                        setItem('userChooseOtherLogin', 'true')
                        setItem('lastChooseOtherTime', this.lastChooseOtherTime.toString())
                        
                        console.log('设置标志后 userChooseOtherLogin:', this.userChooseOtherLogin, 'lastChooseOtherTime:', this.lastChooseOtherTime)
                        this.logintypeid(3)
                        aLiSDKModule.quitLoginPage()
                    } else {
                        plus.nativeUI.toast(
                            '点击了自定义按钮，widgetId：' +
                                customUiResult.widgetId
                        )
                    }
                }
            )
        },

        bindAuthCodeTap: function (e) {
            console.log(this.phone)
            this.phone.length
            if (this.phone.length == 11) {
                if (!this.ischecked) {
                    return uni.showToast({
                        title: '阅读并同意用户服务协议、隐私协议',
                        icon: 'none',
                    })
                }
                this.startCountdown()
                return this.authCode(this.phone)
            }
            uni.showToast({
                title: '手机号码错误',
                icon: 'none',
            })
        },

        authCode(phone) {
            appServerApi
                .requestAuthCode(phone)
                .then((result) => {
                    uni.showToast({
                        title: '发送验证码成功',
                        icon: 'none',
                    })
                })
                .catch((reason) => {
                    uni.showToast({
                        title: reason,
                        icon: 'none',
                    })
                })
        },

        go2ConversationList() {
            console.log('LoginPage: 准备跳转到会话列表页面')
            
            // 登录成功，清除用户选择状态，确保下次登录能正常使用一键登录
            this.userChooseOtherLogin = false;
            this.lastChooseOtherTime = 0;
            removeItem('userChooseOtherLogin')
            removeItem('lastChooseOtherTime')
            console.log('登录成功，清除用户选择状态');
            
            try {
                // 直接使用reLaunch强制跳转，不使用switchTab
                console.log('LoginPage: 使用reLaunch直接跳转')
                uni.reLaunch({
                    url: '/pages/conversationList/ConversationListPage',
                    success: () => {
                        console.log('LoginPage: 跳转到会话列表页成功 (reLaunch)')
                    },
                    fail: (err) => {
                        console.error('LoginPage: reLaunch跳转失败:', err)
                        
                        // 如果reLaunch失败，尝试使用switchTab
                        console.log('LoginPage: 尝试使用switchTab方式跳转')
                        uni.switchTab({
                            url: '/pages/conversationList/ConversationListPage',
                            success: () => {
                                console.log('LoginPage: 跳转到会话列表页成功 (switchTab)')
                            },
                            fail: (navErr) => {
                                console.error('LoginPage: switchTab跳转也失败:', navErr)
                                
                                // 最后尝试重启应用
                                console.log('LoginPage: 尝试重启应用')
                                uni.reLaunch({
                                    url: '/pages/SplashPage',
                                    fail: () => {
                                        uni.showToast({
                                            title: '跳转失败，请重新登录',
                                            icon: 'none',
                                            duration: 2000
                                        })
                                    }
                                })
                            }
                        })
                    }
                })
            } catch (error) {
                console.error('LoginPage: 跳转过程中发生异常:', error)
                if (error.stack) {
                    console.error('LoginPage: 错误堆栈:', error.stack)
                }
                
                // 尝试使用reLaunch作为最后的备选方案
                try {
                    console.log('LoginPage: 尝试在异常处理中使用reLaunch')
                    uni.reLaunch({
                        url: '/pages/conversationList/ConversationListPage',
                        fail: () => {
                            console.error('LoginPage: 异常处理中reLaunch失败');
                            // 尝试重启应用
                            uni.reLaunch({
                                url: '/pages/SplashPage'
                            });
                        }
                    });
                } catch (e) {
                    console.error('LoginPage: 在异常处理中reLaunch也失败:', e)
                    uni.showToast({
                        title: '跳转失败，请重新登录',
                        icon: 'none',
                        duration: 2000
                    })
                }
            }
        },

        async loginByaliSDK(authCode) {
            try {
                console.log('开始一键登录，authCode:', authCode ? '已获取' : '未获取')
                console.log('WFC客户端ID:', wfc.getClientId())
                console.log('平台信息:', Config.getWFCPlatform())
                
                // 检查用户是否同意服务条款和隐私政策
                const privacyAgreed = getItem('privacy_agreement')
                
                // 如果存储中没有值，但用户已勾选同意，则保存同意状态
                if (this.ischecked) {
                    console.log('用户已勾选同意隐私政策，但存储中没有值，保存同意状态')
                    !privacyAgreed && setItem('privacy_agreement', 'true')
                } 
                // 如果存储中没有值，且用户未勾选同意，则提示用户
                else if (!privacyAgreed && !this.ischecked) {
                    console.log('用户未同意隐私政策，取消登录')
                    uni.showToast({
                        title: '请先同意服务条款和隐私政策',
                        icon: 'none',
                        duration: 2000
                    })
                    return
                }
                
                // 显示加载提示
                uni.showLoading({
                    title: '登录中...',
                    mask: true
                })
                
                // 调用登录API
                const loginResult = await appServerApi.loginByaliSDK(authCode)
                console.log('登录结果:', loginResult)
                
                // 检查登录返回数据完整性
                if (!loginResult || !loginResult.userId || !loginResult.access_token || !loginResult.im_token) {
                    console.error('登录返回数据不完整:', loginResult)
                    throw new Error('登录返回数据不完整')
                }
                
                // 保存登录信息
                setItem('userId', loginResult.userId)
                setItem('token', loginResult.access_token)
                setItem('im_token', loginResult.im_token)
                setItem('last_login_time', new Date().getTime().toString())
                
                console.log('登录信息已保存，准备连接WFC')
                
                // 检查WFC初始化状态
                const checkWfcInitialized = async () => {
                    const maxAttempts = 10
                    const checkInterval = 500
                    
                    // 先尝试主动初始化一次
                    if (!wfc.isInitialized) {
                        console.log('WFC未初始化，尝试主动初始化')
                        try {
                            const initResult = wfc.init()
                            if (initResult) {
                                console.log('WFC主动初始化成功')
                                return true
                            } else {
                                console.error('WFC主动初始化失败')
                            }
                        } catch (error) {
                            console.error('WFC主动初始化出错:', error)
                        }
                    }
                    
                    // 如果主动初始化失败，继续等待检查
                    for (let i = 0; i < maxAttempts; i++) {
                        console.log(`检查WFC初始化状态: 第${i + 1}次`)
                        if (wfc.isInitialized) {
                            console.log('WFC已初始化')
                            return true
                        }
                        await new Promise(resolve => setTimeout(resolve, checkInterval))
                    }
                    
                    console.error('WFC初始化超时')
                    throw new Error('WFC初始化超时')
                }
                
                // 等待WFC初始化完成
                await checkWfcInitialized()
                
                // 检查当前连接状态
                const connectionStatus = wfc.getConnectionStatus()
                console.log('当前连接状态:', connectionStatus)
                
                // 如果已经连接，直接跳转
                if (connectionStatus === 1) {
                    console.log('WFC已连接，直接跳转')
                    uni.hideLoading()
                    uni.switchTab({
                        url: '/pages/conversationList/ConversationListPage'
                    })
                    
                    // 保存最后连接时间
                    setItem('last_connection_time', new Date().getTime().toString())
                    
                    // 设置设备推送标识
                    try {
                        // 获取设备推送标识
                        plus.push.getClientInfoAsync((info) => {
                            if (info && info.clientid) {
                                console.log('设置设备推送标识:', info.clientid)
                                wfc.setDeviceToken(7, info.clientid)
                            } else {
                                console.error('获取设备推送标识失败')
                            }
                        })
                    } catch (error) {
                        console.error('设置设备推送标识失败:', error)
                    }
                    
                    // 隐藏加载提示
                    uni.hideLoading()
                    
                    // 关闭授权页面
                    try {
                        if (aLiSDKModule && aLiSDKModule.quitLoginPage) {
                            console.log('关闭授权页面')
                            aLiSDKModule.quitLoginPage()
                        }
                    } catch (e) {
                        console.error('关闭授权页面失败:', e)
                    }
                    
                    // 显示成功提示
                    uni.showToast({
                        title: '登录成功',
                        icon: 'success',
                        duration: 1500
                    })
                    
                    return
                }
                
                // 创建连接Promise
                const connectPromise = new Promise((resolve, reject) => {
                    // 设置连接状态监听
                    const statusHandler = (status) => {
                        console.log('连接状态变化:', status)
                        if (status === 1) { // 已连接
                            wfc.eventEmitter.off('connectionStatusChanged', statusHandler)
                            resolve()
                        } else if (status === -1 || status === 3 || status === 4) { // 连接失败或被踢出
                            wfc.eventEmitter.off('connectionStatusChanged', statusHandler)
                            reject(new Error('连接失败，状态码: ' + status))
                        }
                    }
                    
                    // 添加状态监听
                    wfc.eventEmitter.on('connectionStatusChanged', statusHandler)
                    
                    // 开始连接
                    console.log('开始连接WFC:', { userId: loginResult.userId, im_token: loginResult.im_token })
                    wfc.connect(loginResult.userId, loginResult.im_token)
                    
                    // 设置连接超时
                    setTimeout(() => {
                        wfc.eventEmitter.off('connectionStatusChanged', statusHandler)
                        reject(new Error('连接超时'))
                    }, 15000)
                })
                
                try {
                    // 等待连接完成
                    await connectPromise
                    console.log('WFC连接成功')
                    
                    // 保存最后连接时间
                    setItem('last_connection_time', new Date().getTime().toString())
                    
                    // 设置设备推送标识
                    try {
                        // 获取设备推送标识
                        plus.push.getClientInfoAsync((info) => {
                            if (info && info.clientid) {
                                console.log('设置设备推送标识:', info.clientid)
                                wfc.setDeviceToken(7, info.clientid)
                            } else {
                                console.error('获取设备推送标识失败')
                            }
                        })
                    } catch (error) {
                        console.error('设置设备推送标识失败:', error)
                    }
                    
                    // 隐藏加载提示
                    uni.hideLoading()
                    
                    // 关闭授权页面
                    try {
                        if (aLiSDKModule && aLiSDKModule.quitLoginPage) {
                            aLiSDKModule.quitLoginPage()
                        }
                    } catch (e) {
                        console.error('关闭授权页面失败:', e)
                    }
                    
                    // 显示成功提示
                    uni.showToast({
                        title: '登录成功',
                        icon: 'success',
                        duration: 1000
                    })
                    
                    // 直接强制跳转到会话列表，不使用go2ConversationList方法
                    // 延迟一下再跳转，确保授权页面关闭和toast显示完成
                    setTimeout(() => {
                        // 再次确认授权页面已关闭
                        try {
                            if (aLiSDKModule && aLiSDKModule.quitLoginPage) {
                                aLiSDKModule.quitLoginPage()
                            }
                        } catch (e) {
                            console.error('再次关闭授权页面失败:', e)
                        }
                        
                        // 直接使用reLaunch强制跳转
                        uni.reLaunch({
                            url: '/pages/conversationList/ConversationListPage',
                            success: () => {
                                console.log('强制跳转到会话列表页成功')
                            },
                            fail: (err) => {
                                console.error('强制跳转失败:', err)
                                // 如果reLaunch失败，尝试使用switchTab
                                uni.switchTab({
                                    url: '/pages/conversationList/ConversationListPage',
                                    fail: () => {
                                        console.error('switchTab也失败，尝试重启应用')
                                        // 最后尝试重启应用
                                        uni.reLaunch({
                                            url: '/pages/SplashPage'
                                        })
                                    }
                                })
                            }
                        })
                    }, 1500) // 增加延迟时间，确保授权页面关闭
                } catch (error) {
                    console.error('WFC连接失败:', error)
                    
                    // 关闭授权页面
                    try {
                        if (aLiSDKModule && aLiSDKModule.quitLoginPage) {
                            console.log('连接失败，关闭授权页面')
                            aLiSDKModule.quitLoginPage()
                        }
                    } catch (e) {
                        console.error('关闭授权页面失败:', e)
                    }
                    
                    // 清除登录信息
                    removeItem('userId')
                    uni.removeStorageSync('token')
                    uni.removeStorageSync('im_token')
                    
                    throw error
                }
            } catch (error) {
                console.error('登录过程出错:', error)
                
                // 关闭授权页面
                try {
                    if (aLiSDKModule && aLiSDKModule.quitLoginPage) {
                        console.log('登录出错，关闭授权页面')
                        aLiSDKModule.quitLoginPage()
                    }
                } catch (e) {
                    console.error('关闭授权页面失败:', e)
                }
                
                // 隐藏加载提示
                uni.hideLoading()
                
                // 显示错误提示
                uni.showToast({
                    title: error.message || '登录失败',
                    icon: 'none',
                    duration: 2000
                })
            }
        },
        // 直接关闭授权页并切换到手机号登录页面
        closeAuthPageAndSwitchToPhoneLogin() {
            
            // 关闭授权页
            try {
                if (aLiSDKModule && aLiSDKModule.quitLoginPage) {
                    console.log('关闭授权页')
                    aLiSDKModule.quitLoginPage()
                }
            } catch (e) {
                console.error('关闭授权页失败:', e)
            }
            
            // 切换到手机号登录页面
            setTimeout(() => {
                console.log('切换到手机号登录页面')
                this.typeid = 3
                
                // 确保UI更新
                this.$nextTick(() => {
                    console.log('UI已更新，当前typeid:', this.typeid)
                })
            }, 500)
        },
        
        // 处理SDK授权页面问题
        handleSdkIssue() {
            console.log('处理SDK授权页面问题')
            // 设置标志，表示用户已选择其他登录方式
            this.userChooseOtherLogin = true
            this.lastChooseOtherTime = Date.now()
            
            // 同时保存到localStorage
            setItem('userChooseOtherLogin', 'true')
            setItem('lastChooseOtherTime', this.lastChooseOtherTime.toString())
            
            this.closeAuthPageAndSwitchToPhoneLogin()
        },
        // 连接WFC
        connectToWFC() {
            return new Promise(async (resolve, reject) => {
                try {
                // 获取当前连接状态
                    const connectionStatus = wfc.getConnectionStatus();
                    console.log('当前连接状态:', connectionStatus);
                
                // 如果已经连接，直接返回
                if (connectionStatus === 1) {
                        console.log('WFC已连接，无需重新连接');
                        resolve();
                        return;
                }
                
                // 获取连接参数
                    const userId = getItem('userId');
                    const im_token = getItem('im_token');
                
                // 检查连接参数
                if (!userId || !im_token) {
                        console.error('连接WFC失败: 登录信息不完整', { userId, im_token });
                        reject(new Error('登录信息不完整'));
                        return;
                    }
                    
                    console.log('连接WFC参数:', { userId, im_token });
                    
                    // 检查WFC初始化状态并确保初始化完成
                    await this.ensureWfcInitialized();
                    
                    // 创建连接Promise
                    console.log('创建连接Promise处理');
                    const connectHandler = () => {
                        return new Promise((resolveConnect, rejectConnect) => {
                // 设置连接状态监听
                const statusHandler = (status) => {
                                console.log('WFC连接状态变化:', status);
                    if (status === 1) { // 已连接
                                    console.log('WFC连接成功!');
                                    wfc.eventEmitter.off('connectionStatusChanged', statusHandler);
                                    resolveConnect();
                    } else if (status === -1 || status === 3 || status === 4) { // 连接失败或被踢出
                                    console.error('WFC连接失败，状态码:', status);
                                    wfc.eventEmitter.off('connectionStatusChanged', statusHandler);
                                    rejectConnect(new Error('连接失败, 状态码: ' + status));
                    }
                            };
                
                // 添加状态监听
                            wfc.eventEmitter.on('connectionStatusChanged', statusHandler);
                
                // 开始连接
                            console.log('开始连接WFC:', { userId });
                            wfc.connect(userId, im_token);
                
                            // 设置连接超时 (略长一点)
                setTimeout(() => {
                                if (wfc.getConnectionStatus() !== 1) {
                                    console.error('WFC连接超时');
                                    wfc.eventEmitter.off('connectionStatusChanged', statusHandler);
                                    rejectConnect(new Error('连接超时'));
                                }
                            }, 20000);
                        });
                    };
                    
                    // 尝试多次连接
                    let attemptCount = 0;
                    const maxAttempts = 3;
                    let lastError = null;
                    
                    while (attemptCount < maxAttempts) {
                        attemptCount++;
                        console.log(`尝试连接WFC (${attemptCount}/${maxAttempts})...`);
                        
                        try {
                            await connectHandler();
                            console.log(`连接尝试 ${attemptCount} 成功!`);
                            
                            // 保存最后连接时间
                            setItem('last_connection_time', new Date().getTime().toString());
                            
                            // 返回成功
                            resolve();
                            return;
                        } catch (error) {
                            console.error(`连接尝试 ${attemptCount} 失败:`, error);
                            lastError = error;
                            
                            // 如果不是最后一次尝试，等待后重试
                            if (attemptCount < maxAttempts) {
                                const delay = 2000 * attemptCount;
                                console.log(`${delay}ms后重试...`);
                                await new Promise(r => setTimeout(r, delay));
                            }
                        }
                    }
                    
                    // 所有尝试失败
                    console.error('连接WFC失败，已达到最大尝试次数');
                    reject(lastError || new Error('连接WFC失败，已达到最大尝试次数'));
                } catch (error) {
                    console.error('连接WFC过程中发生异常:', error);
                    reject(error);
                }
            });
        },
        
        // 确保WFC初始化完成
        async ensureWfcInitialized() {
            if (wfc.isInitialized) {
                console.log('WFC已初始化');
                return true;
            }
            
            console.warn('WFC未初始化，尝试初始化...');
            try {
                const initResult = wfc.init();
                console.log('WFC初始化结果:', initResult);
                
                if (initResult) {
                    return true;
                }
            } catch (error) {
                console.error('WFC主动初始化失败:', error);
            }
            
            // 如果主动初始化失败，继续等待检查
            console.log('等待WFC自动初始化...');
            const maxAttempts = 10;
            const checkInterval = 500;
            
            for (let i = 0; i < maxAttempts; i++) {
                console.log(`检查WFC初始化状态: 第${i + 1}次`);
                if (wfc.isInitialized) {
                    console.log('WFC已初始化');
                    return true;
                }
                await new Promise(resolve => setTimeout(resolve, checkInterval));
            }
            
            throw new Error('WFC初始化超时');
        },
        bindPasswordInput: function(e) {
            this.password = e.detail.value;
        },
        
        // 账号密码登录
        bindPasswordLoginTap: function() {
            // 检查用户是否同意隐私政策
            if (!this.ischecked) {
                return uni.showToast({
                    title: '请阅读并同意用户服务协议、隐私协议',
                    icon: 'none',
                });
            }
            
            // 检查手机号和密码
            if (this.phone.length !== 11) {
                return uni.showToast({
                    title: '请输入正确的手机号码',
                    icon: 'none',
                });
            }
            
            if (this.password.length < 6) {
                return uni.showToast({
                    title: '密码长度不能少于6位',
                    icon: 'none',
                });
            }
            
            // 保存隐私政策同意状态
            setItem('privacy_agreement', 'true');
            
            // 通知App.vue隐私政策已同意
            uni.$emit('privacy_agreement_changed', true);
            
            // 显示加载提示
            uni.showLoading({
                title: '登录中...',
                mask: true
            });
            
            // 使用与验证码登录类似的直接调用方式
            this.loginWithPassword(this.phone, this.password);
        },
        
        // 密码登录实现 - 参考验证码登录实现
        loginWithPassword(phone, password) {
            console.log('loginWithPassword', wfc.getClientId(), Config.getWFCPlatform());
            
            // 显示加载提示
            uni.showLoading({
                title: '登录中...',
                mask: true
            });
            
            // 准备请求参数
            const clientId = wfc.getClientId();
            const platform = Config.getWFCPlatform();
            
            // 创建一个Promise以便使用与验证码登录相同的处理链
            new Promise((resolve, reject) => {
                uni.request({
                    url: Config.APP_SERVER + '/auth/phone',
                    method: 'POST',
                    data: {
                        phone: phone,
                        clientId: clientId,
                        platform: platform,
                        password: password
                    },
                    success: (res) => {
                        console.log('密码登录响应:', res);
                        
                        if (res.statusCode === 200 && res.data && res.data.code === 200) {
                            const responseData = res.data.data;
                            
                            // 检查登录返回数据完整性
                            if (!responseData.user_id || !responseData.access_token || !responseData.im_token) {
                                console.error('登录返回数据不完整:', responseData);
                                reject(new Error('登录返回数据不完整'));
                                return;
                            }
                            
                            // 构造与验证码登录相同的结果格式
                            const result = {
                                userId: responseData.user_id,
                                access_token: responseData.access_token,
                                im_token: responseData.im_token
                            };
                            
                            resolve(result);
                        } else {
                            // 登录失败
                            const errorMsg = res.data && res.data.msg ? res.data.msg : '登录失败，请检查账号密码';
                            reject(new Error(errorMsg));
                        }
                    },
                    fail: (err) => {
                        console.error('密码登录请求失败:', err);
                        reject(new Error('网络错误，请稍后重试'));
                    }
                });
            })
            .then((result) => {
                console.log('登录成功，返回数据:', result);
                
                // 存储登录信息
                let userId = result.userId;
                let access_token = result.access_token;
                let im_token = result.im_token;
                
                console.log('保存登录信息:', { userId, access_token, im_token });
                setItem('userId', userId);
                setItem('token', access_token);
                setItem('im_token', im_token);
                setItem('last_login_time', new Date().getTime().toString());
                
                // 检查 WFC 初始化状态
                if (!wfc.isInitialized) {
                    console.warn('WFC未初始化，尝试主动初始化...');
                    try {
                        const initResult = wfc.init();
                        if (initResult) {
                            console.log('WFC主动初始化成功');
                        } else {
                            console.error('WFC主动初始化失败');
                        }
                    } catch (error) {
                        console.error('WFC主动初始化失败:', error);
                    }
                    
                    // 如果主动初始化失败，继续等待检查
                    return new Promise((resolve) => {
                        let checkCount = 0;
                        const maxChecks = 5; // 减少检查次数以加快登录流程
                        const checkInterval = setInterval(() => {
                            checkCount++;
                            if (wfc.isInitialized || checkCount >= maxChecks) {
                                clearInterval(checkInterval);
                                resolve();
                            }
                        }, 300); // 减少等待时间
                    }).then(() => {
                        if (!wfc.isInitialized) {
                            console.warn('WFC初始化超时，但将继续登录流程');
                        }
                        // 即使初始化失败也继续登录流程
                        return Promise.resolve();
                    });
                }
                return Promise.resolve();
            })
            .then(() => {
                // 连接WFC - 使用简化版本的connectToWFC，与验证码登录相同的实现
                return this.simpleConnectToWFC()
            })
            .catch((error) => {
                console.error('登录过程出错:', error);
                
                // 隐藏加载提示
                uni.hideLoading();
                
                // 显示错误提示
                uni.showToast({
                    title: error.message || '登录失败',
                    icon: 'none',
                    duration: 2000
                });
                
                // 如果是在存储用户信息后出错，尝试直接进入主页面
                const userId = getItem('userId');
                const token = getItem('token');
                const im_token = getItem('im_token');
                
                if (userId && token && im_token) {
                    console.log('尽管有错误，但用户信息已保存，尝试进入会话列表');
                    setTimeout(() => {
                        this.go2ConversationList();
                    }, 2000);
                } else {
                    // 清除登录信息
                    removeItem('userId');
                    removeItem('token');
                    removeItem('im_token');
                }
            });
        },
        
        // 简化版的connectToWFC，与验证码登录使用的相同实现
        simpleConnectToWFC() {
            return new Promise((resolve, reject) => {
                // 首先检查网络状态
                this.checkNetworkStatus().then((isConnected) => {
                    if (!isConnected) {
                        console.error('网络连接不可用');
                        uni.showToast({
                            title: '网络连接不可用，请检查网络设置',
                            icon: 'none',
                            duration: 3000
                        });
                        // 即使网络不可用，也尝试进入会话列表
                        setTimeout(() => {
                            this.go2ConversationList();
                            resolve();
                        }, 1000);
                        return;
                    }
                    
                    // 获取当前连接状态
                    const connectionStatus = wfc.getConnectionStatus();
                    console.log('当前连接状态:', connectionStatus);
                    
                    // 如果已经连接，直接返回
                    if (connectionStatus === 1) {
                        console.log('WFC已连接，无需重新连接');
                        
                        // 隐藏加载提示
                        uni.hideLoading();
                        
                        // 跳转到会话列表页
                        this.go2ConversationList();
                        resolve();
                        return;
                    }
                    
                    // 获取连接参数
                    const userId = getItem('userId');
                    const im_token = getItem('im_token');
                    
                    // 检查连接参数
                    if (!userId || !im_token) {
                        console.error('连接WFC失败: 登录信息不完整', { userId, im_token });
                        reject(new Error('登录信息不完整'));
                        return;
                    }
                    
                    console.log('连接WFC参数:', { userId, im_token });
                    
                    // 设置连接超时定时器（10秒）
                    const timeoutTimer = setTimeout(() => {
                        console.log('连接WFC超时，尝试继续导航');
                        wfc.eventEmitter.off('connectionStatusChanged', statusHandler);
                        
                        // 即使连接超时，也尝试进入会话列表
                        uni.hideLoading();
                        
                        // 显示提示
                        uni.showToast({
                            title: '连接超时，可能影响消息收发',
                            icon: 'none',
                            duration: 2000
                        });
                        
                        // 尝试进入会话列表
                        setTimeout(() => {
                            this.go2ConversationList();
                            resolve();
                        }, 1000);
                    }, 10000);
                    
                    // 监听连接状态
                    const statusHandler = (status) => {
                        console.log('连接状态变化:', status);
                        if (status === 1) { // 已连接
                            // 清除超时定时器
                            clearTimeout(timeoutTimer);
                            
                            wfc.eventEmitter.off('connectionStatusChanged', statusHandler);
                            
                            // 保存最后连接时间
                            setItem('last_connection_time', new Date().getTime().toString());
                            
                            // 设置设备推送标识
                            try {
                                // 获取设备推送标识
                                plus.push.getClientInfoAsync((info) => {
                                    if (info && info.clientid) {
                                        console.log('设置设备推送标识:', info.clientid)
                                        wfc.setDeviceToken(7, info.clientid)
                                    } else {
                                        console.error('获取设备推送标识失败')
                                    }
                                })
                            } catch (error) {
                                console.error('设置设备推送标识失败:', error)
                            }
                
                            // 隐藏加载提示
                            uni.hideLoading();
                            
                            // 显示连接成功提示
                            uni.showToast({
                                title: '连接成功',
                                icon: 'success',
                                duration: 1000
                            });
                            
                            // 跳转到会话列表页
                            setTimeout(() => {
                                this.go2ConversationList();
                                resolve();
                            }, 500);
                        } else if (status === 2) { // 连接中
                            console.log('正在连接WFC...');
                        } else if (status === -1) { // 连接失败
                            // 清除超时定时器
                            clearTimeout(timeoutTimer);
                            
                            console.error('连接WFC失败');
                            wfc.eventEmitter.off('connectionStatusChanged', statusHandler);
                            
                            // 隐藏加载提示
                            uni.hideLoading();
                            
                            // 显示错误提示
                            uni.showToast({
                                title: '连接失败，将继续尝试登录',
                                icon: 'none',
                                duration: 2000
                            });
                            
                            // 尝试进入会话列表，即使连接失败
                            setTimeout(() => {
                                this.go2ConversationList();
                                resolve();
                            }, 1000);
                        }
                    };
                    
                    // 注册连接状态监听
                    wfc.eventEmitter.on('connectionStatusChanged', statusHandler);
                    
                    // 显示连接提示
                    uni.showLoading({
                        title: '正在连接...'
                    });
                    
                    // 开始连接
                    wfc.connect(userId, im_token);
                });
            });
        },
        
        // 处理登录成功后的操作
        async handleLoginSuccess() {
            try {
                // 显示加载提示
                uni.showLoading({
                    title: '连接中...',
                    mask: true
                });
                
                // 确保WFC初始化完成
                await this.ensureWfcInitialized();
                
                // 检查当前连接状态
                const connectionStatus = wfc.getConnectionStatus();
                console.log('当前连接状态:', connectionStatus);
                
                // 如果已经连接，直接跳转
                if (connectionStatus === 1) {
                    console.log('WFC已连接，直接跳转');
                    
                    // 保存最后连接时间
                    setItem('last_connection_time', new Date().getTime().toString());
                    
                    // 隐藏加载提示
                    uni.hideLoading();
                
                // 显示成功提示
                uni.showToast({
                    title: '登录成功',
                    icon: 'success',
                    duration: 1500
                    });
                
                    // 延迟跳转，确保提示显示完成
                setTimeout(() => {
                        this.navigateAfterLogin();
                    }, 1500);
                    
                    return;
                }
                
                // 尝试连接WFC
                try {
                    await this.connectToWFC();
                    console.log('WFC连接成功');
                    
                    // 设置设备推送标识
                    try {
                        // 获取设备推送标识
                        plus.push.getClientInfoAsync((info) => {
                            if (info && info.clientid) {
                                console.log('设置设备推送标识:', info.clientid)
                                wfc.setDeviceToken(7, info.clientid)
                            } else {
                                console.error('获取设备推送标识失败')
                            }
                        })
                    } catch (error) {
                        console.error('设置设备推送标识失败:', error)
                    }
                    
                    // 隐藏加载提示
                    uni.hideLoading();
                    
                    // 显示成功提示
                    uni.showToast({
                        title: '登录成功',
                        icon: 'success',
                        duration: 1500
                    });
                    
                    // 延迟跳转，确保提示显示完成
                    setTimeout(() => {
                        this.navigateAfterLogin();
                    }, 1500);
                } catch (error) {
                    console.error('WFC连接失败:', error);
                    
                    // 尝试做最后的挽救，直接跳转
                    this.navigateAfterLogin();
                }
            } catch (error) {
                console.error('登录后处理出错:', error);
                
                // 隐藏加载提示
                uni.hideLoading();
                
                // 显示错误提示
                uni.showToast({
                    title: error.message || '连接服务器失败',
                    icon: 'none',
                    duration: 2000
                });
            }
        },
        
        // 登录后导航到会话列表页
        navigateAfterLogin() {
            console.log('准备跳转到会话列表页...');
            
            // 使用多种跳转方式，增加成功率
            try {
                // 先尝试reLaunch方式
                uni.reLaunch({
                    url: '/pages/conversationList/ConversationListPage',
                    success: () => {
                        console.log('跳转到会话列表页成功 (reLaunch)');
                    },
                    fail: (err) => {
                        console.error('reLaunch跳转失败:', err);
                        
                        // 如果reLaunch失败，尝试switchTab
                        uni.switchTab({
                            url: '/pages/conversationList/ConversationListPage',
                            success: () => {
                                console.log('跳转到会话列表页成功 (switchTab)');
                            },
                            fail: (navErr) => {
                                console.error('switchTab跳转也失败:', navErr);
                                
                                // 最后尝试重启应用
                                uni.reLaunch({
                                    url: '/pages/SplashPage'
                                });
                            }
                        });
                    }
                });
            } catch (error) {
                console.error('跳转过程中发生异常:', error);
                
                // 尝试直接使用go2ConversationList方法
                this.go2ConversationList();
            }
        },
        // 新增：检查网络状态
        checkNetworkStatus() {
            return new Promise((resolve) => {
                uni.getNetworkType({
                    success: (res) => {
                        console.log('当前网络状态:', res.networkType);
                        const isConnected = res.networkType !== 'none';
                        resolve(isConnected);
                    },
                    fail: () => {
                        console.error('获取网络状态失败');
                        resolve(false);
                    }
                });
            });
        },
        
        // 新增：处理重连
        handleReconnect() {
            this.checkNetworkStatus().then((isConnected) => {
                if (!isConnected) {
                    uni.showToast({
                        title: '网络连接不可用，请检查网络设置',
                        icon: 'none',
                        duration: 3000
                    });
                    return;
                }
                
                const userId = getItem('userId');
                const im_token = getItem('im_token');
                
                if (!userId || !im_token) {
                    console.error('重连失败: 登录信息不完整');
                    return;
                }
                
                uni.showLoading({
                    title: '正在重新连接...',
                    mask: true
                });
                
                // 重新连接
                wfc.connect(userId, im_token);
                
                // 5秒后检查连接状态
                setTimeout(() => {
                    const status = wfc.getConnectionStatus();
                    uni.hideLoading();
                    
                    if (status === 1) {
                        uni.showToast({
                            title: '连接成功',
                            icon: 'success',
                            duration: 1500
                        });
                    } else {
                        uni.showToast({
                            title: '连接仍然失败，请稍后再试',
                            icon: 'none',
                            duration: 2000
                        });
                    }
                }, 5000);
            });
        },
        handleOneKeyLogin() {
            // 处理一键登录
            // 参考：https://uniapp.dcloud.net.cn/univerify.html
            const UIStyle = uiConfig.buildFullscreenAndWebviewBg()
            uni.login({
                provider: 'univerify',
                univerifyStyle: UIStyle,
                success: (res) => {
                    console.log('一键登录成功', res)
                    this.hideLogins = true
                    this.typeid = 99
                    this.loginByPhoneToken(res.authResult.access_token)
                },
                fail: (res) => {
                    console.log('一键登录失败', res)
                    // 静默处理登录失败，不显示任何提示，直接设置typeid=3
                    this.hideLogins = true
                    this.typeid = 3
                }
            })
        },
        goToOtherPhoneLogin() {
            // 跳转到其他手机号登录页面
            this.hideLogins = true
            this.typeid = 3  // 改为typeid=3，以保证在一键登录授权页拉起失败时状态不会变成typeid=2
        },
        goToPasswordLogin() {
            // 跳转到账号密码登录页面
            this.hideLogins = true
            this.typeid = 1
        },
        // 微信登录方法
        handleWechatLogin() {
            // 检查用户是否同意隐私政策
            if (!this.ischecked) {
                return uni.showToast({
                    title: '请阅读并同意用户服务协议、隐私协议',
                    icon: 'none',
                });
            }
            
            // 显示加载提示
            uni.showLoading({
                title: '登录中...',
                mask: true
            });
            
            // 检查是否支持微信登录
            uni.getProvider({
                service: 'oauth',
                success: (res) => {
                    console.log("可选登录方式：：：：",res.providers)
                    if (!res.providers.find(i=>i.id==='weixin')) {
                        uni.hideLoading();
                        uni.showToast({
                            title: '当前环境不支持微信登录',
                            icon: 'none'
                        });
                        return;
                    }
                    
                    // 调用微信登录
                    uni.login({
                        provider: 'weixin',
                        success: (loginRes) => {
                            console.log('微信登录成功:', loginRes);
                            
                            // 获取用户信息
                            uni.getUserInfo({
                                provider: 'weixin',
                                success: (userInfoRes) => {
                                    console.log('获取用户信息成功:', userInfoRes);
                                    
                                    // 调用后端接口进行登录
                                    this.loginWithWechat(loginRes.code, userInfoRes.userInfo);
                                },
                                fail: (err) => {
                                    console.error('获取用户信息失败:', err);
                                    uni.hideLoading();
                                    uni.showToast({
                                        title: '获取用户信息失败',
                                        icon: 'none'
                                    });
                                }
                            });
                        },
                        fail: (err) => {
                            console.error('微信登录失败:', err);
                            uni.hideLoading();
                            uni.showToast({
                                title: '微信登录失败',
                                icon: 'none'
                            });
                        }
                    });
                },
                fail: (err) => {
                    console.error('获取服务商失败:', err);
                    uni.hideLoading();
                    uni.showToast({
                        title: '获取服务商失败',
                        icon: 'none'
                    });
                }
            });
        },
        
        // 使用微信登录信息调用后端接口
        loginWithWechat(code, userInfo) {
            // 准备请求参数
            const clientId = wfc.getClientId();
            const platform = Config.getWFCPlatform();
            //TODO 修改为真实的 接口登录
            // 调用后端登录接口
            uni.request({
                url: Config.APP_SERVER + '/auth/wechat',
                method: 'POST',
                data: {
                    code: code,
                    clientId: clientId,
                    platform: platform,
                    userInfo: userInfo
                },
                success: (res) => {
                    console.log('微信登录响应:', res);
                    
                    if (res.statusCode === 200 && res.data && res.data.code === 200) {
                        const responseData = res.data.data;
                        
                        // 检查登录返回数据完整性
                        if (!responseData.user_id || !responseData.access_token || !responseData.im_token) {
                            console.error('登录返回数据不完整:', responseData);
                            throw new Error('登录返回数据不完整');
                        }
                        
                        // 保存登录信息
                        setItem('userId', responseData.user_id);
                        setItem('token', responseData.access_token);
                        setItem('im_token', responseData.im_token);
                        setItem('last_login_time', new Date().getTime().toString());
                        
                        // 保存隐私政策同意状态
                        setItem('privacy_agreement', 'true');
                        
                        // 通知App.vue隐私政策已同意
                        uni.$emit('privacy_agreement_changed', true);
                        
                        // 连接WFC
                        this.simpleConnectToWFC().then(() => {
                            // 在连接成功后设置设备推送标识
                            try {
                                // 获取设备推送标识
                                plus.push.getClientInfoAsync((info) => {
                                    if (info && info.clientid) {
                                        console.log('设置设备推送标识:', info.clientid)
                                        wfc.setDeviceToken(7, info.clientid)
                                    } else {
                                        console.error('获取设备推送标识失败')
                                    }
                                })
                            } catch (error) {
                                console.error('设置设备推送标识失败:', error)
                            }
                        });
                    } else {
                        // 登录失败
                        const errorMsg = res.data && res.data.msg ? res.data.msg : '登录失败';
                        throw new Error(errorMsg);
                    }
                },
                fail: (err) => {
                    console.error('微信登录请求失败:', err);
                    throw new Error('网络错误，请稍后重试');
                }
            }).catch((error) => {
                console.error('微信登录过程出错:', error);
                
                // 隐藏加载提示
                uni.hideLoading();
                
                // 显示错误提示
                uni.showToast({
                    title: error.message || '登录失败',
                    icon: 'none',
                    duration: 2000
                });
            });
        },
    },
}
</script>
<style lang="scss" scoped>
.rqla {
    width: 17px;
    height: 17px;
    background-color: #ffffff;
    border-radius: 50%;
    border: 1px solid #999;
    margin-right: 5px;

    .rqla1 {
        width: 12px;
        height: 12px;
    }
}

.rqlb {
    border: 1px solid #386bf6;
    background-color: #386bf6;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rq-4b {
    background-image: url('../../static/image/login/Ellipse2302.png');
    width: 108px;
    height: 108px;
    border-radius: 50%;
}

.custom-checkbox {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
}

.custom-checkbox input[type='checkbox'] {
    opacity: 0;
    position: absolute;
    cursor: pointer;
}

.custom-checkbox::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    /* 设置为圆形 */
    background-color: white;
    border: 1px solid #ccc;
    transition: all 0.3s;
}

.custom-checkbox input[type='checkbox']:checked::before {
    background-color: #4caf50;
    /* 选中时的背景色 */
    border-color: #4caf50;
}

.custom-checkbox::after {
    content: '';
    position: absolute;
    display: none;
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 3px 3px 0;
    transform: rotate(45deg);
}

.custom-checkbox input[type='checkbox']:checked::after {
    display: block;
}

.rq-back {
    background-image: url('../../static/image/login/chevron-down-1.png');
    width: 8px;
    height: 13px;
    background-size: 100% auto;
}

.rq-l97 {
    display: flex;
    align-items: center;
}

.rq-l98 {
    width: 26px;
    height: 28px;
    margin-left: 10px;
}

.rq-l99 {
    position: absolute;
    top: 0;
    right: 20px;
    width: 188px;
    height: 202px;
    background: url(/static/image/login/bcbg.png);
}

.cinput {
    position: fixed;
    left: -100rpx;
    width: 50rpx;
    height: 50rpx;
}

.rq-4c {
    margin: 30rpx 0;

    .rq-4c1 {
        font-size: 14px;
        color: #999;
    }

    .rq-4c2 {
        font-size: 36rpx;
        color: #000;
        border: 1px solid #e4e4e4;
        border-radius: 10px;
        padding: 10rpx 30rpx;
        margin-top: 10rpx;
        height: 82rpx;
        align-items: center;
        display: flex;

        .rq-4cinp {
            font-size: 36rpx;
            width: 100%;
            color: #000;
        }
    }

    .rq-4c3 {
        margin-top: 10rpx;
        display: flex;

        .rq-4c3m {
            background: #f5f5f5;
            justify-content: space-around;
            align-items: center;
            display: flex;
            width: 184rpx;
            height: 82rpx;
            border-radius: 10px;
        }

        .rq-4c3m:first-child {
            margin-right: 20rpx;
        }

        .rq-4c3mnn {
            color: #386bf6;
        }

        .rq-4c3mnv {
            color: #ff77a4;
        }

        .rq-4c3xnn {
            background: #386bf6;
            color: #fff;
        }

        .rq-4c3xnv {
            background: #ff77a4;
            color: #fff;
        }
    }
}

.avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 10px;
}

.rq-4a {
    color: #000;
    font-family: MiSans;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.rq-3b {
    justify-content: space-around;
    align-items: center;
    display: flex;
    margin: 40rpx 0;
}

.rq-3b > view {
    text-align: center;
    width: 48px;
    height: 57px;
    background-color: #f5f5f5;
    border-radius: 10rpx;
    font-size: 24px;
    border: 1px solid #f1f1f1;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #000;
    font-family: MiSans;
}

.rq-3a {
    width: 100%;
    margin: 80rpx 0 20rpx;
    padding-bottom: 10rpx;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .rq-3a1 {
        width: 72rpx;
        height: 36rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20rpx;
        color: #999;
        background-color: #f5f5f5;
        border-radius: 36rpx;
    }

    .rq-3a2 {
        font-size: 32rpx;
        width: 50%;
    }

    .rq-3a3 {
        font-size: 28rpx;
        color: #acbeff;
    }

    .rq-3a3x {
        color: #386bf6;
        z-index: 50;
    }
}

.rq-l6-a {
    display: flex;
    justify-content: center;
    flex-direction: column;
}

.rq-l6-3 {
    width: 100%;
    text-align: center;
    color: #999999;
    font-size: 14px;
}

.rq-l6-2 {
    margin: 23px auto 0 auto;
}

.rq-l6-2img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.rq-l8 {
    color: #000;
    font-family: MiSans;
    font-size: 28px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.rq-l9 {
    font-size: 14px;
    margin-top: 7px;
    font-weight: 400;
    color: #999999;
}

.rq-l10 {
    font-size: 28px;
    margin-top: 92px;
    color: #000;
    text-align: center;
    font-weight: 500;
}

.rq-l11 {
    font-size: 14px;
    margin-top: 7px;
    font-weight: 400;
    color: #999999;
    text-align: center;
}

.rq-l12 {
    padding-top: 128px;
}

.rq-l13 {
    padding-top: 22px;
}

.page-body {
    padding: 40rpx;
    height: 100vh;
}

.page-body1 {
    background: #ffffff;
    /* 		background:linear-gradient(#51b5fe, #ffffff); */
    position: relative;
}

.rq-l1 {
    margin: 80rpx 0px 20rpx auto;
    padding: 10rpx 30rpx;
    background: #48a2e2;
    border-radius: 10rpx;
    display: inline-block;
    color: #ffffff;
}

.rq-l2 {
    margin: 20rpx 0;
    color: #ffffff;
}

.rq-l3 {
    margin: 20rpx 0;
    color: #ffffff;
    font-size: 14px;
}

.rq-l4 {
    margin: 20rpx 0;
    text-align: center;
}

.rq-l4img {
    width: 540rpx;
    height: 540rpx;
    margin: 0 auto;
}

.confirm-button {
    border-radius: 10px;
    background: #386bf6;
    height: 46px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-family: MiSans;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.complete-btn {
    position: fixed;
    bottom: 0;
    width: 90%;
}

.rqbaise {
    border: 1px solid #e4e4e4;
    background: #fff;
    color: #000000;
}

.rqqlanse {
    border-radius: 10px;
    background: rgba(56, 107, 246, 0.3);
}

.rq-l6 {
    position: absolute;
    left: 40rpx;
    bottom: 60rpx;
    display: flex;
    align-items: center;
}

.rq-l18 {
    margin-top: 17px;
    font-size: 14px;
    color: #999999;
    display: flex;
    align-items: center;
    padding-left: 8px;
}

.rq-l18 .rq-l6xy {
    color: #3b37de;
}

.login-type-title {
    margin-top: 120rpx;
    font-size: 24px;
    margin-bottom: 50rpx;
}

.mobile-container {
    margin-top: 20rpx;
}

.mobile-input-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 40px;
}

.mobile-input-container input {
    font-size: 14px;
    border-bottom: 1px solid #e0e0e0;
}

.mobile-input-container .uni-input-input:focus {
    border-bottom: 1px solid #3f64e4;
}

.auth-code-container {
    margin-top: 30rpx;
}

.auth-code-input-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 40px;
}

.auth-code-input-container input {
    flex: 1;
    margin-right: 5px;
    font-size: 14px;
    border-bottom: 1px solid #e0e0e0;
}

.auth-code-input-container .uni-input-input:focus {
    border-bottom: 1px solid #3f64e4;
}

.login-tip {
    text-align: center;
    margin-top: 20px;
    color: #999;
}

.tip-button {
    background-color: #386bf6;
    border-radius: 10px;
    padding: 10px 20px;
    color: #fff;
    font-family: MiSans;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: 10px;
}

.loginpage5 {
    padding-top: 60px;
}

.rq-l14 {
    padding-top: 15px;
}

.input-container {
    margin-top: 40rpx;
    padding: 0;
    border-bottom: 1px solid #EEEEEE;
}

.input-label {
    font-size: 28rpx;
    color: #999999;
    margin-bottom: 8rpx;
}

.input-field {
    width: 100%;
    height: 90rpx;
    font-size: 32rpx;
    color: #333333;
    padding: 0;
    line-height: 90rpx;
}

.input-field::placeholder {
    color: #999999;
    font-size: 32rpx;
}

.forget-password {
    text-align: right;
    color: #386BF6;
    font-size: 28rpx;
    margin-top: 24rpx;
}

.login-btn {
    margin-top: 80rpx;
    height: 88rpx;
    background: #386BF6;
    border-radius: 44rpx;
    color: #FFFFFF;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-btn.disabled {
    background: rgba(56, 107, 246, 0.3);
}

.agreement-section {
    margin-top: 40rpx;
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #999999;
    padding: 0 20rpx;
}

.agreement-link {
    color: #386BF6;
}

.rq-l8 {
    font-size: 40rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 16rpx;
}

.rq-l9 {
    font-size: 28rpx;
    color: #999999;
    margin-bottom: 60rpx;
}

.password-login-btn-new {
    text-align: left;
    margin: 20rpx 0 30rpx 20rpx;
    color: #386BF6;
    font-size: 28rpx;
}

.password-login-btn {
    margin-top: 20rpx;
    height: 88rpx;
    background: #FFFFFF;
    border: 1px solid #386BF6;
    border-radius: 44rpx;
    color: #386BF6;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.code-login-btn {
    margin-top: 20rpx;
    height: 88rpx;
    background: #FFFFFF;
    border: 1px solid #386BF6;
    border-radius: 44rpx;
    color: #386BF6;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
