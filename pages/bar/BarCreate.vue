<template>
    <view class="create-wrap">
        <CustomHeader>
        </CustomHeader>
        <view class="top-banner">
            <image class="banner-bg" src="@/assets/images/bar/create_bg.png" />
            <view class="banner-text">地球岛，创建属于你的吧！</view>
        </view>
        <view class="main-block">
            <form>
                <view class="title-block">
                    <text class="title">吧名 <sup>*</sup></text>
                    <div class="input-block">
                        <input v-model="bbsInfo.name" required placeholder="请输入吧名" />
                    </div>
                </view>

                <view class="title-block">
                    <text class="title">吧目录<sup>*</sup></text>
                    <!-- <input v-model="bbsInfo.categoryId" placeholder="选择本吧目录" /> -->
                    <div class="input-block">
                        <picker @change="bindPickerChange" :range="categoryList" range-key="name">
                            <view class="uni-input" v-if="index!=null">{{categoryList[index].name}}</view>
                            <view class="uni-input unchecked" v-else>选择本吧所属目录</view>
                        </picker>
                    </div>
                </view>
                <view class="title-block">
                    <text class="title">吧头像<sup>*</sup></text>
                </view>
                <view class="file-block" @tap="compressImage()">
                    <image class="avatar" v-if="bbsInfo.avatar" :src="bbsInfo.avatar"></image>
                    <image class="Frame" v-else src="/static/image/icon/Frame.png"></image>
                    <!-- <view class="info-content common-block-style">
                        <view class="info-pic">
                            <uni-file-picker limit="1" title="" @select="handleFileSelect"></uni-file-picker>
                        </view>
                    </view> -->
                </view>
                <l-clipper v-if="show" :image-url="bbsInfo.avatar" @success="bbsInfo.avatar = $event.url; show = false" @cancel="show = false" />
                <!-- <image :src="url" v-if="url" mode="widthFix"></image> -->

                <view class="op-block">
                    <button type="primary" form-type="submit" @click="uploadFile()">确认</button>
                    <view class="agree-tip">
                        <view class="rqla rqlb" v-if="isAgree == true" @click="onischecked">
                            <image class="rqla1" src="/static/image/login/g2.png"></image>
                        </view>
                        <view class="rqla" v-if="isAgree == false" @click="onischecked"></view>
                        <!-- TODO 《建吧流程》 -->
                        我已阅读并同意 <text>《建吧流程》</text>
                    </view>
                </view>
            </form>
        </view>
    </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import appServerApi from '@/api/appServerApi'
import util from '@/utils/util'
export default {
    components: {
        CustomHeader,
    },
    data() {
        return {
            isAgree: false,
            bbsInfo: {
                name: '',
                categoryId: '',
                avatar: '',
            },
            categoryList: [],
            index: null,
            task: null,
            croppedImage: null,
            show: false,
            url: '',
        }
    },
    onLoad() {
        console.log('Page onLoad.')
        this.getAllBbsCategories()
    },
    methods: {
        onischecked: function () {
            this.isAgree = !this.isAgree
        },
        getAllBbsCategories() {
            appServerApi
                .getAllBbsCategories()
                .then((response) => {
                    console.log('getAllBbsCategories', response)
                    this.categoryList = response.data
                })
                .catch((error) => {
                    console.error('Error uploading file:', error)
                })
        },
        bindPickerChange: function (e) {
            console.log('picker发送选择改变，携带值为', e.detail.value)
            this.index = e.detail.value
        },
        compressImage() {
            var that = this
            if (!that.bbsInfo.avatar) {
                uni.chooseImage({
                    count: 1, // 最多可以选择的图片张数，默认9
                    sizeType: ['compressed'], // 'original' 原图，'compressed' 压缩图，默认二者都有
                    sourceType: ['album'], // 'album' 从相册选图，'camera' 使用相机，默认二者都有
                    success: function (res) {
                        // 成功回调函数
                        that.bbsInfo.avatar = res.tempFilePaths[0]
                        console.log('图片路径为:', res.tempFilePaths) // 选着的图片
                        that.show = true
                    },
                    fail: function (err) {
                        // 失败回调函数
                        console.log('chooseImage fail', err)
                    },
                })
            } else {
                that.show = true
            }
        },
        handleMoreOp() {
            // Handle more operations
        },
        handleFileSelect(e) {
            const file = e.tempFiles[0]
            this.uploadFile(file)
        },
        uploadFile() {
            if (!this.isAgree) {
                uni.showToast({
                    title: '请同意建吧流程',
                    icon: 'none',
                })
                return
            }
            if (!this.bbsInfo.name) {
                uni.showToast({
                    title: '请输入吧名',
                    icon: 'none',
                })
                return
            }
            if (this.index == null) {
                uni.showToast({
                    title: '请选择吧目录',
                    icon: 'none',
                })
                return
            }
            if (!this.bbsInfo.avatar) {
                uni.showToast({
                    title: '亲选择吧头像',
                    icon: 'none',
                })
                return
            }
            uni.showLoading({
                title: '正在上传头像',
            })
            var that = this
            util.uploadimgFile(
                'ossfile/upload',
                this.bbsInfo.avatar,
                function (res) {
                    console.log('图片地址', res)
                    that.url = res.data
                    that.handleSubmit()
                    uni.hideLoading()
                }
            )
        },
        handleSubmit() {
            // e.preventDefault()
            var obj = {
                name: this.bbsInfo.name,
                categoryId: this.categoryList[this.index].id,
                avatar: this.url,
            }
            console.log('bbsInfo', obj)
            // return
            appServerApi
                .createBbs(obj)
                .then((response) => {
                    uni.showToast({
                        title: '创建成功',
                        icon: 'success',
                    })
                    console.log('创建成功', response)
                    
                    // Automatically follow the created bar
                    this.followNewBar(response.id)
                    
                    // Navigate to the bar home page with the new bar's ID
                    uni.navigateTo({
                        url: `/pages/bar/BarHome?id=${response.id}`
                    })
                    // 可以在这里进行页面跳转或其他操作
                })
                .catch((error) => {
                    console.error('Error creating bbs:', error)
                    uni.showToast({
                        title: '创建失败',
                        icon: 'none',
                    })
                })
        },
        async submitPost() {
            try {
                const response = await appServerApi.createPost(/* 发帖参数 */)
                if (response.success) {
                    console.log('发帖成功，准备触发刷新事件')
                    uni.$emit('barPostPublished', this.bbsId)
                    console.log('刷新事件已触发')
                    
                    uni.navigateBack()
                    uni.showToast({
                        title: '发布成功',
                        icon: 'success'
                    })
                }
            } catch (error) {
                console.error('发帖失败:', error)
            }
        },
        
        // Function to follow the newly created bar
        followNewBar(bbsId) {
            appServerApi.toggleBbsFollow({
                id: bbsId,
                follow: 1 // 1 means follow
            }).then(() => {
                console.log('自动关注新创建的吧成功')
            }).catch(error => {
                console.error('自动关注新创建的吧失败:', error)
            })
        }
    },
}
</script>

<style lang="scss" scoped>
.create-wrap {
    min-height: 100vh;
    background: #fff;
}

.top-banner {
    position: relative;
    height: 400rpx;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0 40rpx;

    .banner-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
    }

    .banner-text {
        position: relative;
        margin-top: 260rpx;
        color: #999999;
        font-size: 28rpx;
        font-weight: 500;
        z-index: 2;
    }
}

.main-block {
    margin-top: 30rpx;
    padding: 0 40rpx;
    background: #fff;
    border-radius: 20rpx 20rpx 0 0;
}

.common-block-style {
    margin-top: 10px;
    background-color: #fff;
    padding: 14px;
    border-radius: 10px;
}

.info-content {
    display: flex;
    gap: 18px;

    .info-pic {
        // flex: 4;
    }
}
.rq-l18 {
    margin-top: 17px;
    font-size: 14px;
    color: #999999;
    display: flex;
    align-items: center;
    padding-left: 8px;
}
.rqla {
    width: 17px;
    height: 17px;
    background-color: #ffffff;
    border-radius: 50%;
    border: 1px solid #999;
    margin-right: 5px;

    .rqla1 {
        width: 12px;
        height: 12px;
    }
}

.rqlb {
    border: 1px solid #386bf6;
    background-color: #386bf6;
    display: flex;
    align-items: center;
    justify-content: center;
}
.rq-l18 .rq-l6xy {
    color: #3b37de;
}
.title-block {
    margin-bottom: 16rpx;

    .title {
        margin: 16rpx 0 8rpx;
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;
        color: #bbbbbb;
    }

    sup {
        color: red;
    }
}
.file-block {
    border-radius: 10rpx;
    background: #386bf6;
    width: 120rpx;
    height: 120rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 8rpx;

    .Frame {
        width: 40px;
        height: 40px;
    }

    .avatar {
        width: 100%;
        height: 100%;
        border-radius: 10rpx;
    }
}

.op-block {
    position: fixed;
    bottom: 35px;
    left: 22px;
    right: 22px;
    font-size: 14px;
    font-weight: 400;
    text-align: center;

    button {
        height: 46px;
        margin-top: 18px;
        background: #386bf6;
        font-size: 14px;
    }
}

.agree-tip {
    display: flex;
    align-items: center;
    color: #999;
    font-family: MiSans;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: 9px;

    text {
        color: #3b37de;
        font-size: 12px;
    }
}
.unchecked {
    color: #999999;
    font-size: 12px;
}

/* 调整输入框样式 */
.input-block {
    border: 1px solid #e4e4e4;
    border-radius: 10rpx;
    padding: 8rpx 16rpx;
    height: 72rpx;
    display: flex;
    align-items: center;
    background: #fff;

    input,
    .uni-input {
        font-size: 12px;
        height: 100%;
        width: 100%;
    }

    .unchecked {
        font-size: 12px;
        color: #999999;
    }
}
</style>
