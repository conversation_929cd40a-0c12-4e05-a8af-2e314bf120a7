<template>
  <view class="article-detail-wrap">
    <CustomHeader>
      <template v-slot:right>
        <uni-icons color="#333" type="more-filled" size="20" @click="handleMoreOp" />
      </template>
    </CustomHeader>
    <view class="user-info row-flex">
      <view class="user-info-left row-flex">
        <image class="user-avatar" :src="userInfo.avatar" mode=""></image>
        <view class="user-info-des col-flex">
          <text class="user-name">{{ userInfo.displayName }}</text>
          <view class="other-info">
            <text>{{ article.publishDate }}</text>
            <text>IP属地: {{ userInfo.city }}</text>
          </view>
        </view>
      </view>
      <view class="right-info-focus" @click="toggleFollow">
        {{ isFollowing ? '已关注' : '+ 关注' }}
      </view>
    </view>
    <view class="main-content">
      <view class="article-content">
        {{ article.content }}
        <image :src="article.imageUrl" mode=""></image>
      </view>
      <view class="tags">
        <view class="tag-item" v-for="tag in article.tags" :key="tag.id">
          <image :src="tag.imageUrl" mode=""></image>
          <text>{{ tag.name }}</text>
        </view>
      </view>
    </view>
    <view class="replay-block">
      <view class="replay-top row-flex">
        <view class="r-count">
          {{ comments.length }}条回复
        </view>
        <view class="filter-block">
          <view class="filter-item" :class="{active:activeItem==item.value}" v-for="item in filterItems"
                @click="hanleClickItem(item)">
            <text>{{item.label}}</text>
          </view>
        </view>
      </view>
      <view class="replay-content row-flex" v-for="comment in comments" :key="comment.id">
        <view class="r-pic">
          <image class="r-avatar" :src="comment.user.avatar" mode=""></image>
        </view>
        <view class="right-block">
          <view class="first-user">
            <view class="r-user">
              <view class="r-username">{{ comment.user.displayName }}</view>
              <text class="r-cont">{{ comment.content }}</text>
            </view>
          </view>
          <view class="r-hot row-flex">
            <text>{{ comment.createdAt }}</text>
            <view class="hot-right row-flex">
              <view class="row-flex">
                <image src="../../assets/images/bar/pinglun.png" mode=""></image>
                <text>{{ comment.replyCount }}</text>
              </view>
              <view class="row-flex">
                <image src="../../assets/images/bar/zan.png" mode=""></image>
                <text>{{ comment.likeCount }}</text>
              </view>
            </view>
          </view>
          <view class="r-data" v-if="comment.replies.length > 0">
            <view v-for="reply in comment.replies" :key="reply.id">
              <text class="name">{{ reply.user.displayName }}</text>
              <text>回复</text>
              <text class="name">{{ reply.replyTo.displayName }}</text>
              <text>: {{ reply.content }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="op-block row-flex">
      <view class="">
        <input class="uni-input" type="text" placeholder='说说你的看法'></input>
      </view>
      <view class="row-flex">
        <view class=""><uni-icons color="#333" type="chat" size="18" />
          <text>{{ article.commentCount }}</text>
        </view>
        <view class=""><uni-icons color="#333" type="hand-up" size="18" />
          <text>{{ article.likeCount }}</text>
        </view>
        <view class=""><uni-icons color="#333" type="star" size="18" />
          <text>{{ article.favoriteCount }}</text>
        </view>
      </view>
    </view>

    <!-- 弹框 -->
    <uni-popup ref="morePopup" borderRadius='20px' background-color="#fff" @change="change">
      <view class="top-line"></view>
      <view class="pop-op">
        <view class="col-flex" @click="handleBlack">
          <image src="../../assets/images/bar/lahei.png" mode=""></image>
          <text>拉黑</text>
        </view>
        <view class="col-flex" @click="handleReport">
          <image src="../../assets/images/bar/jubao.png" mode=""></image>
          <text>举报</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import appServerApi from '@/api/appServerApi';

export default {
  components: {
    CustomHeader,
  },
  data() {
    return {
      activeItem: 0,
      filterItems: [{
        label: '默认',
        value: '0'
      },
        {
          label: '最新',
          value: '1'
        },
        {
          label: '热门',
          value: '2'
        },
        {
          label: '楼主',
          value: '3'
        },
      ],
      article: {},
      userInfo: {},
      comments: [],
      isFollowing: false
    }
  },
  methods: {
    handleMoreOp() {
      this.$refs.morePopup.open('bottom')
    },
    hanleClickItem(item) {
      this.activeItem = item.value;
    },
    toggleFollow() {
      this.isFollowing = !this.isFollowing;
      // 调用关注或取关的API
      appServerApi.toggleBbsFollow({
        id: this.userInfo.userId,
        follow: this.isFollowing ? 1 : 0
      }).then(response => {
        console.log('Follow status updated:', response);
      }).catch(error => {
        console.error('Error updating follow status:', error);
      });
    },
    fetchArticleDetail(articleId) {
      appServerApi.getArticleDetail(articleId).then(response => {
        this.article = response;
        this.fetchUserInfo(this.article.authorId);
        this.fetchComments(this.article.id);
      }).catch(error => {
        console.error('Error fetching article detail:', error);
      });
    },
    fetchUserInfo(userId) {
      appServerApi.getUserInfo(userId).then(response => {
        this.userInfo = response;
      }).catch(error => {
        console.error('Error fetching user info:', error);
      });
    },
    fetchComments(articleId) {
      appServerApi.getCommentsByPage(1, 10, articleId).then(response => {
        this.comments = response.comments;
      }).catch(error => {
        console.error('Error fetching comments:', error);
      });
    },
    handleBlack() {
      uni.showToast({
        title: '已拉黑，您将不再收到该用户动态',
        icon: 'none'
      })
      setTimeout(() => {
          uni.navigateBack()
      }, 1000)
    },
    handleReport() {
      uni.navigateTo({
        url: `/pages/complaint/ComplaintPage`
      });
    },
  },
  onLoad(options) {
    const articleId = options.id;
    if (articleId) {
      this.fetchArticleDetail(articleId);
    }
  }
}
</script>

<style scoped lang="scss">
.article-detail-wrap {
  // padding: 0 16px;
}

.main-content {
  padding: 0 16px 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid #EFEFEF;

  .article-content {
    font-size: 16px;
    line-height: 22.4px;

    image {
      width: 100%;
      height: auto;
    }
  }
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .tag-item {
    padding: 0 5px;
    height: 26px;
    font-size: 12px;
    display: flex;
    align-items: center;
    background: #F5F5F5;
    color: #999;

    image {
      width: 17px;
      height: 17px;
      border-radius: 50%;
    }
  }
}

.replay-block {
  padding: 0 16px 0;

  image {
    width: 36px;
    height: 36px;
    border-radius: 50%;
  }

  .replay-top {
    font-size: 14px;
  }

  .r-count {
    height: 36px;
  }

  .filter-block {
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    width: 190px;
    border-radius: 100px;
    padding: 2px;
    background: #F5F5F5;
  }

  .filter-item {
    flex: 1;
    height: 32px;
    line-height: 32px;
    text-align: center;
    font-size: 14px;

    &.active {
      background-color: #fff;
      border-radius: 16px;
    }
  }

  .replay-content {
    align-items: flex-start;
    gap: 13px;
    font-size: 16px;

    .r-pic {
      height: 100%;
    }

    .r-user {
      .r-username {
        margin-bottom: 5px;
        color: #386BF6;
      }
    }

    .r-cont {
      font-size: 16px;
      font-weight: 400;
      line-height: 22px;
    }

    .r-hot {
      margin: 17px 0;

      >text {
        color: #999999;
      }

      .hot-right {
        gap: 23px;
      }

      image {
        width: 16px;
        height: 16px;
        margin-right: 10px;
      }
    }

    .r-data {
      background: #F5F5F5;
      padding: 14px;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      border-radius: 10px;
    }

    .name {
      color: #386BF6;
    }
  }
}

.user-info {
  padding: 16px 16px 0;
  padding-bottom: 23px;
  margin-bottom: 23px;
  border-bottom: 1px solid #EFEFEF;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  gap: 15px;

  .user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
  }

  .user-info-left {
    gap: 15px;
  }

  .user-info-des {
    gap: 10px;
  }

  .other-info {
    color: #999;
  }

  .user-name {
    margin: 0 20px 0 6px;
  }

  .right-info-focus {
    width: 52px;
    height: 22px;
    line-height: 22px;
    text-align: center;
    border-radius: 11px;
    border: 1px solid #C0C0C1;
    color: #C0C0C1;
  }
}

.op-block {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 14px 25px;
  font-size: 14px;
  color: #4B4B4B;
  box-shadow: 0 4px 17px 0 #00000017;

  >view {
    display: flex;
    align-items: center;
  }

  input {
    background: #F5F5F5;
  }

  text {
    margin-left: 7px;
  }
}

.pop-op {
  display: flex;
  gap: 38px;
  padding: 23px 14px;
  font-size: 12px;

  .col-flex {
    gap: 8px;
    text-align: center;
  }

  image {
    width: 48px;
    height: 48px;
  }
}

.top-line {
  width: 38px;
  height: 4px;
  margin: 13px auto 0;
  border-radius: 10px;
  background: #E7E7E7;
}
</style>
