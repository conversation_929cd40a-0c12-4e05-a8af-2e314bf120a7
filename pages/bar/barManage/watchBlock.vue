<template>
	<view class="watch-block">
		<view class="section">
			<view class="option-item" @click="setAllMemberVisible">
				<view class="option-content">
					<text class="option-label">全体成员可见</text>
					<view class="option-check" v-if="visibleType === 'all'">
						<text class="check-mark">✓</text>
					</view>
				</view>
			</view>
			
			<view class="option-item" @click="setPartialMemberVisible">
				<view class="option-content">
					<text class="option-label">指定成员可见</text>
					<view class="option-check" v-if="visibleType === 'partial'">
						<text class="check-mark">✓</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 身份组和指定成员部分，仅当选择"指定成员可见"时显示 -->
		<block v-if="visibleType === 'partial'">
			<view class="section">
				<view class="section-header">
					<text class="section-title">身份组</text>
				</view>
				<view class="identity-list">
					<!-- 身份组列表，这里可以根据实际需求加载身份组数据 -->
					<view class="identity-item">
						<text class="identity-name">管理员</text>
						<switch checked @change="onSwitchChange"/>
					</view>
					<view class="identity-item">
						<text class="identity-name">版主</text>
						<switch @change="onSwitchChange"/>
					</view>
					<view class="identity-item">
						<text class="identity-name">成员</text>
						<switch @change="onSwitchChange"/>
					</view>
				</view>
			</view>
			
			<view class="section">
				<view class="section-header">
					<text class="section-title">指定成员</text>
					<view class="add-member" @click="addMember">
						<text class="add-text">添加</text>
					</view>
				</view>
				<view class="member-list" v-if="selectedMembers.length > 0">
					<view class="member-item" v-for="(member, index) in selectedMembers" :key="index">
						<image class="member-avatar" :src="member.avatar" mode="aspectFill"></image>
						<text class="member-name">{{member.name}}</text>
						<view class="remove-btn" @click="removeMember(index)">
							<text class="remove-text">移除</text>
						</view>
					</view>
				</view>
				<view class="empty-tip" v-else>
					<text class="empty-text">暂无指定成员</text>
				</view>
			</view>
		</block>
		
		<!-- 保存按钮 -->
		<view class="save-section" @click="savePermission">
			<text class="save-btn" :class="{ disabled: saving }">
				{{ saving ? '保存中...' : '保存修改' }}
			</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				blockId: '',
				bbsId: '',
				visibleType: 'all', // 'all'表示全体成员可见，'partial'表示指定成员可见
				selectedGroups: [], // 选中的身份组
				selectedMembers: [], // 选中的指定成员
				saving: false
			}
		},
		onLoad(options) {
			console.log('watchBlock onLoad options:', options);
			if (options.id && options.bbsId) {
				this.blockId = options.id;
				this.bbsId = options.bbsId;
				this.loadPermissionData();
			} else {
				uni.showToast({
					title: '参数错误',
					icon: 'none'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		},
		methods: {
			// 加载权限数据
			loadPermissionData() {
				// 这里实现加载当前板块权限设置的逻辑
				console.log('加载权限数据');
				
				// 模拟数据
				this.selectedMembers = [
					{
						id: '1',
						name: '张三',
						avatar: '/static/default-avatar.png'
					}
				];
			},
			
			// 设置为全体成员可见
			setAllMemberVisible() {
				this.visibleType = 'all';
			},
			
			// 设置为指定成员可见
			setPartialMemberVisible() {
				this.visibleType = 'partial';
			},
			
			// 开关状态改变
			onSwitchChange(e) {
				console.log('开关状态变化:', e.detail.value);
				// 这里处理身份组选择状态变化
			},
			
			// 添加成员
			addMember() {
				uni.showToast({
					title: '添加成员功能开发中',
					icon: 'none'
				});
			},
			
			// 移除成员
			removeMember(index) {
				this.selectedMembers.splice(index, 1);
			},
			
			// 保存权限设置
			savePermission() {
				this.saving = true;
				
				// 模拟保存过程
				setTimeout(() => {
					this.saving = false;
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});
					
					// 延迟返回
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}, 1000);
			}
		}
	}
</script>

<style>
.watch-block {
	min-height: 100vh;
	background-color: #f5f7fa;
	padding-top: 12px;
}

.section {
	background-color: #fff;
	margin-bottom: 12px;
}

.option-item {
	padding: 16px;
	border-bottom: 1px solid #f5f5f5;
}

.option-item:last-child {
	border-bottom: none;
}

.option-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.option-label {
	font-size: 15px;
	color: #333;
}

.option-check {
	width: 20px;
	height: 20px;
	display: flex;
	justify-content: center;
	align-items: center;
}

.check-mark {
	font-size: 16px;
	color: #007AFF;
	font-weight: bold;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px 16px;
	border-bottom: 1px solid #f5f5f5;
}

.section-title {
	font-size: 14px;
	color: #666;
	font-weight: 500;
}

.add-member {
	padding: 4px 8px;
}

.add-text {
	color: #007AFF;
	font-size: 14px;
}

.identity-list, .member-list {
	padding: 0 16px;
}

.identity-item, .member-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px 0;
	border-bottom: 1px solid #f5f5f5;
}

.identity-item:last-child, .member-item:last-child {
	border-bottom: none;
}

.identity-name, .member-name {
	font-size: 14px;
	color: #333;
}

.member-avatar {
	width: 32px;
	height: 32px;
	border-radius: 16px;
	background-color: #e0e0e0;
	margin-right: 10px;
}

.member-item {
	display: flex;
	align-items: center;
}

.member-name {
	flex: 1;
}

.remove-btn {
	padding: 4px 8px;
}

.remove-text {
	color: #ff3b30;
	font-size: 14px;
}

.empty-tip {
	padding: 20px 0;
	text-align: center;
}

.empty-text {
	color: #999;
	font-size: 14px;
}

.save-section {
	margin-top: 20px;
	background-color: #fff;
	padding: 16px;
	text-align: center;
}

.save-btn {
	color: #007AFF;
	font-size: 16px;
	font-weight: 500;
}

.save-btn.disabled {
	color: #999;
}
</style>
