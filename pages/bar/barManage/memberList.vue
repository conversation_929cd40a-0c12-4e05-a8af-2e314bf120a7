<template>
	<view class="member-list">
		<!-- 自定义头部 -->
		<CustomHeader color="#000" :title="roleName + '列表'" backgroundColor="#fff">
			<template #left>
				<view class="back-btn" @click="goBack">
					<uni-icons type="left" size="20" color="#000"></uni-icons>
				</view>
			</template>
		</CustomHeader>
		
		<!-- 搜索框 -->
		<view class="search-container">
			<view class="search-box">
				<uni-icons type="search" size="18" color="#999"></uni-icons>
				<input 
					class="search-input" 
					placeholder="搜索成员" 
					v-model="searchKeyword"
					@input="handleSearch"
				/>
			</view>
		</view>
		
		<!-- 成员列表 -->
		<view class="members-container">
			<view v-if="loading" class="loading">
				<uni-load-more status="loading" :contentText="loadingText"></uni-load-more>
			</view>
			
			<view v-else-if="members.length === 0" class="empty-state">
				<image src="/static/icons/empty-list.png" mode="aspectFit" class="empty-image"></image>
				<text class="empty-text">暂无成员</text>
			</view>
			
			<view v-else class="member-items">
				<view 
					class="member-item"
					v-for="(member, index) in members" 
					:key="member.id"
					@click="showMemberActions(member)"
				>
					<image :src="member.avatar" class="member-avatar" mode="aspectFill"></image>
					<view class="member-info">
						<text class="member-name">{{member.nickname}}</text>
						<view class="member-details">
							<text class="member-status">{{member.status}}</text>
							<text v-if="member.role && !roleId" class="member-role">{{member.role}}</text>
						</view>
					</view>
					<view class="member-actions">
						<uni-icons type="more-filled" size="20" color="#999"></uni-icons>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import appServerApi from '@/api/appServerApi'

export default {
	components: {
		CustomHeader
	},
	data() {
		return {
			bbsId: '', // 贴吧ID
			roleId: '', // 角色ID
			roleName: '', // 角色名称
			members: [], // 成员列表
			loading: true, // 加载状态
			searchKeyword: '', // 搜索关键词
			loadingText: {
				contentdown: '加载更多',
				contentrefresh: '加载中...',
				contentnomore: '没有更多了'
			}
		}
	},
	onLoad(options) {
		// 获取参数
		this.bbsId = options.bbsId || '';
		this.roleId = options.roleId || '';
		this.roleName = decodeURIComponent(options.roleName) || '未知';
		
		console.log('成员列表页面获取到的参数:', {
			bbsId: this.bbsId,
			roleId: this.roleId,
			roleName: this.roleName
		});
		
		// 加载成员列表
		this.fetchMembers();
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 获取成员列表
		fetchMembers() {
			this.loading = true;
			
			// 模拟API请求
			setTimeout(() => {
				// 这里应该调用实际的API
				// 如果有角色ID，则获取该角色的成员
				// 如果没有角色ID，则获取所有成员
				// if (this.roleId) {
				//     appServerApi.getRoleMembers(this.bbsId, this.roleId)
				// } else {
				//     appServerApi.getAllMembers(this.bbsId)
				// }
				
				// 模拟数据
				if (this.roleId) {
					// 特定角色的成员
					this.members = [
						{
							id: '1',
							nickname: '用户1',
							avatar: '/static/avatar/default1.png',
							status: '在线'
						},
						{
							id: '2',
							nickname: '用户2',
							avatar: '/static/avatar/default2.png',
							status: '离线'
						},
						{
							id: '3',
							nickname: '用户3',
							avatar: '/static/avatar/default3.png',
							status: '在线'
						}
					];
				} else {
					// 所有成员
					this.members = [
						{
							id: '1',
							nickname: '用户1',
							avatar: '/static/avatar/default1.png',
							status: '在线',
							role: '贴吧主'
						},
						{
							id: '2',
							nickname: '用户2',
							avatar: '/static/avatar/default2.png',
							status: '离线',
							role: '管理员'
						},
						{
							id: '3',
							nickname: '用户3',
							avatar: '/static/avatar/default3.png',
							status: '在线',
							role: '管理员'
						},
						{
							id: '4',
							nickname: '用户4',
							avatar: '/static/avatar/default1.png',
							status: '在线',
							role: '成员'
						},
						{
							id: '5',
							nickname: '用户5',
							avatar: '/static/avatar/default2.png',
							status: '离线',
							role: '成员'
						}
					];
				}
				this.loading = false;
			}, 1000);
		},
		
		// 处理搜索
		handleSearch() {
			// 实现搜索逻辑
			// 可以在这里筛选本地数据或发起新的API请求
			console.log('搜索关键词:', this.searchKeyword);
		},
		
		// 显示成员操作菜单
		showMemberActions(member) {
			uni.showActionSheet({
				itemList: ['查看资料', '移除成员', '更改角色'],
				success: (res) => {
					switch(res.tapIndex) {
						case 0: // 查看资料
							this.viewMemberProfile(member);
							break;
						case 1: // 移除成员
							this.removeMember(member);
							break;
						case 2: // 更改角色
							this.changeMemberRole(member);
							break;
					}
				}
			});
		},
		
		// 查看成员资料
		viewMemberProfile(member) {
			uni.showToast({
				title: '查看资料功能开发中',
				icon: 'none'
			});
		},
		
		// 移除成员
		removeMember(member) {
			uni.showModal({
				title: '确认移除',
				content: `确定要将"${member.nickname}"从${this.roleName}中移除吗？`,
				success: (res) => {
					if (res.confirm) {
						// 实现移除逻辑
						uni.showToast({
							title: '移除成功',
							icon: 'success'
						});
						
						// 从列表中移除
						const index = this.members.findIndex(m => m.id === member.id);
						if (index !== -1) {
							this.members.splice(index, 1);
						}
					}
				}
			});
		},
		
		// 更改成员角色
		changeMemberRole(member) {
			uni.showToast({
				title: '更改角色功能开发中',
				icon: 'none'
			});
		}
	}
}
</script>

<style>
.member-list {
	min-height: 100vh;
	background-color: #f5f7fa;
	position: relative;
}

.back-btn {
	padding: 8px;
}

/* 搜索框 */
.search-container {
	padding: 10px 15px;
	background-color: #fff;
}

.search-box {
	display: flex;
	align-items: center;
	background-color: #f0f2f5;
	border-radius: 20px;
	padding: 8px 12px;
}

.search-input {
	flex: 1;
	height: 24px;
	margin-left: 8px;
	font-size: 14px;
}

/* 成员列表容器 */
.members-container {
	background-color: #fff;
	margin-top: 10px;
}

/* 加载状态 */
.loading {
	padding: 20px 0;
	display: flex;
	justify-content: center;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 0;
}

.empty-image {
	width: 100px;
	height: 100px;
	margin-bottom: 15px;
}

.empty-text {
	font-size: 14px;
	color: #999;
}

/* 成员列表 */
.member-items {
	padding-bottom: env(safe-area-inset-bottom);
}

.member-item {
	display: flex;
	align-items: center;
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
	transition: background-color 0.3s ease;
}

.member-item:active {
	background-color: #f8f8f8;
}

.member-avatar {
	width: 50px;
	height: 50px;
	border-radius: 50%;
	margin-right: 12px;
}

.member-info {
	flex: 1;
}

.member-name {
	font-size: 16px;
	color: #333;
	margin-bottom: 4px;
}

.member-details {
	display: flex;
	align-items: center;
}

.member-status {
	font-size: 12px;
	color: #999;
	margin-right: 4px;
}

.member-role {
	font-size: 12px;
	color: #fff;
	background-color: #007AFF;
	padding: 2px 6px;
	border-radius: 10px;
}

.member-actions {
	padding: 8px;
}

/* 状态标签 */
.status-label {
	font-size: 12px;
	border-radius: 10px;
	padding: 2px 6px;
}

.status-online {
	background-color: #e6f7ff;
	color: #1890ff;
}

.status-offline {
	background-color: #f5f5f5;
	color: #999;
}
</style>
