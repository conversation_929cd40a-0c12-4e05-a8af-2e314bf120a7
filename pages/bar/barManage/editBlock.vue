<template>
	<view class="edit-block">
		<!-- Loading State -->
		<view class="loading-state" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>

		<!-- Content -->
		<view v-else>
			<!-- 板块基本信息 -->
			<view class="section">
				<view class="input-item">
					<text class="label">板块名称</text>
					<input 
						type="text" 
						v-model="blockName" 
						placeholder="请输入板块名称" 
						class="input" 
						@input="onNameInput"
					/>
				</view>
				<view class="input-item" @click="showIconPicker">
					<text class="label">板块图标</text>
					<view class="icon-display">
						<text class="selected-icon" v-if="blockIcon">{{ blockIcon }}</text>
						<text class="placeholder" v-else>请选择图标</text>
					</view>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>

			<!-- 图标选择器 -->
			<view class="icon-picker" v-if="showPicker">
				<view class="picker-header">
					<text class="picker-title">选择图标</text>
					<text class="picker-close" @click="hidePicker">完成</text>
				</view>
				<view class="icon-grid">
					<view 
						class="icon-item" 
						v-for="(icon, index) in iconList" 
						:key="index"
						:class="{ active: blockIcon === icon }"
						@click="selectIcon(icon)"
					>
						<text class="icon-text">{{ icon }}</text>
					</view>
				</view>
			</view>

			<!-- 管理员设置 -->
			<view class="section">
				<view class="input-item" @click="navigateToManagers">
					<text class="label">板块管理员</text>
					<text class="value">{{managerCount}}名成员</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>

			<!-- 权限设置 -->
			<view class="section">
				<view class="input-item" @click="setViewPermission">
					<text class="label">谁可以查看</text>
					<text class="value">全体成员</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
				<view class="input-item" @click="setPostPermission">
					<text class="label">谁可以发帖</text>
					<text class="value">全体成员</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>

			<!-- 保存按钮 -->
			<view class="save-section" v-if="hasChanges" @click="saveChanges">
				<text class="save-btn" :class="{ disabled: saving }">
					{{ saving ? '保存中...' : '保存修改' }}
				</text>
			</view>

			<!-- 删除按钮 - 已注释 -->
			<!-- <view class="delete-section" @click="deleteBlock">
				<text class="delete-btn">删除板块</text>
			</view> -->
		</view>
	</view>
</template>

<script>
import appServerApi from '@/api/appServerApi.js'

export default {
	data() {
		return {
			blockId: '',
			bbsId: '',
			blockName: '',
			originalBlockName: '',
			blockIcon: '',
			originalBlockIcon: '',
			managerCount: 0,
			viewPermission: '全体成员',
			postPermission: '全体成员',
			loading: false,
			saving: false,
			hasChanges: false,
			showPicker: false,
			iconList: [
				'📝', '💡', '🎨', '🎵', '🎮', '📚', '🏆', '⭐',
				'🔥', '💎', '🌟', '🎯', '🚀', '💫', '🎪', '🎭',
				'📱', '💻', '📷', '🎬', '📺', '🎧', '🎤', '🎸',
				'🎹', '🥁', '🎺', '🎼', '📖', '🔍', '⚡', '🌈'
			]
		}
	},
	onLoad(options) {
		console.log('editBlock onLoad options:', options);
		if (options.id && options.bbsId) {
			this.blockId = options.id;
			this.bbsId = options.bbsId;
			this.loadBlockData();
		} else {
			uni.showToast({
				title: '参数错误',
				icon: 'none'
			});
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		}
	},
	methods: {
		async loadBlockData() {
			this.loading = true;
			try {
				console.log('获取板块列表，bbsId:', this.bbsId);
				const result = await appServerApi.getBbsModuleList(this.bbsId);
				console.log('板块列表结果:', result);
				
				if (result && result.code === 200) {
					const moduleData = result.data || [];
					// 根据blockId查找对应的板块信息
					const targetBlock = moduleData.find(block => block.id == this.blockId);
					
					if (targetBlock) {
						this.blockName = targetBlock.name;
						this.originalBlockName = targetBlock.name;
						this.blockIcon = targetBlock.icon || '';
						this.originalBlockIcon = targetBlock.icon || '';
						console.log('加载板块信息:', targetBlock);
					} else {
						uni.showToast({
							title: '板块不存在',
							icon: 'none'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				} else {
					console.error('获取板块列表失败:', result?.msg || result?.message);
					uni.showToast({
						title: '获取板块信息失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取板块信息异常:', error);
				uni.showToast({
					title: '网络异常，请重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		onNameInput() {
			// 检查是否有修改
			this.hasChanges = this.blockName.trim() !== this.originalBlockName || this.blockIcon !== this.originalBlockIcon;
		},
		async saveChanges() {
			const newName = this.blockName.trim();
			if (!newName) {
				uni.showToast({
					title: '板块名称不能为空',
					icon: 'none'
				});
				return;
			}
			
			if (newName === this.originalBlockName && this.blockIcon === this.originalBlockIcon) {
				this.hasChanges = false;
				return;
			}
			
			this.saving = true;
			try {
				// 详细的参数验证和日志
				const updateParams = {
					id: this.blockId,
					bbsId: this.bbsId,
					name: newName,
					icon: this.blockIcon || '📁'
				};
				
				console.log('更新板块信息 - 参数详情:');
				console.log('- blockId:', this.blockId, '(类型:', typeof this.blockId, ')');
				console.log('- bbsId:', this.bbsId, '(类型:', typeof this.bbsId, ')');
				console.log('- name:', newName, '(类型:', typeof newName, ')');
				console.log('- icon:', updateParams.icon, '(类型:', typeof updateParams.icon, ')');
				console.log('- 完整参数对象:', JSON.stringify(updateParams));
				
				const result = await appServerApi.updateBbsModule(updateParams);
				
				console.log('updateBbsModule result:', result);
				console.log('result类型:', typeof result);
				
				// _post方法成功时返回 true 或者响应数据
				if (result === true || (result && result.code === 200)) {
					console.log('板块修改成功');
					this.originalBlockName = newName;
					this.originalBlockIcon = this.blockIcon;
					this.hasChanges = false;
					
					uni.showToast({
						title: '修改成功',
						icon: 'success'
					});
					
					// 延迟返回，让用户看到成功提示
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					console.error('板块修改失败:', result?.msg || result?.message);
					uni.showToast({
						title: result?.msg || result?.message || '修改失败',
						icon: 'none'
					});
				}
			} catch (error) {
				// 改进错误处理，确保能正确显示错误信息
				console.error('板块修改异常:', error);
				console.error('错误类型:', typeof error);
				console.error('错误详情:', JSON.stringify(error));
				
				let errorMessage = '修改失败，请重试';
				if (error) {
					if (typeof error === 'string') {
						errorMessage = error;
					} else if (error.message) {
						errorMessage = error.message;
					} else if (error.msg) {
						errorMessage = error.msg;
					}
				}
				
				console.log('最终错误消息:', errorMessage);
				
				uni.showToast({
					title: errorMessage,
					icon: 'none'
				});
			} finally {
				this.saving = false;
			}
		},
		navigateToManagers() {
			// 跳转到管理员设置页面
			uni.navigateTo({
				url: './blockManagers?id=' + this.blockId
			});
		},
		setViewPermission() {
			// 设置查看权限
			uni.navigateTo({
				url: './watchBlock?id=' + this.blockId + '&bbsId=' + this.bbsId
			});
		},
		setPostPermission() {
			// 设置发帖权限
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			});
		},
		showIconPicker() {
			this.showPicker = true;
		},
		hidePicker() {
			this.showPicker = false;
		},
		selectIcon(icon) {
			this.blockIcon = icon;
			this.hasChanges = this.blockName.trim() !== this.originalBlockName || this.blockIcon !== this.originalBlockIcon;
			this.hidePicker();
		},
		// deleteBlock() {
		// 	uni.showModal({
		// 		title: '确认删除',
		// 		content: '确定要删除该板块吗？删除后不可恢复。',
		// 		success: (res) => {
		// 			if (res.confirm) {
		// 				// TODO: 实现删除功能（需要删除接口）
		// 				uni.showToast({
		// 					title: '删除功能开发中',
		// 					icon: 'none'
		// 				});
		// 			}
		// 		}
		// 	});
		// }
	}
}
</script>

<style>
.edit-block {
	min-height: 100vh;
	background-color: #f5f7fa;
	padding-top: 12px;
}

/* Loading State Styles */
.loading-state {
	display: flex;
	justify-content: center;
	align-items: center;
	padding-top: 100px;
}

.loading-text {
	font-size: 16px;
	color: #999;
}

.section {
	background-color: #fff;
	margin-bottom: 12px;
}

.input-item {
	display: flex;
	align-items: center;
	padding: 16px;
	border-bottom: 1px solid #f5f5f5;
}

.input-item:last-child {
	border-bottom: none;
}

.label {
	font-size: 15px;
	color: #333;
	min-width: 90px;
}

.input {
	flex: 1;
	font-size: 15px;
	margin: 0 10px;
}

.value {
	flex: 1;
	font-size: 15px;
	color: #999;
	text-align: right;
	margin-right: 10px;
}

.save-section {
	margin-bottom: 12px;
	background-color: #fff;
	padding: 16px;
	text-align: center;
}

.save-btn {
	color: #007AFF;
	font-size: 16px;
	font-weight: 500;
}

.save-btn.disabled {
	color: #999;
}

.delete-section {
	margin-top: 20px;
	background-color: #fff;
	padding: 16px;
	text-align: center;
}

.delete-btn {
	color: #ff3b30;
	font-size: 16px;
}

/* 图标选择器样式 */
.icon-picker {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	border-radius: 20px 20px 0 0;
	max-height: 60vh;
	z-index: 1000;
	box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

.picker-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px 20px;
	border-bottom: 1px solid #f5f5f5;
}

.picker-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

.picker-close {
	font-size: 15px;
	color: #007AFF;
}

.icon-grid {
	display: grid;
	grid-template-columns: repeat(8, 1fr);
	gap: 10px;
	padding: 20px;
	max-height: 40vh;
	overflow-y: auto;
}

.icon-item {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 40px;
	height: 40px;
	border-radius: 8px;
	background-color: #f8f8f8;
	transition: all 0.2s ease;
}

.icon-item.active {
	background-color: #007AFF;
	transform: scale(1.1);
}

.icon-item.active .icon-text {
	transform: scale(1.2);
}

.icon-text {
	font-size: 18px;
	transition: transform 0.2s ease;
}

.icon-display {
	flex: 1;
	margin: 0 10px;
	text-align: right;
}

.selected-icon {
	font-size: 20px;
}

.placeholder {
	font-size: 15px;
	color: #999;
}
</style>
