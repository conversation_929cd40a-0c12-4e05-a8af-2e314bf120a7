<template>
	<view class="block-manage">
		<!-- Empty State -->
		<view class="empty-state" v-if="blocks.length === 0 && !loading">
			<view class="empty-box">
				<image class="empty-image" src="/static/image/icon/block.png" mode="aspectFit"></image>
			</view>
			<text class="empty-description">暂无板块</text>
			<text class="empty-sub-description">增加板块，更方便查看和管理帖子</text>
		</view>

		<!-- Loading State -->
		<view class="loading-state" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>

		<!-- Content State -->
		<view class="content-state" v-else-if="blocks.length > 0">
			<view class="block-list">
				<view 
					class="block-item" 
					v-for="(block, index) in blocks" 
					:key="block.id || index"
					@click="editBlock(block)"
				>
					<view class="block-info">
						<text class="block-icon" v-if="block.icon">{{ block.icon }}</text>
						<text class="block-name">{{ block.name }}</text>
					</view>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>

		<!-- Add Block Button -->
		<view class="add-button" @click="addNewBlock">
			<text>+ 新建板块</text>
		</view>
	</view>
</template>

<script>
import appServerApi from '@/api/appServerApi.js'

export default {
	data() {
		return {
			blocks: [],
			loading: false,
			bbsId: '' // 当前贴吧ID
		}
	},
	onLoad(options) {
		// 获取贴吧ID - 优先从URL参数获取，然后从本地存储获取
		if (options.bbsId) {
			this.bbsId = options.bbsId;
			// 同时更新本地存储，确保数据一致性
			uni.setStorageSync('currentBbsId', options.bbsId);
		} else {
			this.bbsId = uni.getStorageSync('currentBbsId') || 1; // 默认值
		}
		console.log('blockManage 获取到的 bbsId:', this.bbsId);
		this.loadBlocks();
	},
	onShow() {
		// 页面显示时刷新数据（从新建页面返回时会触发）
		this.loadBlocks();
	},
	methods: {
		async loadBlocks() {
			this.loading = true;
			try {
				console.log('获取板块列表，bbsId:', this.bbsId);
				const result = await appServerApi.getBbsModuleList(this.bbsId);
				console.log('板块列表结果:', result);
				console.log('result类型:', typeof result);
				console.log('result.code:', result?.code);
				console.log('result.data:', result?.data);
				
				// _get方法成功时返回完整的响应对象 {code: 200, msg: null, data: [...]}
				if (result && result.code === 200) {
					this.blocks = result.data || [];
					console.log('板块数据:', this.blocks);
				} else {
					console.error('获取板块列表失败:', result?.msg || result?.message);
					uni.showToast({
						title: result?.msg || result?.message || '获取板块列表失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取板块列表异常:', error);
				uni.showToast({
					title: '网络异常，请重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		addNewBlock() {
			uni.navigateTo({
				url: `./addBlock?bbsId=${this.bbsId}`
			})
		},
		editBlock(block) {
			uni.navigateTo({
				url: `./editBlock?id=${block.id}&bbsId=${this.bbsId}`
			})
		}
	}
}
</script>

<style>
.block-manage {
	min-height: 100vh;
	background-color: #f5f7fa;
	padding: 20px;
	position: relative;
}

/* Loading State Styles */
.loading-state {
	display: flex;
	justify-content: center;
	align-items: center;
	padding-top: 100px;
}

.loading-text {
	font-size: 16px;
	color: #999;
}

/* Empty State Styles */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-top: 100px;
}

.empty-box {
	width: 200px;
	height: 200px;
	background-color: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 12px;
	margin-bottom: 20px;
	overflow: hidden;
}

.empty-image {
	width: 200px;
	height: 200px;
}

.empty-description {
	font-size: 18px;
	color: #333;
	margin-bottom: 10px;
}

.empty-sub-description {
	font-size: 14px;
	color: #999;
}

/* Content State Styles */
.block-list {
	margin-bottom: 80px;
}

.block-item {
	background-color: #fff;
	padding: 16px;
	border-radius: 10px;
	margin-bottom: 10px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
	transition: all 0.3s ease;
}

.block-item:active {
	transform: scale(0.98);
	background-color: #f8f8f8;
}

.block-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.block-icon {
	font-size: 18px;
	margin-right: 12px;
}

.block-name {
	font-size: 15px;
	color: #333;
}

/* Add Button Styles */
.add-button {
	position: fixed;
	bottom: 30px;
	left: 50%;
	transform: translateX(-50%);
	width: 90%;
	background-color: #fff;
	padding: 16px 0;
	border-radius: 12px;
	text-align: center;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.add-button:active {
	transform: translateX(-50%) scale(0.98);
	background-color: #f8f8f8;
}

.add-button text {
	color: #007AFF;
	font-size: 16px;
}
</style> 