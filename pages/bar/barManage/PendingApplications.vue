<template>
	<view class="pending-applications">
		<!-- 自定义头部 -->
		<CustomHeader color="#000" title="待处理申请" backgroundColor="#fff">
			<template #left>
				<view class="back-btn" @click="goBack">
					<uni-icons type="left" size="20" color="#000"></uni-icons>
				</view>
			</template>
		</CustomHeader>
		
		<!-- 内容区域 -->
		<view class="content-area">
			<!-- 筛选选项 -->
			<view class="filter-section">
				<view 
					class="filter-item" 
					:class="{ active: currentStatus === 'all' }" 
					@click="filterByStatus('all')"
				>全部</view>
				<view 
					class="filter-item" 
					:class="{ active: currentStatus === 'pending' }" 
					@click="filterByStatus('pending')"
				>待处理</view>
				<view 
					class="filter-item" 
					:class="{ active: currentStatus === 'approved' }" 
					@click="filterByStatus('approved')"
				>已通过</view>
				<view 
					class="filter-item" 
					:class="{ active: currentStatus === 'rejected' }" 
					@click="filterByStatus('rejected')"
				>已拒绝</view>
			</view>
			
			<!-- 申请列表 -->
			<view class="applications-list">
				<view v-if="filteredApplications.length === 0" class="empty-state">
					<image src="/static/icons/empty.png" mode="aspectFit" class="empty-icon"></image>
					<text class="empty-text">暂无{{statusText}}申请</text>
				</view>
				
				<view 
					v-for="(item, index) in filteredApplications" 
					:key="index" 
					class="application-item"
				>
					<view class="user-info">
						<image :src="item.avatar" mode="aspectFill" class="user-avatar"></image>
						<view class="user-details">
							<view class="user-name-row">
								<text class="user-name">{{item.name}}</text>
								<view :class="['status-badge', item.status]">{{getStatusText(item.status)}}</view>
							</view>
							<text class="application-time">申请时间：{{formatDate(item.applyTime)}}</text>
						</view>
					</view>
					
					<view class="application-content">
						<view class="reason-section">
							<text class="reason-label">申请理由：</text>
							<text class="reason-text">{{item.reason}}</text>
						</view>
						
						<view class="role-section">
							<text class="role-label">申请身份组：</text>
							<text class="role-name">{{item.applyRole}}</text>
						</view>
					</view>
					
					<view class="action-section" v-if="item.status === 'pending'">
						<button class="action-btn reject" @click="handleReject(item, index)">拒绝</button>
						<button class="action-btn approve" @click="handleApprove(item, index)">通过</button>
					</view>
					
					<view class="result-info" v-else>
						<text class="result-time">处理时间：{{formatDate(item.processTime)}}</text>
						<text class="result-operator" v-if="item.operator">处理人：{{item.operator}}</text>
						<text class="result-comment" v-if="item.comment">备注：{{item.comment}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 拒绝理由弹窗 -->
		<uni-popup ref="rejectPopup" type="center">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">拒绝申请</text>
				</view>
				<view class="popup-body">
					<text class="popup-label">拒绝理由（可选）：</text>
					<textarea class="reject-reason" v-model="rejectReason" placeholder="请输入拒绝理由" />
				</view>
				<view class="popup-footer">
					<button class="btn btn-cancel" @click="cancelReject">取消</button>
					<button class="btn btn-confirm" @click="confirmReject">确认拒绝</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import store from '@/store'

export default {
	components: {
		CustomHeader
	},
	data() {
		return {
			bbsId: '',
			currentStatus: 'all', // 当前筛选状态：all, pending, approved, rejected
			applications: [], // 所有申请列表
			rejectReason: '', // 拒绝原因
			currentRejectItem: null, // 当前要拒绝的申请
			currentRejectIndex: -1 // 当前要拒绝的申请索引
		}
	},
	computed: {
		// 根据筛选条件过滤申请列表
		filteredApplications() {
			if (this.currentStatus === 'all') {
				return this.applications;
			} else {
				return this.applications.filter(item => item.status === this.currentStatus);
			}
		},
		// 获取当前状态的文本描述
		statusText() {
			switch(this.currentStatus) {
				case 'pending': return '待处理';
				case 'approved': return '已通过';
				case 'rejected': return '已拒绝';
				default: return '';
			}
		}
	},
	onLoad(options) {
		// 获取贴吧ID
		if (options.bbsId) {
			this.bbsId = options.bbsId;
		} else {
			this.bbsId = uni.getStorageSync('currentBbsId') || '';
		}
		
		// 获取申请列表
		this.fetchApplications();
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 根据状态筛选申请
		filterByStatus(status) {
			this.currentStatus = status;
		},
		
		// 获取申请列表（模拟数据）
		fetchApplications() {
			// 模拟异步获取数据
			uni.showLoading({
				title: '加载中...'
			});
			
			setTimeout(() => {
				// 模拟数据
				this.applications = [
					{
						id: 1,
						name: '张三',
						avatar: 'https://picsum.photos/200?random=1',
						reason: '我是贴吧老用户，希望能协助管理内容，维护社区秩序',
						applyRole: '管理员',
						status: 'pending',
						applyTime: Date.now() - 86400000, // 1天前
						processTime: null,
						operator: null,
						comment: null
					},
					{
						id: 2,
						name: '李四',
						avatar: 'https://picsum.photos/200?random=2',
						reason: '我是设计师，希望能为贴吧提供设计和排版建议',
						applyRole: '板块管理员',
						status: 'pending',
						applyTime: Date.now() - 172800000, // 2天前
						processTime: null,
						operator: null,
						comment: null
					},
					{
						id: 3,
						name: '王五',
						avatar: 'https://picsum.photos/200?random=3',
						reason: '我是资深玩家，熟悉游戏规则，希望能为新手提供帮助',
						applyRole: '版主',
						status: 'approved',
						applyTime: Date.now() - 259200000, // 3天前
						processTime: Date.now() - 86400000, // 1天前
						operator: '贴吧主',
						comment: '经过考核，符合资格要求'
					},
					{
						id: 4,
						name: '赵六',
						avatar: 'https://picsum.photos/200?random=4',
						reason: '我想协助管理社区，维护良好讨论氛围',
						applyRole: '管理员',
						status: 'rejected',
						applyTime: Date.now() - 345600000, // 4天前
						processTime: Date.now() - 172800000, // 2天前
						operator: '贴吧主',
						comment: '暂无管理员名额'
					},
					{
						id: 5,
						name: '钱七',
						avatar: 'https://picsum.photos/200?random=5',
						reason: '我是美术设计专业毕业，希望能帮忙管理美术板块',
						applyRole: '板块管理员',
						status: 'pending',
						applyTime: Date.now() - 43200000, // 12小时前
						processTime: null,
						operator: null,
						comment: null
					}
				];
				
				uni.hideLoading();
			}, 1000);
		},
		
		// 格式化日期
		formatDate(timestamp) {
			if (!timestamp) return '';
			
			const now = Date.now();
			const diff = now - timestamp;
			
			// 小于1分钟
			if (diff < 60000) {
				return '刚刚';
			}
			// 小于1小时
			else if (diff < 3600000) {
				return Math.floor(diff / 60000) + '分钟前';
			}
			// 小于24小时
			else if (diff < 86400000) {
				return Math.floor(diff / 3600000) + '小时前';
			}
			// 小于30天
			else if (diff < 2592000000) {
				return Math.floor(diff / 86400000) + '天前';
			}
			// 其他情况显示具体日期
			else {
				const date = new Date(timestamp);
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
			}
		},
		
		// 获取状态文本
		getStatusText(status) {
			switch(status) {
				case 'pending': return '待处理';
				case 'approved': return '已通过';
				case 'rejected': return '已拒绝';
				default: return '未知';
			}
		},
		
		// 处理拒绝申请
		handleReject(item, index) {
			this.currentRejectItem = item;
			this.currentRejectIndex = index;
			this.rejectReason = '';
			this.$refs.rejectPopup.open();
		},
		
		// 取消拒绝
		cancelReject() {
			this.$refs.rejectPopup.close();
			this.currentRejectItem = null;
			this.currentRejectIndex = -1;
		},
		
		// 确认拒绝
		confirmReject() {
			if (this.currentRejectItem) {
				// 更新申请状态
				this.applications[this.currentRejectIndex].status = 'rejected';
				this.applications[this.currentRejectIndex].processTime = Date.now();
				this.applications[this.currentRejectIndex].operator = '贴吧主'; // 模拟数据，实际应该使用当前登录用户
				this.applications[this.currentRejectIndex].comment = this.rejectReason || '申请未通过';
				
				// 关闭弹窗
				this.$refs.rejectPopup.close();
				
				// 提示成功
				uni.showToast({
					title: '已拒绝申请',
					icon: 'success'
				});
				
				// 重置数据
				this.currentRejectItem = null;
				this.currentRejectIndex = -1;
			}
		},
		
		// 处理通过申请
		handleApprove(item, index) {
			uni.showModal({
				title: '确认通过',
				content: `确定要通过"${item.name}"的${item.applyRole}申请吗？`,
				success: (res) => {
					if (res.confirm) {
						// 更新申请状态
						this.applications[index].status = 'approved';
						this.applications[index].processTime = Date.now();
						this.applications[index].operator = '贴吧主'; // 模拟数据，实际应该使用当前登录用户
						this.applications[index].comment = '申请已通过';
						
						// 提示成功
						uni.showToast({
							title: '已通过申请',
							icon: 'success'
						});
					}
				}
			});
		}
	}
}
</script>

<style>
.pending-applications {
	min-height: 100vh;
	background-color: #f5f7fa;
}

.back-btn {
	padding: 8px;
}

/* 内容区域 */
.content-area {
	padding-top: 10px;
}

/* 筛选选项 */
.filter-section {
	display: flex;
	background-color: #fff;
	padding: 12px 15px;
	margin-bottom: 10px;
	box-shadow: 0 1px 3px rgba(0,0,0,0.1);
	overflow-x: auto;
	white-space: nowrap;
}

.filter-item {
	padding: 6px 12px;
	margin-right: 10px;
	border-radius: 20px;
	font-size: 14px;
	color: #666;
	background-color: #f5f5f5;
	transition: all 0.3s ease;
	font-weight: 500;
	white-space: nowrap;
	flex-shrink: 0;
}

.filter-item.active {
	color: #fff;
	background-color: #007AFF;
	transform: translateY(-1px);
	box-shadow: 0 2px 8px rgba(0,122,255,0.3);
}

/* 申请列表 */
.applications-list {
	padding-bottom: 20px;
}

.application-item {
	margin: 0 15px 12px 15px;
	background-color: #fff;
	border-radius: 12px;
	overflow: hidden;
	padding: 18px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.08);
	transition: all 0.3s ease;
}

.application-item:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}

/* 用户信息 */
.user-info {
	display: flex;
	align-items: center;
	margin-bottom: 15px;
}

.user-avatar {
	width: 50px;
	height: 50px;
	border-radius: 50%;
	margin-right: 12px;
	border: 2px solid #f0f0f0;
}

.user-details {
	flex: 1;
}

.user-name-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 6px;
}

.user-name {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

.status-badge {
	padding: 4px 10px;
	border-radius: 12px;
	font-size: 12px;
	color: #fff;
	font-weight: 500;
}

.status-badge.pending {
	background: linear-gradient(135deg, #ffc107, #ffb300);
}

.status-badge.approved {
	background: linear-gradient(135deg, #28a745, #20c997);
}

.status-badge.rejected {
	background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.application-time {
	font-size: 12px;
	color: #999;
}

/* 申请内容 */
.application-content {
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border-radius: 10px;
	padding: 15px;
	margin-bottom: 15px;
	border: 1px solid #f0f0f0;
}

.reason-section, .role-section {
	margin-bottom: 10px;
}

.reason-section:last-child, .role-section:last-child {
	margin-bottom: 0;
}

.reason-label, .role-label {
	font-size: 13px;
	color: #666;
	margin-right: 4px;
	font-weight: 500;
}

.reason-text, .role-name {
	font-size: 14px;
	color: #333;
	word-break: break-all;
	line-height: 1.6;
}

/* 操作按钮 */
.action-section {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}

.action-btn {
	padding: 8px 40px;
	border-radius: 8px;
	font-size: 14px;
	border: none;
	font-weight: 500;
	transition: all 0.3s ease;
	min-width: 110px;
	height: 36px;
	line-height: 20px;
}

.action-btn.reject {
	background-color: #f8f9fa;
	color: #666;
	border: 1px solid #e9ecef;
}

.action-btn.reject:hover {
	background-color: #e9ecef;
	color: #495057;
	transform: translateY(-1px);
}

.action-btn.approve {
	background-color: #007AFF;
	color: #fff;
	border: 1px solid #007AFF;
}

.action-btn.approve:hover {
	background-color: #0056b3;
	color: #fff;
	transform: translateY(-1px);
}

/* 处理结果信息 */
.result-info {
	display: flex;
	flex-direction: column;
	font-size: 12px;
	color: #666;
	background-color: #f8f9fa;
	padding: 10px;
	border-radius: 8px;
}

.result-time, .result-operator {
	margin-bottom: 4px;
}

.result-comment {
	color: #333;
	font-weight: 500;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 0;
}

.empty-icon {
	width: 120px;
	height: 120px;
	margin-bottom: 20px;
	opacity: 0.6;
}

.empty-text {
	font-size: 16px;
	color: #999;
	font-weight: 500;
}

/* 弹窗样式 */
.popup-content {
	width: 85vw;
	background-color: #fff;
	border-radius: 16px;
	overflow: hidden;
	box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.popup-header {
	padding: 20px;
	border-bottom: 1px solid #f0f0f0;
	text-align: center;
	background: linear-gradient(135deg, #f8f9fa, #ffffff);
}

.popup-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

.popup-body {
	padding: 20px;
}

.popup-label {
	display: block;
	font-size: 14px;
	color: #333;
	margin-bottom: 10px;
	font-weight: 500;
}

.reject-reason {
	width: 100%;
	height: 100px;
	border: 1px solid #ddd;
	border-radius: 8px;
	padding: 12px;
	font-size: 14px;
	resize: none;
	transition: border-color 0.3s ease;
}

.reject-reason:focus {
	border-color: #007AFF;
	outline: none;
	box-shadow: 0 0 0 3px rgba(0,122,255,0.1);
}

.popup-footer {
	display: flex;
	border-top: 1px solid #f0f0f0;
}

.btn {
	flex: 1;
	height: 50px;
	line-height: 50px;
	text-align: center;
	font-size: 16px;
	border: none;
	background-color: transparent;
	transition: all 0.3s ease;
	font-weight: 500;
}

.btn-cancel {
	color: #666;
	border-right: 1px solid #f0f0f0;
}

.btn-cancel:hover {
	background-color: #f8f9fa;
	color: #495057;
}

.btn-confirm {
	color: #007AFF;
	font-weight: 600;
}

.btn-confirm:hover {
	background-color: #f0f8ff;
}
</style>
