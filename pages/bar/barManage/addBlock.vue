<template>
	<view class="add-block">
		<!-- 板块基本信息 -->
		<view class="section">
			<view class="input-item">
				<text class="label">板块名称</text>
				<input 
					type="text" 
					v-model="blockName" 
					placeholder="请输入板块名称" 
					class="input"
					maxlength="20"
				/>
			</view>
			<view class="input-item" @click="showIconPicker">
				<text class="label">板块图标</text>
				<view class="icon-display">
					<text class="selected-icon" v-if="selectedIcon">{{ selectedIcon }}</text>
					<text class="placeholder" v-else>请选择图标</text>
				</view>
				<uni-icons type="right" size="16" color="#999"></uni-icons>
			</view>
		</view>

		<!-- 图标选择器 -->
		<view class="icon-picker" v-if="showPicker">
			<view class="picker-header">
				<text class="picker-title">选择图标</text>
				<text class="picker-close" @click="hidePicker">完成</text>
			</view>
			<view class="icon-grid">
				<view 
					class="icon-item" 
					v-for="(icon, index) in iconList" 
					:key="index"
					:class="{ active: selectedIcon === icon }"
					@click="selectIcon(icon)"
				>
					<text class="icon-text">{{ icon }}</text>
				</view>
			</view>
		</view>

		<!-- 保存按钮 -->
		<view class="save-button" @click="saveBlock">
			<text class="save-text">保存</text>
		</view>
	</view>
</template>

<script>
import appServerApi from '@/api/appServerApi.js'

export default {
	data() {
		return {
			blockName: '',
			selectedIcon: '',
			showPicker: false,
			bbsId: '', // 当前贴吧ID
			iconList: [
				'📝', '💡', '🎨', '🎵', '🎮', '📚', '🏆', '⭐',
				'🔥', '💎', '🌟', '🎯', '🚀', '💫', '🎪', '🎭',
				'📱', '💻', '📷', '🎬', '📺', '🎧', '🎤', '🎸',
				'🎹', '🥁', '🎺', '🎼', '📖', '🔍', '⚡', '🌈'
			]
		}
	},
	onLoad(options) {
		// 获取贴吧ID，可能从上级页面传递或从全局状态获取
		if (options.bbsId) {
			this.bbsId = options.bbsId;
			console.log('addBlock 从URL参数获取 bbsId:', this.bbsId);
		} else {
			// 如果没有传递bbsId，可以从全局状态或缓存中获取
			this.bbsId = uni.getStorageSync('currentBbsId') || 1; // 默认值
			console.log('addBlock 从本地存储获取 bbsId:', this.bbsId);
		}
		console.log('addBlock 最终使用的 bbsId:', this.bbsId);
	},
	methods: {
		showIconPicker() {
			this.showPicker = true;
		},
		hidePicker() {
			this.showPicker = false;
		},
		selectIcon(icon) {
			this.selectedIcon = icon;
		},
		async saveBlock() {
			// 验证输入
			if (!this.blockName.trim()) {
				uni.showToast({
					title: '请输入板块名称',
					icon: 'none'
				});
				return;
			}
			
			if (!this.selectedIcon) {
				uni.showToast({
					title: '请选择板块图标',
					icon: 'none'
				});
				return;
			}

			// 显示加载提示
			uni.showLoading({
				title: '创建中...'
			});

			try {
				const moduleInfo = {
					bbsId: parseInt(this.bbsId),
					name: this.blockName.trim(),
					icon: this.selectedIcon
				};

				console.log('创建板块参数:', moduleInfo);

				const result = await appServerApi.createBbsModule(moduleInfo);
				
				console.log('创建板块API返回结果:', result);
				console.log('result类型:', typeof result);
				console.log('result值:', result);
				
				uni.hideLoading();
				
				// 根据API实际返回结构判断成功
				// API成功时返回 true，失败时返回完整的错误对象
				if (result === true) {
					console.log('进入成功分支');
					uni.showToast({
						title: '创建成功',
						icon: 'success'
					});
					
					// 延迟返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					console.log('进入失败分支');
					console.log('失败结果:', result);
					// 如果result是对象，尝试获取错误信息
					const errorMsg = (typeof result === 'object' && result) ? 
						(result.msg || result.message || '创建失败') : '创建失败';
					uni.showToast({
						title: errorMsg,
						icon: 'none'
					});
				}
			} catch (error) {
				uni.hideLoading();
				console.error('创建板块失败:', error);
				
				// 检查是否是ID相关的错误
				if (error.message && error.message.includes("Field 'id' doesn't have a default value")) {
					uni.showToast({
						title: '系统配置错误，请联系管理员',
						icon: 'none'
					});
				} else {
					uni.showToast({
						title: '创建失败，请重试',
						icon: 'none'
					});
				}
			}
		}
	}
}
</script>

<style>
.add-block {
	min-height: 100vh;
	background-color: #f5f7fa;
	padding-top: 12px;
	position: relative;
}

.section {
	background-color: #fff;
	margin-bottom: 12px;
}

.input-item {
	display: flex;
	align-items: center;
	padding: 16px;
	border-bottom: 1px solid #f5f5f5;
}

.input-item:last-child {
	border-bottom: none;
}

.label {
	font-size: 15px;
	color: #333;
	min-width: 90px;
}

.input {
	flex: 1;
	font-size: 15px;
	margin: 0 10px;
	color: #333;
}

.icon-display {
	flex: 1;
	margin: 0 10px;
	text-align: right;
}

.selected-icon {
	font-size: 20px;
}

.placeholder {
	font-size: 15px;
	color: #999;
}

/* 图标选择器样式 */
.icon-picker {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	border-radius: 20px 20px 0 0;
	max-height: 60vh;
	z-index: 1000;
	box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

.picker-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px 20px;
	border-bottom: 1px solid #f5f5f5;
}

.picker-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

.picker-close {
	font-size: 15px;
	color: #007AFF;
}

.icon-grid {
	display: grid;
	grid-template-columns: repeat(8, 1fr);
	gap: 10px;
	padding: 20px;
	max-height: 40vh;
	overflow-y: auto;
}

.icon-item {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 40px;
	height: 40px;
	border-radius: 8px;
	background-color: #f8f8f8;
	transition: all 0.2s ease;
}

.icon-item.active {
	background-color: #007AFF;
	transform: scale(1.1);
}

.icon-item.active .icon-text {
	transform: scale(1.2);
}

.icon-text {
	font-size: 18px;
	transition: transform 0.2s ease;
}

/* 保存按钮样式 */
.save-button {
	position: fixed;
	bottom: 30px;
	left: 50%;
	transform: translateX(-50%);
	width: 90%;
	background-color: #007AFF;
	padding: 16px 0;
	border-radius: 12px;
	text-align: center;
	box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
	transition: all 0.3s ease;
}

.save-button:active {
	transform: translateX(-50%) scale(0.98);
	background-color: #0056CC;
}

.save-text {
	color: #fff;
	font-size: 16px;
	font-weight: 500;
}

/* 当显示图标选择器时，调整保存按钮位置 */
.add-block:has(.icon-picker) .save-button {
	bottom: 65vh;
}
</style> 