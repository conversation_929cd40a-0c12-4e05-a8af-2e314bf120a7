<template>
  <view class="bar-square-container">
    <CustomHeader title="吧广场" color="#000">
      <template v-slot:right>
        <view class="op-btns">
          <image class="op-btns-icon" @click="toggleSearch" src="../../assets/images/bar/search.png" mode="aspectFit"></image>
        </view>
      </template>
    </CustomHeader>

    <!-- 搜索框 -->
    <view class="search-container" v-if="showSearch">
      <view class="search-box">
        <image class="search-icon" src="../../assets/images/bar/search.png"></image>
        <input 
          class="search-input" 
          type="text" 
          v-model="searchKeyword" 
          placeholder="搜索吧名称" 
          @input="handleSearchInput" 
          confirm-type="search"
          @confirm="handleSearch"
        />
        <image 
          v-if="searchKeyword.length > 0" 
          class="clear-icon" 
          src="../../assets/images/bar/closesearch.png" 
          @click="clearSearch"
        ></image>
      </view>
    </view>

    <!-- 主内容区域 - 两栏布局 -->
    <view class="main-content">
      <!-- 左侧分类导航 -->
      <view class="category-sidebar">
        <scroll-view scroll-y class="category-scroll">
          <view 
            v-for="(item, index) in categories" 
            :key="index" 
            class="category-item" 
            :class="{ active: currentCategory === index }"
            @click="handleCategoryClick(index)"
          >
            {{ item.name }}
          </view>
        </scroll-view>
      </view>

      <!-- 右侧吧列表 -->
      <view class="bar-list-container">
        <scroll-view 
          class="bar-list-scroll" 
          scroll-y 
          @scrolltolower="loadMoreBars"
          refresher-enabled
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onRefresh"
        >
          <view class="bar-list">
            <view 
              v-for="(bar, index) in barList" 
              :key="index" 
              class="bar-item"
              @click="toBarHome(bar)"
            >
              <view class="bar-avatar">
                <image :src="bar.avatar" mode="aspectFill"></image>
              </view>
              <view class="bar-info">
                <view class="bar-name">{{ bar.name }}</view>
                <view class="bar-stats">
                  <text>关注 {{ bar.fansNum }}</text>
                  <text>帖子 {{ bar.topicNum }}</text>
                </view>
              </view>
              <button 
                class="follow-button" 
                :class="{ 'followed': bar.follow === 1 }"
                @click.stop="toggleFollow(bar.bbsId, bar.follow)"
              >
                {{ bar.follow === 1 ? '已关注' : '+ 关注' }}
              </button>
            </view>
          </view>
          
          <!-- 加载状态 -->
          <view v-if="isLoading" class="loading-text">加载中...</view>
          <view v-if="!isLoading && !hasMore && barList.length > 0" class="loading-text">没有更多了</view>
          <view v-if="!isLoading && barList.length === 0" class="empty-state">
            <image src="/static/image/icon/empty.png" mode="aspectFit"></image>
            <text>暂无数据</text>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import appServerApi from '@/api/appServerApi'

export default {
  name: 'BarSquarePage',
  components: {
    CustomHeader
  },
  data() {
    return {
      showSearch: false,
      searchKeyword: '',
      categories: [
        { name: '全部', id: '' }
      ],
      currentCategory: 0,
      barList: [],
      pageNo: 1,
      pageSize: 20,
      hasMore: true,
      isLoading: false,
      isRefreshing: false
    }
  },
  onLoad() {
    // 获取所有吧分类
    this.getAllBbsCategories()
    // 默认加载全部吧
    this.currentCategory = 0 // 设置为"全部"分类
    this.fetchBarList()
  },
  methods: {
    // 获取所有吧分类
    getAllBbsCategories() {
      appServerApi
        .getAllBbsCategories()
        .then((response) => {
          console.log('getAllBbsCategories', response)
          var arr1 = [
            {
              name: '全部',
              id: '',
            }
          ]
          var arr2 = response.data
          this.categories = [...arr1, ...arr2]
        })
        .catch((error) => {
          console.error('获取吧分类失败:', error)
        })
    },
    
    // 切换搜索框显示状态
    toggleSearch() {
      this.showSearch = !this.showSearch
      if (!this.showSearch) {
        this.searchKeyword = ''
        this.fetchBarList()
      }
    },
    
    // 清除搜索内容
    clearSearch() {
      this.searchKeyword = ''
      this.fetchBarList()
    },
    
    // 处理搜索输入
    handleSearchInput() {
      // 可以实现实时搜索，这里简化处理
      if (this.searchKeyword.trim() === '') {
        this.fetchBarList()
      }
    },
    
    // 处理搜索确认
    handleSearch() {
      this.pageNo = 1
      this.fetchBarList()
    },
    
    // 处理分类点击
    handleCategoryClick(index) {
      if (this.currentCategory === index) return
      
      // 更新当前选中的分类
      this.currentCategory = index
      
      // 重置页码和列表数据
      this.pageNo = 1
      this.barList = []
      
      // 显示加载中状态
      this.isLoading = true
      
      // 获取新分类的数据
      this.fetchBarList()
      
      // 滚动到顶部
      this.$nextTick(() => {
        const scrollView = uni.createSelectorQuery().in(this).select('.bar-list-scroll')
        if (scrollView) {
          scrollView.node && scrollView.node.scrollTo({ top: 0 })
        }
      })
    },
    
    // 获取吧列表
    async fetchBarList(loadMore = false) {
      if (this.isLoading && loadMore) return
      
      this.isLoading = true
      
      try {
        // 如果不是加载更多，重置页码
        if (!loadMore) {
          this.pageNo = 1
          this.barList = []
        }
        
        // 获取当前分类ID
        const categoryId = this.categories[this.currentCategory].id
        
        // 调用API获取吧列表，确保categoryId是正确的类型
        const params = {
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          keyword: this.searchKeyword
        }
        
        // 只有当categoryId不为空时才添加到参数中
        if (categoryId) {
          // 如果categoryId是数字字符串，转换为数字类型
          if (!isNaN(categoryId)) {
            params.categoryId = parseInt(categoryId)
          } else {
            params.categoryId = categoryId
          }
        }
        
        console.log('获取吧列表参数:', params)
        
        // 根据当前选择的分类决定调用哪个API
        let response;
        try {
          // 获取所有吧或按分类获取吧
          response = await appServerApi.queryBbsList(params);
          console.log('获取吧列表响应:', response)
        } catch (apiError) {
          console.error('API调用失败:', apiError)
        }
        
        if (response?.data?.result) {
          const newBars = response.data.result
          
          if (loadMore) {
            // 加载更多时，追加数据
            this.barList = [...this.barList, ...newBars]
          } else {
            // 首次加载或刷新时，替换数据
            this.barList = newBars
          }
          
          // 判断是否还有更多数据
          this.hasMore = newBars.length >= this.pageSize
          
          // 如果有数据，页码加1
          if (newBars.length > 0) {
            this.pageNo++
          }
        } else {
          this.hasMore = false
          if (!loadMore && (!this.barList || this.barList.length === 0)) {
            console.log('没有获取到吧列表数据')
          }
        }
      } catch (error) {
        console.error('获取吧列表失败:', error)
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        })
        this.hasMore = false
      } finally {
        this.isLoading = false
        this.isRefreshing = false
      }
    },
    
    // 加载更多吧
    loadMoreBars() {
      if (this.hasMore && !this.isLoading) {
        this.fetchBarList(true)
      }
    },
    
    // 下拉刷新
    onRefresh() {
      this.isRefreshing = true
      this.pageNo = 1
      this.fetchBarList()
    },
    
    // 切换关注状态
    async toggleFollow(bbsId, follow) {
      try {
        const isFollowing = follow ? 0 : 1
        await appServerApi.toggleBbsFollow({
          id: bbsId,
          follow: isFollowing
        })
        
        // 更新本地数据
        this.barList = this.barList.map(bar => {
          if (bar.bbsId === bbsId) {
            return { ...bar, follow: isFollowing }
          }
          return bar
        })
        
        uni.showToast({
          title: isFollowing ? '关注成功' : '取消关注成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('更新关注状态失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    },
    
    // 跳转到吧主页
    toBarHome(bar) {
      uni.navigateTo({
        url: '/pages/bar/BarHome?id=' + bar.bbsId
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.bar-square-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.op-btns {
  display: flex;
  align-items: center;
}

.op-btns-icon {
  width: 20px;
  height: 20px;
}

/* 搜索框样式 */
.search-container {
  padding: 10px 15px;
  background-color: #fff;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 0 15px;
  height: 36px;
}

.search-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.search-input {
  flex: 1;
  height: 36px;
  font-size: 14px;
  background-color: transparent;
}

.clear-icon {
  width: 16px;
  height: 16px;
}

/* 主内容区域 - 两栏布局 */
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧分类导航 */
.category-sidebar {
  width: 80px;
  background-color: #f8f8f8;
  border-right: 1px solid #eee;
}

.category-scroll {
  height: 100%;
}

.category-item {
  padding: 15px 0;
  text-align: center;
  font-size: 14px;
  color: #666;
  position: relative;
}

.category-item.active {
  color: #386BF6;
  font-weight: bold;
  background-color: #fff;
}

.category-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background-color: #386BF6;
  border-radius: 0 1.5px 1.5px 0;
}

/* 右侧吧列表 */
.bar-list-container {
  flex: 1;
  overflow: hidden;
}

.bar-list-scroll {
  height: 100%;
}

.bar-list {
  padding: 10px;
}

.bar-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  margin-bottom: 10px;
  padding: 15px;
  border-radius: 10px;
}

.bar-avatar {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  overflow: hidden;
  margin-right: 12px;
}

.bar-avatar image {
  width: 100%;
  height: 100%;
}

.bar-info {
  flex: 1;
}

.bar-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #333;
}

.bar-stats {
  font-size: 12px;
  color: #999;
}

.bar-stats text {
  margin-right: 10px;
}

.follow-button {
  background-color: #d8e7fe;
  color: #386bf6;
  border-radius: 15px;
  font-size: 12px;
  width: 64px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  border: none;
  padding: 0;
}

.follow-button.followed {
  background-color: #f5f5f5;
  color: #999;
}

.follow-button::after {
  border: none;
}

/* 加载状态 */
.loading-text {
  text-align: center;
  padding: 15px;
  color: #999;
  font-size: 14px;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.empty-state image {
  width: 100px;
  height: 100px;
  margin-bottom: 10px;
}

.empty-state text {
  font-size: 14px;
  color: #999;
}
</style> 