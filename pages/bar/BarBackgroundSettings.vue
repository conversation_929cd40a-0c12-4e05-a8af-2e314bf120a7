<template>
  <view class="background-settings-container">
    <CustomHeader>
      <template v-slot:title>
        <text>背景设置</text>
      </template>
    </CustomHeader>

    <view class="preview-section">
      <image 
        :src="currentBackground || defaultBackground" 
        mode="aspectFill"
        class="preview-image"
      />
    </view>

    <view class="action-section">
      <button class="action-btn" @click="chooseImage">选择图片</button>
      <button class="action-btn reset" @click="resetBackground">恢复默认背景</button>
    </view>
  </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'

export default {
  components: {
    CustomHeader
  },

  data() {
    return {
      currentBackground: '',
      defaultBackground: '../../assets/images/bar/bar_bg_demo.png'
    }
  },

  onLoad() {
    this.loadBackgroundSetting()
  },

  methods: {
    loadBackgroundSetting() {
      try {
        const background = uni.getStorageSync('barBackgroundImage')
        if (background) {
          this.currentBackground = background
        }
      } catch (error) {
        console.error('读取背景设置失败:', error)
      }
    },

    saveBackgroundSetting(imagePath) {
      try {
        uni.setStorageSync('barBackgroundImage', imagePath)
        this.currentBackground = imagePath
      } catch (error) {
        console.error('保存背景设置失败:', error)
      }
    },

    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0]
          this.saveBackgroundSetting(tempFilePath)
        }
      })
    },

    resetBackground() {
      this.saveBackgroundSetting('')
      uni.showToast({
        title: '已恢复默认背景',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.background-settings-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.preview-section {
  margin: 20px;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.preview-image {
  width: 100%;
  height: 100%;
}

.action-section {
  padding: 20px;
}

.action-btn {
  width: 100%;
  height: 44px;
  border-radius: 22px;
  margin-bottom: 15px;
  background-color: #386BF6;
  color: #fff;
  font-size: 16px;
  border: none;

  &.reset {
    background-color: #f5f5f5;
    color: #666;
  }
}
</style> 