<script>
import { getItem } from '../../pages/util/storageHelper'

export default {
    data() {
        return {
            current: 0,
            tabs: [
                {
                    iconPath: '/static/image/conversation2.png',
                    selectedIconPath: '/static/image/conversation3.png',
                    text: '消息',
                },
                {
                    iconPath: '/static/image/contact2.png',
                    selectedIconPath: '/static/image/contact3.png',
                    text: '通讯录',
                },
                {
                    iconPath: '/static/image/discovery2.png',
                    selectedIconPath: '/static/image/discovery3.png',
                    text: '发现',
                },
                {
                    iconPath: '/static/image/me2.png',
                    selectedIconPath: '/static/image/me3.png',
                    text: '我的',
                },
            ],
            routes: [
                '/pages/conversationList/ConversationListPage',
                '/pages/contact/ContactPage',
                '/pages/discovery/DiscoveryPage',
                '/pages/me/MePage',
            ],
            youthModeEnabled: false
        }
    },
    onShow() {
        this._getPageCurrent()
        
        // 检查青少年模式状态
        this.youthModeEnabled = getItem('youth_mode_status') === 'enabled'
        
        // 如果开启了青少年模式，且当前页面是通讯录或发现，则跳转到消息页面
        if (this.youthModeEnabled && (this.current === 1 || this.current === 2)) {
            uni.switchTab({
                url: this.routes[0]
            })
        }
    },
    computed: {
        displayTabs() {
            // 如果开启了青少年模式，只显示消息和我的标签
            if (this.youthModeEnabled) {
                return [this.tabs[0], this.tabs[3]]
            }
            return this.tabs
        },
        displayRoutes() {
            // 如果开启了青少年模式，只返回消息和我的路由
            if (this.youthModeEnabled) {
                return [this.routes[0], this.routes[3]]
            }
            return this.routes
        }
    },
    methods: {
        tabbarSwitch(index) {
            const actualIndex = this.youthModeEnabled ? (index === 1 ? 3 : 0) : index
            this.current = actualIndex
            uni.switchTab({
                url: this.routes[actualIndex],
            })
        },
    },
}
</script>

<template>
    <view class="tab-bar" v-if="showTabBar">
        <view class="tab-bar-border"></view>
        <view
            v-for="(item, index) in displayTabs"
            :key="index"
            class="tab-bar-item"
            :class="{ active: displayTabs[index] === tabs[current] }"
            @click="tabbarSwitch(index)"
        >
            <image
                :src="
                    displayTabs[index] === tabs[current]
                        ? item.selectedIconPath
                        : item.iconPath
                "
            ></image>
            <view class="text">{{ item.text }}</view>
            <view
                v-if="
                    index === 0 &&
                    messageUnreadCount > 0
                "
                class="badge"
                >{{ messageUnreadCount > 99 ? '99+' : messageUnreadCount }}</view
            >
        </view>
    </view>
</template> 