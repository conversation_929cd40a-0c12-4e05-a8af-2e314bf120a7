<template>
    <view class="create-wrap">
        <view class="fixed-header">
            <CustomHeader title="帖子发布">
                <template v-slot:right>
                    <span class="publishbut" @click="uploadFile()">发布</span>
                </template>
            </CustomHeader>
        </view>
        <view class="main-block">
            <form>
                <div class="img-block">
                    <!-- 视频预览 -->
                    <view v-if="video" class="video-block">
                        <video 
                            :src="video" 
                            class="preview-video"
                            controls
                        />
                        <view class="delete">
                            <image src="/static/image/icon/delete.png" @click="deleteVideo"></image>
                        </view>
                    </view>
                    
                    <!-- 图片预览 -->
                    <template v-else>
                        <view class="file-block" v-for="(item,index) in imgList" :key="index"> 
                            <image class="avatar" :src="item"></image>
                            <image class="delete" src="/static/image/icon/delete.png" @click="deleteImage(index)"></image>
                        </view>
                        <!-- 添加媒体按钮 -->
                        <div v-if="imgList.length < 9" class="file-block" @tap="showMediaPicker">
                            <i class="icon-ion-android-add" style="font-size: 40px;color: #666666"></i>
                        </div>
                    </template>
                </div>
                <div style="height: 20px;margin-top: 20px;">
                    <textarea style="min-height:20px" auto-height placeholder="添加标题…" v-model="bbsInfo.title" />
                </div>
                <!-- <view class="divider"></view> -->
                <textarea 
                    class="post-content-textarea"
                    placeholder="添加正文…"
                    v-model="bbsInfo.content"
                    :auto-height="true"
                    @input="handleContentInput"
                    ref="contentTextarea"
                    style="width: 100%; min-height: 140px; max-height: 1000px; overflow-y: auto; resize: none; box-sizing: border-box;"
                    maxlength="2000"
                ></textarea>
                <!-- 分割线 -->
                <view class="divider"></view>
                 <!-- 记录地点 -->
                <!-- <view class="option-item" @click="onLocationClick">
                    <image class="option-icon" src="../../assets/images/bar/location.png" alt="location" />
                    <text>记录地点</text>
                    <view class="option-value">{{ location || '未选择' }}</view>
                    <i class="icon-ion-ios-arrow-right"></i>
                </view>
                <view class="divider"></view> -->
                <!-- 公开可见 -->
                <!-- <view class="option-item" @click="onVisibleClick">
                    <image class="option-icon" src="../../assets/images/bar/publiclyvisible.png" alt="publiclyvisible" />
                    <text>公开可见</text>
                    <view class="option-value">{{ visibleText }}</view>
                    <i class="icon-ion-ios-arrow-right"></i>
                </view>
                <view class="divider"></view> -->
                <!-- 发布渠道 -->
                <view class="option-item" @click="showBarSelectModal = true">
                    <image class="option-icon" src="../../assets/images/bar/publishingchannels.png" alt="publishingchannels" />
                    <text>选择的吧</text>
                    <view class="option-value" style="display: flex; align-items: center;">
                        <template v-if="bardata">
                            <image v-if="bardata.avatar" :src="bardata.avatar" style="width: 24px; height: 24px; border-radius: 4px; margin-right: 6px;" />
                            <span v-if="bardata.name" style="font-size: 14px; color: #333;">{{ bardata.name }}</span>
                        </template>
                        <span v-else>{{ channelText }}</span>
                    </view>
                    <i class="icon-ion-ios-arrow-right"></i>
                </view>

                <!-- 添加板块选择 -->
                <view class="option-item" @click="showModuleSelect">
                    <uni-icons type="grid" size="20" color="#386bf6" style="margin-right: 10px;"></uni-icons>
                    <text>选择板块</text>
                    <view class="option-value" style="display: flex; align-items: center;">
                        <template v-if="selectedModuleName">
                            <span style="font-size: 14px; color: #333;">{{ selectedModuleName }}</span>
                        </template>
                        <span v-else>{{ '选择合适的板块会有更多点赞哦' }}</span>
                    </view>
                    <i class="icon-ion-ios-arrow-right"></i>
                </view>

                <!-- 推荐吧横向列表 -->
                <scroll-view v-if="recommendBarsList && recommendBarsList.length" class="recommend-bar-list-scroll" scroll-x>
                    <view class="recommend-bar-list">
                        <view v-for="(bar, idx) in recommendBarsList" :key="bar.id" @click="onRecommendBarClick(bar)" class="recommend-bar-item">
                            <image :src="bar.avatar" class="recommend-bar-avatar" />
                            <span class="recommend-bar-name">{{ bar.name }}</span>
                        </view>
                    </view>
                </scroll-view>

                <!-- 自定义选择吧弹窗 -->
                <view v-if="showBarSelectModal" class="bar-select-modal-mask" @click="handleMaskClick">
                    <view class="bar-select-modal" @click.stop>
                        <!-- 顶部 吸顶 -->
                        <view class="bar-select-modal-header">
                            <span class="bar-select-modal-close" @click="showBarSelectModal = false">×</span>
                            <span v-if="!showSearch" class="bar-select-modal-title">选择的吧</span>
                            <!-- <span class="bar-select-modal-search" @click="onBarSearch"><i class="iconfont icon-search"></i></span> -->
                            <image v-if="!showSearch" class="op-btns-icon" @click.stop="toggleSearch" src="../../assets/images/bar/search.png" mode="">
                            </image>

                            <view v-if="showSearch" class="search-container" @click.stop>
                                <view class="search-input-wrapper">
                                    <input 
                                        class="search-input" 
                                        type="text" 
                                        v-model="searchKeyword" 
                                        placeholder="请输入搜索内容" 
                                        @input="handleSearchInput" 
                                        ref="searchInput" 
                                        focus 
                                    />
                                    <view class="cancel-btn" @click.stop="toggleSearch">取消</view>
                                </view>
                            </view>
                        </view>
                        <!-- 搜索框 -->
                        <!-- <view class="bar-select-modal-searchbox">
                            <input class="bar-select-modal-searchinput" v-model="searchKeyword" placeholder="搜索吧名称" @confirm="onBarSearch" />
                        </view> -->
                        <!-- 内容滚动区 -->
                        <view class="bar-select-modal-scroll">
                            <!-- 搜索结果展示 -->
                            <view v-if="showSearch && searchKeyword" class="bar-select-modal-section">
                                <div class="bar-select-modal-section-title">搜索结果</div>
                                <div class="search-result-section">
                                    <view v-if="searchResults.length > 0" class="bar-select-modal-recommend-list">
                                        <view v-for="bar in searchResults" 
                                              :key="bar.bbsId" 
                                              class="bar-select-modal-recommend-item" 
                                              @click="onRecommendBarClick(bar)">
                                            <image :src="bar.avatar" class="bar-select-modal-recommend-avatar" />
                                            <div class="bar-select-modal-recommend-name">{{ bar.name }}</div>
                                        </view>
                                    </view>
                                    <view v-else class="bar-select-modal-empty">
                                        暂无搜索结果
                                    </view>
                                </div>
                            </view>

                            <!-- 非搜索状态或搜索框为空时显示的内容 -->
                            <template v-else>
                                <!-- 为你推荐 -->
                                <view class="bar-select-modal-section">
                                    <div class="bar-select-modal-section-title">为你推荐</div>
                                    <view class="bar-select-modal-recommend-list">
                                        <view v-for="bar in recommendBarsList" 
                                              :key="bar.id" 
                                              class="bar-select-modal-recommend-item" 
                                              @click="onRecommendBarClick(bar)">
                                            <image :src="bar.avatar" class="bar-select-modal-recommend-avatar" />
                                            <div class="bar-select-modal-recommend-name">{{ bar.name }}</div>
                                        </view>
                                    </view>
                                </view>

                                <!-- 最近浏览 -->
                                <view class="bar-select-modal-section">
                                    <div class="bar-select-modal-collapse" @click="toggleCollapse('recent')">
                                        <span>最近浏览</span>
                                        <i v-if="!collapse.recent" class="icon-ion-ios-arrow-right"></i>
                                        <i v-if="collapse.recent" class="icon-ion-ios-arrow-down"></i>
                                    </div>
                                    <view v-if="collapse.recent" class="bar-select-modal-collapse-content">
                                        <!-- 有数据时渲染列表 -->
                                        <view v-if="viewHistory && viewHistory.length > 0" 
                                              v-for="bar in viewHistory" 
                                              :key="bar.id" 
                                              class="bar-select-modal-recommend-item" 
                                              @click="onRecommendBarClick(bar)">
                                            <image :src="bar.avatar" class="bar-select-modal-recommend-avatar" />
                                            <div class="bar-select-modal-recommend-name">{{ bar.name }}</div>
                                        </view>
            
                                        <!-- 无数据时显示提示 -->
                                        <view v-else class="bar-select-modal-empty">
                                            目前还没有最近浏览的贴吧哦，快去贴吧广场看看吧！
                                        </view>
                                    </view>
                                </view>

                                <!-- 我创建的 -->
                                <view class="bar-select-modal-section">
                                    <div class="bar-select-modal-collapse" @click="toggleCollapse('created')">
                                        <span>我创建的</span>
                                        <i v-if="!collapse.created" class="icon-ion-ios-arrow-right"></i>
                                        <i v-if="collapse.created" class="icon-ion-ios-arrow-down"></i>
                                    </div>
                                    <view v-if="collapse.created" class="bar-select-modal-collapse-content">
                                        <view v-if="userCreatedBars && userCreatedBars.length > 0"
                                              v-for="bar in userCreatedBars" 
                                              :key="bar.id" 
                                              class="bar-select-modal-recommend-item" 
                                              @click="onRecommendBarClick(bar)">
                                            <image :src="bar.avatar" class="bar-select-modal-recommend-avatar" />
                                            <div class="bar-select-modal-recommend-name">{{ bar.name }}</div>
                                        </view>
                                        <view v-else class="bar-select-modal-empty">
                                            目前您还没有创建过吧哦，快去创建一个吧！
                                        </view>
                                    </view>
                                </view>

                                <!-- 我关注的 -->
                                <view class="bar-select-modal-section">
                                    <div class="bar-select-modal-collapse" @click="toggleCollapse('followed')">
                                        <span>我关注的</span>
                                        <i v-if="!collapse.followed" class="icon-ion-ios-arrow-right"></i>
                                        <i v-if="collapse.followed" class="icon-ion-ios-arrow-down"></i>
                                    </div>
                                    <view v-if="collapse.followed" class="bar-select-modal-collapse-content">
                                        <view v-if="userFellowBars && userFellowBars.length > 0"
                                              v-for="bar in userFellowBars" 
                                              :key="bar.id" 
                                              class="bar-select-modal-recommend-item" 
                                              @click="onRecommendBarClick(bar)">
                                            <image :src="bar.avatar" class="bar-select-modal-recommend-avatar" />
                                            <div class="bar-select-modal-recommend-name">{{ bar.name }}</div>
                                        </view>
                                        <view v-else class="bar-select-modal-empty">
                                            目前您还没有关注的吧哦，去吧广场关注一些吧！
                                        </view>
                                    </view>
                                </view>

                                <!-- BarSquarePanel组件 -->
                                <view class="bar-select-modal-component">
                                    <BarSquarePanel ref="barSquarePanel" @selectBar="onBarSelected" />
                                </view>
                            </template>
                        </view>
                    </view>
                </view>

                <!-- 板块选择弹窗 -->
                <uni-popup ref="modulePopup" type="bottom">
                    <view class="module-popup">
                        <view class="module-popup-header">
                            <text class="module-popup-title">选择板块</text>
                            <text class="module-popup-close" @click="hideModulePopup">×</text>
                        </view>
                        <view class="module-popup-content">
                            <view v-if="moduleList.length > 0" class="module-list">
                                <view 
                                    v-for="module in moduleList" 
                                    :key="module.id" 
                                    class="module-item"
                                    :class="{'module-item-selected': selectedModuleId === module.id}"
                                    @click="selectModule(module)"
                                >
                                    <view class="module-item-content">
                                        <text class="module-emoji" v-if="isEmoji(module.icon)">{{ module.icon }}</text>
                                        <image v-else-if="module.icon" :src="module.icon" class="module-icon" @error="handleIconError($event, module)" />
                                        <text class="module-name">{{ module.name }}</text>
                                    </view>
                                    <uni-icons v-if="selectedModuleId === module.id" type="checkmarkempty" size="20" color="#386bf6"></uni-icons>
                                </view>
                            </view>
                            <view v-else class="module-empty">
                                <text>暂无可选板块</text>
                            </view>
                        </view>
                    </view>
                </uni-popup>
            </form>
        </view>
    </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import appServerApi from '@/api/appServerApi'
import util from '@/utils/util'
import topMessage from '@/common/topMessageView'
import BarSelectPanel from '@/components/barselect/BarSelectPanel.vue'
import BarSquarePanel from '../../components/barselect/BarSquarePanel.vue'
import { getItem } from '../util/storageHelper'

export default {
    components: {
        CustomHeader,
        BarSelectPanel,
        BarSquarePanel,
    },
    data() {
        return {
            userInfo: {},
            isAgree: false,
            bbsInfo: {
                name: '',
                categoryId: '',
                avatar: '',
                title: '',
                content: '',
            },
            categoryList: [],
            recommendBarsList: [],
            index: null,
            task: null,
            croppedImage: null,
            show: false,
            imgList: [],
            url: [],
            id: '',
            bardata: null,
            video: '', // 新增视频URL
            location: '',
            visibleText: '公开可见',
            channelText: '选择合适的吧会有更多点赞哦',
            showBarSelectModal: false,
            collapse: {
                recent: false,
                created: false,
                followed: false,
            },

            viewHistory: [],//最近浏览吧
            userCreatedBars: [],
            userFellowBars: [],

            pageNo: 1,
            pageSize: 10,
            showSearch: false,
            searchKeyword: '',
            searchResults: [], // 添加搜索结果数组
            isSearching: false, // 添加搜索状态标识
            moduleList: [], // 板块列表
            selectedModuleId: '', // 选中的板块ID
            selectedModuleName: '', // 选中的板块名称
            textareaHeight: '140px', // 添加新的数据属性
        }
    },
    onLoad(options) {
        console.log('PublishPosts onLoad options:', options)
        
        // 处理可能传递的贴吧ID
        if (options && options.id) {
            this.id = options.id
        }
        
        // 处理可能传递的贴吧对象
        if (options && options.obj) {
            try {
                this.bardata = JSON.parse(options.obj)
            } catch (e) {
                console.error('解析贴吧对象失败:', e)
            }
        }
        
        this.getAllBbsCategories()
        this.getRecommendBars()

        const userInfo = getItem('userInfo')
        console.log("页面得到的用户信息：", userInfo)
        this.userInfo = userInfo

        // 只有 userInfo 存在且 userId 有值时再调用后续方法
        if (userInfo && userInfo.userId) {
            this.getUserCreatedBars()
            this.getUserFollowedBars()
        } else {
            console.warn('userInfo 或 userId 不存在，无法获取用户相关的吧')
        }

        this.fetchViewHistory()
    },
    onShow() {
        var that = this
        uni.$on('bardata', function (data) {
            console.log('bardata', data)
            that.bardata = data
            that.channelText = data.name
        })
    },
    methods: {
        onischecked: function () {
            this.isAgree = !this.isAgree
        },
        toBarSelect() {
            uni.navigateTo({
                url: '/pages/bar/BarSelect',
            })
        },
        getAllBbsCategories() {
            appServerApi
                .getAllBbsCategories()
                .then((response) => {
                    console.log('getAllBbsCategories', response)
                    this.categoryList = response.data
                })
                .catch((error) => {
                    console.error('Error uploading file:', error)
                })
        },
        getRecommendBars() {
            appServerApi
                .getRecommendBars()
                .then((response) => {
                    console.log('获取推荐吧：', response)
                    this.recommendBarsList = response.data
                    console.log("页面中得到的推荐吧数据：",this.recommendBarsList)
                })
                .catch((error) => {
                    console.error('获取推荐吧失败:', error)
                })
        },
        //获取用户创建的吧
        getUserCreatedBars(){
            console.log('getUserCreatedBars called, userInfo:', this.userInfo)
            appServerApi
                .getUserCreatedBars(
                    this.userInfo.userId,
                    this.pageNo,
                    this.pageSize
                )
                .then((response) => {
                    console.log('获取用户创建吧：', response)
                    this.userCreatedBars = response.data.result
                    console.log("页面中得到的创建吧数据：",this.userCreatedBars)
                })
                .catch((error) => {
                    console.error('获取用户创建吧失败:', error)
                })
      },
      
        //获取用户关注的吧
        getUserFollowedBars(){
            console.log('getUserFollowedBars called, userInfo:', this.userInfo)
            appServerApi
                .getUserFollowedBars(
                    this.userInfo.userId,
                    this.pageNo,
                    this.pageSize
                )
                .then((response) => {
                    console.log('获取用户关注吧：', response)
                    this.userFellowBars = response.data.result
                    console.log("页面中得到的关注吧数据：",this.userFellowBars)
                })
                .catch((error) => {
                    console.error('获取用户创建吧失败:', error)
                })
      },

      fetchViewHistory() {
            try {
                const history = uni.getStorageSync('barViewHistory') || []
                this.viewHistory = history
            } catch (error) {}
        },

        toggleSearch() {
            this.showSearch = !this.showSearch;
            
            if (!this.showSearch) {
                // 关闭搜索时，重置搜索相关状态
                this.searchKeyword = '';
                this.searchResults = [];
                this.isSearching = false;
                
                // 重新获取所有数据
                this.getRecommendBars();
                if (this.userInfo && this.userInfo.userId) {
                    this.getUserCreatedBars();
                    this.getUserFollowedBars();
                }
                this.fetchViewHistory();
                
                // 如果有barSquarePanel组件，刷新其数据
                this.$nextTick(() => {
                    if (this.$refs.barSquarePanel) {
                        this.$refs.barSquarePanel.getAllBbsCategories && this.$refs.barSquarePanel.getAllBbsCategories();
                        this.$refs.barSquarePanel.fetchBarList && this.$refs.barSquarePanel.fetchBarList();
                    }
                });
            }
        },
        bindPickerChange: function (e) {
            console.log('picker发送选择改变，携带值为', e.detail.value)
            this.index = e.detail.value
        },
        deleteImage(index) {
            this.imgList.splice(index, 1)
        },
        async showMediaPicker() {
            uni.showActionSheet({
                itemList: ['拍摄', '从相册选择图片', '从相册选择视频'],
                success: async (res) => {
                    switch(res.tapIndex) {
                        case 0: // 拍摄
                            uni.showActionSheet({
                                itemList: ['拍照', '拍视频'],
                                success: async (res2) => {
                                    if (res2.tapIndex === 0) {
                                        this.compressImage(['camera'])
                                    } else {
                                        // 拍视频
                                        this.chooseVideo('camera')
                                    }
                                }
                            })
                            break
                        case 1: // 从相册选择图片
                            break
                        case 2: // 从相册选择视频
                            this.chooseVideo('album')
                            break
                    }
                }
            })
        },
        async compressImage(sourceType = ['album']) {
            if (this.video) {
                uni.showToast({
                    title: '不能同时选择图片和视频',
                    icon: 'none'
                })
                return
            }
            
            const maxCount = 9
            const remain = maxCount - this.imgList.length
            if(remain <= 0){
                uni.showToast({
                    title: '最多只能选择9张图片',
                    icon: 'none'
                })
                return
            }
            

            // 没有权限问题或iOS设备，直接选择图片
            this.directChooseImage(sourceType, remain);
        },
        
        // 提取出直接选择图片的方法
        directChooseImage(sourceType, remain) {
            uni.chooseImage({
                count: remain,
                sizeType: ['compressed'],
                sourceType: sourceType,
                success: (res) => {
                    this.imgList = [...this.imgList, ...res.tempFilePaths]
                },
                fail: (err) => {
                    console.log('chooseImage fail', err)
                    // 增加更详细的错误反馈
                    if (err.errMsg && err.errMsg.indexOf('permission') > -1) {
                        uni.showToast({
                            title: '无法访问相册，请在系统设置中授权',
                            icon: 'none',
                            duration: 3000
                        });
                    }
                },
            })
        },
        chooseVideo(sourceType = 'album') {
            if (this.imgList.length > 0) {
                uni.showToast({
                    title: '不能同时选择图片和视频',
                    icon: 'none'
                })
                return
            }
            
            uni.chooseVideo({
                sourceType: [sourceType],
                maxDuration: 30,
                success: (res) => {
                    if (res.duration > 30) {
                        uni.showToast({
                            title: '视频时长不能超过30秒',
                            icon: 'none'
                        })
                        return
                    }
                    
                    if (res.size > 100 * 1024 * 1024) {
                        uni.showToast({
                            title: '视频大小不能超过100MB',
                            icon: 'none'
                        })
                        return
                    }
                    
                    // 上传视频
                    util.uploadimgFile(
                        'ossfile/upload',
                        res.tempFilePath,
                        (uploadRes) => {
                            console.log('视频上传成功:', uploadRes)
                            this.video = uploadRes.data
                            this.url = [uploadRes.data]
                        },
                        (error) => {
                            console.error('视频上传失败:', error)
                            uni.showToast({
                                title: '视频上传失败',
                                icon: 'none'
                            })
                        }
                    )
                }
            })
        },
        deleteVideo() {
            this.video = ''
            this.url = []
        },
        handleMoreOp() {
            // Handle more operations
        },
        uploadFile() {
            // 检查是否选择了贴吧
            if (!this.bardata) {
                this.showBarSelectModal = true;
                return;
            }
            // 检查标题是否为空
            if (!this.bbsInfo.title || this.bbsInfo.title.trim() === '') {
                uni.showToast({
                    title: '请添加标题',
                    icon: 'none'
                });
                return;
            }
            // 确保设置了bbsId
            this.id = this.bardata.bbsId;
            
            // 检查内容是否为空
            // if (!this.bbsInfo.content || this.bbsInfo.content.trim() === '') {
            //     uni.showToast({
            //         title: '请输入帖子内容',
            //         icon: 'none'
            //     });
            //     return;
            // }
            
            // 继续原有的上传逻辑
            uni.showLoading({
                title: '正在发布中',
            })

            // 如果已经有视频URL，直接发布
            if (this.video) {
                this.handlePublish()
                return
            }

            // 处理图片上传
            if (this.imgList.length == 0) {
                this.handlePublish()
                return
            }

            var that = this
            const tasks = []
            for (var i = 0; i < that.imgList.length; i++) {
                tasks.push(
                    new Promise((resolve, reject) => {
                        console.log('i', i)
                        console.log('i', that.imgList[i])
                        util.uploadimgFile(
                            'ossfile/upload',
                            that.imgList[i],
                            function (res) {
                                console.log('图片地址', res)
                                // that.url = res.data
                                that.url.push(res.data)
                                resolve()
                            },
                            function (res) {
                                console.log('图片上传失败', res)
                                reject()
                            }
                        )
                    })
                )
            }

            // 所有 Promise 完成后执行函数
            Promise.all(tasks)
                .then(() => {
                    that.handlePublish()
                    console.log('所有图片上传完成')
                })
                .catch((error) => {
                    uni.hideLoading()
                })
        },
        handlePublish() {
            try {
                const bbsId = this.bardata ? this.bardata.bbsId : this.id;
                
                if (!bbsId) {
                    uni.hideLoading();
                    this.showBarSelectModal = true;
                    return;
                }
                
                const title = this.bbsInfo.title;
                const content = this.bbsInfo.content;
                const url = this.url || [];
                
                console.log('准备发布帖子，参数：', {
                    title,
                    content,
                    bbsId,
                    moduleId: this.selectedModuleId,
                    url: JSON.stringify(url)
                });
                
                if (!Array.isArray(url)) {
                    console.warn('url不是数组，正在转换');
                    url = url ? [url] : [];
                }
                
                appServerApi
                    .publishPost(title, content, this.selectedModuleId, bbsId, url)
                    .then((response) => {
                        console.log('发布帖子成功，响应：', response);
                        
                        uni.$emit('barPostPublished', bbsId);
                        console.log('已触发刷新事件 barPostPublished');
                        
                        const pages = getCurrentPages();
                        const prevPage = pages[pages.length - 2];
                        
                        if (prevPage && prevPage.route === 'pages/bar/BarHome') {
                            prevPage.$vm.pageNo = 1;
                            prevPage.$vm.posts = [];
                            prevPage.$vm.hasMore = true;
                            
                            prevPage.$vm.fetchPosts(bbsId, 'reset').then(() => {
                                uni.showToast({
                                    title: '发布成功',
                                    icon: 'success',
                                    duration: 1500
                                });
                                
                                setTimeout(() => {
                                    uni.navigateBack({
                                        delta: 1
                                    });
                                }, 1500);
                            });
                        } else {
                            uni.showToast({
                                title: '发布成功',
                                icon: 'success'
                            });
                            uni.navigateBack({
                                delta: 1
                            });
                        }
                    })
                    .catch((error) => {
                        uni.hideLoading();
                        console.error('Error publishing post:', error);
                        uni.showToast({
                            title: '发布失败: ' + (error?.message || '未知错误'),
                            icon: 'none',
                            duration: 2000
                        });
                    });
            } catch (e) {
                uni.hideLoading();
                console.error('发布帖子过程中出现异常:', e);
                uni.showToast({
                    title: '发布失败: ' + (e?.message || '未知错误'),
                    icon: 'none',
                    duration: 2000
                });
            }
        },
        onLocationClick() {
            uni.showActionSheet({
                itemList: ['不显示', '北京', '上海', '广州'],
                success: (res) => {
                    if (res.tapIndex === 0) {
                        this.location = ''
                    } else {
                        this.location = ['北京', '上海', '广州'][res.tapIndex - 1]
                    }
                }
            })
        },
        onVisibleClick() {
            uni.showActionSheet({
                itemList: ['公开可见', '仅好友可见', '仅自己可见'],
                success: (res) => {
                    this.visibleText = ['公开可见', '仅好友可见', '仅自己可见'][res.tapIndex]
                }
            })
        },
        onChannelClick() {
            uni.showActionSheet({
                itemList: ['贴吧', '视界'],
                success: (res) => {
                    if (res.tapIndex === 0) {
                        this.channelText = '贴吧';
                        this.showBarSelectModal = true;
                    } else if (res.tapIndex === 1) {
                        this.channelText = '视界';
                        this.showBarSelectModal = false;
                    }
                }
            })
        },
        onBarSelected(bar) {
            this.bardata = bar;
            this.showBarSelectModal = false;
            this.channelText = bar.name;
        },
        onRecommendBarClick(bar) {
            // 兼容推荐吧没有bbsId字段的情况
            this.bardata = {
                ...bar,
                bbsId: bar.bbsId || bar.id // 优先用bbsId，没有就用id
            };
            this.showBarSelectModal = false;
            this.channelText = bar.name;
        },
        toggleCollapse(type) {
            // this.collapse[type] = !this.collapse[type];
            // 保存当前状态
            const currentState = this.collapse[type];
            // 先关闭所有折叠项
            for (const key in this.collapse) {
                this.collapse[key] = false;
            }
            // 然后切换当前点击的折叠项状态
            this.collapse[type] = !currentState;
        },
        onBarSearch() {
            // 搜索点击事件，后续可扩展
        },
        handleMaskClick(e) {
            this.showBarSelectModal = false;
        },
        // 显示板块选择弹窗
        showModuleSelect() {
            if (!this.bardata || !this.bardata.bbsId) {
                uni.showToast({
                    title: '请先选择要发布到的贴吧',
                    icon: 'none'
                });
                return;
            }
            this.fetchModuleList();
            this.$refs.modulePopup.open();
        },
        
        // 隐藏板块选择弹窗
        hideModulePopup() {
            this.$refs.modulePopup.close();
        },
        
        // 获取板块列表
        fetchModuleList() {
            const bbsId = this.bardata?.bbsId;
            if (!bbsId) return;
            
            appServerApi.getBbsModuleList(bbsId)
                .then(response => {
                    if (response && response.code === 200) {
                        this.moduleList = response.data || [];
                    } else {
                        console.error('获取板块列表失败:', response?.msg);
                    }
                })
                .catch(error => {
                    console.error('获取板块列表出错:', error);
                });
        },
        
        // 选择板块
        selectModule(module) {
            this.selectedModuleId = module.id;
            this.selectedModuleName = module.name;
            this.hideModulePopup();
        },
        
        // 检查是否是emoji
        isEmoji(icon) {
            if (!icon) return false;
            const emojiRegex = /^(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])+$/;
            return emojiRegex.test(icon);
        },
        
        // 处理图标加载错误
        handleIconError(event, module) {
            if (!this.isEmoji(module.icon)) {
                console.error('板块图片图标加载失败:', {
                    module: module.name,
                    iconUrl: module.icon
                });
                module.icon = '';
            }
        },
        handleContentInput() {
            // 使用nextTick确保DOM已更新
            this.$nextTick(() => {
                const textarea = this.$refs.contentTextarea;
                if (!textarea) return;
                
                // 重置高度，以便正确计算scrollHeight
                textarea.style.height = '140px';
                
                // 计算内容的实际高度
                const scrollHeight = textarea.scrollHeight;
                // 设置最小高度（5行）和最大高度（10行）
                const minHeight = 140; // 约5行
                const maxHeight = 220; // 约10行
                
                // 根据内容高度调整textarea高度
                if (scrollHeight <= maxHeight) {
                    this.textareaHeight = Math.max(minHeight, scrollHeight) + 'px';
                    textarea.style.height = this.textareaHeight;
                    textarea.style.overflowY = 'hidden';
                } else {
                    this.textareaHeight = maxHeight + 'px';
                    textarea.style.height = this.textareaHeight;
                    textarea.style.overflowY = 'auto';
                }
            });
        },
        // 处理搜索输入
        handleSearchInput() {
            if (!this.searchKeyword.trim()) {
                this.searchResults = [];
                this.isSearching = false;
                return;
            }
            
            this.isSearching = true;
            // 使用防抖处理搜索
            if (this.searchTimer) {
                clearTimeout(this.searchTimer);
            }
            
            this.searchTimer = setTimeout(() => {
                this.searchBars();
            }, 300);
        },
        
        // 搜索贴吧
        searchBars() {
            const params = {
                pageNo: 1,
                pageSize: 10,
                keyword: this.searchKeyword.trim()
            };
            
            appServerApi.queryBbsList(params)
                .then(response => {
                    if (response && response.data && response.data.result) {
                        this.searchResults = response.data.result;
                    } else {
                        this.searchResults = [];
                    }
                })
                .catch(error => {
                    console.error('搜索贴吧失败:', error);
                    this.searchResults = [];
                });
        },
    },
    watch: {
        showBarSelectModal(val) {
            if (val) {
                // 弹窗显示时，主动加载数据
                this.$nextTick(() => {
                    if (this.$refs.barSquarePanel) {
                        this.$refs.barSquarePanel.getAllBbsCategories && this.$refs.barSquarePanel.getAllBbsCategories();
                        this.$refs.barSquarePanel.fetchBarList && this.$refs.barSquarePanel.fetchBarList();
                    }
                });
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.create-wrap {
    background: #f5f5f5;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    z-index: 1000;
    background: #f5f5f5;
    // 你可以根据 CustomHeader 的高度调整高度
    // 假设 CustomHeader 高度为 56px
    height: 80px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.03);
}

.main-block {
    padding: 10px 10px;
    background: #fff;
    border-radius: 10px;
    margin: 0 12px;
    margin-top: 80px;
    overflow: visible !important;
}


.op-btns-icon{
    width: 16px;
    height: 16px;
}
.common-block-style {
    margin-top: 10px;
    background-color: #fff;
    padding: 14px;
    border-radius: 10px;
}

.info-content {
    display: flex;
    gap: 18px;

    // .info-pic {
    //     // flex: 4;
    // }
}

.rq-l18 {
    margin-top: 17px;
    font-size: 14px;
    color: #999999;
    display: flex;
    align-items: center;
    padding-left: 8px;
}

.rqla {
    width: 17px;
    height: 17px;
    background-color: #ffffff;
    border-radius: 50%;
    border: 1px solid #999;
    margin-right: 5px;

    .rqla1 {
        width: 12px;
        height: 12px;
    }
}

.rqlb {
    border: 1px solid #386bf6;
    background-color: #386bf6;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rq-l18 .rq-l6xy {
    color: #3b37de;
}

.title-block {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    border-bottom: 1px solid #e5e5e5;

    i {
        font-size: 20px;
    }

    textarea {
        width: 100%;
        height: 100px;
        padding: 14px 24px;
        border-radius: 10px;
        background: #ffffff;
        font-size: 16px;
    }

    .title2 {
        color: #000;
        font-family: MiSans;
        font-size: 16px;
        margin-left: 15px;
    }

    .uni-input {
        color: #999;
        font-family: MiSans;
        font-size: 11px;
        display: flex;
        align-items: center;
        gap: 6px;
        padding-right: 8px;
        
        .avatar {
            width: 28px;
            height: 28px;
            border-radius: 5px;
            margin-right: 4px;
        }
    }

    .left {
        display: flex;
        align-items: center;

        .ibxo {
            width: 28px;
            height: 28px;
            border-radius: 5px;
            background: #ecf1ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #386bf6;
        }
    }

    .right {
        display: flex;
        align-items: center;
        color: #999;
        gap: 4px;
        
        i {
            margin-left: 2px;
        }
    }
}

.file-block {
    border-radius: 5px;
    background: #f5f5f5;
    width: 80px;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
    margin-bottom: 10px;
    position: relative;
    overflow: hidden;

    .delete {
        position: absolute;
        top: -1px;
        right: -1px;
        width: 24px;
        height: 24px;
        background: #FF4D4F;
        border-radius: 0 5px 0 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 4px;

        &:active {
            opacity: 0.8;
        }

        image {
            width: 100%;
            height: 100%;
        }
    }

    .avatar {
        width: 100%;
        height: 100%;
        border-radius: 4px;
    }
}

.img-block {
    display: flex;
    flex-wrap: wrap;
    margin-top: 11px;
    // justify-content: space-around;
}

.file-block .avatar {
    width: 100%;
    height: 100%;
}

.op-block {
    position: fixed;
    bottom: 35px;
    left: 22px;
    right: 22px;
    font-size: 16px;
    font-weight: 400;
    text-align: center;

    button {
        height: 46px;
        margin-top: 18px;
        background: #386bf6;
    }
}

.title {
    margin: 24px 0 10px;
    font-size: 14px;
    font-weight: 400;
    line-height: 18.56px;
    color: #bbbbbb;
}

.red {
    color: #ff2222;
}

.agree-tip {
    display: flex;
    align-items: center;
    color: #999;
    font-family: MiSans;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: 9px;

    text {
        color: #3b37de;
    }
}

.unchecked {
    color: #999999;
    padding: 0 8px;
}

.publishbut {
    color: #fff;
    font-size: 14px;
    border-radius: 5px;
    background: #386bf6;
    padding: 5px 17px;
    width: 63px;
    overflow: hidden;
    display: inline-block;
}

.video-block {
    width: 240px;
    height: 180px;
    position: relative;
    margin-right: 10px;
    margin-bottom: 10px;
    border-radius: 5px;
    overflow: hidden;

    .preview-video {
        width: 100%;
        height: 100%;
        border-radius: 4px;
    }

    .delete {
        position: absolute;
        top: -1px;
        right: -1px;
        width: 24px;
        height: 24px;
        background: #FF4D4F;
        border-radius: 0 5px 0 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 4px;
        z-index: 1;

        &:active {
            opacity: 0.8;
        }

        image {
            width: 100%;
            height: 100%;
        }
    }
}

.divider {
    height: 1px;
    background: #f0f0f0;
    margin: 0 0 0 0;
}

.option-item {
    display: flex;
    align-items: center;
    padding: 10px;
    height: 48px;
    font-size: 16px;
    background: #fff;
    position: relative;
    .option-icon {
        width: 16px;
        height: 20px;
        margin-right: 10px;
        display: inline-block;
        vertical-align: middle;
        object-fit: contain;
    }

    text {
        color: #333;
        font-size: 15px;
    }
    .option-value {
        margin-left: auto;
        color: #999;
        font-size: 10px;
    }

    .icon-ion-ios-arrow-right {
        font-size: 25px;
        color: #bbb;
        margin-right: 14px;
        margin-left: 8px;
    }
}

// 推荐吧横向列表样式
.recommend-bar-list-scroll {
  width: 100%;
  overflow-x: auto;
  white-space: nowrap;
}
.recommend-bar-list {
  display: flex;
  flex-direction: row;
  margin: 10px 0 0 0;
  gap: 4px;
}
.recommend-bar-item {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 16px;
  padding: 0 4px 0 4px;
  height: 32px;
  cursor: pointer;
  margin-right: 0;
}
.recommend-bar-avatar {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  background: #e0e0e0;
  margin-right: 4px;
}
.recommend-bar-name {
  font-size: 12px;
  color: #333;
}

.bar-select-modal-mask {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.35);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 100vh;
}
.bar-select-modal {
  width: 100vw;
  max-width: 600px;
  height: 85vh; /* 设置固定高度 */
  background: #fff;
  border-top-left-radius: 18px;
  border-top-right-radius: 18px;
  padding-bottom: 0;
  box-shadow: 0 -2px 16px rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
}
.bar-select-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 12px 10px 12px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 2;
}
.bar-select-modal-close {
  font-size: 22px;
  color: #333;
  cursor: pointer;
}
.bar-select-modal-title {
  padding-top: 5px;
  font-size: 16px;
  height: 30px;
  font-weight: bold;
  color: #222;
}
.bar-select-modal-search {
  font-size: 20px;
  color: #666;
  cursor: pointer;
}
.bar-select-modal-searchbox {
  padding: 8px 18px 8px 18px;
  background: #fff;
  position: sticky;
  top: 56px;
  z-index: 2;
}
.bar-select-modal-searchinput {
  width: 100%;
  height: 36px;
  border-radius: 18px;
  border: 1px solid #eee;
  padding: 0 14px;
  font-size: 15px;
  background: #f5f5f5;
  outline: none;
}
.bar-select-modal-scroll {
    height: calc(100% - 60px); /* 减去header高度 */
    overflow-y: auto;
    flex: 1;
}

.search-container {
    width: 100%;
}

.search-input-wrapper {
    padding-left: 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
}

.search-input {
    padding: 0 10px;
    border: 1px solid #888;
    border-radius: 20px;
    width: 100%;
    height: 30px;
    background: #fff;
}

.cancel-btn {
    color: #000;
    font-size: 14px;
    padding: 0 10px;
    margin-left: 10px;
    white-space: nowrap;
    animation: slideInRight 0.3s ease-out;
}

.bar-select-modal-empty {
    height: 100%; /* 设置空状态高度为100% */
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;
}

.post-content-textarea {
    width: 100%;
    height: auto;
    min-height: 140px; /* 约5行 */
    max-height: 220px; /* 约10行 */
    background-color: #fff;
    border: none;
    outline: none;
    font-size: 16px;
    line-height: 1.5;
    padding-top: 10px;
    margin-bottom: 12px;
    overflow-y: auto;
    resize: none;
    display: block;
    transition: height 0.2s ease-in-out;
    box-sizing: border-box;
}

.post-content-textarea::-webkit-scrollbar {
    width: 4px;
}

.post-content-textarea::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 2px;
}

.post-content-textarea::-webkit-scrollbar-track {
    background-color: #f5f5f5;
}

.module-popup {
    background-color: #fff;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    padding: 20px;
    max-height: 70vh;
    display: flex;
    flex-direction: column;
}

.module-popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.module-popup-title {
    font-size: 18px;
    font-weight: 500;
}

.module-popup-close {
    font-size: 24px;
    color: #999;
    padding: 5px;
}

.module-popup-content {
    flex: 1;
    overflow-y: auto;
}

.module-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.module-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    background-color: #f5f5f5;
    cursor: pointer;
}

.module-item-selected {
    background-color: #ECF1FF;
}

.module-item-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.module-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
}

.module-emoji {
    font-size: 20px;
    width: 24px;
    text-align: center;
}

.module-name {
    font-size: 16px;
    color: #333;
}

.module-empty {
    text-align: center;
    padding: 20px;
    color: #999;
}

.bar-select-modal-section {
    padding: 0px 15px;
    // height: 100%; /* 设置section高度为100% */
}

.bar-select-modal-component {
    padding: 0px 0px 0px 0px;
}

.bar-select-modal-section-title {
    font-size: 14px;
    color: #888;
    margin-bottom: 8px;
}

.bar-select-modal-recommend-list {
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 10px 0;
}

.bar-select-modal-recommend-item {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 6px 0;
    cursor: pointer;
}

.bar-select-modal-recommend-avatar {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background: #f5f5f5;
}

.bar-select-modal-recommend-name {
    font-size: 16px;
    color: #333;
}

.bar-select-modal-collapse {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    font-size: 15px;
    color: #444;
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;
    user-select: none;
}

.bar-select-modal-arrow {
    font-size: 16px;
    color: #bbb;
    margin-left: 8px;
}

.bar-select-modal-collapse-content {
    padding: 0px 0 0px 0px;
    color: #888;
    font-size: 13px;
}

.bar-select-modal-empty {
    padding: 40px 0;
    color: #999;
    text-align: center;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px; /* 为空状态添加最小高度 */
}

/* 修改搜索结果区域样式 */
.search-result-section {
    height: calc(100% - 20px); /* 减去section标题的高度 */
    overflow-y: auto;
}

/* 优化滚动条样式 */
.bar-select-modal-scroll::-webkit-scrollbar,
.search-result-section::-webkit-scrollbar {
    width: 4px;
}

.bar-select-modal-scroll::-webkit-scrollbar-thumb,
.search-result-section::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 2px;
}

.bar-select-modal-scroll::-webkit-scrollbar-track,
.search-result-section::-webkit-scrollbar-track {
    background-color: transparent;
}
</style>
