<template>
    <view class="article-detail-wrap">
        <CustomHeader backgroundColor="#fff" @height-update="updateHeaderHeight">
            <template #title>
                <!-- 优先显示吧信息 -->
                <view class="header-bar-info" style="width: 100%; text-align: left; padding-left: 12px; display: flex; align-items: center;">
                    <view class="header-bar-container" @click="goToBarHome">
                        <image :src="article.barAvatar || '/static/logo.png'" style="width: 22px; height: 22px; border-radius: 6px;" />
                        <text style="font-size: 14px; font-weight: 500; margin-left: 6px;">{{ article.barName }}</text>
                        <uni-icons type="right" size="14" color="#666" style="margin-left: 4px;" />
                    </view>
                </view>
                <!-- 当没有吧信息或不显示吧来源时，且用户信息区域不可见时，显示用户信息
                <view class="user-title" style="width: 100%; text-align: left; padding-left: 12px; display: flex; align-items: center; gap: 12px;" v-else-if="shouldShowHeaderUserInfo && (!article.barName || !showBarSource)">
                    <image :src="article.userInfo?.avatar" style="width: 30px; height: 30px; border-radius: 8px; font-size: 16px;" />
                    <view style="display: flex; flex-direction: column; gap: 4px;">
                        <view style="display: flex; align-items: center; gap: 8px;">
                            <text style="font-size: 18px; color: #000">{{ article.userInfo?.displayName || '加载中...' }}</text>
                        </view>
                    </view>
                </view> -->
                <!-- 默认空白标题
                <view v-else style="width: 100%; text-align: left; padding-left: 12px;">
                    <text style="font-size: 18px; color: #000"></text>
                </view> -->
            </template>
            <template v-slot:right>
                <view class="header-right-wrapper">
                    <!-- 当显示吧信息时，显示进吧按钮
                    <view v-if="article.barName && showBarSource" ></view> -->
                    <!-- 当显示用户信息时，显示关注按钮
                    <view class="right-info-focus header-follow-btn" v-else-if="shouldShowHeaderUserInfo && (!article.barName || !showBarSource)" @click="toggleFollow">
                        {{ isFollowing ? '已关注' : '+ 关注' }}
                    </view> -->
                    <uni-icons color="#386BF6" type="redo" size="24" @click="handleShare" style="margin-right: 24px;" />
                    <uni-icons color="#333" type="more-filled" size="20" @click="handleMoreOp" />
                </view>
            </template>
        </CustomHeader>

        <view class="user-info row-flex">
            <view class="user-info-left row-flex">
                <image class="user-avatar" :src="article.userInfo?.avatar" mode="" @click="navigateToUserHome" />
                <view class="user-info-des col-flex" @click="navigateToUserHome">
                    <text class="user-name">{{ article.userInfo?.displayName || '用户' }}</text>
                    <!-- 将时间和IP属性移动到这里 -->
                    <view class="user-meta">
                        <text>{{ formatPostTime(article.createTime) }}</text>
                        <text>IP属地: {{ displayIpAddress }}</text>
                    </view>
                </view>
            </view>
            <view class="right-info-focus" :class="{ 'followed': isFollowing }" @click="toggleFollow">
                {{ isFollowing ? '已关注' : '+ 关注' }}
            </view>
        </view>
        <view class="main-content">
            <view class="title" v-if="article.title">
                {{ article.title }}
            </view>
            <view class="article-content">
                <text class="infocontent" selectable>
                    {{ article.content }}
                </text>
                <!-- {{ article.url }}
                {{ article.url.length }} -->
                <div v-if="article.url.length>0" class="post-image-container">
                    <!-- 视频播放 -->
                    <div v-if="isVideo(article.url[0])" 
                        class="video-wrapper"
                        :class="getVideoClass(article.id)">
                        <nVideo :src="article.url[0]" :poster="article.url[0] + '?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'" :object-fit="getVideoObjectFit(article.id)"></nVideo>
                    </div>
                    <!-- 图片展示 -->
                    <div v-else>
                        <div v-if="article.url.length==1">
                            <image v-for="(item,index) in article?.url" :src="item" mode="aspectFill" class="post-image" @click.stop="previewImage(index,article?.url)" />
                        </div>
                        <div v-if="article.url.length==2" class="post-image-group">
                            <image v-for="(item,index) in article?.url" :src="item" mode="aspectFill" class="post-image2" @click.stop="previewImage(index,article?.url)" />
                        </div>
                        <div v-if="article.url.length==3" class="post-image-group">
                            <image v-for="(item,index) in article?.url" :src="item" mode="aspectFill" class="post-image3" @click.stop="previewImage(index,article?.url)" />
                        </div>
                        <div v-if="article.url.length>3" class="post-image-group2">
                            <image v-for="(item,index) in article?.url" :src="item" mode="aspectFill" class="post-image4" @click.stop="previewImage(index,article?.url)" />
                        </div>
                    </div>
                </div>
            </view>

            <!-- 修改板块显示逻辑 -->
            <view class="module-info" v-if="article.moduleName">
                <view class="module-tag">
                    <text class="module-name"> # {{ article.moduleName }}</text>
                </view>
            </view>

            <!-- 从吧来源，修改为按钮样式 -->
            <view class="bar-source" v-if="article.barName && showBarSource">
                <view class="bar-info" @click="goToBarHome">
                    <image class="bar-avatar" :src="article.barAvatar || '/static/logo.png'" mode="aspectFill"></image>
                    <view class="bar-text-info">
                        <text class="bar-name">{{ article.barName }}</text>
                        <view class="bar-stats">
                            <text class="bar-stat-item">{{ formatNumber(article.barFollowCount || 0) }}关注</text>
                            <text class="bar-stat-item">{{ formatNumber(article.barPostCount || 0) }}帖子</text>
                        </view>
                    </view>
                </view>
                <view class="enter-bar-btn" @click="goToBarHome">
                    <text>进吧</text>
                </view>
            </view>

            <!-- 移除原来的other-info区域，因为时间和IP已经移动到用户信息区域 -->

            <view class="tags">
                <view class="tag-item" v-for="tag in article.tags" :key="tag.id">
                    <image :src="tag.imageUrl" mode=""></image>
                    <text>{{ tag.name }}</text>
                </view>
            </view>
        </view>

        <!-- 评论统计和筛选区域 -->
        <view class="comment-header-container">
            <view class="comment-header" :class="{ 'sticky': isSticky }" :style="stickyStyle">
                <view class="replay-top row-flex">
                    <view class="r-count">
                        {{ this.total|| 0 }}条回复
                    </view>
                    <view class="filter-block">
                        <view class="filter-item" :class="{active:activeItem==item.value}" v-for="item in filterItems" @click="hanleClickItem(item)">
                            <text>{{item.label}}</text>
                        </view>
                    </view>
                </view>
            </view>
            <!-- 占位元素，当评论头部固定时显示 -->
            <view v-if="isSticky" class="comment-header-placeholder"></view>
        </view>

        <view class="replay-block">
            <view class="replay-content" v-for="comment in comments" :key="comment.id" @longpress="handleDeleteComment(comment)">
                <view class="r-pic">
                    <image class="r-avatar" :src="comment.simpleUserInfo?.avatar||'/static/logo.png'" mode="" @click.stop="navigateToCommenterHome(comment.simpleUserInfo)" />
                </view>
                <view class="right-block">
                    <view class="first-user">
                        <view class="r-user">
                            <view class="r-username-row">
                                <text class="r-username" @click.stop="navigateToCommenterHome(comment.simpleUserInfo)">{{ comment.simpleUserInfo?.displayName }}</text>
                                <text v-if="comment.simpleUserInfo?.userId === article.userInfo?.userId" class="author-label">楼主</text>
                            </view>
                            <text class="r-cont" @click="showCommentInput(comment)">
                                <CollapseText :content="comment.content" :fontSize="14">
                                </CollapseText>
                            </text>
                        </view>
                    </view>
                    <view class="r-hot row-flex">
                        <text>{{ comment?.createTime || '刚刚' }}</text>
                        <view class="hot-right row-flex">
                            <view class="row-flex" @click="handleLike2(comment)">
                                <uni-icons :type="comment.isLiked ? 'hand-up-filled' : 'hand-up'" size="20" :color="comment.isLiked ? '#FF0000' : '#666'" />
                                <text>{{ formatNumber(comment.likeNum || 0) }}</text>
                            </view>
                            <!-- 添加删除按钮 -->
                            <view class="row-flex" v-if="comment.simpleUserInfo?.userId === userInfo.userId" @click="handleDeleteComment(comment)">
                                <uni-icons type="trash" size="20" color="#666" />
                            </view>
                        </view>
                    </view>
                    <view class="r-data" v-if="comment.comments && comment.commentNum > 0">
                        <view v-for="(reply, index) in comment.comments.result" 
                              :key="reply.commentId" 
                              @click="showCommentInput3(comment,reply)"
                              v-show="expandedComments[comment.floorId] || index < initialReplyCount">
                            <view class="reply-content">
                                <text class="name" @click.stop="navigateToCommenterHome(reply.sendUser)">{{ reply.sendUser.displayName || '' }}</text>
                                <text v-if="reply.sendUser.userId === article.userInfo?.userId" class="author-label">楼主</text>
                                <text v-if="reply.toUser">回复</text>
                                <text class="name" v-if="reply.toUser" @click.stop="navigateToCommenterHome(reply.toUser)">{{ reply.toUser.displayName || '' }}</text>
                                <text>：{{ reply.content || '' }}</text>
                            </view>
                        </view>
                        <view class="more-op" @click="toggleReplies(comment)" 
                              v-if="comment.commentNum > initialReplyCount">
                            <template v-if="expandedComments[comment.floorId]">
                                <text v-if="comment.commentNum - comment.comments.result.length > 0">加载更多回复({{ comment.commentNum - comment.comments.result.length }})</text>
                                <text v-else>收起回复</text>
                            </template>
                            
                            <template v-else>
                                展开更多回复({{ comment.commentNum - initialReplyCount }})
                            </template>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 加载状态提示 -->
            <view v-if="isLoading" class="loading-status">
                <text>加载中...</text>
            </view>
            
            <view v-if="!hasMore && !isLoading && comments.length > 0" class="loading-status">
                <text>没有更多评论了</text>
            </view>
            
            <view v-if="!comments.length && !isLoading" class="empty-status">
                <text>还没有评论，快来抢沙发吧！</text>
            </view>
        </view>
        <view class="op-block row-flex">
            <view class="">
                <input 
                    class="uni-input" 
                    type="text" 
                    v-model="commentText" 
                    placeholder='说说你的看法' 
                    placeholder-class="input-placeholder"
                    :adjust-position="false"
                    :cursor-spacing="20"
                    @click="showCommentInput2()"
                    readonly
                />
            </view>
            <view class="row-flex">
                <view class="" @click="showCommentInput2()">
                    <uni-icons :color="isCommented ? '#386BF6' : '#333'" type="chat" size="24" />
                    <text>{{ formatNumber(article.floorNum || 0) }}</text>
                </view>
                <view class="" @click="handleLike()">
                    <uni-icons :color="article.hasLike ? '#FF0000' : '#333'" :type="article.hasLike ? 'hand-up-filled' : 'hand-up'" size="24" />
                    <text>{{ formatNumber(article.likeNum || 0) }}</text>
                </view>
                <view class="" @click="handleFavorite">
                    <uni-icons :color="isFavorited ? '#386BF6' : '#333'" type="star" size="24" />
                    <text>{{ formatNumber(article.favoriteCount || 0) }}</text>
                </view>
            </view>
        </view>
        <!-- 弹框 -->
        <uni-popup ref="morePopup" borderRadius='20px 20px 0 0 ' background-color="#fff" @change="handlePopupChange" z-index="1000">
            <view class="top-line"></view>
            <view class="pop-op">
                <!-- 发帖者本人显示删除按钮 -->
                <view v-if="article.userInfo?.userId === userInfo.userId" class="col-flex" @click="handleDelete">
                    <image src="../../assets/images/bar/delete.png" mode=""></image>
                    <text>删除</text>
                </view>
                <!-- 非发帖者显示其他按钮 -->
                <template v-else>
                    <view class="col-flex" @click="handleBlack">
                        <image src="../../assets/images/bar/lahei.png" mode=""></image>
                        <text>拉黑</text>
                    </view>
                    <view class="col-flex" @click="handleReport">
                        <image src="../../assets/images/bar/jubao.png" mode=""></image>
                        <text>举报</text>
                    </view>
                    <view class="col-flex" @click="handleDisinterest">
                        <image src="../../assets/images/bar/buganxingqu.png" mode=""></image>
                        <text>不感兴趣</text>
                    </view>
                </template>
            </view>
        </uni-popup>
        <!-- 跟帖 -->
        <view class="comment-popup" v-if="showComment2" :class="{'comment-popup-show': showComment2}">
            <view class="comment-mask" @click="handleCloseComment2"></view>
            <div class="comment-contentblock" :style="{ bottom: keyboardHeight + 'px' }">
                <view class="comment-content">
                    <input 
                        v-model="commentText" 
                        class="comment-input" 
                        :placeholder="'请输入跟帖内容'" 
                        :adjust-position="false"
                        :cursor-spacing="20"
                        @focus="onInputFocus"
                        @blur="onInputBlur"
                        focus 
                    />
                    <button class="comment-submit" @click="submitComment2">发送</button>
                </view>
            </div>
        </view>

        <!-- 添加评论弹窗 -->
        <view class="comment-popup" v-if="showComment" :class="{'comment-popup-show': showComment}">
            <view class="comment-mask" @click="handleCloseComment"></view>
            <div class="comment-contentblock" :style="{ bottom: keyboardHeight + 'px' }">
                <view class="comment-content">
                    <input 
                        v-model="commentText" 
                        class="comment-input" 
                        :placeholder="toUserData?'回复:'+toUserData?.displayName:'请输入评论内容'" 
                        :adjust-position="false"
                        :cursor-spacing="20"
                        @focus="onInputFocus"
                        @blur="onInputBlur"
                        focus 
                    />
                    <button class="comment-submit" @click="submitComment">发送</button>
                </view>
            </div>
        </view>

        <!-- 删除确认弹窗 -->
        <uni-popup ref="deletePopup" type="dialog">
            <uni-popup-dialog
                type="warn"
                title="删除评论"
                content="确定要删除这条评论吗？"
                :before-close="true"
                @confirm="confirmDelete"
                @close="cancelDelete"
            ></uni-popup-dialog>
        </uni-popup>
        
        <!-- 分享弹窗 -->
        <uni-popup ref="share" type="share" safeArea backgroundColor="#fff">
            <uni-popup-share title="帖子分享" @select="shareselect"></uni-popup-share>
        </uni-popup>
    </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import CollapseText from '@/components/CollapseText'
import appServerApi from '@/api/appServerApi'
import { getItem, setItem } from '../util/storageHelper'
import nVideo from './nVideo.vue'
import store from '@/store'

export default {
    components: {
        CustomHeader,
        CollapseText,
        nVideo
    },
    data() {
        return {
            activeItem: 0,
            filterItems: [
                {
                    label: '默认',
                    value: '0',
                },
                {
                    label: '最新',
                    value: '1',
                },
                {
                    label: '热门',
                    value: '2',
                },
                {
                    label: '楼主',
                    value: '3',
                },
            ],
            article: {
                url: [],
                bbsId: '', // 添加贴吧ID字段
                moduleName: '', // 直接使用接口返回的板块名称
            },
            userInfo: {},
            comments: [],
            isFollowing: false,
            defaultAvatars: [
                '/static/image/me/tx1.png',
                '/static/image/me/tx2.png',
                '/static/image/me/tx3.png',
                '/static/image/me/tx4.png',
                '/static/image/me/tx5.png',
            ],
            provinces: [
                { name: '北京', cities: ['北京'] },
                { name: '上海', cities: ['上海'] },
                { name: '广东', cities: ['广州', '深圳', '珠海', '佛山'] },
                { name: '江苏', cities: ['南京', '苏州', '无锡', '常州'] },
                { name: '浙江', cities: ['杭州', '宁波', '温州', '嘉兴'] },
                { name: '四川', cities: ['成都', '绵阳', '德阳', '宜宾'] },
                { name: '湖北', cities: ['武汉', '宜昌', '襄阳', '十堰'] },
                { name: '河南', cities: ['郑州', '洛阳', '开封', '许昌'] },
            ],
            defaultContents: [
                '今天天气真好，阳光明媚，适合出去走走。',
                '分享一个有趣的发现，希望大家喜欢。',
                '生活中处处都有美，关键是要用心去发现。',
                '每一天都是新的开始，保持积极乐观的心态。',
                '最近学到了很多新东西，感觉收获满满。',
                '与其担心未来，不如好好把握现在。',
                '世界那么大，总有值得期待的事情。',
                '平凡的日子里也能找到快乐。',
                '保持热爱，奔赴山海。',
                '愿每个人都能遇见更好的自己。',
            ],
            commentText: '',
            showComment: false,
            showComment2: false,
            isLiked: false,
            isFavorited: false,
            isCommented: false,
            pageNo: 1,
            pageSize: 10,
            total: 0,
            hasMore: true, // 是否有更多数据,
            id: '',
            toUserData: null,
            tofloorData: null, //当前评论的动态
            keyboardHeight: 0,
            isPlaying: {},
            isFullscreen: false,
            videoRatios: {},
            videoObserver: null,
            currentPlayingVideoId: null,
            likeThrottleTimers: {}, // 添加节流计时器对象
            localLikeStatus: {}, // 添加本地点赞状态存储
            initialReplyCount: 2, // 初始显示的回复数量
            expandedComments: {}, // 记录已展开的评论
            isSticky: false,
            headerHeight: 0, // 添加变量存储 CustomHeader 的高度
            scrollTop: 0,
            scrollTimer: null,
            isLoading: false, // 是否正在加载数据
            shouldShowHeaderUserInfo: false, // 是否在顶部导航栏显示用户信息
            userInfoRect: null, // 用于存储用户信息区域的位置信息
            showBarSource: true, // 是否显示吧来源，默认显示
            currentPost: null, // 当前分享的帖子数据
            keyboardHeightChangeCallback: null
        }
    },
    computed: {
        stickyStyle() {
            if (this.isSticky) {
                return {
                    top: this.headerHeight + 'px'
                }
            }
            return {}
        },
        displayIpAddress() {
            const ip = this.article.ipAddress;
            if (!ip || ip === 'null·null' || ip === 'null' || ip === 'undefined' || ip === undefined) {
                return '未知';
            }
            return ip;
        }
    },
    methods: {
        handleMoreOp() {
            this.$refs.morePopup.open('bottom')
        },
        previewImage(index, images) {
            uni.previewImage({
                current: index, // 当前显示图片索引
                urls: images, // 需要预览的图片http链接列表
            })
        },
        hanleClickItem(item) {
            this.activeItem = item.value;
            if (item.value === '3') { // 楼主筛选
                this.fetchAuthorComments();
            } else if (item.value === '1') { // 最新筛选
                this.fetchLatestComments();
            } else if (item.value === '2') { // 热门筛选
                this.fetchHotComments();
            } else {
                this.pageNo = 1;
                this.fetchComments(this.id);
            }
        },
        fetchCheckUserFollow() {
            // 检查用户是否已登录
            if (!this.userInfo || !this.userInfo.userId) {
                this.isFollowing = false;
                return;
            }
            
            // 检查是否是自己的帖子
            if (this.article.userInfo?.userId === this.userInfo.userId) {
                this.isFollowing = false;
                return;
            }
            
            appServerApi.getCheckUserFollow(this.article.userInfo?.userId)
                .then((response) => {
                    console.log('关注状态检查结果:', response);
                    // 更新关注状态
                    this.isFollowing = response.data || false;
                })
                .catch((error) => {
                    console.error('检查关注状态失败:', error);
                    this.isFollowing = false;
                });
        },
        fetchAuthorComments() {
            this.pageNo = 1;
            this.comments = [];
            //一次性获得更多数据
            const largePageSize = 100;
            //显示加载
            this.isLoading = true;
            uni.showLoading({
                title: '加载中...'
            });

            appServerApi
                .getAllFollowUps(1, largePageSize, this.id)
                .then((response) => {
                    // 过滤出楼主的评论
                    const authorComments = response.data.result.filter(
                        comment => comment.simpleUserInfo?.userId === this.article.userInfo?.userId
                    );

                    this.comments = authorComments;
                    this.total = authorComments.length;
                    this.hasMore = false; // 楼主评论一次性加载完毕
                    
                })
                .catch((error) => {
                    console.error('Error fetching author comments:', error);
                    this.comments = [];
                })
                .finally(() => {
                    this.isLoading = false;
                    uni.hideLoading();
                });
        },
        fetchLatestComments() {
            // 重置页码，确保从第一页开始获取所有数据
            this.pageNo = 1;
            this.comments = [];
            
            // 使用较大的pageSize一次性获取更多数据
            const largePageSize = 100;
            
            // 显示加载提示
            this.isLoading = true;
            uni.showLoading({
                title: '加载中...'
            });

            appServerApi
                .getAllFollowUps(1, largePageSize, this.id)
                .then((response) => {
                    // 按创建时间排序
                    const sortedComments = response.data.result.sort((a, b) => {
                        const timeA = new Date(a.createTime).getTime();
                        const timeB = new Date(b.createTime).getTime();
                        return timeB - timeA; // 降序排序，最新的在前
                    });
                    
                    this.comments = sortedComments;
                    
                    this.total = response.data.total;
                    this.hasMore = this.total > this.comments.length;
                })
                .catch((error) => {
                    console.error('Error fetching latest comments:', error);
                    this.comments = [];
                })
                .finally(() => {
                    this.isLoading = false;
                    uni.hideLoading();
                });
        },
        fetchHotComments() {
            this.pageNo = 1;
            this.comments = [];
            
            // 使用较大的pageSize一次性获取更多数据
            const largePageSize = 100;

            // 显示加载提示
            this.isLoading = true;
            uni.showLoading({
                title: '加载中...'
            });
            
            appServerApi
                .getAllFollowUps(1, largePageSize, this.id)
                .then((response) => {
                    // 按点赞数和评论数排序
                    const sortedComments = response.data.result.sort((a, b) => {
                        // 首先比较点赞数
                        const likeDiff = (b.likeNum || 0) - (a.likeNum || 0);
                        if (likeDiff !== 0) {
                            return likeDiff;
                        }
                        // 如果点赞数相同，则比较评论数
                        return (b.commentNum || 0) - (a.commentNum || 0);
                    });
                    
                    this.comments = sortedComments;
                    
                    
                    this.total = response.data.total;
                    this.hasMore = this.total > this.comments.length;
                })
                .catch((error) => {
                    console.error('Error fetching hot comments:', error);
                    this.comments = [];
                })
                .finally(() => {
                    this.isLoading = false;
                    uni.hideLoading();
                });
        },
        toggleFollow() {
            // Check if user is logged in first
            if (!this.userInfo || !this.userInfo.userId) {
                uni.showToast({
                    title: '请先登录',
                    icon: 'none'
                });
                return;
            }

            // Don't allow following yourself
            if (this.article.userInfo?.userId === this.userInfo.userId) {
                uni.showToast({
                    title: '不能关注自己',
                    icon: 'none'
                });
                return;
            }

            // Optimistically update UI first
            this.isFollowing = !this.isFollowing;

            // Call the API
            appServerApi
                .toggleUserFollow(this.article.userInfo?.userId)
                .then((response) => {
                    console.log('Follow status updated:', response);
                    // Store the follow status in local storage for future reference
                    const followedUsers = uni.getStorageSync('followedUsers') || {};
                    if (this.isFollowing) {
                        followedUsers[this.article.userInfo?.userId] = true;
                    } else {
                        delete followedUsers[this.article.userInfo?.userId];
                    }
                    uni.setStorageSync('followedUsers', followedUsers);
                    
                    uni.showToast({
                        title: this.isFollowing ? '关注成功' : '已取消关注',
                        icon: 'success'
                    });
                    
                    // 更新关注状态
                    this.fetchCheckUserFollow();
                })
                .catch((error) => {
                    console.error('Error updating follow status:', error);
                    // Revert UI if the API call fails
                    this.isFollowing = !this.isFollowing;
                    uni.showToast({
                        title: '操作失败',
                        icon: 'none'
                    });
                });
        },
        fetchArticleDetail(topicId) {
            appServerApi
                .getPostsDetail(topicId)
                .then((response) => {
                    console.log('Article detail:', response);
                    // 获取本地存储的点赞状态
                    const localLikeStatus = uni.getStorageSync('localLikeStatus') || {};
                    const localLikeState = localLikeStatus[topicId];
                    const serverLikeState = response.data.hasLike || false;
                    
                    // 如果本地状态与服务器状态不一致，以服务器状态为准
                    if (localLikeState !== undefined && localLikeState !== serverLikeState) {
                        delete localLikeStatus[topicId];
                        uni.setStorageSync('localLikeStatus', localLikeStatus);
                    }
                    
                    this.article = {
                        ...response.data,
                        hasLike: serverLikeState,
                        likeNum: parseInt(response.data.likeNum || 0),
                        ipAddress: response.data.ipAddress || '未知'
                    };
                    
                    // 如果有bbsId但没有barName，调用接口获取吧名
                    if (this.article.bbsId && !this.article.barName) {
                        this.fetchBarName(this.article.bbsId);
                    }
                    
                    // 获取帖子详情后，检查关注状态
                    this.fetchCheckUserFollow();
                })
                .catch((error) => {
                    console.error('Error fetching article detail:', error);
                });
        },
        
        // 添加获取吧名的方法
        fetchBarName(bbsId) {
            appServerApi
                .getBbsDetail(bbsId)
                .then((response) => {
                    if (response && response.data) {
                        this.$set(this.article, 'barName', response.data.name);
                        this.$set(this.article, 'barAvatar', response.data.avatar || '/static/logo.png');
                        this.$set(this.article, 'barFollowCount', response.data.fansNum || 0);
                        this.$set(this.article, 'barPostCount', response.data.topicNum || 0);
                    }
                })
                .catch((error) => {
                    console.error('获取吧名失败:', error);
                });
        },
        fetchUserInfo() {
            appServerApi
                .getUserInfo()
                .then((response) => {
                    this.userInfo = {
                        ...response,
                        avatar: response.avatar || this.getRandomAvatar(),
                        city: response.city || this.getRandomLocation(),
                    }

                    // 获取用户信息后，检查关注状态
                    this.fetchCheckUserFollow();
                })
                .catch((error) => {
                    console.error('Error fetching user info:', error)
                })
        },
        fetchComments(articleId) {
            // getCommentsByPage
            appServerApi
                .getAllFollowUps(this.pageNo, this.pageSize, articleId)
                .then((response) => {
                    // this.comments = response.data.result || [] // 确保 comments 是数组
                    this.total = response.data.total
                    // 如果是第一页，直接替换数据
                    if (this.pageNo == 1) {
                        this.comments = response.data.result || [] // 确保 comments 是数组
                    } else {
                        // 否则追加数据
                        this.comments = [
                            ...this.comments,
                            ...response.data.result,
                        ]
                    }
                    if (this.total == this.comments.length) {
                        this.hasMore = false
                        console.log('已经到底了')
                    } else {
                        this.hasMore = true
                    }
                    console.log('评论列表', response)
                })
                .catch((error) => {
                    console.error('Error fetching comments:', error)
                    this.comments = [] // 确保 comments 是数组
                })
        },
        getRandomAvatar() {
            const index = Math.floor(Math.random() * this.defaultAvatars.length)
            return this.defaultAvatars[index]
        },
         getRandomLocation() {
            const province =
                this.provinces[
                    Math.floor(Math.random() * this.provinces.length)
                ]
            const city =
                province.cities[
                    Math.floor(Math.random() * province.cities.length)
                ]
            return `${province.name} ${city}`
        },
        getRandomContent() {
            const index = Math.floor(
                Math.random() * this.defaultContents.length
            )
            return this.defaultContents[index]
        },
        // 评论相关
        showCommentInput(row) {
            console.log('row', row)
            this.tofloorData = row
            // 如果没有传入 row,说明是直接跟贴
            if (!row) {
                this.showComment2 = true
                // 监听键盘高度变化
                uni.onKeyboardHeightChange(this.onKeyboardHeightChange)
                return
            }
            // 设置被回复用户
            this.toUserData = row.simpleUserInfo
            this.showComment = true
            // 监听键盘高度变化
            uni.onKeyboardHeightChange(this.onKeyboardHeightChange)
        },

        // 直接对帖子进行评论
        showCommentInput2() {
            this.tofloorData = null;
            this.toUserData = null;
            
            // 优先监听键盘高度变化
            uni.onKeyboardHeightChange(this.onKeyboardHeightChange);
            
            // 直接设置状态，不使用DOM操作
            this.showComment = true;
            
            // uni-app环境下不需要手动操作DOM，直接使用focus属性
            // 延迟确保键盘能正常弹出
            setTimeout(() => {
                // 如有需要，可在此处添加其他操作
            }, 50);
        },
        // 切换回复的展开/收起状态
        toggleReplies(comment) {
            if (!comment.floorId) return;
            
            // 如果当前是展开状态，且有更多回复，则加载更多
            if (this.expandedComments[comment.floorId]) {
                // 如果还有更多回复，加载更多
                if (comment.commentNum - comment.comments.result.length > 0) {
                    this.loadMoreReplies(comment);
                } else {
                    // 如果没有更多回复，则收起
                    this.$set(this.expandedComments, comment.floorId, false);
                }
            } else {
                // 如果未展开，则展开显示初始回复
                this.$set(this.expandedComments, comment.floorId, true);
            }
        },
        loadMoreReplies(comment) {
            if (!comment.floorId) return;
            
            // 获取当前显示的回复数量
            const currentCount = this.expandedComments[comment.floorId] ? 
                comment.comments.result.length : 
                this.initialReplyCount;
            
            // 计算新的显示数量
            const newCount = currentCount + 5;
            
            // 调用API获取更多回复
            appServerApi
                .getCommentsByPage(comment.comments.pageNo + 1, comment.comments.pageSize, comment.floorId)
                .then((response) => {
                    // 合并新的回复到现有回复列表中
                    const newComments = [...comment.comments.result, ...response.data.result];
                    this.$set(comment.comments, 'result', newComments);
                    this.$set(comment.comments, 'pageNo', comment.comments.pageNo + 1);
                    
                    // 更新展开状态
                    this.$set(this.expandedComments, comment.floorId, true);
                    
                    // 如果还有更多回复，保持展开状态
                    if (newComments.length < comment.commentNum) {
                        this.$set(this.expandedComments, comment.floorId, true);
                    }
                })
                .catch((error) => {
                    console.error('Error loading more replies:', error);
                    uni.showToast({
                        title: '加载更多回复失败',
                        icon: 'none'
                    });
                });
        },
        SeeMore(data, floorId) {
            console.log('data', data)
            var obj = data
            obj.pageNo++
            // return
            // getCommentsByPage
            appServerApi
                .getCommentsByPage(obj.pageNo, obj.pageSize, floorId)
                .then((response) => {
                    // this.$set(obj, 'result', [])
                    console.log('评论列表', response)
                    const newComments = [...response.data.result, ...obj.result]
                    console.log('newComments', newComments)
                    this.$set(obj, 'result', newComments)
                })
                .catch((error) => {
                    console.error('Error fetching comments:', error)
                })
        },
        // 回复评论中的回复
        showCommentInput3(row, row2) {
            this.tofloorData = row
            // 设置被回复用户
            this.toUserData = row2.sendUser
            this.showComment = true
            // 监听键盘高度变化
            uni.onKeyboardHeightChange(this.onKeyboardHeightChange)
        },
        submitComment() {
            if (!this.commentText.trim()) {
                uni.showToast({
                    title: '请输入评论内容',
                    icon: 'none',
                })
                return
            }

            // 如果没有 tofloorData,说明是直接跟贴
            if (!this.tofloorData) {
                this.submitComment2() // 调用跟贴方法
                return
            }

            var toUserId = ''
            if (this.toUserData) {
                toUserId = this.toUserData.userId
            }

            appServerApi
                .publishComment({
                    content: this.commentText.trim(),
                    floorId: this.tofloorData.floorId,
                    toUserId: toUserId,
                    url: [],
                })
                .then((res) => {
                    uni.showToast({
                        title: '评论成功',
                        icon: 'success',
                    })
                    if (!this.tofloorData.comments) {
                        this.$set(this.tofloorData, 'comments', {
                            result: [],
                        })
                    }
                    // 创建新评论对象
                    var obj = {
                        id: Date.now(),
                        sendUser: {
                            userId: this.userInfo.userId,
                            displayName: this.userInfo.displayName,
                            avatar: this.userInfo.avatar,
                        },
                        toUser: this.toUserData,
                        floorId: 2,
                        content: this.commentText,
                        url: [],
                        createTime: new Date().toISOString(),
                    }
                    // 使用数组方法确保响应式
                    const newComments = [
                        obj,
                        ...this.tofloorData.comments.result,
                    ]
                    this.$set(this.tofloorData.comments, 'result', newComments)
                    // 更新缓存
                    // this.commentsMap.set(this.currentMoment.id, [
                    //     ...newComments,
                    // ])
                    this.tofloorData.commentNum++
                    this.isCommented = true
                    
                    // 清空评论内容并关闭评论框
                    this.commentText = '';
                    this.handleCloseComment();
                    this.toUserData = null;
                })
                .catch((err) => {
                    console.error('评论失败:', err)
                    uni.showToast({
                        title: '跟贴失败',
                        icon: 'none',
                    })
                })
        },
        submitComment2() {
            if (!this.commentText.trim()) {
                uni.showToast({
                    title: '请输入跟贴内容',
                    icon: 'none',
                })
                return
            }
            var toUserId = ''
            if (this.toUserData) {
                toUserId = this.toUserData.userId
            }
            // {"topicId":"1","content":"沙发","url":["url","url2"]}
            appServerApi
                .publishFollowUp({
                    topicId: this.article.topicId,
                    content: this.commentText.trim(),
                    url: [],
                })
                .then((res) => {
                    uni.showToast({
                        title: '跟贴成功',
                        icon: 'success',
                    })
                    this.article.commentCount++
                    this.isCommented = true
                    
                    // 清空评论内容并关闭评论框
                    this.commentText = '';
                    this.handleCloseComment2();
                    
                    // 重新获取评论列表
                    this.fetchComments(this.article.topicId)
                })
                .catch((err) => {
                    console.error('跟贴失败:', err)
                    uni.showToast({
                        title: '跟贴失败',
                        icon: 'none',
                    })
                })
        },
        // 点赞
        handleLike() {
            // 检查节流
            if (this.likeThrottleTimers[this.article.topicId]) {
                return;
            }

            // 设置节流标记
            this.likeThrottleTimers[this.article.topicId] = true;
            setTimeout(() => {
                this.likeThrottleTimers[this.article.topicId] = false;
            }, 500);

            // 乐观更新
            const hasLike = !this.article.hasLike;
            const newLikeNum = hasLike ? parseInt(this.article.likeNum) + 1 : parseInt(this.article.likeNum) - 1;
            
            // 直接更新对应的属性
            this.$set(this.article, 'hasLike', hasLike);
            this.$set(this.article, 'likeNum', newLikeNum);

            // 更新本地存储
            this.localLikeStatus[this.article.topicId] = hasLike;
            uni.setStorageSync('localLikeStatus', this.localLikeStatus);

            // 发送点赞状态变化的通知
            uni.$emit('topicLikeChanged', {
                topicId: this.article.topicId,
                hasLike: hasLike,
                likeNum: newLikeNum
            });

            // 发送请求到服务器
            appServerApi
                .likeTopic({ topicId: this.article.topicId })
                .catch((err) => {
                    console.error('点赞失败:', err);
                    // 如果失败，回滚更新
                    this.$set(this.article, 'hasLike', !hasLike);
                    this.$set(this.article, 'likeNum', parseInt(this.article.likeNum));
                    // 同时回滚本地存储
                    delete this.localLikeStatus[this.article.topicId];
                    uni.setStorageSync('localLikeStatus', this.localLikeStatus);
                    // 发送点赞状态回滚的通知
                    uni.$emit('topicLikeChanged', {
                        topicId: this.article.topicId,
                        hasLike: !hasLike,
                        likeNum: parseInt(this.article.likeNum)
                    });
                    uni.showToast({
                        title: '操作失败',
                        icon: 'none',
                    });
                });
        },
        // 跟帖点赞
        handleLike2(row) {
            const isLiked = !row.isLiked
            var obj = row
            appServerApi
                .likeFloor(obj.floorId)
                .then((res) => {
                    obj.isLiked = isLiked
                    obj.likeNum += isLiked ? 1 : -1
                })
                .catch((err) => {
                    console.error('点赞失败:', err)
                    uni.showToast({
                        title: '操作失败',
                        icon: 'none',
                    })
                })
        },
        // 收藏
        handleFavorite() {
            appServerApi
                .likeTopic(this.article.id)
                .then((res) => {
                    this.isFavorited = !this.isFavorited
                    const currentFavoriteCount = this.article.favoriteCount || 0
                    const favoriteCount = this.isFavorited ?  currentFavoriteCount+ 1 : currentFavoriteCount - 1
                    this.article = {...this.article, favoriteCount: favoriteCount}
                    uni.showToast({
                        title: this.isFavorited ? '收藏成功' : '取消收藏',
                        icon: 'success',
                    })
                })
                .catch((err) => {
                    console.error('收藏失败:', err)
                    uni.showToast({
                        title: '操作失败',
                        icon: 'none',
                    })
                })
        },
        handleBlack() {
            uni.showToast({
                title: '已拉黑，您将不再收到该用户动态',
                icon: 'none',
            })
            setTimeout(() => {
                uni.navigateBack()
            }, 1000)
        },
        handleReport() {
            uni.navigateTo({
                url: `/pages/complaint/ComplaintPage`,
            })
        },
        handleDisinterest() {
            uni.showToast({
                title: '操作成功，将减少此类内容推荐',
                icon: 'none',
            })
            setTimeout(() => {
                uni.navigateBack()
            }, 1000)
        },
        onKeyboardHeightChange(e) {
            if (e && e.detail) {
                this.keyboardHeight = e.detail.height || 0;
            } else {
                this.keyboardHeight = 0;
            }
        },
        onInputFocus(e) {
            const that = this;
            uni.onKeyboardHeightChange(function(res) {
                that.keyboardHeight = res.height;
            });
        },
        
        onInputBlur() {
            // 延迟处理，确保在键盘收起后再判断是否需要关闭评论框
            setTimeout(() => {
                if (!this.commentText.trim()) {
                    this.showComment = false;
                    this.showComment2 = false;
                    this.keyboardHeight = 0;
                }
            }, 200);
        },
        // 判断是否为视频URL
        isVideo(url) {
            if (!url) return false;
            const videoExtensions = ['.mp4', '.mov', '.m4v', '.3gp', '.avi', '.m3u8'];
            return videoExtensions.some(ext => url.toLowerCase().includes(ext));
        },

        // 处理视频播放
        onVideoPlay(event, videoId) {
            // 暂停其他视频
            Object.keys(this.isPlaying).forEach(id => {
                if (id !== videoId && this.isPlaying[id]) {
                    const videoContext = uni.createVideoContext('video-' + id);
                    if (videoContext) {
                        videoContext.pause();
                    }
                }
            });
            
            this.$set(this.isPlaying, videoId, true);
            this.currentPlayingVideoId = videoId;
        },

        // 处理视频暂停
        onVideoPause(event, videoId) {
            this.$set(this.isPlaying, videoId, false);
            if (this.currentPlayingVideoId === videoId) {
                this.currentPlayingVideoId = null;
            }
        },

        // 处理视频播放结束
        onVideoEnded(event, videoId) {
            this.$set(this.isPlaying, videoId, false);
            if (this.currentPlayingVideoId === videoId) {
                this.currentPlayingVideoId = null;
            }
        },

        // 处理全屏变化
        onFullscreenChange(e) {
            this.isFullscreen = e.detail.fullScreen;
            const videoId = this.currentPlayingVideoId;
            
            if (!e.detail.fullScreen && videoId) {
                const videoContext = uni.createVideoContext(videoId);
                if (videoContext) {
                    videoContext.pause();
                }
                this.$set(this.isPlaying, videoId, false);
                this.currentPlayingVideoId = null;
            }
        },

        // 视频加载完成处理
        onVideoLoaded(event, videoId) {
            const video = event.target;
            if (video) {
                const ratio = video.videoWidth / video.videoHeight;
                this.$set(this.videoRatios, videoId, ratio);
                this.$forceUpdate();
            }
        },

        // 获取视频样式
        getVideoStyle(videoId) {
            const ratio = this.videoRatios[videoId];
            if (!ratio) return {};
            
            if (ratio < 1) {
                // 竖屏视频
                return {
                    width: '56.25vw',
                    height: `${56.25 / ratio}vw`,
                    margin: '0 auto'
                };
            } else {
                // 横屏或方形视频
                return {
                    width: '100%',
                    height: `${100 / ratio}%`
                };
            }
        },

        // 获取视频容器类名
        getVideoClass(momentId) {
            const ratio = this.videoRatios[momentId];
            if (!ratio) return '';
            
            if (ratio > 1.2) return 'landscape';
            if (ratio < 0.8) return 'portrait';
            return 'square';
        },

        getVideoObjectFit(momentId) {
            const ratio = this.videoRatios[momentId];
            if (!ratio) return 'contain';
            
            if (ratio > 1.2) return 'cover';
            if (ratio < 0.8) return 'contain';
            return 'cover';
        },

        // 视频点击处理
        handleVideoClick(videoId) {
            const videoContext = uni.createVideoContext(videoId);
            if (this.isPlaying[videoId]) {
                videoContext.pause();
            } else {
                videoContext.play();
            }
        },

        // 添加滚动到评论区域的方法
        scrollToComments() {
            // 使用 nextTick 确保 DOM 已更新
            this.$nextTick(() => {
                const query = uni.createSelectorQuery().in(this);
                query.select('.comment-header').boundingClientRect(data => {
                    if (data) {
                        // 计算需要滚动的位置，考虑评论头部的固定高度
                        const scrollTop = data.top - this.headerHeight - 10;
                        
                        // 使用平滑滚动效果，提高体验
                        uni.pageScrollTo({
                            scrollTop: scrollTop,
                            duration: 0 // 使用300ms的滚动动画，而不是立即滚动
                        });
                    }
                }).exec();
            });
        },
        updateHeaderHeight(height) {
            this.headerHeight = height;
        },
        handleCloseComment2() {
            if (!this.commentText.trim()) {
                this.showComment2 = false;
                this.commentText = '';
                this.tofloorData = null;
                this.toUserData = null;
                this.onInputBlur();
            }
        },
        handleCloseComment() {
            if (!this.commentText.trim()) {
                this.showComment = false;
                this.commentText = '';
                this.tofloorData = null;
                this.toUserData = null;
                this.onInputBlur();
            }
        },
        handlePopupChange(e) {
            // 处理弹出框变化逻辑
        },
        // 长按评论处理
        handleDeleteComment(comment) {
            // 检查是否是当前用户的评论
            if (comment.simpleUserInfo?.userId === this.userInfo.userId) {
                this.currentCommentToDelete = comment;
                this.$refs.deletePopup.open();
            }
        },
        // 确认删除
        confirmDelete() {
            if (!this.currentCommentToDelete) return;
            
            // 根据评论类型选择删除方法
            const deletePromise = this.currentCommentToDelete.floorId ? 
                appServerApi.deleteFloor(this.currentCommentToDelete.floorId) :
                appServerApi.deleteComment(this.currentCommentToDelete.id);
            
            deletePromise
                .then(() => {
                    // 从评论列表中移除被删除的评论
                    this.comments = this.comments.filter(
                        comment => comment.floorId !== this.currentCommentToDelete.floorId
                    );
                    // 更新评论总数
                    this.total--;
                    
                    uni.showToast({
                        title: '删除成功',
                        icon: 'success'
                    });
                })
                .catch(err => {
                    console.error('删除失败:', err);
                    uni.showToast({
                        title: '删除失败',
                        icon: 'none'
                    });
                })
                .finally(() => {
                    this.currentCommentToDelete = null;
                    this.$refs.deletePopup.close();
                });
        },
        
        // 取消删除
        cancelDelete() {
            this.currentCommentToDelete = null;
            this.$refs.deletePopup.close();
        },
        handleDelete() {
            uni.showModal({
                title: '删除帖子',
                content: '确定要删除这条帖子吗？',
                success: (res) => {
                    if (res.confirm) {
                        appServerApi
                            .deletePost(this.article.topicId)
                            .then(() => {
                                uni.showToast({
                                    title: '删除成功',
                                    icon: 'success'
                                });
                                // 发送删除成功事件
                                uni.$emit('postDeleted', this.article.topicId);
                                // 延迟返回上一页，让用户看到成功提示
                                setTimeout(() => {
                                    uni.navigateBack();
                                }, 1500);
                            })
                            .catch(err => {
                                console.error('删除帖子失败:', err);
                                uni.showToast({
                                    title: '删除失败',
                                    icon: 'none'
                                });
                            });
                    }
                }
            });
        },
        handleBack() {
            uni.navigateBack();
        },
        /**
         * Check if the current user is following the author of the article
         */
        checkFollowStatus() {
            if (!this.article.userInfo?.userId || !this.userInfo.userId) {
                return;
            }
            
            // First check local storage for quick response
            const followedUsers = uni.getStorageSync('followedUsers') || {};
            if (followedUsers[this.article.userInfo?.userId] !== undefined) {
                this.isFollowing = followedUsers[this.article.userInfo?.userId];
                return;
            }
            
            // If not in local storage, can make an API call here to get the status
            // For now we'll use local storage only
        },
        // 添加检测用户信息区域可见性的方法
        checkUserInfoVisibility() {
            // 每次检查时重新获取用户信息区域的位置信息
            const query = uni.createSelectorQuery().in(this);
            query.select('.user-info').boundingClientRect(data => {
                if (data) {
                    this.userInfoRect = data;
                    
                    // 修正判断逻辑：只有当用户信息区域顶部超出屏幕（负值）时才显示导航栏信息
                    // 并且考虑导航栏的高度，确保用户信息区域完全不可见
                    this.shouldShowHeaderUserInfo = data.top < 0 && Math.abs(data.top) > data.height * 0.5;
                }
            }).exec();
        },
        
        // 获取用户信息区域的位置信息
        getUserInfoRect() {
            const query = uni.createSelectorQuery().in(this);
            query.select('.user-info').boundingClientRect(data => {
                if (data) {
                    this.userInfoRect = data;
                    // 初始化时默认为不显示导航栏用户信息
                    this.shouldShowHeaderUserInfo = false;
                }
            }).exec();
        },
        // 格式化数字，大于10000显示为x万
        formatNumber(number) {
            if (number >= 10000) {
                return (number / 10000).toFixed(1) + '万';
            }
            return number.toString();
        },
        // 新增跳转到吧主页的方法
        goToBarHome() {
            if (this.article.bbsId) {
                uni.navigateTo({
                    url: `/pages/bar/BarHome?id=${this.article.bbsId}`
                });
            } else {
                uni.showToast({
                    title: '未找到对应的吧',
                    icon: 'none'
                });
            }
        },
        formatPostTime(dateString) {
            if (!dateString) return '';
            // 兼容CST格式
            let date = new Date(dateString.replace(/CST/, 'GMT+0800'));
            if (isNaN(date.getTime())) {
                // 兜底处理
                date = new Date(dateString);
            }
            const now = new Date();
            const diff = (now - date) / 1000; // 秒

            if (diff < 60) return '刚刚';
            if (diff < 3600) return Math.floor(diff / 60) + '分钟前';
            if (diff < 86400) return Math.floor(diff / 3600) + '小时前';
            if (diff < 604800) return Math.floor(diff / 86400) + '天前';

            // 超过7天
            const y = date.getFullYear();
            const m = String(date.getMonth() + 1).padStart(2, '0');
            const d = String(date.getDate()).padStart(2, '0');
            return `${y}-${m}-${d}`;
        },
        
        // 处理分享
        handleShare() {
            // 获取缩略图 - 优先使用帖子的第一张图片，其次是用户头像
            let thumbnail = '/static/logo.png'; // 默认缩略图
            if (this.article.url && this.article.url.length > 0) {
                // 使用帖子的第一张图片作为缩略图
                thumbnail = this.article.url[0];
            } else if (this.article.userInfo?.avatar) {
                // 使用用户头像作为缩略图
                thumbnail = this.article.userInfo.avatar;
            }
            
            this.currentPost = {
                topicId: this.article.topicId,
                name: this.article.userInfo?.displayName || '用户',
                content: this.article.content || '',
                avatar: this.article.userInfo?.avatar || '/static/logo.png',
                title: this.article.title,
                thumbnail: thumbnail // 添加缩略图字段
            };
            this.$refs.share.open();
        },
        
        // 分享选择处理
        shareselect(e) {
            console.log('分享选择:', e);
            console.log('当前帖子:', this.currentPost);
            let title = this.currentPost.title ??''
            if (e.index === 0) {
                // 微信好友
                var obj = {
                    provider: 'weixin',
                    type: 0,
                    title: title,
                    summary: this.currentPost.content,
                    imageUrl: this.currentPost.avatar,
                    scene: 'WXSceneSession',
                    topicId: this.currentPost.topicId,
                };
                store.shareContent(obj);
            }
            if (e.index === 1) {
                // 微信朋友圈
                var obj = {
                    provider: 'weixin',
                    type: 0,
                    title: title,
                    summary: this.currentPost.content,
                    imageUrl: this.currentPost.avatar,
                    scene: 'WXSceneTimeline',
                    topicId: this.currentPost.topicId,
                };
                store.shareContent(obj);
            }
            if (e.index === 2) {
                // 发送给好友
                // 将分享数据存储到全局store中
                store.setShareData(this.currentPost);
                uni.navigateTo({
                    url: '/pages/conversationList/ChooseChat',
                });
                return;
            }
            // 添加复制链接功能
            if (e.index === 3) {
                this.copyPostLink();
                return;
            }
            
            // 调用分享接口
            appServerApi
                .sharePost({ topicId: this.currentPost.topicId })
                .then((res) => {
                    // 可选：显示分享成功提示
                    // uni.showToast({
                    //     title: '分享成功',
                    //     icon: 'success',
                    // });
                })
                .catch((err) => {
                    console.error('分享失败:', err);
                    uni.showToast({
                        title: '分享失败',
                        icon: 'none',
                    });
                });
        },
        // 复制帖子链接
        copyPostLink() {
            if (!this.currentPost || !this.currentPost.topicId) {
                uni.showToast({
                    title: '帖子信息不完整',
                    icon: 'none'
                });
                return;
            }
            
            // 构建帖子链接
            const postUrl = `https://web.ykjrhl.com/share/topic.html?topicId=${this.currentPost.topicId}`;
            
            // 使用uni.setClipboardData复制到剪贴板
            uni.setClipboardData({
                data: postUrl,
                success: () => {
                    uni.showToast({
                        title: '链接已复制',
                        icon: 'success',
                        duration: 2000
                    });
                    
                    // 调用分享接口增加分享数
                    appServerApi
                        .sharePost({ topicId: this.currentPost.topicId })
                        .then((res) => {
                            // 可选：更新分享数显示
                            console.log('分享统计成功');
                        })
                        .catch((err) => {
                            console.error('分享统计失败:', err);
                        });
                },
                fail: (err) => {
                    console.error('复制失败:', err);
                    uni.showToast({
                        title: '复制失败',
                        icon: 'none'
                    });
                }
            });
        },
        // 添加检查是否是emoji的方法
        isEmoji(icon) {
            if (!icon) return false;
            const emojiRegex = /^(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])+$/;
            return emojiRegex.test(icon);
        },
        
        // 添加获取板块信息的方法
        fetchModuleInfo() {
            if (!this.article.bbsId) return;
            
            appServerApi.getBbsModuleList(this.article.bbsId)
                .then(response => {
                    if (response && response.code === 200) {
                        const moduleList = response.data || [];
                        // 通过板块名称匹配，因为接口只返回了板块名称
                        const currentModule = moduleList.find(m => m.name === this.article.moduleName);
                        if (currentModule) {
                            this.moduleIcon = currentModule.icon;
                        }
                    }
                })
                .catch(error => {
                    console.error('获取板块信息失败:', error);
                });
        },
        
        // 修改获取帖子详情的方法
        getArticleDetail(id) {
            appServerApi
                .getTopicDetail(id)
                .then((response) => {
                    if (response && response.code === 200) {
                        this.article = response.data;
                        // 如果有板块名称，获取板块图标
                        if (this.article.moduleName) {
                            this.fetchModuleInfo();
                        }
                        // ... 其他处理逻辑 ...
                    }
                })
                .catch(error => {
                    console.error('获取帖子详情失败:', error);
                });
        },
        handleKeyboardHeightChange(res) {
            // 处理键盘高度变化的逻辑
            console.log('键盘高度变化：', res.height);
        },
        // 添加跳转到用户主页的方法
        navigateToUserHome() {
            if (!this.article.userInfo || !this.article.userInfo.userId) {
                uni.showToast({
                    title: '用户信息不完整',
                    icon: 'none'
                });
                return;
            }
            
            uni.navigateTo({
                url: `/pages/bar/MyBarHome?userId=${this.article.userInfo.userId}`
            });
        },
        // 添加跳转到评论者主页的方法
        navigateToCommenterHome(userInfo) {
            if (!userInfo || !userInfo.userId) {
                uni.showToast({
                    title: '用户信息不完整',
                    icon: 'none'
                });
                return;
            }
            
            uni.navigateTo({
                url: `/pages/bar/MyBarHome?userId=${userInfo.userId}`
            });
        },
    },
    onLoad(options) {
        const articleId = options.topicId
        this.id = options.topicId
        
        // 根据来源页面决定是否显示吧来源
        // 如果来源是BarHome页面，则不显示吧来源
        if (options.from === 'BarHome') {
            this.showBarSource = false;
        } else {
            this.showBarSource = true;
        }
        
        if (articleId) {
            this.fetchArticleDetail(articleId)
            this.fetchComments(articleId)
        }
        console.log(options)
        
        // Initialize local point status
        this.localLikeStatus = uni.getStorageSync('localLikeStatus') || {};
        
        // If there is a showComment parameter, scroll to the comment area
        if(options.showComment === 'true') {
            setTimeout(() => {
                this.scrollToComments();
            }, 300);
        }

        // Create observer
        this.$nextTick(() => {
            const observer = uni.createIntersectionObserver(this, {
                thresholds: [0, 0.1, 0.5, 1]
            });
            
            observer.relativeToViewport({
                top: 0
            }).observe('.comment-header-container', (res) => {
                const rect = res.boundingClientRect;
                const headerOffset = this.headerHeight;
                
                const shouldSticky = rect.top <= headerOffset && 
                                   this.scrollTop > headerOffset;
                
                this.$nextTick(() => {
                    this.isSticky = shouldSticky;
                });
            });
            
            // 监听用户信息区域的可见性变化
            const userInfoObserver = uni.createIntersectionObserver(this);
            userInfoObserver.relativeToViewport({
                top: -this.headerHeight
            }).observe('.user-info', (res) => {
                // 获取用户信息区域的高度和位置信息
                const rect = res.boundingClientRect;
                // 计算用户信息区域有多少比例不在可视区域内
                const topOutOfView = rect.top < 0 ? Math.abs(rect.top) : 0;
                const percentHidden = topOutOfView / rect.height;
                
                // 只有当用户信息区域超过一半被隐藏时才显示顶部导航栏的用户信息
                this.shouldShowHeaderUserInfo = percentHidden > 0.5;
            });
        });

        // 在页面加载后获取用户信息区域的位置
        setTimeout(() => {
            this.getUserInfoRect();
        }, 500); // 延迟获取，确保DOM已经渲染

        // 保存回调函数引用
        this.keyboardHeightChangeCallback = this.handleKeyboardHeightChange;
        // 注册键盘监听
        uni.onKeyboardHeightChange(this.keyboardHeightChangeCallback);
    },
    onShow() {
        this.fetchUserInfo(this.article.authorId);
        this.fetchCheckUserFollow();
        // Check if we're following the author after user info is loaded
        this.$nextTick(() => {
            if (this.article.userInfo?.userId) {
                this.checkFollowStatus();
            }
        });
    },
    onReachBottom() {
        if (this.hasMore) {
            // this.status = 'loading'
            this.pageNo++
            this.fetchComments(this.id)
            // this.fetchPosts(this.id, 'push')
        }
    },
    onUnload() {
        // 使用保存的回调函数引用移除监听
        if (this.keyboardHeightChangeCallback) {
            uni.offKeyboardHeightChange(this.keyboardHeightChangeCallback);
        }
    },
    beforeDestroy() {
        if (this.videoObserver) {
            this.videoObserver.disconnect();
        }
        if (this.scrollTimer) {
            clearTimeout(this.scrollTimer);
        }
    },
    onHide() {
        // 页面隐藏时暂停所有视频
        Object.keys(this.isPlaying).forEach(videoId => {
            if (this.isPlaying[videoId]) {
                const videoContext = uni.createVideoContext(videoId);
                if (videoContext) {
                    videoContext.pause();
                }
                this.$set(this.isPlaying, videoId, false);
            }
        });
        this.currentPlayingVideoId = null;
    },
    // 优化页面滚动监听
    onPageScroll(e) {
        // 保存滚动位置
        this.scrollTop = e.scrollTop;
        
        // 检查用户信息区域的可见性
        this.checkUserInfoVisibility();
        
        // 检查评论标题是否需要固定
        if (!this.scrollTimer) {
            this.scrollTimer = setTimeout(() => {
                const query = uni.createSelectorQuery().in(this);
                query.select('.comment-header-container').boundingClientRect(data => {
                    if (data) {
                        const shouldSticky = data.top <= this.headerHeight && 
                                        this.scrollTop > this.headerHeight;
                        
                        if (this.isSticky !== shouldSticky) {
                            this.isSticky = shouldSticky;
                        }
                    }
                }).exec();
                this.scrollTimer = null;
            }, 50); // 添加50ms的节流，避免频繁触发
        }
    },
}
</script>

<style scoped lang="scss">
.article-detail-wrap {
    min-height: 100vh;
    background: transparent;
    position: relative;
    padding-bottom: 60px; /* 为底部操作栏留出空间 */
    display: flex;
    flex-direction: column;
}

.post-image-container {
    border-radius: 15px;
    overflow: hidden;
}
.post-image-group {
    display: flex;
    .post-image2:nth-child(1) {
        margin-right: 2.5px;
    }
    .post-image2:nth-child(2) {
        margin-left: 2.5px;
    }
    .post-image3 {
        height: 200px;
    }
    .post-image3:nth-child(1) {
        margin-right: 5px;
    }
    .post-image3:nth-child(3) {
        margin-left: 5px;
    }
}
.post-image-group2 {
    display: flex;
    flex-wrap: wrap;
    .post-image4 {
        // flex-grow: 1;
        width: 30%;
        height: 100px;
        margin: 2.5px;
    }
}
.post-image {
    width: 100%;
    margin-top: 16px;
    border-radius: 15px;
    height: 210px;
}
.main-content {
    padding: 0 16px 15px;
    margin-bottom: 15px;
    border-bottom: 1px solid #efefef;
    flex-shrink: 0; /* 防止内容区域被压缩 */

    .title {
        font-size: 18px;
        font-weight: 800;
        color: #333;
        margin-bottom: 12px;
        line-height: 1.6;
    }

    .article-content {
        font-size: 15px;
        line-height: 1.8;

        // image {
        //     width: 100%;
        //     height: auto;
        // }
    }
}

.other-info {
    font-size: 11px;
    color: #999;
    padding: 4px 0px; /* 减小上下内边距 */
    display: flex;
    flex-wrap: wrap;
    gap: 8px; /* 减小元素间距 */
}

.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .tag-item {
        padding: 0 5px;
        height: 24px;
        font-size: 10px;
        display: flex;
        align-items: center;
        background: #f5f5f5;
        color: #999;

        image {
            width: 17px;
            height: 17px;
            border-radius: 50%;
        }
    }
}

.comment-header-container {
    position: relative;
    z-index: 100;
}

.comment-header {
    background: #fff;
    padding: 0 16px;
    z-index: 100;
    position: relative;
    flex-shrink: 0;
    width: 100%;
    height: 56px;
    transform-origin: top;
    will-change: transform, position;
    
    &.sticky {
        position: fixed;
        left: 0;
        right: 0;
        top: var(--header-height);
        border-bottom: 1px solid #efefef;
        background: #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .replay-top {
        height: 100%;
        font-size: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #fff;
    }

    .r-count {
        height: 36px;
        line-height: 36px;
    }

    .filter-block {
        margin-bottom: 0;
        display: flex;
        align-items: center;
        width: 190px;
        border-radius: 100px;
        padding: 2px;
        background: #f5f5f5;
    }

    .filter-item {
        flex: 1;
        height: 32px;
        line-height: 32px;
        text-align: center;
        font-size: 12px;

        &.active {
            background-color: #fff;
            border-radius: 16px;
        }
    }
}

.comment-header-placeholder {
    height: 56px;
    visibility: hidden;
}

.replay-block {
    padding: 0 16px;
    padding-bottom: 100px;
    margin-top: 0;
    position: relative;
    z-index: 1;
    flex: 1;
    background: #fff;
    overflow-y: auto;

    image {
        width: 36px;
        height: 36px;
        border-radius: 8px;
    }

    .replay-content {
        display: flex;
        align-items: flex-start;
        gap: 13px;
        font-size: 14px;
        padding-bottom: 10px;
        .right-block {
            width: 100%;
        }

        .r-pic {
            width: 36px;
            height: 36px;
        }

        .r-user {
            .r-username-row {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 5px;
            }
            .r-username {
                margin-bottom: 0;
                color: #386bf6;
                font-size: 14px;
            }
            .author-label {
                background-color: #386bf6;
                color: #fff;
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 4px;
                line-height: 1;
                display: inline-flex;
                align-items: center;
            }
        }

        .r-cont {
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            cursor: pointer;
        }

        .r-hot {
            // background: #386bf6;
            width: 100%;
            display: flex;
            justify-content: space-between;
            margin: 17px 0;

            > text {
                color: #999999;
            }

            .hot-right {
                gap: 23px;
            }

            image {
                width: 16px;
                height: 16px;
                margin-right: 10px;
            }
        }

        .r-data {
            background: #f5f5f5;
            padding: 14px;
            font-size: 14px;
            font-weight: 400;
            line-height: 18px;
            border-radius: 10px;
            
            > view {
                margin-bottom: 8px; /* 增加回复之间的间距 */
                
                &:last-child {
                    margin-bottom: 0;
                }
            }
            
            .reply-content {
                word-break: break-all;
                line-height: 1.4; /* 根据实际情况调整行高 */
            }

            .name {
                color: #386bf6;
                font-size: 12px;
                margin-right: 4px; /* 添加右侧间距 */
            }

            .author-label {
                background-color: #386bf6;
                color: #fff;
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 4px;
                margin-right: 4px; /* 添加右侧间距 */
                display: inline-block; /* 确保标签正确显示 */
            }

            .content {
                display: inline; /* 确保内容行内显示 */
                word-break: break-all; /* 允许内容换行 */
            }

            /* 调整其他文本元素的间距 */
            .reply-content text {
                display: inline;
                margin-right: 4px;
            }
            .reply-content text:last-child {
                margin-right: 0;
            }
            
            .more-op {
                color: #386bf6;
                margin-top: 8px; /* 增加"展开更多"按钮的顶部间距 */
                text-align: center; /* 文字居中 */
                position: relative; /* 为分割线定位 */
                padding-top: 8px; /* 为分割线留出空间 */
                
                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background-color: rgba(0, 0, 0, 0.1); /* 分割线颜色 */
                }
            }
        }
    }

    /* 加载状态提示 */
    .loading-status {
        text-align: center;
        padding: 15px 0;
        color: #999;
        font-size: 12px;
    }
    
    .no-more-status {
        text-align: center;
        padding: 15px 0;
        color: #999;
        font-size: 12px;
        margin-bottom: 50px;
    }
    
    .empty-status {
        text-align: center;
        padding: 40px 0;
        color: #999;
        font-size: 14px;
    }
}

.user-info {
    padding: 16px 16px 0;

    margin-bottom: 12px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    gap: 15px;

    .user-avatar {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        overflow: hidden;
    }

    .user-info-left {
        gap: 15px;
    }

    .user-info-des {
        gap: 2px; /* 减少间距以适应新增的meta信息 */
    }

    .user-name {
        // margin: 0 20px 0 6px;
        font-size: 15px;
    }

    /* 新增用户元信息样式 */
    .user-meta {
        font-size: 11px;
        color: #999;
        display: flex;
        flex-wrap: wrap;
        gap: 2px;
    }

    .right-info-focus {
        background-color: #d8e7fe;
        color: #386bf6;
        border-radius: 15px;
        font-size: 12px;
        width: 64px;
        height: 26px;
        line-height: 26px;
        text-align: center;
        border: none;
        transition: all 0.3s ease;
    }
    
    /* 已关注状态的样式 */
    .right-info-focus.followed {
        background-color: #f5f5f5;
        color: #999;
    }
}

.op-block {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 16px;
    font-size: 12px;
    color: #4b4b4b;
    background: #fff;
    box-shadow: 0 4px 17px 0 #00000017;
    z-index: 998; /* 确保在评论框下方 */


    > view {
        display: flex;
        align-items: center;
    }

    .row-flex {
        gap: 12px;
        
        view {
            display: flex;
            align-items: center;
            height: 30px;
            min-width: 48px;
            justify-content: center;
            
            .uni-icons {
                display: flex;
                align-items: center;
                margin-right: 3px;
            }
            
            text {
                line-height: 1;
                font-size: 12px;
                white-space: nowrap;
            }
        }
    }

    input {
        background: #f5f5f5;
        font-size: 12px;
        width: 145px;
        height: 30px;
        border-radius: 16px;
        padding: 0 12px;
    }
    
    input::placeholder {
        text-align: left;
    }
}

.pop-op {
    display: flex;
    gap: 38px;
    padding: 23px 14px;
    font-size: 12px;

    .col-flex {
        gap: 8px;
        text-align: center;
    }

    image {
        width: 48px;
        height: 48px;
    }
}

.top-line {
    width: 38px;
    height: 4px;
    margin: 13px auto 0;
    border-radius: 10px;
    background: #e7e7e7;
}

.comment-popup {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    /* 使用uni-app支持的方式设置动画 */
    transform: translateY(100%); /* 初始状态为隐藏 */
    transition-property: transform, opacity;
    transition-duration: 0.25s;
    transition-timing-function: ease-out;
    opacity: 0;
    
    .comment-mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.4);
        z-index: -1;
    }
    
    .comment-contentblock {
        position: relative;
        background: #fff;
        padding: 10px 16px;
        display: flex;
        align-items: center;
        gap: 12px;
        box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
        
        .comment-content {
            display: flex;
            align-items: center;
            width: 100%;
            
            .comment-input {
                flex: 1;
                height: 36px;
                background-color: #f5f5f5;
                border-radius: 18px;
                padding: 0 16px;
                font-size: 14px;
                margin-right: 12px;
            }
            
            .comment-submit {
                width: 60px;
                height: 36px;
                background-color: #386BF6;
                color: #fff;
                border-radius: 18px;
                font-size: 14px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0;
                padding: 0;
            }
        }
    }
}

/* 显示状态的样式 */
.comment-popup-show {
    transform: translateY(0);
    opacity: 1;
}

.uni-input {
    height: 36px;
    background-color: #f5f5f5;
    border-radius: 36px;
    padding: 0 16px;
    font-size: 14px;
    border: 1px solid #e5e5e5;
    width: 120px;
}

.input-placeholder {
    color: #999;
    font-size: 14px;
}

/* 添加视频相关样式 */
.video-wrapper {
    width: 100%;
    height: 200px; /* 添加固定高度 */
    //margin: 12rpx 0;
    //background: #f8f8f8;
    //border-radius: 8rpx;
    //overflow: hidden;
    //position: relative;
    //display: flex;
    //justify-content: center;
    //align-items: center;
}

.post-video {
    display: block;
    width: 100%;
    background: transparent;
    border-radius: 8rpx;
    
    /* 移除全屏按钮 */
    &::-webkit-media-controls-fullscreen-button {
        display: none;
    }
}

/* 竖屏视频容器样式 */
.video-wrapper.portrait {
    padding: 0;
    display: flex;
    justify-content: center;
    
    .post-video {
        width: 56.25vw; /* 适当调整宽度 */
        max-height: 80vh;
        margin: 0 auto;
    }
}

/* 横屏视频容器样式 */
.video-wrapper.landscape {
    width: 100%;
    
    .post-video {
        width: 100%;
        max-height: 56.25vw; /* 保持16:9的比例 */
    }
}

/* 方形视频容器样式 */
.video-wrapper.square {
    width: 100%;
    
    .post-video {
        width: 100%;
        height: 100vw; /* 保持1:1的比例 */
        max-height: 750rpx; /* 设置最大高度 */
    }
}

.uni-popup {
    z-index: 1000; /* 确保弹出菜单在最上层 */
}

.reply-user-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    
    .author-label {
        background-color: #386bf6;
        color: #fff;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 4px;
        line-height: 1;
        display: inline-flex;
        align-items: center;
    }
}

// 为顶部导航栏的用户信息添加过渡效果
.user-title {
    transition: all 0.3s ease;
    opacity: 1;
    transform: translateY(0);
}

// 为顶部导航栏的吧信息添加过渡效果
.header-bar-info {
    transition: all 0.3s ease;
    opacity: 1;
    transform: translateY(0);
}

.header-bar-container {
    display: flex;
    align-items: center;
    background-color: #D3E4FE;
    border: 1px solid #D3E4FE;
    border-radius: 24px;
    padding: 6px 12px;
    max-width: fit-content;
}

// 确保其他元素有相同的过渡效果
.right-info-focus {
    transition: all 0.3s ease;
}

.header-right-wrapper {
    display: flex;
    align-items: center;
    height: 100%;
    padding-right: 8px;
}

.header-follow-btn {
    width: 52px;
    height: 22px;
    line-height: 22px;
    text-align: center;
    border-radius: 8px;
    border: 1px solid #B5CDE1;
    color: #B5CDE1;
    margin-right: 8px;
}

/* 添加吧来源按钮样式 */
.bar-source {
    margin: 8px 0 4px 0;
    display: flex;
    align-items: center;
    justify-content: space-between; /* 左右分布 */
    width: 100%;
    background-color: #D3E4FE; /* 改为浅灰色背景 */
    padding: 8px 8px; /* 增加内边距 */
    border-radius: 8px;
    border: 1px solid #D3E4FE; /* 浅灰色边框 */
    box-sizing: border-box;
}

.bar-info {
    display: flex;
    align-items: flex-start; /* 改为顶部对齐，适应多行文本 */
    gap: 12px;
    flex: 1;
}

.bar-avatar {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0; /* 防止头像被压缩 */
}

.bar-name {
    font-size: 12px;
    color: #333;
    font-weight: 500;
    line-height: 1.2; /* 添加行高 */
}

.enter-bar-btn {
    width: 60px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border-radius: 8px;
    background-color: #386BF6;
    color: #fff;
    font-size: 12px;
    border: none;
    flex-shrink: 0; /* 防止按钮被压缩 */
    
    &:active {
        opacity: 0.8;
        background-color: #2d5bd4;
    }
}

.bar-text-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1; /* 占据剩余空间 */
}

.bar-stats {
    display: flex;
    gap: 12px; /* 增加统计项之间的间距 */
}

.bar-stat-item {
    font-size: 11px;
    color: #333;
    line-height: 1.2;
}

.header-enter-bar-btn {
    width: 60px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border-radius: 14px;
    background-color: #1890FF;
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    border: none;
    margin-right: 12px;
    flex-shrink: 0;
    
    &:active {
        opacity: 0.8;
        background-color: #1677FF;
    }
}

.share-button {
    padding: 4px;
    background-color: #f0f0f0;
    border-radius: 4px;
}

.module-info {
    margin: 10px 0;
    display: flex;
    align-items: center;
}

.module-tag {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 16px;
    background-color: #f5f5f5;
    gap: 6px;
}

.module-name {
    font-size: 13px;
    color: #666;
    line-height: 1.2;
}

.r-avatar {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    cursor: pointer;
    
    &:hover {
        opacity: 0.8;
        box-shadow: 0 0 4px rgba(56, 107, 246, 0.5);
    }
}

.r-username {
    margin-bottom: 0;
    color: #386bf6;
    font-size: 14px;
    cursor: pointer;
    
    &:hover {
        text-decoration: underline;
    }
}

.name {
    color: #386bf6;
    font-size: 12px;
    margin-right: 4px; /* 添加右侧间距 */
    cursor: pointer;
    
    &:hover {
        text-decoration: underline;
    }
}
</style>