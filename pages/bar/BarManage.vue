<template>
	<view class="bar-manage">
		<!-- Management List -->
		<view class="manage-list">
			<!-- Module 1: 主页管理 -->
			<view class="section-title">主页管理</view>
			<view class="list-item" @click="navigateTo('blockManage')">
				<text>帖子板块管理</text>
				<uni-icons type="right" size="16" color="#999"></uni-icons>
			</view>
			<view class="list-item" @click="navigateTo('appManage')">
				<text>应用管理</text>
				<uni-icons type="right" size="16" color="#999"></uni-icons>
			</view>
			
			<!-- Module 2: 权限管理 -->
			<view class="section-title">权限管理</view>
			<view class="list-item" @click="navigateTo('permissionManage')">
				<text>谁可以发起私信</text>
				<uni-icons type="right" size="16" color="#999"></uni-icons>
			</view>
			
			<!-- Module 3: 内容管理 -->
			<view class="section-title">内容管理</view>
			<view class="list-item" @click="navigateTo('postManage')">
				<text>帖子管理</text>
				<uni-icons type="right" size="16" color="#999"></uni-icons>
			</view>
			
			<!-- Module 4: 成员管理 -->
			<view class="section-title">成员管理</view>
			<view class="list-item" @click="navigateTo('memberManage')">
				<text>身份组</text>
				<uni-icons type="right" size="16" color="#999"></uni-icons>
			</view>
			
			<!-- Module 5: 更多
			<view class="section-title">更多</view>
			<view class="list-item" @click="navigateTo('more')">
				<text>更多</text>
				<uni-icons type="right" size="16" color="#999"></uni-icons>
			</view>
			
			<!-- Module 6: 转让频道
			<view class="section-title">转让频道</view>
			<view class="list-item" @click="navigateTo('transfer')">
				<text>转让频道</text>
				<uni-icons type="right" size="16" color="#999"></uni-icons>
			</view> -->
		</view>
		
		<!-- Bottom Button
		<view class="bottom-button" @click="dissolveBar">
			<text class="dissolve-text">解散贴吧</text>
		</view> -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				bbsId: '' // 当前贴吧ID
			}
		},
		onLoad(options) {
			// 获取贴吧ID
			if (options.bbsId) {
				this.bbsId = options.bbsId;
				// 同时更新本地存储，确保其他页面能获取到正确的bbsId
				uni.setStorageSync('currentBbsId', options.bbsId);
			} else {
				// 如果没有传递bbsId，从本地存储获取
				this.bbsId = uni.getStorageSync('currentBbsId') || 1;
			}
			console.log('BarManage 获取到的 bbsId:', this.bbsId);
		},
		methods: {
			navigateTo(page) {
				// 页面导航逻辑，传递bbsId参数
				uni.navigateTo({
					url: `/pages/bar/barManage/${page}?bbsId=${this.bbsId}`
				})
			},
			dissolveBar() {
				// 解散贴吧的确认弹窗
				uni.showModal({
					title: '提示',
					content: '确定要解散该贴吧吗？',
					success: (res) => {
						if (res.confirm) {
							// 执行解散操作
							console.log('执行解散操作')
						}
					}
				})
			}
		}
	}
</script>

<style>
.manage-list {
	margin-top: 10px;
	padding: 0 15px;
}

.section-title {
	padding: 12px 0;
	font-size: 15px;
	color: #333;
	font-weight: 600;
	position: relative;
	margin-left: 10px;
}

.section-title::before {
	content: '';
	position: absolute;
	left: -10px;
	top: 50%;
	transform: translateY(-50%);
	width: 4px;
	height: 16px;
	background: #007AFF;
	border-radius: 2px;
}

.list-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px 15px;
	background-color: #ffffff;
	border-radius: 10px;
	margin-bottom: 10px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
	transition: all 0.3s ease;
}

.list-item:active {
	transform: scale(0.98);
	background-color: #f8f8f8;
}

.list-item text {
	font-size: 15px;
	color: #333;
}

.bottom-button {
	position: fixed;
	bottom: 30px;
	left: 50%;
	transform: translateX(-50%);
	width: 90%;
	padding: 16px 0;
	text-align: center;
	background-color: #ffffff;
	border-radius: 12px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;
}

.bottom-button:active {
	transform: translateX(-50%) scale(0.98);
	background-color: #fff5f5;
}

.dissolve-text {
	color: #ff3b30;
	font-size: 16px;
	font-weight: 500;
}

/* Add page background */
.bar-manage {
	min-height: 100vh;
	background-color: #f5f7fa;
	padding-bottom: 100px;
}
</style>
