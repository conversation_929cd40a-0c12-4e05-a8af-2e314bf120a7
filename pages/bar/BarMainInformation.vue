<template>
	<view class= "bar-info-container">
		<!-- 基本信息 -->
		<view class="content">
			<view class="basic-info">
    			<view class="avatar-container">
        			<image v-if="barInfo.avatar" :src="barInfo.avatar" mode="aspectFill" class="avatar-image"></image>
    			</view>
    			<view class="info-right">
        			<view class="creator-info">
            			<text class="name">{{ barInfo.name }}</text>
        			</view>
        			<view class="bar-id">
            			<text>贴吧号：{{ bbsId }}</text>
        			</view>
    			</view>
			</view>
			 
			<!-- 成员列表 -->
			<view class="section">
				<view class="section-header">
					<text class="section-title">贴吧成员</text>
				</view>
				<view class="avatar-list">
					<view class="avatar" v-for="(item, index) in 5" :key="index"></view>
					<view class="section-right">
						<text class="count">{{ barInfo.memberCount || 0 }}</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>
			</view>
			
			<!-- 相关群聊 -->
			<view class="section">
				<view class="section-header">
					<view class="section-title">相关群聊</view>
				</view>
				<view class="avatar-list">
					<view class="avatar" v-for="(item, index) in 3" :key="index"></view>
					<view class="section-right">
						<text class="count">3个</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>
			</view>
			
			<!-- 贴吧介绍 -->
			<view class="section intro-section">
    			<text class="section-title">贴吧介绍</text>
    			<view class="section-content">
        			<text class="intro-text">欢迎大家来共同加油</text>
    			</view>
			</view>

			<!-- 贴吧规则 -->
			<view class="section rules-section">
    			<text class="section-title">贴吧规则</text>
    			<view class="section-content">
        			<text class="rules-text">贴吧规则贴吧规则贴吧规则贴吧规则</text>
    			</view>
			</view>
		</view>
	</view>
</template>

<script>
import appServerApi from '@/api/appServerApi'

export default {
	data() {
		return {
			bbsId: '',
        	userId: '',
       		isCreator: false,
        	barInfo: {
            	name: '',
            	id: '',
            	memberCount: 0,
            	groupCount: 0,
            	intro: '',
            	rules: ''
        	}
		}
	},

	onLoad(options) {
    	if (options.id) {
        	this.bbsId = options.id;
        	this.getUserInfo().then(() => {
            	this.fetchBbsDetail();
        	});
    	}
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},

		async getUserInfo() {
        	try {
            	const userInfo = uni.getStorageSync('userInfo');
            	if (userInfo && userInfo.userId) {
                	this.userId = userInfo.userId;
            	}
            	return Promise.resolve();
        	} catch (error) {
            	console.error('获取用户信息失败:', error);
            	return Promise.reject(error);
        	}
    	},

		async fetchBbsDetail() {
        		try {
            		uni.showLoading({ title: '加载中...' });
            		const result = await appServerApi.getBbsDetail(this.bbsId);
            		if (result && result.data) {
						this.barInfo = {
                			name: result.data.name,
							avatar: result.data.avatar,
                			id: result.data.id,
                			memberCount: result.data.fansNum,
                			groupCount: result.data.groupCount,
                			intro: result.data.intro,
                			rules: result.data.rules
            			};
					// 检查是否是创建者
					if (this.userId && result.data.creator && result.data.creator.userId) {
						this.isCreator = result.data.creator.userId === this.userId;
					}
            		}
        		} catch (error) {
            		console.error('获取贴吧详情失败:', error);
            		uni.showToast({
                		title: '获取贴吧详情失败',
                		icon: 'none'
            		});
        		} finally {
            		uni.hideLoading();
        		}
    		},
	}
}
</script>

<style>
.bar-info-container {
	min-height: 100vh;
	background-color: #f8f9fa;
}

.content {
	padding: 20rpx;
}

.basic-info {
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
    padding: 40rpx;
    background: #ffffff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
    
    .avatar-container {
        width: 140rpx;
        height: 140rpx;
        border-radius: 20rpx;
        overflow: hidden;
        margin-right: 30rpx;
        flex-shrink: 0;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
        
        .avatar-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
    
    .info-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;

		.creator-info {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;
		
			.tag {
				background-color: #f0f0f0;
				padding: 6rpx 16rpx;
				border-radius: 6rpx;
				font-size: 24rpx;
				margin-right: 16rpx;
			}
		
			.name {
				font-size: 32rpx;
				color: #333;
				font-weight: 600;
			}
	    }

		.bar-id {
			font-size: 26rpx;
			color: #666;
			margin-top: 8rpx;
		}
	}
}

.section {
	padding: 30rpx 40rpx;
	background: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;
		
		.section-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
		}
	}
}

.section-right {
	display: flex;
	align-items: center;
	margin-left: auto;
	
	.count {
		font-size: 28rpx;
		color: #666;
		margin-right: 12rpx;
	}
}

.avatar-list {
	display: flex;
	flex-wrap: wrap;
	margin-top: 20rpx;
}

.avatar {
	width: 90rpx;
	height: 90rpx;
	border-radius: 50%;
	box-shadow: 0 4rpx 8rpx rgba(107, 140, 255, 0.2);
	margin-left:-40rpx;
	position: relative;
}

.avatar:first-child {
	margin-left: 0;
	background: linear-gradient(135deg, #6b8cff, #8e9fff);
}
.avatar:nth-child(2) {
	background-color: #333;
}
.avatar:nth-child(3) {
	background-color: aqua;
}
.avatar:nth-child(4) {
	background-color: #8e9fff;
}
.avatar:nth-child(5) {
	background-color: #333;
}

.section-content {
    margin-top: 24rpx;
    font-size: 28rpx;
    color: #666;
    line-height: 1.8;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
}

.intro-section, .rules-section {
    .section-content {
        background: #f8f9fa;
        padding: 24rpx;
        border-radius: 12rpx;
    }
}

</style>