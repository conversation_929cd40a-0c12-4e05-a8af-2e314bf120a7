<template>
  <view class="group-square-container">
    <scroll-view class="group-list" scroll-y @scrolltolower="onScrollToLower">
      <!-- 已关注的吧列表 -->
      <view>
        <view v-for="(bar, index) in bars" :key="index" class="group-item" @click="toBarHome(bar)">
          <view class="group-avatar">
            <image :src="bar.avatar" class="avatar" />
          </view>
          <view class="group-info">
            <view class="info-title">
              <text class="group-name">{{ bar.name }}</text>
              <!-- <text class="tag">{{ bar.categoryName }}</text> -->
            </view>
            <!-- <text class="group-desc">{{ group.description || "暂无介绍" }}</text> -->
          </view>
          <view class="group-status">
            <button class="follow-button" @click.stop="toggleFollow(bar.bbsId,bar.follow)">
              {{ bar.follow==1 ? '已关注' : '+ 关注' }}
            </button>
          </view>
        </view>
      </view>

      <!-- 推荐关注 -->
      <!-- 暂时隐藏推荐关注功能
      <view v-if="!isLoading && recommendedBars.length > 0">
        <view class="recommend-title">推荐关注</view>
        <view v-for="(bar, index) in recommendedBars" :key="index" class="group-item" @click="toBarHome(bar)">
          <view class="group-avatar">
            <image :src="bar.avatar" class="avatar" />
          </view>
          <view class="group-info">
            <view class="info-title">
              <text class="group-name">{{ bar.name }}</text>
            </view>
            <text class="group-desc">{{ bar.description || "暂无介绍" }}</text>
          </view>
          <view class="group-status">
            <button class="follow-button" @click.stop="toggleFollow(bar.bbsId, 0)">
              + 关注
            </button>
          </view>
        </view>
      </view>
      -->

      <view v-if="isLoading" class="loading-text">加载中...</view>
      <view v-if="!isLoading && !hasMore && bars.length > 0" class="loading-text">没有更多了</view>
    </scroll-view>
  </view>
</template>

<script>
import { getItem, setItem } from '../util/storageHelper'
import appServerApi from '@/api/appServerApi'

export default {
  name: 'followPage',
  components: {},
  data() {
    return {
      tabs: [
        {
          name: '全部',
          id: 0,
        },
      ],
      keyword: '',
      isTriggered: false,
      activeCategory: '0',
      bars: [],
      selectedGroup: {},
      pageNo: 1,
      pageSize: 20,
      hasMore: true, // 是否还有更多数据
      isLoading: false, // 是否正在加载
      recommendedBars: [], // 推荐的吧列表
      recommendPageNo: 1,
      recommendPageSize: 20,
      followedBarsCache: 'followed_bars_cache', // 缓存key
      // 新增：请求去重相关状态
      isRefreshing: false, // 是否正在刷新数据
      lastRefreshTime: 0, // 上次刷新时间
      refreshInterval: 30000, // 刷新间隔（30秒）
      pendingRequest: null, // 当前进行中的请求
    }
  },
  onLoad() {
    this.checkAndLoadData()
  },
  // onShow() {
  //   this.checkAndLoadData()
  // },
  methods: {
    handleSearch(event) {
      console.log('handleSearch', event)
      this.keyword = event.detail.value
      this.fetchGroups()
    },
    handleTabClick(item) {
      this.activeCategory = item.id
      this.fetchGroups()
    },
    toBarHome(post) {
      uni.navigateTo({
        url: '/pages/bar/BarHome?id=' + post.bbsId,
      })
    },
    async toggleFollow(ID, follow) {
      try {
        const isFollowing = follow ? 0 : 1
        await appServerApi.toggleBbsFollow({
          id: ID,
          follow: isFollowing,
        })

        if (isFollowing) {
          // 关注操作
          // 1. 从推荐列表中找到对应的吧
          const followedBar = this.recommendedBars.find(bar => bar.bbsId === ID)
          if (followedBar) {
            // 2. 添加到已关注列表
            followedBar.follow = 1
            this.bars.push(followedBar)
            // 3. 更新缓存和刷新时间
            setItem(this.followedBarsCache, this.bars)
            this.lastRefreshTime = Date.now() // 更新刷新时间
            // 4. 从推荐列表中移除
            this.recommendedBars = this.recommendedBars.filter(bar => bar.bbsId !== ID)
            
            // 5. 如果推荐列表为空，获取新的推荐
            if (this.recommendedBars.length === 0) {
              await this.fetchRecommendedBars()
            }
          }
        } else {
          // 取消关注操作
          // 1. 从已关注列表中移除
          this.bars = this.bars.filter(bar => bar.bbsId !== ID)
          // 2. 更新缓存和刷新时间
          setItem(this.followedBarsCache, this.bars)
          this.lastRefreshTime = Date.now() // 更新刷新时间
          
          // 3. 如果取消关注后没有关注的吧了，则获取推荐列表
          if (this.bars.length === 0) {
            await this.fetchRecommendedBars()
          }
        }

        uni.showToast({
          title: isFollowing ? '关注成功' : '取消关注成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('更新关注状态失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    },
    // 修改 fetchGroups 方法
    async fetchGroups(isLoadMore = false) {
      var userInfo = getItem('userInfo')
      if (this.isLoading) return
      
      // 如果不是加载更多，且正在刷新中，避免重复请求
      if (!isLoadMore && this.isRefreshing) {
        console.log('正在刷新中，跳过fetchGroups请求')
        return
      }
      
      this.isLoading = true

      if (!isLoadMore) {
        this.pageNo = 1
      }
      
      try {
        // 如果不是加载更多，且距离上次刷新时间很短，使用缓存数据
        if (!isLoadMore && this.lastRefreshTime > 0) {
          const timeSinceLastRefresh = Date.now() - this.lastRefreshTime
          if (timeSinceLastRefresh < 10000) { // 10秒内使用缓存
            console.log('使用最近的缓存数据，跳过网络请求')
            if (this.bars.length === 0) {
              await this.fetchRecommendedBars()
            }
            return
          }
        }

        const response = await appServerApi.getUserFollowedBars(
          userInfo.userId,
          this.pageNo,
          this.pageSize
        )
        
        if (response.data && response.data.result) {
          var arr = response.data.result.map((post) => ({
            ...post,
            follow: 1,
          }))
          if (isLoadMore) {
            this.bars = [...this.bars, ...arr].filter((bar, index, self) =>
              index === self.findIndex(b => b.bbsId === bar.bbsId)
            )
          } else {
            this.bars = arr.filter((bar, index, self) =>
              index === self.findIndex(b => b.bbsId === bar.bbsId)
            )
            // 更新缓存和刷新时间
            setItem(this.followedBarsCache, this.bars)
            this.lastRefreshTime = Date.now()
          }

          // 修改判断是否还有更多数据的逻辑
          this.hasMore = arr.length >= this.pageSize
          if (arr.length > 0) {
            this.pageNo++
          }
        } else {
          if (!isLoadMore) {
            this.bars = []
          }
          this.hasMore = false
        }

        // 如果没有关注的吧，则获取推荐的吧
        if (this.bars.length === 0) {
          await this.fetchRecommendedBars()
        }
      } catch (error) {
        console.error('Error fetching bars:', error)
        if (!isLoadMore) {
          this.bars = []
        }
        this.hasMore = false
      } finally {
        this.isLoading = false
      }
    },

    // 添加滚动到底部的处理方法
    async onScrollToLower() {
      if (!this.hasMore || this.isLoading) return
      if (this.bars.length > 0) {
        await this.fetchGroups(true)
      } else {
        await this.fetchRecommendedBars(true)
      }
    },
    async checkAndLoadData() {
      console.log('开始检查并加载数据')
      
      // 防止重复调用
      if (this.isRefreshing) {
        console.log('正在刷新中，跳过重复请求')
        return
      }

      // 1. 先从缓存加载数据
      this.loadFromCache()

      // 2. 检查是否需要刷新数据
      const now = Date.now()
      const shouldRefresh = this.shouldRefreshData(now)
      
      if (shouldRefresh) {
        console.log('开始智能更新数据')
        await this.smartRefresh()
      } else {
        console.log('缓存数据仍然有效，跳过网络请求')
      }

      // 3. 如果没有关注的吧，则获取推荐的吧
      if (this.bars.length === 0) {
        console.log('没有关注的吧，获取推荐列表')
        await this.fetchRecommendedBars()
      }
    },
    // 从缓存加载数据
    loadFromCache() {
      try {
        const cachedBars = getItem(this.followedBarsCache)
        if (cachedBars && Array.isArray(cachedBars)) {
          this.bars = cachedBars
          console.log('从缓存加载关注的吧:', cachedBars.length, '条')
          
          // 如果有缓存数据，设置一个较早的刷新时间，确保后续会进行网络更新
          if (this.lastRefreshTime === 0) {
            this.lastRefreshTime = Date.now() - this.refreshInterval + 5000 // 5秒后会触发刷新
          }
        }
      } catch (error) {
        console.error('加载缓存数据失败:', error)
      }
    },
    // 新增：判断是否需要刷新数据
    shouldRefreshData(currentTime) {
      // 如果从未刷新过，需要刷新
      if (this.lastRefreshTime === 0) {
        return true
      }
      
      // 如果距离上次刷新超过指定间隔，需要刷新
      if (currentTime - this.lastRefreshTime > this.refreshInterval) {
        return true
      }
      
      // 如果缓存为空，需要刷新
      if (this.bars.length === 0) {
        return true
      }
      
      return false
    },
    // 新增：智能刷新方法
    async smartRefresh() {
      // 如果已有请求在进行中，等待该请求完成
      if (this.pendingRequest) {
        console.log('等待现有请求完成')
        try {
          await this.pendingRequest
        } catch (error) {
          console.error('等待现有请求失败:', error)
        }
        return
      }

      this.isRefreshing = true
      
      try {
        const userInfo = getItem('userInfo')
        if (!userInfo || !userInfo.userId) {
          console.warn('用户信息不存在，跳过刷新')
          return
        }

        // 创建请求Promise并保存引用
        this.pendingRequest = appServerApi.getUserFollowedBars(
          userInfo.userId,
          1,
          this.pageSize
        )

        const response = await this.pendingRequest

        if (response.data && response.data.result) {
          const newBars = response.data.result.map(bar => ({
            ...bar,
            follow: 1,
          }))

          // 更新缓存和数据时去重
          const uniqueBars = newBars.filter((bar, index, self) =>
            index === self.findIndex(b => b.bbsId === bar.bbsId)
          )

          // 更新数据和缓存
          this.bars = uniqueBars
          setItem(this.followedBarsCache, uniqueBars)
          
          // 更新最后刷新时间
          this.lastRefreshTime = Date.now()
          
          console.log('智能刷新完成，获取到', uniqueBars.length, '个关注的吧')
        }
      } catch (error) {
        console.error('智能刷新失败:', error)
      } finally {
        this.isRefreshing = false
        this.pendingRequest = null
      }
    },
    // 新增：强制刷新数据（用于下拉刷新等场景）
    async forceRefresh() {
      console.log('强制刷新数据')
      this.lastRefreshTime = 0 // 重置刷新时间，强制触发网络请求
      await this.smartRefresh()
      
      // 如果没有关注的吧，获取推荐列表
      if (this.bars.length === 0) {
        await this.fetchRecommendedBars()
      }
    },
    // 获取推荐的吧
    async fetchRecommendedBars(isLoadMore = false) {
      if (this.isLoading) return
      this.isLoading = true

      if (!isLoadMore) {
        this.recommendPageNo = 1
      }

      try {
        const response = await appServerApi.getRecommendedTopics({
          pageNo: this.recommendPageNo,
          pageSize: this.recommendPageSize,
        })

        if (response.data && response.data.result) {
          // 过滤掉已关注的吧
          const newBars = response.data.result
            .filter(bar => !this.bars.some(followedBar => followedBar.bbsId === bar.bbsId))
            .map(bar => ({
              ...bar,
              follow: 0
            }))

          if (!isLoadMore) {
            this.recommendedBars = newBars
          } else {
            // 合并时去重
            const allBars = [...this.recommendedBars, ...newBars]
            this.recommendedBars = allBars.filter((bar, index, self) =>
              index === self.findIndex(b => b.bbsId === bar.bbsId)
            )
          }

          this.hasMore = newBars.length >= this.recommendPageSize
          if (this.hasMore) {
            this.recommendPageNo++
          }

          // 打印日志以便调试
          console.log('获取到推荐吧数量:', newBars.length)
          console.log('当前推荐列表数量:', this.recommendedBars.length)
        }
      } catch (error) {
        console.error('获取推荐吧失败:', error)
      } finally {
        this.isLoading = false
      }
    },

    // 新增：组件销毁时清理
    cleanup() {
      if (this.pendingRequest) {
        console.log('组件销毁，取消进行中的请求')
        // 注意：实际的请求取消需要根据具体的HTTP库实现
        this.pendingRequest = null
      }
      this.isRefreshing = false
    },
  },

  // 新增：组件销毁生命周期
  beforeUnmount() {
    this.cleanup()
  },
  
  // 兼容Vue2
  beforeDestroy() {
    this.cleanup()
  },
}
</script>

<style lang="scss" scoped>
.follow-button {
  background-color: #d8e7fe;
  color: #386bf6;
  border-radius: 15px;
  font-size: 12px;
  width: 64px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  border: none;
}

.follow-button:after {
  content: none;
}
.loading-text {
  text-align: center;
  padding: 16px;
  color: #999;
  font-size: 14px;
}

.group-square-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.op-btns,
.op-btns-icon,
.uni-navbar__header-btns,
.search-bar,
.search-input {
  display: none;
}

/* 分类标签导航 */
.category-tabs {
  display: flex;
  background-color: #ffffff;
  padding: 10px 0;
  border-bottom: 1px solid #e6e6e6;
}

/* 群列表 */
.group-list {
  flex: 1;
  padding: 10px 12px;
  overflow-y: auto;
}

.group-item {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  margin-bottom: 10px;
  padding: 10px 13px;
  border-radius: 10px;
}

.group-avatar {
  width: 60px;
  height: 60px;
  background: #f9fafc;
  box-shadow: inset 0px 0px 4px 0px rgba(23, 104, 139, 0.12);
  border-radius: 6px 6px 6px 6px;
  flex-shrink: 0;
  padding: 2px;
  justify-content: space-between;
  .avatar {
    width: 60px;
    height: 60px;
    border-radius: 4px 4px 4px 4px;
    margin-right: 1px;
  }
}

.group-info {
  height: 60px;
  position: relative;
  flex: 1;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  padding: 6px 12px;
  width: 50%;
}

.info-title {
  display: flex;
  align-items: center;
}

.group-name {
  font-weight: 500;
  font-size: 14px;
  max-width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.group-desc {
  color: #999;
  font-size: 12px;
}

.group-status {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tag {
  // height: 16px;
  background: #0cc86b;
  border-radius: 2px 2px 2px 2px;
  // line-height: 16px;
  color: #fff;
  font-size: 10px;
  margin-left: 5px;
  padding: 2px;
}

.join-button {
  height: 22px;
  font-size: 12px;
  line-height: 22px;
  background: #ffffff;
  border-radius: 5px 5px 5px 5px;
  border: 1px solid #386bf6;
  color: #386bf6;
}

.has-button {
  height: 22px;
  font-size: 12px;
  line-height: 22px;
  // background: #FFFFFF;
  border-radius: 5px;
  border: 1px solid #666;
  color: #666;
}

/* 底部按钮 */
.create-group {
  padding: 10px;
}

.create-group-button {
  width: 158px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  color: #ffffff;
  background-color: #007aff;
  border-radius: 10px;
  font-size: 16px;
}

.popup-content {
  display: flex;
  flex-direction: column;
  padding: 23px;
  width: 100%;
  height: auto;
  align-items: center;

  .top-line {
    width: 38px;
    height: 4px;
    margin: 0 auto 17px auto;
    border-radius: 10px;
    background: #e7e7e7;
  }

  .pop-title {
    padding-top: 18px;
  }

  .pop-desc {
    border-top: 1px solid #efefef;
    padding: 18px 0;
    margin-top: 20px;
    width: 100%;

    .desc-line {
      display: flex;
      position: relative;
      padding-bottom: 15px;
      align-items: center;

      .desc-label {
        height: 15px;
        font-size: 11px;
        background: #f5f5f5;
        color: #000000;
        line-height: 15px;
        text-align: center;
        padding: 2px;
        margin-right: 10px;
      }

      .desc-content {
        font-size: 14px;
        color: #999999;
        display: flex;
      }

      .desc-name {
        color: #000000;
      }

      .header-right {
        position: absolute;
        right: 22px;
        font-size: 14px;
        color: #999999;
        line-height: 16px;

        i {
          margin-left: 10px;
        }
      }

      .avatar-small {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        border-radius: 4px 4px 4px 4px;
      }
    }
  }

  .op-block {
    margin-bottom: 20px;
    width: 100%;
    height: 46px;
    background: #386bf6;
    border-radius: 10px 10px 10px 10px;
  }
}

/* 推荐标题样式 */
.recommend-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  padding: 20rpx 0;
  margin-bottom: 10rpx;
}

/* 推荐描述样式 */
.group-desc {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>