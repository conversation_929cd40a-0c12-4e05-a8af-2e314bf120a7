<template>
  <view class="edit-field-container">
    <CustomHeader>
      <template v-slot:title>
        <text class="title">{{ title }}</text>
      </template>
    </CustomHeader>

    <view class="content">
      <!-- 富文本编辑器 -->
      <editor
        id="editor"
        class="editor"
        :placeholder="'请输入' + title"
        @ready="onEditorReady"
        @input="onEditorInput"
      ></editor>

      <!-- 底部工具栏 -->
      <view class="toolbar">
        <view class="tool-item" @click="insertImage">
          <image src="/static/image/icon/image.png" class="tool-icon"></image>
          <text class="tool-text">图片</text>
        </view>
        <view class="tool-item" @click="insertVideo">
          <image src="/static/image/icon/video.png" class="tool-icon"></image>
          <text class="tool-text">视频</text>
        </view>
      </view>
    </view>

    <view class="bottom-bar">
      <button class="btn-save" @click="save">保存</button>
    </view>
  </view>
</template>

<script>
import CustomHeader from '../../components/custom-header'
import appServerApi from '@/api/appServerApi'

export default {
  components: {
    CustomHeader
  },

  data() {
    return {
      editorCtx: null,
      content: '',
      type: '',
      title: '',
      value: '',
      uploading: false
    }
  },

  onLoad(options) {
    this.type = options.type || ''
    this.title = options.title || ''
    this.value = options.value ? decodeURIComponent(options.value) : ''
  },

  methods: {
    // 编辑器就绪
    onEditorReady() {
      uni.createSelectorQuery()
        .select('#editor')
        .context((res) => {
          this.editorCtx = res.context
          // 设置初始内容
          if (this.value) {
                this.editorCtx.setContents({
                html: this.value
            })
          }
        })
        .exec()
    },

    // 编辑器输入
    onEditorInput(e) {
      this.content = e.detail.html
    },

    // 插入图片
    async insertImage() {
      if (!this.editorCtx || this.uploading) return

      try {
        const res = await uni.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: ['album', 'camera']
        })

        if (res.tempFilePaths && res.tempFilePaths[0]) {
          const imagePath = res.tempFilePaths[0]
          this.uploading = true
          
          // 使用uploadimgFile方法上传图片
          appServerApi.uploadimgFile(
            imagePath,
            (uploadRes) => {
              this.uploading = false
              // 上传成功后插入图片
              this.editorCtx.insertImage({
                src: uploadRes.data,
                width: '95%',
                height: 'auto',
                extClass: 'editor-image'
              })
            },
            (error) => {
              this.uploading = false
              uni.showToast({ 
                title: error.msg || '上传图片失败', 
                icon: 'none' 
              })
            }
          )
        }
      } catch (error) {
        this.uploading = false
        // uni.showToast({ title: '选择图片失败', icon: 'none' })
      }
    },

    // 插入视频
    async insertVideo() {
      if (!this.editorCtx || this.uploading) return

      try {
        const res = await uni.chooseVideo({
          sourceType: ['album', 'camera'],
          compressed: true,
          maxDuration: 60
        })

        if (res.tempFilePath) {
          this.uploading = true
          
          // 使用uploadimgFile方法上传视频
          appServerApi.uploadimgFile(
            res.tempFilePath,
            (uploadRes) => {
              this.uploading = false
              // 上传成功后插入视频占位图片
              this.editorCtx.insertImage({
                src: 'https://img.zcool.cn/community/01055859b8e37fa8012075341db67f.gif',  // 使用视频占位图标
                width: '95%',
                height: 'auto',
                alt: uploadRes.data,  // 在alt属性中存储实际视频地址
                extClass: 'editor-video'
              })
            },
            (error) => {
              this.uploading = false
              uni.showToast({ 
                title: error.msg || '上传视频失败', 
                icon: 'none' 
              })
            }
          )
        }
      } catch (error) {
        this.uploading = false
        uni.showToast({
          title: '选择视频失败',
          icon: 'none'
        })
      }
    },

    // 保存
    save() {
      this.editorCtx.getContents({
        success: (res) => {
          uni.setStorageSync('editFieldResult', {
            type: this.type,
            value: res.html
          })
          uni.navigateBack()
        }
      })
    }
  }
}
</script>

<style scoped>
.edit-field-container {
  min-height: 100vh;
  background: #f8f9fb;
  display: flex;
  flex-direction: column;
}
.title{
  /* 居中 */
  text-align: center;
}
.content {
  flex: 1;
  padding: 10px 18px 80px 18px;
  position: relative;
}

.editor {
  width: 100%;
  min-height: calc(100vh - 250px);
  background: #fff;
  border-radius: 8px;
  padding: 12px;
  font-size: 15px;
  color: #333;
  border: 1px solid #e6e6e6;
}

/* 工具栏样式 */
.toolbar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 60px;
  background: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 10px 0;
  border-top: 1px solid #e6e6e6;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px 15px;
}

.tool-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.tool-text {
  font-size: 12px;
  color: #666;
}

.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.03);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
  z-index: 200;
}

.btn-save {
  width: 90%;
  height: 40px;
  border-radius: 20px;
  font-size: 15px;
  font-weight: 500;
  background: #4E7CF7;
  color: #fff;
  border: none;
}

/* 编辑器内容样式 */
:deep(.editor-image) {
  max-width: 100%;
  margin: 10px 0;
  display: block;
}

:deep(.editor-video) {
  max-width: 100%;
  margin: 10px 0;
  display: block;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.editor-video video) {
  width: 100%;
  height: auto;
  display: block;
}
</style> 