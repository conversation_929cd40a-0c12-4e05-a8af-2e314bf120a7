<template>
	<view class="show-card-container">
		<!-- 顶部栏 -->
		<view class="preview-header">
			<view class="preview-back" @click="goBack">
				<uni-icons type="back" size="24" color="#222" />
			</view>
			<text class="preview-title">选择展示渠道</text>
		</view>

		<!-- 主内容可滑动区域 -->
		<scroll-view class="main-scroll" scroll-y>
			<!-- 平台和社群选项 -->
			<view class="option-list">

				<!-- <view class="option-item" @click="toggleOption('platform')">

					<view :class="['custom-checkbox', selectedOptions.platform ? 'checked' : '']">
						<view class="dot" v-if="selectedOptions.platform"></view>
					</view>

					<view class="option-label">
						<text class="option-title">平台</text>
						<text class="option-desc">(人脉圈)</text>
					</view>
				</view> -->

				<view class="option-item" @click.stop="toggleCommunitySelectAll">
					<view :class="['custom-checkbox', selectedOptions.community ? 'checked' : '']">
						<view class="dot" v-if="selectedOptions.community"></view>
					</view>
					<view class="option-label">
						<text class="option-title">社群</text>
						<!-- <text class="option-desc">(仅在已加入的社群中展示,默认以最后一次覆盖为准)</text> -->
					</view>
				</view>
			</view>

			<!-- 群聊列表（社群下） -->
			<view class="group-list">
				<view v-for="group in groups" :key="group.gid" class="group-item-row" @click="toggleGroup(group.gid)">

					<view class="group-radio">
						<view :class="['custom-checkbox', selectedGroups.includes(group.gid) ? 'checked blue' : '']">
							<view class="dot" v-if="selectedGroups.includes(group.gid)"></view>
						</view>
					</view>

					<view class="group-info-wrap">
						<view class="group-avatar-wrap">
							<image :src="group.avatar || defaultGroupAvatar" class="avatar" mode="aspectFill" />
						</view>
						<view class="group-info">
							<view class="info-title-row">
								<text class="group-name">{{ group.name.length > 20 ? group.name.slice(0,20)+'…' : group.name }}</text>
								<text class="tag" v-if="group.tag || group.categoryName">{{ (group.tag || group.categoryName).length > 10 ? (group.tag || group.categoryName).slice(0,10)+'…' : (group.tag || group.categoryName) }}</text>
							</view>
							<text class="group-desc">{{ (group.desc || group.description || '暂无介绍').length > 20 ? (group.desc || group.description || '暂无介绍').slice(0,20)+'…' : (group.desc || group.description || '暂无介绍') }}</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<view v-if=showConfirmModal class="confirm-modal-mask" @click="dismissShowConfirmModal">
			<view class="confirm-modal-box" @click.stop>
				<view class="confirm-modal-content">
					名片将会展示到您选择的群,若群中有您的其他名片将会进行覆盖!
				</view>
				<view class="confirm-modal-actions">
					<button class="empty-modal-btn cancel" @click="dismissShowConfirmModal">暂不展示</button>
					<button class="empty-modal-btn confirm" @click="confirmSave">确认展示</button>
				</view>
			</view>
		</view>
		<!-- 底部悬浮按钮 -->
		<view class="bottom-btn-wrap">
			<button class="save-btn" @click="onSave">保存并展示</button>
		</view>
	</view>
</template>

<script>
import appServerApi from "../../api/appServerApi";
export default {
	data() {
		return {
			cardId: '',
			groups: [],
			hasMore: true,
			pageNo: 1,
			pageSize: 100,
			selectedOptions: {
				platform: false,
				community: false,
			},
			selectedGroups: [],
			defaultGroupAvatar: '/static/default-group.png', // 你可以替换为实际默认头像路径
			showConfirmModal: false, // 新增弹窗开关
		}
	},
	onLoad(options) {
		// 接收传递的 cardId 参数
		if (options.cardId) {
			this.cardId = options.cardId;
			console.log('选择渠道页面接收到的名片ID:', this.cardId);
		}
		this.fetchGroups();
		this.getCardDisplayGroups();
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		toggleOption(type) {
			this.selectedOptions[type] = !this.selectedOptions[type];
		},
		toggleGroup(groupId) {
			const idx = this.selectedGroups.indexOf(groupId);
			console.log("选择的群索引为：",idx)
			console.log("选择的群索引对应的群ID为：",groupId)
			if (idx > -1) {
				this.selectedGroups.splice(idx, 1);
				// console.log("选择的群存在！")
			} else {
				// console.log("选择的群不存在！")
				this.selectedGroups.push(groupId);
			}
		},
		async fetchGroups(isLoadMore = false) {
			if (!isLoadMore) {
				this.isLoading = true;
				// 只在非刷新状态下显示loading
				if (!this.isRefreshing) {
					uni.showLoading({
						title: '加载中...'
					});
				}
				this.pageNo = 1;
				this.hasMore = true;
			}

			try {
				const response = await appServerApi.searchGroups({
					pageNo: this.pageNo,
					pageSize: this.pageSize,
					joined: 1, // 只获取已加入的群
					keyword: this.keyword || '',
				});
				console.log(`搜索群组参数: pageNo=${this.pageNo}, pageSize=${this.pageSize}, keyword='${this.keyword || ""}'`);
				if (response.data && response.data.result) {
					let groupList = response.data.result;
					if (!isLoadMore) {
						this.groups = groupList;
					} else {
						this.groups = [...this.groups, ...groupList];
					}

					this.hasMore = response.data.result.length >= this.pageSize;
					if (this.hasMore) {
						this.pageNo++;
					}
					console.log("搜索到的群列表：",this.groups)
					// 根据数据情况设置分类展开状态
					// this.updateCategoryExpandState();
				} else {
					if (!isLoadMore) {
						this.groups = [];
					}
					this.hasMore = false;
					// this.updateCategoryExpandState();
				}
			} catch (error) {
				console.error("Error fetching groups:", error);
				if (!isLoadMore) {
					this.groups = [];
				}
				this.hasMore = false;
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
				this.updateCategoryExpandState();
			} finally {
				this.isLoading = false;
				if (!isLoadMore && !this.isRefreshing) {
					uni.hideLoading();
				}
			}
		},
		async getCardDisplayGroups() {
			if (!this.cardId) return;
			try {
				const res = await appServerApi.getCardDisplayGroups(this.cardId);
				if (res && res.data && Array.isArray(res.data)) {
					this.selectedGroups = res.data;
				}
			} catch (e) {
				console.error('获取名片展示群失败', e);
			}
		},
		async setCardDisplayGroupsBatch() {
			try {
				await appServerApi.setCardDisplayGroupsBatch(this.selectedGroups, this.cardId);
				uni.showToast({ title: '保存成功', icon: 'success' });
			} catch (e) {
				uni.showToast({ title: '保存失败', icon: 'none' });
				console.error('批量设置名片展示群失败', e);
			}
		},
		onSave() {
			// 保存逻辑
			console.log("选择的群聊为：",this.selectedGroups)
			// uni.showToast({ title: '保存成功', icon: 'success' });
			// this.setCardDisplayGroupsBatch();
			this.showConfirmModal = true
		},
		confirmSave() {
			this.setCardDisplayGroupsBatch();
			this.showConfirmModal = false
		},
		toggleCommunitySelectAll() {
			if (this.selectedGroups.length === this.groups.length && this.groups.length > 0) {
				this.selectedGroups = [];
			} else {
				this.selectedGroups = this.groups.map(g => g.gid);
			}
			// 同步社群选中状态
			this.selectedOptions.community = this.selectedGroups.length > 0;
		},
		dismissShowConfirmModal() {
            this.showConfirmModal = false;
        },
	}
}
</script>

<style  lang="scss" scoped>
.show-card-container {
	min-height: 100vh;
	background: #fff;
	display: flex;
	flex-direction: column;
	position: relative;
}

.preview-header {
	height: 80px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-bottom: 1px solid #f0f0f0;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding-top: 40px;
	box-sizing: border-box;
	z-index: 3100;
}

.preview-back {
	position: absolute;
	left: 16px;
	top: 40px;
	height: 36px;
	display: flex;
	align-items: center;
}

.preview-title {
	font-size: 18px;
	/* color: #222; */
	/* font-weight: 500; */
}

.main-scroll {
	flex: 1;
	margin-top: 80px;
	/* padding: 0 20px 80px 20px; */
	padding-bottom: 80px;
	box-sizing: border-box;
	overflow-y: auto;
}

.option-list {
	margin-top: 20px;
	margin-bottom: 12px;
    gap: 20px;
}

.option-item {
	display: flex;
	align-items: center;
	margin-bottom: 16px;
	cursor: pointer;
	padding: 0 10px 0 10px;
	margin: 0px 15px 15px 15px;
}

.custom-checkbox {
	width: 20px;
	height: 20px;
	border: 2px solid #222;
	border-radius: 50%;
	margin-right: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #fff;
	transition: border-color 0.2s;
}

.custom-checkbox.checked {
	border-color: #007aff;
}

.custom-checkbox.checked .dot {
	background: #007aff;
}

.custom-checkbox.blue {
	border-color: #007aff;
}

.dot {
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background: #222;
}

.custom-checkbox.checked .dot {
	background: #007aff;
}

.option-label {
	display: flex;
	flex-direction: row;
	align-items: baseline;
    padding-left: 10px;
}

.option-title {
	font-size: 16px;
	color: #222;
	font-weight: 500;
}

.option-desc {
	font-size: 12px;
	color: #888;
	margin-left: 6px;
}

.group-list {
	margin-top: 8px;
}

.group-item-row {
	display: flex;
	align-items: center;
	/* margin-bottom: 15px; */
	padding: 0 10px 0 10px;
	margin: 0px 15px 15px 15px;
	border-radius: 6px;
	background: #fff;
}

.group-radio {
	display: flex;
	align-items: center;
	margin-right: 10px;
}

.custom-checkbox {
	width: 20px;
	height: 20px;
	border: 2px solid #222;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #fff;
	transition: border-color 0.2s;
}

.custom-checkbox.checked {
	border-color: #007aff;
}

.custom-checkbox.checked .dot {
	background: #007aff;
}

.custom-checkbox.blue {
	border-color: #007aff;
}

.dot {
	width: 10px;
	height: 10px;
	border-radius: 50%;
	background: #222;
}

.custom-checkbox.checked .dot {
	background: #007aff;
}

.group-info-wrap {
	display: flex;
	align-items: center;
	background-color: #fff;
	border-radius: 10px;
	/* box-shadow: 0 2px 8px 0 rgba(23,104,139,0.06); */
	/* padding: 8px 12px; */
	flex: 1;
	min-height: 60px;
}

.group-avatar-wrap {
	width: 50px;
	height: 50px;
	border-radius: 6px;
	overflow: hidden;
	margin-right: 15px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f9fafc;
	/* box-shadow: inset 0px 0px 4px 0px rgba(23, 104, 139, 0.12); */
}

.avatar {
	width: 50px;
	height: 50px;
	border-radius: 6px;
}

.group-info {
	display: flex;
	flex-direction: column;
	justify-content: center;
	flex: 1;
	min-width: 0;
}

.info-title-row {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.group-name {
	font-weight: 500;
	font-size: 14px;
	max-width: 160px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.tag {
	background: #0cc86b;
	border-radius: 2px;
	color: #fff;
	font-size: 10px;
	margin-left: 10px;
	padding: 2px 4px;
	max-width: 60px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.group-desc {
	color: #999;
	font-size: 12px;
	max-width: 120px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.bottom-btn-wrap {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 3200;
	pointer-events: none;
}

.save-btn {
	display: flex;
	width: 60vw;
	max-width: 500px;
	height: 48px;
	background: #007aff;
	color: #fff;
	font-size: 16px;
	border-radius: 12px;
	box-shadow: 0 4px 12px rgba(0,122,255,0.10);
	border: none;
	pointer-events: auto;
	align-items: center;
	justify-content: center;
	font-weight: 500;
}

.confirm-modal-mask {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.15);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.role-badge {
    margin-left: 8px;
    // background-color: #e7edff; /* 淡蓝色背景 */
    // color: #256dff; /* 蓝色文字 */
    // font-size: 12px;
    // padding: 2px 6px;
    // border-radius: 4px;
    font-weight: normal;
    margin-bottom: 5px;

    padding: 2px 8px;
    background: #007AFF;
    border-radius: 8px;
    font-size: 12px;
    color: #fff;
}

.confirm-modal-box {
    background: #fff;
    border-radius: 10px;
    // border: 1.5px solid #4E7CF7;
    box-shadow: 0 2px 16px rgba(0,0,0,0.08);
    padding: 28px 18px 18px 18px;
    min-width: 280px;
    max-width: 90vw;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.confirm-modal-content {
    font-size: 16px;
    color: #222;
    margin-bottom: 28px;
    margin-left: 20px;
    margin-right: 20px;
    text-align: center;
    line-height: 1.6;
}

.confirm-modal-actions {
    display: flex;
    width: 100%;
    justify-content: space-between;
}

.empty-modal-btn {
    flex: 1;
    height: 38px;
    margin: 0 8px;
    border-radius: 6px;
    font-size: 18px;
    font-weight: 500;
    border: none;
    outline: none;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 38px;
}

.empty-modal-btn.cancel {
    background: #e0e0e0;
    color: #aaa;
}

.empty-modal-btn.confirm {
    background: #4E7CF7;
    color: #fff;
}
</style>