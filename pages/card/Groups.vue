<template>
	<view class="groups-container">
		<view class="groups-header">
			<view class="groups-back" @click="goBack">
				<uni-icons type="back" size="24" color="#222" />
			</view>
			<text class="groups-title">社群</text>
		</view>

		<view class="groups-content">
			<!-- 内部群列表 -->
			<view class="group-list">
				<view class="group-item" v-for="item in dqdGroups" :key="item.id" @click="handleGroupClick(item)">
					<image class="grouplist-avatar" :src="item.avatar || defaultAvatar" mode="aspectFill" />
					<view class="group-info">
						<view class="group-name">{{ item.name }}</view>
						<view class="group-desc">入群条件: 无限制</view>
					</view>
					<button v-if="item.joined==0" class="group-btn" @click="applyJoin(item)">申请加入</button>
				</view>
			</view>
			<!-- 分割线 -->
			<view v-if="dqdGroups.length>0" class="divider"></view>
			<!-- 第三方社群提示 -->
			<view v-if="otherGroups.length>0" class="third-title">第三方平台社群</view>
			<!-- 外部群列表 -->
			<view class="group-list">
				<view class="group-item" v-for="item in otherGroups" :key="item.id" @click="viewGroup(item)">
					<image class="grouplist-avatar" :src="item.avatar || defaultAvatar" mode="aspectFill" />
					<view class="group-info">
						<view class="group-name">{{ item.groupName }}</view>
						<view class="group-desc">群介绍: {{ item.description || '暂无介绍' }}</view>
					</view>
					<button class="group-btn" @click="viewGroup(item)">点击查看</button>
				</view>
			</view>
		</view>

		<!-- 遮罩和弹窗 -->
		<view v-if="showGroupModal" class="modal-mask" @touchmove.stop.prevent @click="closeGroupModal">
			<view class="group-modal" :style="{height: modalHeight}">
				<view class="top-line"></view>
				<view class="modal-close" @click="closeGroupModal"></view>
				<!-- 群信息区域 -->
				<view class="modal-group-info">
					<image class="modal-avatar" :src="modalGroup.avatar || defaultAvatar" mode="aspectFill" />
					<view class="modal-meta">
						<view class="modal-title">{{ modalGroup.groupName }}</view>
						<view class="modal-desc">群介绍: {{ modalGroup.description || '暂无介绍' }}</view>
					</view>
				</view>
				<!-- 二维码区域 -->
				<view class="modal-qrcode-area">
					<image v-if="modalGroup.qrcode" class="modal-qrcode" :src="modalGroup.qrcode" mode="aspectFit" />
					<view v-else class="modal-qrcode-placeholder">暂无二维码</view>
				</view>
				<!-- 按钮操作区域 -->
				<view class="modal-btns">
					<!-- <button class="modal-btn" @click="jumpToWechat">跳转到微信</button> -->
					<button class="modal-btn save" @click="handleSave">保存群二维码</button>
				</view>
			</view>
		</view>

		<uni-popup ref="popup" borderRadius="10px 10px 0 0" background-color="#fff" @click.stop>
            <view class="popup-content" >
                <view class="top-line"></view>
                <view class="group-avatar">
                    <view v-if="selectedGroup.price" class="price-badge">
                        <text class="badge-text">付费</text>
                    </view>
                    <image :src="selectedGroup.avatar" class="group-avatar-img" />
                </view>
                <view class="pop-title">
                    <text class="group-name">{{ selectedGroup.groupName }}</text>
                    <text v-if="selectedGroup.categoryName" class="tag">{{ selectedGroup.categoryName }}</text>
                </view>
                <view class="pop-desc">
                    <view class="desc-line" v-if="false">
                        <view class="desc-label">群介绍</view>
                        <view class="desc-content">{{ selectedGroup.description || "该群主很懒，什么都没留下" }}</view>
                    </view>
                    <view class="desc-line" @click.stop="checkCreatorHome">
                        <view class="desc-label">群主</view>
                        <view class="desc-content">
                            <image :src="creatorInfo.portrait" class="avatar-small" />
                            <view class="desc-name">{{ selectedGroup.CreatorName }}</view>
                        </view>
                        <view class="header-right" @click.stop="checkCreatorHome">查看主页<i class="icon-ion-ios-arrow-right"></i></view>
                    </view>                 
                </view>

                <view class="op-block">
                    <view v-if="selectedGroup.price > 0" class="price-header">
                        <text class="validity">有效期：{{ getValidityPeriod(selectedGroup.duration) }}</text>
                        <text class="daily-price">低至 {{ (selectedGroup.price / getDurationDays(selectedGroup.duration)).toFixed(3) }} 元/天</text>
                    </view>
                    <button v-if="selectedGroup.price > 0" type="primary" @click="handlePayJoin(selectedGroup)">
                        立即加入 ¥{{ selectedGroup.price }}
                    </button>
                    <button v-else type="primary" @click="joinGroup(selectedGroup)">
                        立即加入
                    </button>
                </view>
            </view>
        </uni-popup>
		<payment-selector ref="paymentselector" v-model:visible="paymentVisible" :amount="payAmount" :balance="userBalance" @close="handlePaymentClose" @confirm="handlePaymentConfirm" />
        <ios-payment-selector v-model:visible="iosPaymentVisible" @close="handleIosPaymentClose" @confirm="handleIosPaymentConfirm" />

		<view v-if="dqdGroups.length === 0 && otherGroups.length === 0" class="empty-groups">
			<!-- <image class="empty-img" src="/static/image/empty-group.png" mode="widthFix" /> -->
			<view class="empty-text">该用户并未添加社群</view>
		</view>
	</view>
</template>

<script>
import appServerApi from '@/api/appServerApi.js'

import PaymentSelector from '@/components/payment/PaymentSelector2'
import IosPaymentSelector from '@/components/payment/IosPaymentSelector'
import wfc from '@/wfc/client/wfc'
import store from '@/store'
import { getItem } from '@/pages/util/storageHelper'

import Conversation from '@/wfc/model/conversation'
import ConversationType from '@/wfc/model/conversationType'
import ConversationInfo from '@/wfc/model/conversationInfo'
import util from '@/utils/util'
import topMessage from '@/common/topMessageView'

export default {
	components: {
        PaymentSelector,
        IosPaymentSelector
    },
	data() {
		return {
			cardId: '',
			dqdGroups: [],
			otherGroups: [],
			defaultAvatar: 'https://static.ykimg.com/avatar/default-group.png',
			showGroupModal: false,
			modalGroup: {},
			modalHeight: '50vh',
			selectedGroup: {},
			paymentVisible : false,
			iosPaymentVisible: false,
			payAmount: '299.00',
			userBalance: '0',
		}
	},
	onLoad(options) {
		this.cardId = options.cardId
		this.getCardGroups()
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},
		async getCardGroups() {
			try {
				// 并行获取内部群和外部群
				const [innerRes, outerRes] = await Promise.all([
					appServerApi.getCardGroupsInner(this.cardId),
					appServerApi.getCardGroupsOuter(this.cardId)
				]);

				// 处理内部群数据
				if (innerRes && innerRes.code === 200 && Array.isArray(innerRes.data)) {
					this.dqdGroups = innerRes.data;
				} else {
					console.warn('获取内部群组数据失败或格式不正确', innerRes);
					this.dqdGroups = []; // 保证dqdGroups是数组
				}
				console.log('地球岛内部群组(dqdGroups):', this.dqdGroups);

				// 处理外部群数据
				if (outerRes && outerRes.code === 200 && Array.isArray(outerRes.data)) {
					this.otherGroups = outerRes.data;
				} else {
					console.warn('获取外部群组数据失败或格式不正确', outerRes);
					this.otherGroups = []; // 保证otherGroups是数组
				}
				console.log('第三方外部群组(otherGroups):', this.otherGroups);

			} catch (error) {
				console.error('并行获取群组列表时发生错误', error);
				// 出错时清空两个列表，防止页面展示脏数据
				this.dqdGroups = [];
				this.otherGroups = [];
			}
		},
		applyJoin(item) {
			// 申请加入逻辑
			// uni.showToast({ title: '申请加入功能待实现', icon: 'none' })
		},

		handlePayJoin(group) {
            console.log(group)
            this.payAmount = group.price
            // this.$refs.paymentselector.init()
            this.paymentVisible = true
        },

		viewGroup(item) {
			// 显示弹窗并填充群信息
			this.modalGroup = item
			this.showGroupModal = true
		},
		closeGroupModal() {
			this.showGroupModal = false
		},
		jumpToWechat() {
			uni.showToast({ title: '跳转微信功能待实现', icon: 'none' })
		},

		getDurationDays(duration) {
            if (!duration) return 365;
            return parseInt(duration, 10);
        },

		handlePaymentClose() {
            console.log('支付弹窗关闭')
            // 确保重置支付选择器状态
            this.$refs.paymentselector.resetState()
            this.$refs.popup.close('bottom')
        },
        handleIosPaymentClose() {
            console.log('iOS支付弹窗关闭')
            this.$refs.popup.close('bottom')
        },

		async handleSave() {
            try {

                // iOS或已获得权限，直接保存
                await this.saveQRCodeToAlbum();
                
            } catch (error) {
                uni.hideLoading();
                console.error('保存二维码失败:', error);
                uni.showToast({
                    title: '保存失败: ' + (error?.message || '未知错误'),
                    icon: 'none',
                    duration: 2000
                });
            }
        },
		async saveQRCodeToAlbum() {
			if (!this.modalGroup.qrcode) {
				uni.showToast({
					title: '二维码不存在',
					icon: 'none'
				})
				return
			}
			uni.showLoading({
				title: '保存中...'
			})
			try {
				// 1. 下载二维码图片
				const downloadRes = await uni.downloadFile({
					url: this.modalGroup.qrcode
				});

				if (downloadRes.statusCode !== 200) {
					console.error('下载二维码失败', downloadRes);
					throw new Error('下载二维码失败');
				}

				// 2. 保存图片到相册
				await uni.saveImageToPhotosAlbum({
					filePath: downloadRes.tempFilePath
				});

				uni.showToast({
					title: '已保存到相册',
					icon: 'success'
				});

			} catch (e) {
				// 3. 统一处理异常
				if (e && e.errMsg) {
					// 用户取消保存
					if (e.errMsg.includes('cancel')) {
						uni.showToast({
							title: '取消保存',
							icon: 'none'
						});
						// 用户取消不是一个需要上报的错误，所以我们在这里处理后直接返回
						return;
					}
				}
				// 对于其他错误，我们将其抛出，由上层调用者(handleSave)统一处理
				console.error('保存二维码过程中出错:', e);
				throw e;

			} finally {
				uni.hideLoading();
			}
		},
		async getGroupDetail(groupId) {
			try {
				const res = await appServerApi.getGroupDetail(groupId)
				console.log('群组详情:', res)
			} catch (error) {
				console.error('获取群组详情失败', error)
			}
		},

		checkCreatorHome() {
            // 跳转到当前群主的主页，而不是创建者
            const targetUserId = this.selectedGroup.currentOwner || this.selectedGroup.creator;
            uni.navigateTo({
                url: '/pages/contact/UserDetailPage?userId=' + targetUserId,
            })
        },

		getValidityPeriod(duration) {
            if (!duration) return '永久有效';
            switch (parseInt(duration, 10)) {
                case 30:
                    return '一个月';
                case 90:
                    return '三个月';
                case 180:
                    return '半年';
                case 365:
                    return '一年';
                default:
                    return '一个月';
            }
        },

		// 跳转群消息页面
        async handleGroupClick(group) {
			console.log('current group',group)
			// const currentGroupDetail = await appServerApi.getGroupDetail(group.gid);
            // console.log('通过接口获取到的群组信息是：', currentGroupDetail.data)
			
            if (group.joined == 0) {
                this.handleJoin(group)
                return
            }
            let conversation = new Conversation(
                ConversationType.Group,
                group.gid,
                0
            )
            store.setCurrentConversation(conversation)
            this.$go2ConversationPage()
        },
		async handleJoin(group) {
            try {
                // 获取群组详情
                const detailResponse = await appServerApi.getGroupDetail(group.gid);
                console.log('群组详情:', detailResponse.data);

                this.selectedGroup = {
                    ...group,
                    duration: detailResponse.data?.duration || 30  // 使用接口返回的 duration，如果没有则默认 30
                }
                
                // 使用本地WFC获取最新的群信息，确保群主信息是最新的
                const localGroupInfo = wfc.getGroupInfo(group.gid, true); // refresh设为true强制刷新
                const actualOwner = localGroupInfo.owner || group.creator; // 使用最新的群主信息，如果没有则回退到原始数据
                
                // 注意：这里显示的是当前群主，不是创建者
                this.selectedGroup.CreatorName = wfc.getUserDisplayName(actualOwner)
                // 获取当前群主的完整信息
                this.creatorInfo = wfc.getUserInfo(actualOwner, true)
                // 保存当前群主信息用于显示，但不修改原始creator字段
                this.selectedGroup.currentOwner = actualOwner
				console.log("点击群聊列表得到的selectedGroup数据",this.selectedGroup)
                this.$refs.popup.open('bottom')
            } catch (error) {
                console.error('获取群组详情失败:', error);
                uni.showToast({
                    title: '获取群组信息失败',
                    icon: 'none'
                });
            }
        },
		async joinGroup(group) {
            let userId = getItem('userId')
            console.log('准备加入群聊:', userId, group)

            try {
                // 获取群详情，判断是否需要审核
                const detailResponse = await appServerApi.getGroupDetail(group.gid);
                console.log('群详情audit状态:', detailResponse.data?.audit);

                const needAudit = detailResponse.data?.audit === 1; // 1表示需要审核，0表示不需要审核

                if (needAudit) {
                    // 需要审核，使用申请进群API
                    console.log('群开启审核，提交申请')
                    const result = await appServerApi.requestJoinGroup(group.gid, '申请加入群聊')

                    if (result && result.code === 200) {
                        console.log('申请提交成功')
                        uni.showToast({
                            icon: 'success',
                            title: '申请已提交，等待审核',
                            duration: 2000,
                        })
                        this.$refs.popup.close('bottom')
                    } else {
                        const errorMsg = result?.msg || '申请失败'
                        console.error('申请进群失败:', errorMsg)
                        uni.showToast({
                            title: errorMsg,
                            icon: 'none',
                            duration: 2000
                        })
                        // 如果是业务错误（如重复申请），也要关闭弹窗
                        this.$refs.popup.close('bottom')
                    }
                } else {
                    // 不需要审核，直接加入群聊
                    console.log('群未开启审核，直接加入')
                    this.directJoinGroup(group)
                }
            } catch (error) {
                console.error('加入群聊出错:', error)

                // 检查是否是业务错误（后端返回的错误信息）
                if (error && typeof error === 'string') {
                    // 如果error是字符串，说明是业务错误，直接显示错误信息
                    uni.showToast({
                        title: error,
                        icon: 'none',
                        duration: 2000
                    })
                    this.$refs.popup.close('bottom')
                    return
                }

                // 只有在真正的网络错误或接口异常时，才回退到申请加群的方式
                console.log('获取群详情失败，回退到申请加群方式')
                try {
                    const result = await appServerApi.requestJoinGroup(group.gid, '申请加入群聊')

                    if (result && result.code === 200) {
                        console.log('申请提交成功')
                        uni.showToast({
                            icon: 'success',
                            title: '申请已提交，等待审核',
                            duration: 2000,
                        })
                        this.$refs.popup.close('bottom')
                    } else {
                        const errorMsg = result?.msg || '申请失败'
                        console.error('申请进群失败:', errorMsg)
                        uni.showToast({
                            title: errorMsg,
                            icon: 'none',
                            duration: 2000
                        })
                        this.$refs.popup.close('bottom')
                    }
                } catch (fallbackError) {
                    console.error('申请加群也失败:', fallbackError)

                    // 检查是否是业务错误
                    if (fallbackError && typeof fallbackError === 'string') {
                        uni.showToast({
                            title: fallbackError,
                            icon: 'none',
                            duration: 2000
                        })
                    } else {
                        uni.showToast({
                            title: '网络错误，请稍后重试',
                            icon: 'none'
                        })
                    }
                    this.$refs.popup.close('bottom')
                }
            }
        },

		// 直接加入群聊的方法（作为备用方案）
        directJoinGroup(group) {
            let userId = getItem('userId')
            console.log('直接加入群聊:', userId, group)

            wfc.addGroupMembers(
                group.gid,
                [userId],
                null,
                [0],
                null,
                () => {
                    console.log('直接加入群聊成功')
                    uni.showToast({
                        icon: 'success',
                        title: '加入成功',
                        duration: 1000,
                    })
                    this.$refs.popup.close('bottom')

                    let conversation = new Conversation(ConversationType.Group, group.gid, 0)
                    let conversationInfo = new ConversationInfo()
                    conversationInfo.conversation = conversation
                    this.showConversation(conversationInfo)
                },
                (err) => {
                    uni.showModal({
                        title: '提示',
                        content: '加入失败，请联系管理员',
                        confirmText: '确定',
                        showCancel: false,
                        success: (res) => {
                            // 处理确认后的操作
                        }
                    })
                    console.log('直接加入群聊失败', err)
                }
            )
        },

		async handlePaymentConfirm({ method, amount, password, coinAmount }) {
            var that = this
            try {
                console.log('支付确认', method, amount, coinAmount)
                switch (method) {
                    case 'wechat':
                        const response = await appServerApi.joinPayGroup({
                            gid: that.selectedGroup.gid,
                            payType: 2,
                        })
                        util.wxpay(
                            response.data,
                            function (res) {
                                console.log('成功', res)
                                that.selectedGroup.joined = 1
                                that.paymentVisible = false
                                uni.showToast({
                                    icon: 'success',
                                    title: '加入成功',
                                    duration: 1000,
                                })
                                that.$refs.popup.close('bottom')
                            
                            },
                            function (res) {
                                console.log('失败', res)
                                uni.showToast({
                                    title: '支付失败',
                                    icon: 'none',
                                })
                            }
                        )
                        break
                    case 'alipay':
                        const response2 = await appServerApi.joinPayGroup({
                            gid: that.selectedGroup.gid,
                            payType: 1,
                        })
                        util.alipay(
                            response2.data,
                            function (res) {
                                console.log('成功', res)
                                uni.showToast({
                                    title: '支付成功',
                                    icon: 'none',
                                })
                                that.selectedGroup.joined = 1
                                that.paymentVisible = false
                                uni.showToast({
                                    icon: 'success',
                                    title: '加入成功',
                                    duration: 1000,
                                })
                                that.$refs.popup.close('bottom')
                                
                            },
                            function (res) {
                                console.log('失败', res)
                                uni.showToast({
                                    title: '支付失败',
                                    icon: 'none',
                                })
                            }
                        )
                        break
                    case 'balance':
                        // TODO: 处理密码校验
                        // password
                        uni.showLoading({
                            title: '正在校验密码',
                        })
                        const response3 = await appServerApi.joinPayGroup({
                            gid: that.selectedGroup.gid,
                            payType: 0,
                        })
                        uni.hideLoading()
                        uni.showToast({
                            icon: 'success',
                            title: '加入成功',
                            duration: 1000,
                        })
                        
                        break
                    case 'coin':
                        uni.showLoading({
                            title: '处理中',
                        })
                        const response4 = await appServerApi.joinPayGroup({
                            gid: that.selectedGroup.gid,
                            payType: 5,
                            coinAmount: coinAmount // 传递金币数量到接口
                        })
                        uni.hideLoading()
                        uni.showToast({
                            icon: 'success',
                            title: '加入成功',
                            duration: 1000,
                        })
                        that.selectedGroup.joined = 1
                        that.paymentVisible = false
                        that.$refs.popup.close('bottom')
                        
                        break
                    default:
                        break
                }
                // 处理支付逻辑
                // const result = await this.processPayment(method, amount)
                // if (result.success) {
                //     this.$message.success('支付成功')
                //     this.paymentVisible = false
                // }
            } catch (error) {
                this.$message.error('支付失败')
            }
		},

		async handleIosPaymentConfirm({ status }) {
            try {
                if (status === 'success') {
                    console.log('iOS支付确认成功')
                    appServerApi.getWallet().then(response => {
                        this.userBalance = response?.data?.balance || '0'
                        this.gold = response?.data?.gold || 0
                        this.iosPaymentVisible = false
                    })
                }
            } catch (error) {
                console.error('iOS支付确认失败:', error)
            }
        },
	}
}
</script>

<style lang="scss" scoped>
.groups-container {
	min-height: 100vh;
	background: #fff;
	display: flex;
	flex-direction: column;
}

.groups-header {
	height: 80px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-bottom: 1px solid #f0f0f0;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding-top: 40px;
	box-sizing: border-box;
	z-index: 3100;
}

.groups-back {
	position: absolute;
	left: 16px;
	top: 40px;
	height: 36px;
	display: flex;
	align-items: center;
}

.groups-title {
	font-size: 18px;
	color: #222;
}

.groups-content {
	margin-top: 80px;
	padding: 0px;
}

.group-list {
	display: flex;
	flex-direction: column;
	/* gap: 16px; */
}

.group-item {
	display: flex;
	align-items: center;
	background: #fff;
	border-radius: 12px;
	padding: 12px 12px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.03);
	margin-bottom: 10px;
}

.grouplist-avatar {
	width: 48px;
	height: 48px;
	border-radius: 8px;
	margin-right: 12px;
	background: #f0f0f0;
	object-fit: cover;
}

.group-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.group-name {
	font-size: 16px;
	font-weight: 500;
	color: #222;
}

.group-desc {
	font-size: 12px;
	color: #888;
}

.group-btn {
	background: #3478f6;
	color: #fff;
	font-size: 14px;
	border-radius: 16px;
	padding: 0 16px;
	height: 32px;
	line-height: 32px;
	border: none;
	outline: none;
}

.divider {
	height: 1px;
	background: #ececec;
	margin: 0px 0 8px 0;
}

.third-title {
	font-size: 14px;
	color: #888;
	margin-top: 8px;
	// margin-bottom: 8px;
	margin-left: 12px;
}

/* 遮罩和弹窗样式 */
.modal-mask {
	position: fixed;
	z-index: 4000;
	left: 0; top: 0; right: 0; bottom: 0;
	background: rgba(0,0,0,0.35);
	display: flex;
	align-items: flex-end;
	justify-content: center;
}
.group-modal {
	width: 100vw;
	max-width: 500px;
	background: #fff;
	border-radius: 18px 18px 0 0;
	box-shadow: 0 -2px 16px rgba(0,0,0,0.08);
	padding-bottom: 0;
	position: relative;
	height: 50vh;
	display: flex;
	flex-direction: column;
	animation: modalUp 0.2s;
}
.top-line {
	width: 38px;
	height: 4px;
	margin: 15px auto 0px auto;
	border-radius: 10px;
	background: #e7e7e7;
}
@keyframes modalUp {
	0% { transform: translateY(100%); }
	100% { transform: translateY(0); }
}
.modal-close {
	position: absolute;
	top: 0;
	right: 0;
	width: 42px;
	height: 42px;
	background: #ff4d4f;
	border-radius: 0 0 0 42px;
	z-index: 12;
	cursor: pointer;
	display: flex;
}
.modal-close::before {
	content: '✕';
	color: white;
	font-size: 16px;
	font-weight: bold;
	transform: translate(12px, 8px);
}
.modal-group-info {
	display: flex;
	align-items: center;
	padding: 32px 24px 0 24px;
}
.modal-avatar {
	width: 56px;
	height: 56px;
	border-radius: 10px;
	margin-right: 16px;
	background: #f0f0f0;
}
.modal-meta {
	display: flex;
	flex-direction: column;
	gap: 4px;
	max-width: 220px;
}
.modal-title {
	font-size: 18px;
	font-weight: 600;
	color: #222;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 220px;
}
.modal-desc {
	font-size: 13px;
	color: #888;
	word-break: break-all;
	max-width: 220px;
}
.modal-qrcode-area {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	flex: 1;
}
.modal-qrcode {
	width: 180px;
	height: 180px;
	margin: 0 auto 8px auto;
	background: #f6f6f6;
	border-radius: 8px;
	object-fit: contain;
}
.modal-qrcode-placeholder {
	width: 180px;
	height: 180px;
	margin: 0 auto 8px auto;
	background: #f6f6f6;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #bbb;
	font-size: 15px;
}
.modal-btns {
	display: flex;
	width: 100%;
	gap: 12px;
	padding: 0 24px 18px 24px;
	margin-top: 0;
}
.modal-btn {
	flex: 1;
	height: 45px;
	font-size: 16px;
	border-radius: 18px;
	align-items: center;
	justify-content: center;
	display: flex;
	background: #f5f6fa;
	color: #3478f6;
	border: none;
	outline: none;
}
.modal-btn.save {
	background: #3478f6;
	color: #fff;
}

.popup-content {
    display: flex;
    flex-direction: column;
    padding: 23px;
    width: 100%;
    height: auto;
    align-items: center;

    .top-line {
        width: 38px;
        height: 4px;
        margin: 0 auto 17px auto;
        border-radius: 10px;
        background: #e7e7e7;
    }

    .pop-title {
        padding-top: 18px;
    }
	
    .pop-desc {
        border-top: 1px solid #efefef;
        padding: 18px 0;
        margin-top: 20px;
        margin-bottom: 20px;
        width: 100%;

        .desc-line {
            display: flex;
            position: relative;
            padding-bottom: 20px;
            align-items: center;

            &:last-child {
                padding-bottom: 0;
            }

            .desc-label {
                height: 15px;
                font-size: 11px;
                color: #000000;
                line-height: 15px;
                text-align: left;
                padding: 2px 0;
                margin-right: 10px;
                width: 44px;
                flex-shrink: 0;
            }

            .desc-content {
                font-size: 14px;
                color: #999999;
                display: flex;
                align-items: center;
                padding-left: 0;
                flex: 1;
            }

            .desc-name {
                color: #000000;
            }

            .header-right {
                position: absolute;
                right: 0;
                font-size: 14px;
                color: #999999;
                line-height: 16px;

                i {
                    margin-left: 10px;
                }
            }

            .avatar-small {
                width: 20px;
                height: 20px;
                margin-right: 10px;
                border-radius: 4px;
            }
        }
    }

    .op-block {
        margin-bottom: 20px;
        width: 100%;

        .price-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0;
            margin-bottom: 8px;
            
            .validity {
                font-size: 8px;
                color: #666666;
                font-weight: 400;
            }
            
            .daily-price {
                font-size: 8px;
                color: #386bf6;
                font-weight: 500;
            }
        }
        
        button[type="primary"] {
            width: 100%;
            height: 46px;
            background: #386bf6;
            border-radius: 4px;
            color: #fff;
            font-size: 16px;
            border: none;
            margin-top: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            line-height: 1;
            padding: 0;
            
            &:active {
                opacity: 0.9;
            }
        }
    }
}
.tag {
    // height: 16px;
    background: #0cc86b;
    border-radius: 2px 2px 2px 2px;
    // line-height: 16px;
    color: #fff;
    font-size: 10px;
    margin-left: 5px;
    padding: 2px;
}
.group-avatar {
    width: 60px;
    height: 60px;
    background: #f9fafc;
    box-shadow: inset 0px 0px 4px 0px rgba(23, 104, 139, 0.12);
    border-radius: 6px;
    flex-shrink: 0;
    margin-right: 2px;
    justify-content: space-between;
    position: relative;
    overflow: hidden;

    .price-badge {
        position: absolute;
        top: -12px;
        left: -30px;
        width: 80px;
        height: 24px;
        background-color: #FF2222;
        transform: rotate(-45deg);
        transform-origin: center;
        z-index: 1;
        
        .badge-text {
            position: absolute;
            width: 100%;
            text-align: center;
            color: #FFFFFF;
            font-size: 10px;
            bottom: 1px;
            left: -7px;     /* 文字向左移动 */
            transform: translateY(2px);  /* 文字向下移动 */
        }
    }
}
.group-avatar-img {
	width: 60px;
	height: 60px;
	border-radius: 6px;
}

.empty-groups {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 300px;
	// margin-top: 80px;
}
.empty-img {
	width: 120px;
	height: 120px;
	margin-bottom: 18px;
	opacity: 0.7;
}
.empty-text {
	font-size: 16px;
	color: #888;
	text-align: center;
}
</style>
