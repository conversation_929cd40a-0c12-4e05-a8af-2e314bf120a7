<template>
  <view class="edit-container">
    <!-- 顶部导航栏 -->
    <view class="preview-header">
      <view class="preview-back" @click="goBack">
        <uni-icons type="back" size="24" color="#222" />
      </view>
      <text class="preview-title">{{ title }}</text>
    </view>

    <!-- 编辑区域 -->
    <scroll-view 
      scroll-y 
      class="editor-container"
      :style="{
        height: editorContainerHeight + 'px',
        marginTop: headerHeight + 'px',
        marginBottom: toolbarHeight + 'px'
      }"
      :scroll-top="scrollTop"
      :show-scrollbar="true"
      :enable-back-to-top="true"
      :scroll-anchoring="true"
      @scroll="onScroll"
    >
      <view class="editor-content" @tap="focusLastTextBlock">
        <!-- 动态内容列表 -->
        <view 
          v-for="(block, index) in blocks" 
          :key="block.id"
          class="content-block"
        >
          <!-- 文本块 -->
          <view 
            v-if="block.type === 'text'"
            class="text-block"
            :class="{'focused': currentFocus === index}"
          >
            <textarea
              :value="block.content"
              class="text-input"
              :id="'block-' + index"
              maxlength="1000"
              placeholder="输入文本..."
              auto-height
              :adjust-position="false"
              :focus="currentFocus === index"
              @input="(e) => handleTextInput(e, index)"
              @focus="() => handleFocus(index)"
              @blur="() => handleBlur(index)"
              @tap.stop
            />
          </view>

          <!-- 图片块 -->
          <view 
            v-else-if="block.type === 'image'"
            class="media-block"
          >
            <image 
              :src="block.content"
              mode="widthFix"
              class="media-content"
              @tap.stop="previewImage(block.content)"
            />
            <view class="delete-btn" @tap.stop="deleteBlock(index)">×</view>
          </view>

          <!-- 视频块 -->
          <view 
            v-else-if="block.type === 'video'"
            class="media-block"
          >
            <nVideo
              :src="block.content"
              :poster="block.content + '?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'"
              :object-fit="'contain'"
            />
            <view class="delete-btn" @tap.stop="deleteBlock(index)">×</view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部工具栏 -->
    <view 
      class="toolbar" 
      :style="{ bottom: isKeyboardVisible ? keyboardHeight + 'px' : '0' }"
    >
      <view class="tool-item" @tap="insertImage">
        <text class="iconfont">🖼️</text>
      </view>
      <view class="tool-item" @tap="insertVideo">
        <text class="iconfont">🎥</text>
      </view>
      <view class="save-button" @tap="saveNote">
        保存并返回
      </view>
    </view>
  </view>
</template>

<script>
import { ref, reactive } from 'vue'
import appServerApi from '@/api/appServerApi'
import nVideo from '@/pages/bar/nVideo.vue'
import CustomHeader from '../../components/custom-header'

export default {
  components: {
    nVideo,
    CustomHeader
  },

  data() {
    return {
      title: '',
      type: '',
      blocks: [{
        id: 1,
        type: 'text',
        content: ''
      }],
      currentFocus: -1,
      scrollTop: 0,
      nextId: 2,
      keyboardHeight: 0,
      isKeyboardVisible: false,
      windowHeight: 0,
      headerHeight: 80,
      toolbarHeight: 50,
      contentPadding: 16
    }
  },

  computed: {
    editorContainerHeight() {
      const systemInfo = uni.getSystemInfoSync();
      const windowHeight = systemInfo.windowHeight;
      const keyboardOffset = this.isKeyboardVisible ? this.keyboardHeight : 0;
      return windowHeight - this.headerHeight - this.toolbarHeight - keyboardOffset;
    }
  },

  onLoad(options) {
    this.title = decodeURIComponent(options.title || '');
    this.type = options.type || '';
    const initialValue = decodeURIComponent(options.value || '');

    if (initialValue) {
      this.blocks = [];
      this.nextId = 1; // Reset nextId for parsing

      const lines = initialValue.split('\\n'); // Split by literal newline characters
      lines.forEach(line => {
        const trimmedLine = line.trim();
        if (trimmedLine) {
          // 处理本地文件路径
          if (trimmedLine.startsWith('file:///')) {
            // 检查是否是图片
            if (trimmedLine.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
              this.blocks.push({
                id: this.generateId(),
                type: 'image',
                content: trimmedLine
              });
            }
            // 检查是否是视频
            else if (trimmedLine.match(/\.(mp4|mov|avi)$/i) || trimmedLine.includes('compress_video')) {
              this.blocks.push({
                id: this.generateId(),
                type: 'video',
                content: trimmedLine
              });
            }
          }
          // 处理OSS链接
          else if (trimmedLine.includes('oss-cn-hangzhou.aliyuncs.com')) {
            // 检查是否是图片
            if (trimmedLine.match(/\.(jpg|jpeg|png|gif|webp)($|\?)/i) || 
                !trimmedLine.includes('.mp4')) {
              this.blocks.push({
                id: this.generateId(),
                type: 'image',
                content: trimmedLine
              });
            }
            // 检查是否是视频
            else if (trimmedLine.match(/\.(mp4|mov|avi)($|\?)/i) || 
                     trimmedLine.includes('compress_video')) {
              this.blocks.push({
                id: this.generateId(),
                type: 'video',
                content: trimmedLine
              });
            }
          }
          // 处理文本内容
          else {
            this.blocks.push({
              id: this.generateId(),
              type: 'text',
              content: trimmedLine
            });
          }
        }
      });
      // 确保最后有一个空的文本块用于输入
      if (this.blocks.length === 0 || 
          (this.blocks[this.blocks.length - 1].type !== 'text' && 
           this.blocks[this.blocks.length - 1].content.trim() !== '')) {
        this.blocks.push({
          id: this.generateId(),
          type: 'text',
          content: ''
        });
      }
    }

    // 获取系统信息
    const systemInfo = uni.getSystemInfoSync();
    this.windowHeight = systemInfo.windowHeight;
    
    // 监听键盘高度变化
    uni.onKeyboardHeightChange(res => {
      this.isKeyboardVisible = res.height > 0;
      this.keyboardHeight = res.height;
      // console.log("键盘高度是：",this.keyboardHeight)
      
      // 键盘弹出时，确保当前输入框可见
      if (this.isKeyboardVisible && this.currentFocus >= 0) {
        this.$nextTick(() => {
          const query = uni.createSelectorQuery().in(this);
          query.select(`#block-${this.currentFocus}`).boundingClientRect(rect => {
            if (rect) {
              const viewHeight = this.windowHeight;
              const keyboardTop = viewHeight - this.keyboardHeight - this.toolbarHeight;
              
              if (rect.bottom > keyboardTop) {
                // 计算需要滚动的距离，确保输入框在键盘上方
                const scrollDistance = rect.bottom - keyboardTop + 20;
                
                // 使用平滑滚动
                uni.pageScrollTo({
                  scrollTop: this.scrollTop + scrollDistance,
                  duration: 300
                });
                
                // 更新 scrollTop
                this.scrollTop += scrollDistance;
              }
            }
          }).exec();
        });
      }
    });
  },

  // 在页面卸载时移除键盘监听，避免内存泄漏
  onUnload() {
    uni.offKeyboardHeightChange();
  },

  methods: {
    // 生成唯一ID
    generateId() {
      return this.nextId++
    },

    // 处理文本输入
    handleTextInput(e, index) {
      this.blocks[index].content = e.detail.value
      
      // 如果当前块内容为空且不是唯一的文本块，则删除
      if (!e.detail.value && this.blocks.length > 1) {
        const textBlocks = this.blocks.filter(b => b.type === 'text')
        if (textBlocks.length > 1) {
          this.blocks.splice(index, 1)
        }
      }
    },

    // 处理焦点
    handleFocus(index) {
      this.currentFocus = index;
      
      // 延迟执行以确保键盘已经完全弹出
      setTimeout(() => {
        const query = uni.createSelectorQuery().in(this);
        query.select(`#block-${index}`).boundingClientRect(rect => {
          if (rect) {
            const viewHeight = this.windowHeight;
            const keyboardTop = viewHeight - this.keyboardHeight - this.toolbarHeight;
            
            if (rect.bottom > keyboardTop) {
              // 计算需要滚动的距离，确保输入框在键盘上方
              const scrollDistance = rect.bottom - keyboardTop + 20;
              
              // 使用平滑滚动
              uni.pageScrollTo({
                scrollTop: this.scrollTop + scrollDistance,
                duration: 300
              });
              
              // 更新 scrollTop
              this.scrollTop += scrollDistance;
            }
          }
        }).exec();
      }, 100);
    },

    handleBlur(index) {
      this.currentFocus = -1;
    },

    // 插入图片
    async insertImage() {
      try {
        const res = await uni.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: ['album', 'camera']
        })
        
        if (res.tempFilePaths && res.tempFilePaths[0]) {
          // 在当前焦点位置后插入图片块
          const insertIndex = this.currentFocus >= 0 ? this.currentFocus + 1 : this.blocks.length
          this.blocks.splice(insertIndex, 0, {
            id: this.generateId(),
            type: 'image',
            content: res.tempFilePaths[0]
          })
          
          // 在图片后添加文本块
          this.blocks.splice(insertIndex + 1, 0, {
            id: this.generateId(),
            type: 'text',
            content: ''
          })
        }
      } catch (error) {
        uni.showToast({
          title: '选择图片失败',
          icon: 'none'
        })
      }
    },

    // 插入视频
    async insertVideo() {
      try {
        const res = await uni.chooseVideo({
          sourceType: ['album', 'camera'],
          compressed: true,
          maxDuration: 60
        })
        
        if (res.tempFilePath) {
          const insertIndex = this.currentFocus >= 0 ? this.currentFocus + 1 : this.blocks.length
          this.blocks.splice(insertIndex, 0, {
            id: this.generateId(),
            type: 'video',
            content: res.tempFilePath
          })
          
          this.blocks.splice(insertIndex + 1, 0, {
            id: this.generateId(),
            type: 'text',
            content: ''
          })
        }
      } catch (error) {
        uni.showToast({
          title: '选择视频失败',
          icon: 'none'
        })
      }
    },

    // 删除内容块
    deleteBlock(index) {
      this.blocks.splice(index, 1)
      
      // 确保至少有一个文本块
      if (!this.blocks.some(b => b.type === 'text')) {
        this.blocks.push({
          id: this.generateId(),
          type: 'text',
          content: ''
        })
      }
    },

    // 预览图片
    previewImage(url) {
      const urls = this.blocks
        .filter(b => b.type === 'image')
        .map(b => b.content)
      uni.previewImage({
        urls,
        current: url
      })
    },

    // 保存便签
    saveNote() {
      // 获取所有非空内容块
      const content = this.blocks.filter(b => b.content.trim() !== '')
      
      // 将所有内容块合并为一个字符串，媒体内容直接使用URL并以换行符分隔
      let textContent = content.map(block => {
        return block.content; // text, image (URL), video (URL)
      }).join('\\n'); // Use \\n to represent newline in the string for EditCard.vue

      // 将内容保存到本地存储
      uni.setStorageSync('editFieldResult', {
        type: this.type, // Use the dynamically set type
        value: textContent
      })
      console.log("合并之后的内容是：",textContent)
      // 返回上一页
      uni.navigateBack()
    },

    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 处理滚动
    onScroll(e) {
      this.scrollTop = e.detail.scrollTop;
    },

    // 点击空白区域聚焦到最后一个文本块
    focusLastTextBlock() {
      // 找到最后一个文本块的索引
      let lastTextIndex = -1;
      for (let i = this.blocks.length - 1; i >= 0; i--) {
        if (this.blocks[i].type === 'text') {
          lastTextIndex = i;
          break;
        }
      }
      // 如果已经聚焦最后一个文本块，则不再重复聚焦
      if (lastTextIndex !== -1 && this.currentFocus !== lastTextIndex) {
        this.currentFocus = lastTextIndex;
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.edit-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #ffffff;
  position: relative;
}

.preview-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  padding-top: 40px;
  box-sizing: border-box;
  border-bottom: 1px solid #f0f0f0;
  z-index: 100;
}

.preview-back {
  position: absolute;
  left: 16px;
  top: 40px;
  height: 36px;
  display: flex;
  align-items: center;
}

.preview-title {
  font-size: 18px;
  color: #222;
}

.editor-container {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 10;
//   background: #fd0202;
  overflow: hidden;
}

.editor-content {
  padding: 16px;
  min-height: 100%;
  box-sizing: border-box;
  position: relative;
  // background: #51fd02;
}

.toolbar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 50px;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 100;
  transition: bottom 0.3s ease-out;
}

.content-block {
  margin-bottom: 12px;
}

.text-block {
  position: relative;
  margin-bottom: 8px;
  
  &.focused {
    // background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    padding: 8px;
    margin: -8px;
    margin-bottom: 0;
  }
}

.text-input {
  width: 100%;
  min-height: 24px;
  font-size: 16px;
  line-height: 1.5;
  padding: 0;
  background: transparent;
  
  &::placeholder {
    color: #999;
  }
}

.media-block {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f8f8f8;
  margin-bottom: 12px;
  
  .media-content {
    width: 100%;
    display: block;
  }
}

.delete-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 12px;
  color: #fff;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  transform: translate(50%, -50%);
}

.video-delete-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.delete-icon {
  color: #fff; 
  font-size: 20px;
  line-height: 24px;
}

.tool-item {
  padding: 8px 12px;
  
  .iconfont {
    font-size: 24px;
    color: #666;
  }
}

.save-button {
  background-color: #256DFF; /* Blue color as per image */
  color: #fff;
  padding: 8px 20px; /* Adjust padding for button size */
  border-radius: 20px; /* Rounded corners */
  font-size: 16px;
  font-weight: bold;
  margin-right: 16px; /* Right margin as requested */
}

// 添加nVideo组件的样式
:deep(.nVideo) {
  width: 100%;
  height: 240px;
  border-radius: 8px;
  overflow: hidden;
}
</style> 