<template>
	<view class="more-social-container">
		<!-- 顶部栏 -->
		<view class="preview-header">
			<view class="preview-back" @click="goBack">
				<uni-icons type="back" size="24" color="#222" />
			</view>
			<text class="preview-title">其他社交媒体</text>
			<view class="preview-more" @click="saveSocialLinks">
				<text class="add-btn-text">保存</text>
			</view>
		</view>
		<!-- 内容区 -->
		<view class="social-list">
			<view class="social-row">
				<image class="icon-placeholder" src="/static/image/icon/douyin.png"></image>
				<text class="social-label">抖音</text>
				<input
					v-model="socialLinks.douyin"
					placeholder="主页链接:"
					class="social-input"
					maxlength="100"
				/>
			</view>
			<view class="social-row">
				<image class="icon-placeholder" src="/static/image/icon/xiaohongshu.png"></image>
				<text class="social-label">小红书</text>
				<input
					v-model="socialLinks.xiaohongshu"
					placeholder="主页链接:"
					class="social-input"
					maxlength="100"
				/>
			</view>
			<view class="social-row">
				<image class="icon-placeholder" src="/static/image/icon/kuaishou.png"></image>
				<text class="social-label">快手</text>
				<input
					v-model="socialLinks.kuaishou"
					placeholder="主页链接:"
					class="social-input"
					maxlength="100"
				/>
			</view>
			<view class="social-row">
				<!-- <uni-icons  type="weibo" size="16"></uni-icons> -->
				<image class="icon-placeholder" src="/static/image/icon/weibo.png"></image>
				<text class="social-label">微博</text>
				<input
					v-model="socialLinks.weibo"
					placeholder="主页链接:"
					class="social-input"
					maxlength="100"
				/>
			</view>
			<view class="social-row">
				<image class="icon-placeholder" src="/static/image/icon/toutiao.png"></image>
				<text class="social-label">今日头条</text>
				<input
					v-model="socialLinks.toutiao"
					placeholder="主页链接:"
					class="social-input"
					maxlength="100"
				/>
			</view>
			<view class="social-row">
				<image class="icon-placeholder" src="/static/image/icon/gongzhonghao.png"></image>
				<text class="social-label">公众号</text>
				<input
					v-model="socialLinks.gongzhonghao"
					placeholder="主页链接:"
					class="social-input"
					maxlength="100"
				/>
			</view>
		</view>
	</view>
</template>

<script>
export default {
  data() {
    return {
		// socialMediaList: [
		// 	{ key: 'douyin', label: '抖音' },
		// 	{ key: 'xiaohongshu', label: '小红书' },
		// 	{ key: 'kuaishou', label: '快手' },
		// 	{ key: 'weibo', label: '微博' },
		// 	{ key: 'toutiao', label: '今日头条' },
		// 	{ key: 'gongzhonghao', label: '公众号' },
		// ],
		socialLinks: {
			douyin: '',
			xiaohongshu: '',
			kuaishou: '',
			weibo: '',
			toutiao: '',
			gongzhonghao: '',
		},
		}
  },
	onLoad(options) {
		// 监听原生导航栏按钮点击
		// uni.$on('navigationBarButtonTap', this.onNavigationBarButtonTap);
		// 如果有传递已填写内容，初始化socialLinks
		if (options.socialLinks) {
		try {
			const links = JSON.parse(decodeURIComponent(options.socialLinks));
			this.socialLinks = { ...this.socialLinks, ...links };
		} catch (e) {}
		}
	},
	//   onShow() {
	//     uni.$on('navigationBarButtonTap', this.onNavigationBarButtonTap);
	//     console.log('platform:', uni.getSystemInfoSync().platform)
	//   },
	//   onHide() {
	//     uni.$off('navigationBarButtonTap', this.onNavigationBarButtonTap);
	//   },
	//   onUnload() {
	//     uni.$off('navigationBarButtonTap', this.onNavigationBarButtonTap);
	//   },
	methods: {
		goBack() {
			uni.navigateBack();
		},
		saveSocialLinks() {
		// 保存到本地，或通过事件/页面返回参数传递
			console.log("点击了保存按钮,2触发")
			uni.setStorageSync('editMoreSocialLinks', this.socialLinks);
			console.log('editMoreSocialLinks', this.socialLinks)
			uni.navigateBack();
		},
	},
}
</script>

<style scoped>
.preview-header {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding-top: 40px;
  box-sizing: border-box;
  z-index: 3100;
}
.preview-back {
  position: absolute;
  left: 16px;
  top: 40px;
  height: 36px;
  display: flex;
  align-items: center;
}
.preview-title {
  font-size: 18px;
  color: #222;
}
.preview-more {
  position: absolute;
  right: 16px;
  top: 40px;
  height: 36px;
  display: flex;
  align-items: center;
}
.add-btn-text {
  font-size: 15px;
  color: #4E7CF7;
}
.more-social-container {
  min-height: 100vh;
  background: #fff;
}
.header {
  position: relative;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}
.header-left {
  position: absolute;
  left: 12px;
  top: 0;
  height: 56px;
  display: flex;
  align-items: center;
}
.header-title {
  font-size: 20px;
  font-weight: 500;
  color: #222;
}
.header-right {
  position: absolute;
  right: 16px;
  top: 0;
  height: 56px;
  display: flex;
  align-items: center;
}
.save-btn {
  font-size: 16px;
  color: #256DFF;
}
.social-list {
  /* padding-top: 80px; */
  padding: 90px 18px 0px 18px;
}
.social-row {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
}
.icon-placeholder {
  width: 30px;
  height: 30px;
  background: transparent;
  border-radius: 4px;
  margin-right: 10px;
}
.social-label {
  width: 64px;
  color: #222;
  font-size: 14px;
}
.social-input {
  flex: 1;
  height: 32px;
  border: none;
  background: #ededed;
  font-size: 14px;
  padding: 0 8px;
  border-radius: 4px;
  margin-left: 8px;
  box-sizing: border-box;
}
</style>
