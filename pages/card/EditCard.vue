<template>
    <view class="edit-card-container">
        <!-- 顶部栏 -->
        <view class="preview-header">
            <view class="preview-back" @click="goBack">
                <uni-icons type="back" size="24" color="#222" />
            </view>
            <text class="preview-title">编辑名片</text>
            <!-- <view class="preview-more" @click="saveSocialLinks">
				<text class="add-btn-text">保存</text>
			</view> -->
        </view>

        <!-- 名片信息编辑栏 -->
        <scroll-view class="edit-content" scroll-y>
            <!-- 基础资料 -->
            <view class="section-title-row">
                <view class="section-bar"></view>
                <text class="section-title">基础资料</text>
            </view>

            <view class="cardInfo-row">
                <text class="cardInfo-label">头像</text>
                <view class="cardInfo-value-avatar">
                    <image :src="userInfo.avatar || defaultAvatar" class="avatar-img" />
                </view>
                <!-- <i class="icon-ion-ios-arrow-right"></i> -->
            </view>

            <view class="cardInfo-row">
                <text class="cardInfo-label">昵称</text>
                <text class="cardInfo-value">{{ userInfo.displayName || '请填写昵称' }}</text>
                <!-- <i class="icon-ion-ios-arrow-right"></i> -->
            </view>
            <view class="cardInfo-row">
                <text class="cardInfo-label">性别</text>
                <text class="cardInfo-value">{{ userInfo.gender ? genderOptions[userInfo.gender] : '请填写性别' }}</text>
                <!-- <i class="icon-ion-ios-arrow-right"></i> -->
            </view>

            <view class="section-title-row">
                <view class="section-bar"></view>
                <text class="section-title">一句话介绍自己*</text>
            </view>
            <!-- 新增：一句话介绍自己输入框 -->
            <view class="intro-textarea-wrap">
                <textarea v-model="cardInfo.introduction" maxlength="50" placeholder="好的介绍，让更多的人认识到你~"
                    class="intro-textarea"></textarea>
                <view class="intro-textarea-count">{{ (cardInfo.introduction || '').length }}/50</view>
            </view>

            <!-- 名片信息 -->
            <view class="section-title-row">
                <view class="section-bar"></view>
                <text class="section-title">名片信息</text>
            </view>
            <view class="cardInfo-row" @click="showCardNameEdit">
                <text class="cardInfo-label">姓名*</text>
                <text class="cardInfo-value">{{ cardInfo.name || '请填写姓名' }}</text>
                <i class="icon-ion-ios-arrow-right"></i>
            </view>
            <view class="cardInfo-row" @click="showCardCityEdit">
                <text class="cardInfo-label">城市</text>
                <text class="cardInfo-value">{{ cardInfo.city ? cardInfo.city : '请选择所在城市' }}</text>
                <i class="icon-ion-ios-arrow-right"></i>
            </view>
            <view class="cardInfo-row" @click="showCardCompanyEdit">
                <text class="cardInfo-label">公司与职位</text>
                <text class="cardInfo-value">{{ cardInfo.company || '请填写公司与职位' }}</text>
                <i class="icon-ion-ios-arrow-right"></i>
            </view>
            <view class="cardInfo-row" @click="showCardRolePicker">
                <text class="cardInfo-label">角色</text>
                <text class="cardInfo-value" :class="{ 'required': !cardInfo.roleName }">{{ cardInfo.roleName ?
                    cardInfo.roleName
                    : '请选择角色(必选)' }}</text>
                <i class="icon-ion-ios-arrow-right"></i>
            </view>
            <view class="cardInfo-row" @click="gotoCardIntroEdit">
                <text class="cardInfo-label">个人简介</text>
                <!-- <editor
                    id="editor"
                    class="cardInfo-value"
                    :readOnly="true"
                    @ready="onEditorReady"
                    
                ></editor> -->
                <text class="cardInfo-value">{{ descriptionShort || '请填写个人简介' }}</text>
                <i class="icon-ion-ios-arrow-right"></i>
            </view>
            <view class="cardInfo-row" @click="gotoCardResourceEdit">
                <text class="cardInfo-label">我的资源</text>
                <text class="cardInfo-value">{{ advantageShort || '请填写我的资源' }}</text>
                <i class="icon-ion-ios-arrow-right"></i>
            </view>
            <view class="cardInfo-row" @click="gotoCardNeedEdit">
                <text class="cardInfo-label">我的需求</text>
                <text class="cardInfo-value">{{ defectShort || '请填写我的需求' }}</text>
                <i class="icon-ion-ios-arrow-right"></i>
            </view>
            <view class="cardInfo-row" @click="showCardSalaryEdit">
                <text class="cardInfo-label">年收入</text>
                <text class="cardInfo-value">{{ cardInfo.income ? cardInfo.income : '请选择收入范围' }}</text>
                <i class="icon-ion-ios-arrow-right"></i>
            </view>

            <!-- 其他社交媒体 -->
            <view class="section-title-row">
                <view class="section-bar"></view>
                <text class="section-title">其他社交媒体</text>
                <text class="section-right" @click="gotoMoreSocial">更多</text>
            </view>
            <!-- 新增：社交媒体输入框 -->
            <view class="social-media-list">
                <view class="social-media-row">
                    <image class="social-media-icon" src="/static/image/icon/douyin.png" mode="widthFix"></image>
                    <text class="social-media-label">抖音</text>
                    <input v-model="cardInfo.platformIndex.douyin" placeholder="主页链接:" class="social-media-input"
                        maxlength="100" />
                </view>
                <view class="social-media-row">
                    <image class="social-media-icon" src="/static/image/icon/xiaohongshu.png" mode="widthFix"></image>
                    <text class="social-media-label">小红书</text>
                    <input v-model="cardInfo.platformIndex.xiaohongshu" placeholder="主页链接:" class="social-media-input"
                        maxlength="100" />
                </view>
                <view class="social-media-row">
                    <image class="social-media-icon" src="/static/image/icon/kuaishou.png" mode="widthFix"></image>
                    <text class="social-media-label">快手</text>
                    <input v-model="cardInfo.platformIndex.kuaishou" placeholder="主页链接:" class="social-media-input"
                        maxlength="100" />
                </view>
            </view>


            <!-- 社群 -->
            <view class="section-title-row">
                <view class="section-bar"></view>
                <text class="section-title">社群</text>
            </view>

            <scroll-view class="showcase-scroll" scroll-x>
                <view class="group-block" v-for="(item, index) in cardInfo.groups" :key="item.gid">
                    <image class="group-avatar" :src="item.avatar"></image>
                    <view class="group-delete" @click="deleteGroupShowcaseImage(index)">
                        <image src="/static/image/icon/delete.png"></image>
                    </view>
                    <view class="group-block-name">{{ item.name }}</view>
                </view>
                <view v-if="cardInfo.groups.length < 9" class="group-block add-block" @tap="showGroupShowcasePicker">
                    <i class="icon-ion-android-add"
                        style="font-size: 40px;color: #666666;justify-content: center;align-items: center"></i>
                </view>
            </scroll-view>
            <!-- 风采展示 -->
            <view class="section-title-row">
                <view class="section-bar"></view>
                <text class="section-title">风采展示</text>
            </view>

            <scroll-view class="showcase-scroll" scroll-x>
                <!-- 视频预览 -->
                <view v-if="video" class="file-block video-block">
                    <video :src="video" class="preview-video" controls></video>
                    <view class="delete" @click="deleteVideo">
                        <image src="/static/image/icon/delete.png"></image>
                    </view>
                </view>
                <!-- 图片预览 -->
                <template v-else>
                    <view class="file-block" v-for="(item, index) in cardInfo.url" :key="index">
                        <image class="avatar" :src="item"></image>
                        <view class="delete" @click="deleteImage(index)">
                            <image src="/static/image/icon/delete.png"></image>
                        </view>
                    </view>
                    <!-- 添加按钮 -->
                    <view v-if="cardInfo.url.length < 9" class="file-block add-block" @tap="showMediaPicker">
                        <i class="icon-ion-android-add"
                            style="font-size: 40px;color: #666666;justify-content: center;align-items: center"></i>
                    </view>
                </template>
            </scroll-view>

            <!-- 对外联系方式 -->
            <view class="section-title-row">
                <view class="section-bar"></view>
                <text class="section-title">对外联系方式</text>
                <text class="section-right">仅会员可见,手机号仅官方可见</text>
            </view>
            <view class="cardInfo-row" @click="showCardPhoneEdit">
                <uni-icons type="phone" size="24" color="#4E7CF7"></uni-icons>
                <text class="cardInfo-label">手机号</text>
                <text class="cardInfo-value">{{ cardInfo.phone || '请填写手机号' }}</text>
                <i class="icon-ion-ios-arrow-right"></i>
            </view>
            <view class="cardInfo-row" @click="showCardWechatEdit">
                <uni-icons type="weixin" size="24" color="#07C160"></uni-icons>
                <text class="cardInfo-label">微信号</text>
                <text class="cardInfo-value">{{ cardInfo.wechatId || '请填写微信号' }}</text>
                <i class="icon-ion-ios-arrow-right"></i>
            </view>

            <!-- 发布渠道 -->
            <!-- <view class="section-title-row">
                <view class="section-bar"></view>
                <text class="section-title">发布渠道</text>
            </view>
            <view class="cardInfo-row">
                <text class="cardInfo-value">{{ cardInfo.publishChannel === 'all' ? '全平台' : cardInfo.publishChannel === 'group' ? '仅该群' : '请选择发布渠道' }}</text>
            </view> -->

            <!-- 编辑名字的弹窗 -->
            <uni-popup ref="cardNamePopup" type="dialog">
                <uni-popup-dialog mode="input" title="修改名片名字" v-model="newCardName" placeholder="请填写姓名"
                    @confirm="handleCardNameConfirm">
                </uni-popup-dialog>
            </uni-popup>

            <!-- 编辑城市的弹窗 -->
            <!-- <uni-popup ref="cardCityPopup" type="dialog">
                <uni-popup-dialog mode="input" title="修改名片城市" :value="newCardCity" placeholder="请选择城市" @confirm="handleCardCityConfirm">
                </uni-popup-dialog>
            </uni-popup> -->
            <!-- 地区选择弹窗 -->
            <uni-popup ref="cardCityPopup" type="bottom" @close="onPopupClose" @change="onPopupChange">
                <view class="region-picker">
                    <view class="picker-buttons-top">
                        <text class="picker-cancel" @click="closeCityPopup">取消</text>
                        <text class="picker-title">选择城市</text>
                        <text class="picker-confirm" @click="confirmCity">确定</text>
                    </view>
                    <picker-view :value="regionValue" @change="onRegionChange" :indicator-style="'height: 44px;'"
                        :style="'height: 220px;'">
                        <picker-view-column>
                            <view class="picker-item" v-for="(item, index) in provinces" :key="index">
                                {{ item.name }}
                            </view>
                        </picker-view-column>
                        <picker-view-column>
                            <view class="picker-item" v-for="(item, index) in cities" :key="index">
                                {{ item.name }}
                            </view>
                        </picker-view-column>
                    </picker-view>
                    <!-- <view class="picker-buttons">
						<text @click="closeCityPopup">取消</text>
						<text @click="confirmCity">确定</text>
					</view> -->
                </view>
            </uni-popup>

            <!-- 编辑公司的弹窗 -->
            <uni-popup ref="cardCompanyPopup" type="dialog">
                <uni-popup-dialog mode="input" title="修改名片公司" :value="newCardCompany" placeholder="请填写公司与职位"
                    @confirm="handleCardCompanyConfirm">
                </uni-popup-dialog>
            </uni-popup>

            <!-- 编辑角色的弹窗 -->
            <!-- <uni-popup ref="cardRolePopup" type="dialog">
                <uni-popup-dialog mode="input" title="修改名片角色" :value="newCardRole" placeholder="请填写角色" @confirm="handleCardRoleConfirm">
                </uni-popup-dialog>
            </uni-popup> -->

            <!-- 角色选择弹窗 -->
            <uni-popup ref="cardRolePopup" type="bottom" @close="onPopupClose" @change="onPopupChange">
                <view class="role-picker">
                    <view class="picker-buttons-top">
                        <text class="picker-cancel" @click="closeRolePopup">取消</text>
                        <text class="picker-title">选择角色</text>
                        <text class="picker-confirm" @click="confirmRole">确定</text>
                    </view>
                    <picker-view :value="[tempRoleIndex]" @change="onRoleChange" :indicator-style="'height: 44px;'"
                        :style="'height: 220px;'">
                        <picker-view-column>
                            <view class="picker-item" v-for="(item, index) in roleOptions" :key="index">
                                {{ item }}
                            </view>
                        </picker-view-column>
                    </picker-view>
                </view>
            </uni-popup>

            <!-- 编辑年收入的弹窗 -->
            <uni-popup ref="cardSalaryPopup" type="dialog">
                <uni-popup-dialog mode="input" title="设置年收入" :value="newCardSalary" placeholder="请填写年收入"
                    @confirm="handleCardSalaryConfirm">
                </uni-popup-dialog>
            </uni-popup>

            <!-- 编辑手机的弹窗 -->
            <uni-popup ref="cardPhonePopup" type="dialog">
                <uni-popup-dialog mode="input" title="修改名片手机号" :value="newCardPhone" placeholder="请填写手机号"
                    @confirm="handleCardPhoneConfirm">
                </uni-popup-dialog>
            </uni-popup>

            <!-- 编辑微信的弹窗 -->
            <uni-popup ref="cardWechatPopup" type="dialog">
                <uni-popup-dialog mode="input" title="修改名片微信号" :value="newCardWechat" placeholder="请填写微信号"
                    @confirm="handleCardWechatConfirm">
                </uni-popup-dialog>
            </uni-popup>
        </scroll-view>
        <!-- 底部操作栏 -->
        <view class="bottom-bar" v-if="!showAnyPopup">
            <view class="btn-draft" @click="saveDraft">
                <image class="draft-icon" src="/static/image/icon/draft.png"></image>
                <text class="draft-text">存草稿</text>
            </view>
            <view v-if="!showPreview" class="btn-preview" @click="previewCard">预览名片</view>
            <view v-else class="btn-preview" @click="closePreview">取消预览</view>
            <!-- <view class="btn-publish" @click="uploadFileTest">立即发布</view> -->
            <view class="btn-publish" @click="uploadAndPublishCard">保存</view>
        </view>

        <!-- 预览名片遮罩层 -->
        <view v-if="showPreview" class="preview-mask">
            <!-- 顶部栏 -->
            <view class="preview-header">
                <view class="preview-back" @click="closePreview">
                    <uni-icons type="back" size="24" color="#222" />
                </view>
                <text class="preview-title">名片信息</text>
            </view>
            <!-- 内容区整体可滑动，复用CardContent组件 -->
            <scroll-view class="preview-scroll-content" scroll-y="true" show-scrollbar="false">
                <CardContent class="preview-card-box"
                    :card="cardInfo"
                    :userInfo="userInfo"
                    :userScore="userScore"
                    
                />
            </scroll-view>
            <!-- :userScore="userScore"
                :defaultAvatar="defaultAvatar"
                :tabs="tabs"
                :currentTab="currentTab"
                :currentContent="currentContent"
                :switchTab="switchTab"
                :previewImage="previewImage"
                :onImageLoad="onImageLoad"
                :gotoGroups="gotoGroups" -->
            <!-- 底部栏复用 -->
            <view class="bottom-bar preview-bottom-bar">
                <view class="btn-draft" @click="saveDraft">
                    <image class="draft-icon" src="/static/image/icon/draft.png"></image>
                    <text class="draft-text">存草稿</text>
                </view>
                <view class="btn-preview" @click="closePreview">取消预览</view>
                <!-- <view class="btn-publish" @click="uploadFileTest">立即发布</view> -->
                <view class="btn-publish" @click="uploadAndPublishCard">保存</view>
            </view>
        </view>

        <!-- 社群选择弹窗 -->
        <view v-if="showGroupPicker" class="group-picker-mask" @click="showGroupPicker = false">
            <view class="group-picker-popup bottom-popup">
                <view class="group-picker-header">
                    <view class="group-picker-close" @click="showGroupPicker = false">
                        <text style="color: #f00; font-size: 24px; padding-right: 8px;">×</text>
                    </view>
                </view>
                <view class="group-picker-content">
                    <view class="group-picker-option" @click="onSelectEarthGroup">
                        选择地球岛社群
                        <text class="group-picker-arrow">&gt;</text>
                    </view>
                    <view class="group-picker-divider"></view>
                    <view class="group-picker-option" @click="onSelectThirdGroup">
                        第三方社群
                        <text class="group-picker-arrow">&gt;</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 第三方社群弹窗 -->
        <view v-if="showThirdGroupDialog" class="third-group-mask">
            <view class="third-group-popup">
                <view class="third-group-close" @click="showThirdGroupDialog = false">
                    <text style="color: #f00; font-size: 18px;">×</text>
                </view>
                <view class="third-group-content">
                    <view class="third-group-row">
                        <text class="third-group-label">社群头像：</text>
                        <view class="third-group-avatar-upload" @click="chooseThirdGroupAvatar">
                            <image v-if="thirdGroup.avatar" :src="thirdGroup.avatar" class="third-group-avatar-img" />
                            <view v-else class="third-group-avatar-add">+</view>
                        </view>
                        <text class="third-group-avatar-tip">不上传系统将随机生成</text>
                    </view>
                    <view class="third-group-row">
                        <text class="third-group-label">群名称：</text>
                        <input v-model="thirdGroup.name" class="third-group-input" placeholder="请输入群名称" />
                    </view>
                    <view class="third-group-row">
                        <text class="third-group-label">社群号：</text>
                        <input v-model="thirdGroup.number" class="third-group-input" placeholder="输入社群号" />
                    </view>
                    <view class="third-group-row">
                        <text class="third-group-label">二维码*：</text>
                        <view class="third-group-qrcode-upload" @click="chooseThirdGroupQrcode">
                            <image v-if="thirdGroup.qrcode" :src="thirdGroup.qrcode" class="third-group-qrcode-img" />
                            <view v-else class="third-group-qrcode-add">+</view>
                        </view>
                    </view>
                    <view class="third-group-row">
                        <input v-model="thirdGroup.intro" class="third-group-intro" maxlength="20"
                            placeholder="介绍一下吸引更多人~" />
                        <text class="third-group-intro-count">{{ (thirdGroup.intro || '').length }}/20</text>
                    </view>
                    <view class="third-group-submit-btn" @click="submitThirdGroup">提交</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import appServerApi from '@/api/appServerApi'
import regionData from '@/utils/regionData.js'
import util from '@/utils/util'
import topMessage from '@/common/topMessageView'
import { getItem } from '@/pages/util/storageHelper'
import CardContent from '@/components/CardContent'

export default {
    components: {
        CustomHeader,
        CardContent
    },
    async onLoad(options) {
        // 接收可能传递过来的名片数据和群ID
        let cardData = null;
        if (options && options.card) {
            try {
                cardData = JSON.parse(decodeURIComponent(options.card));
                console.log("EditCard.vue接收到的名片数据：", cardData);
                // 如果接收到名片数据，说明是修改操作，使用接收到的数据初始化 cardInfo
                this.cardInfo = {
                    ...this.cardInfo,
                    ...cardData,
                    income: cardData.income || '', // 映射 income
                    introduction: cardData.introduction || '', // 映射 introduction
                    platformIndex: {
                        ...this.cardInfo.platformIndex, // 保留默认的键
                        ...(cardData.platformIndex || {}) // 合并传入的数据
                    },
                    groups: cardData.groups || [], // 映射 groups
                };

                // 处理风采展示urlList（如果接口返回url数组）
                if (cardData.url && Array.isArray(cardData.url)) {
                    // 简单判断是图片还是视频，这里假设urlList中的第一个元素决定类型
                    if (cardData.url.length > 0) {
                        const firstUrl = cardData.url[0];
                        if (firstUrl.toLowerCase().endsWith('.mp4') || firstUrl.toLowerCase().endsWith('.mov')) {
                            this.video = firstUrl;
                            this.cardInfo.url = [];
                        } else {
                            this.cardInfo.url = cardData.url;
                            this.video = '';
                        }
                    }
                }
                // 将接收到的名片id存入 cardInfo，用于判断是修改操作
                if (cardData.id) {
                    this.cardInfo.id = cardData.id;
                }
                console.log("此时EditCard.vue页面的cardInfo:", this.cardInfo)
            } catch (e) {
                console.error("解析名片数据失败：", e);
            }
        }

        // 接收群ID
        if (options && options.groupId) {
            this.groupId = options.groupId;
            console.log("接收到的群ID为：", this.groupId);
        } else {
            console.log("从我的名片页面进入，未接收到群id")
        }

        // 优先加载草稿
        // const draft = uni.getStorageSync('editCardDraft');
        // if (draft && typeof draft === 'object') {
        //   this.cardInfo = { ...this.cardInfo, ...draft };
        // } else {
        //   // 没有草稿再去请求接口
        //   this.fetchUserInfo();
        // }
        // console.log("当前群ID为：",this.groupId)
        // this.getAllRoles()
        // this.cities = this.provinces[0].cities

        // 如果是新建操作（没有传入名片id），尝试加载草稿或获取用户信息初始化部分数据
        if (!this.cardInfo.id) {
            const draft = uni.getStorageSync('editCardDraft');
            if (draft && typeof draft === 'object' && Object.keys(draft).length > 0) {
                console.log("加载草稿：", draft);
                // 确保加载草稿时也正确映射新字段
                this.cardInfo = {
                    ...this.cardInfo,
                    ...draft,
                    income: draft.income || '',
                    introduction: draft.introduction || '',
                    platformIndex: draft.platformIndex || {},
                    groups: draft.groups || [],
                };
                // 如果草稿中有id，说明草稿是针对一个现有卡片的修改，这里为了简单处理，我们只在新建时加载无id草稿
                if (this.cardInfo.id) { // 如果草稿有id，忽略此草稿，重新初始化
                    this.cardInfo = { // 重新初始化为默认值
                        avatar: '', nickname: '', gender: '', name: '', city: '', company: '', roleName: '', description: '', advantage: '', defect: '', url: [], video: '', phone: '', wechatId: '', publishChannel: '',
                        income: '', introduction: '', platformIndex: {}, groups: [], // 新增字段也要初始化
                    };
                    // 仍然尝试获取用户信息初始化
                    await this.fetchUserInfo();
                } else {
                    // 草稿无id，说明是新建草稿，加载后获取用户信息填充基础信息
                    await this.fetchUserInfo();
                    // 如果草稿中没有姓名等信息，使用用户信息填充
                    if (!this.cardInfo.avatar && this.userInfo.avatar) this.cardInfo.avatar = this.userInfo.avatar;
                }
            } else {
                // 没有草稿且是新建，获取用户信息初始化
                await this.fetchUserInfo();
                if (this.userInfo.avatar) this.cardInfo.avatar = this.userInfo.avatar;
            }
        } else {
            // 如果是修改操作（有传入名片id），也获取一下用户信息（比如头像和昵称可能需要从userInfo获取）
            await this.fetchUserInfo();
            // 如果 cardInfo 中没有头像，使用 userInfo 的头像
            if (!this.cardInfo.avatar && this.userInfo.avatar) this.cardInfo.avatar = this.userInfo.avatar;
        }

        // 确保 cardInfo 中的 url 和 video 有初始值
        if (!this.cardInfo.url) this.cardInfo.url = [];
        if (!this.video) this.video = '';

        // 加载角色列表
        this.getAllRoles();
        // 初始化地区选择器数据
        this.cities = this.provinces[0].cities;
        //获取用户评分，供预览名片使用
        await this.getUserScore();
    },

    onShow() {
        this.fetchUserInfo();
        // 检查是否有资料编辑返回
        const editResult = uni.getStorageSync('editFieldResult');
        if (editResult && editResult.type) {
            if (editResult.type === 'description') this.cardInfo.description = editResult.value;
            if (editResult.type === 'advantage') this.cardInfo.advantage = editResult.value;
            if (editResult.type === 'defect') this.cardInfo.defect = editResult.value;
            uni.removeStorageSync('editFieldResult');
        }
        // 检查是否有更多社交媒体信息返回
        const moreSocialLinks = uni.getStorageSync('editMoreSocialLinks');
        if (moreSocialLinks && typeof moreSocialLinks === 'object') {
            this.cardInfo.platformIndex = { ...this.cardInfo.platformIndex, ...moreSocialLinks };
            uni.removeStorageSync('editMoreSocialLinks');
        }

        // 监听群聊选择事件
        uni.$off('selectGroupFromEditCard'); // 避免重复监听
        uni.$on('selectGroupFromEditCard', (group) => {
            // 检查是否已存在该群组，避免重复
            const exists = this.cardInfo.groups.some(item => item.gid === group.gid);
            if (!exists) {
                this.cardInfo.groups.push({
                    avatar: group.avatar,
                    name: group.name,
                    gid: group.gid,
                    groupType: 1, // 地球岛社群类型为1
                });
            }
        });
    },

    data() {
        return {
            groupId: '', // 新增：接收群ID
            defaultAvatar: '/static/images/user.png',
            userInfo: {},
            cardInfo: {
                name: '', // 姓名
                city: '', // 城市
                company: '', // 公司与职位
                roleName: '', // 角色
                description: '', // 个人简介
                advantage: '', // 我的资源
                defect: '', // 我的需求
                income: '', // 年收入
                url: [], // 风采展示图片
                video: '', // 风采展示视频
                phone: '', // 手机号
                wechatId: '', // 微信号
                introduction: '', // 一句话介绍自己
                platformIndex: {
                    douyin: '',
                    xiaohongshu: '',
                    kuaishou: '',
                    weibo: '',
                    toutiao: '',
                    gongzhonghao: ''
                },
                groups: [], // 社群展示列表
            },
            provinces: regionData,
            cities: [],
            regionValue: [0, 0],
            genderOptions: ['女', '男'],
            roleOptions: [], // 初始化为空，后续用接口数据填充
            roleIdOptions: [], // 新增：存储角色id，方便后续提交
            newCardName: '',
            newCardCity: '',
            newCardCompany: '',
            newCardRole: '',
            newCardSalary: '',
            newCardPhone: '',
            newCardWechat: '',
            // 新增用于风采展示的图片和视频
            url: [],
            video: '',
            showRolePicker: false,
            //   rolePickerOptions: ['创业者', '合伙人', '投资者'],
            rolePickerIndex: 0,
            tempRoleIndex: 0,
            showPreview: false, // 新增：预览开关
            cardData: { // 示例名片数据结构，请根据您的实际情况调整
                advantage: '',
                company: '',
                defect: '',
                name: '',
                url: [], // 图片/视频 URL 数组
                city: '',
                roleId: 1, // 示例 roleId
                description: '',
                phone: '',
                wechatId: '',
                // ... 其他名片相关字段
            },
            uploading: false,
            showAnyPopup: false, // 新增
            showGroupPicker: false, // 控制社群选择弹窗显示
            showThirdGroupDialog: false, // 控制第三方社群弹窗显示
            thirdGroup: {
                avatar: '',
                name: '',
                number: '',
                qrcode: '',
                intro: ''
            },
            userScore: 1.0,
        }
    },
    methods: {
        onEditorReady(e) {
            this.editorCtx = e.detail.context;
        },
        onEditorInput(e) {
            console.log('onEditorInput', e.detail.value);
        },

        goBack() {
            // 如果showPreview为true，表明在预览页，点击返回执行关闭预览
            if (this.showPreview) {
                this.showPreview = false;
                return;
            }
            // 判断是否有编辑内容（简单判断cardInfo是否有非空字段）
            const info = this.cardInfo;
            const hasEdit = info.name || info.city || info.company || info.roleName || info.description || info.advantage || info.defect || (info.url && info.url.length > 0) || info.video || info.phone || info.wechatId || info.introduction || (Object.values(info.platformIndex).some(v => v)) || (info.groups && info.groups.length > 0);
            if (hasEdit) {
                uni.showModal({
                    title: '提示',
                    content: '是否保留此次编辑内容?',
                    confirmText: '保留',
                    cancelText: '不保留',
                    success: (res) => {
                        if (res.confirm) {
                            this.saveDraft();
                            uni.navigateBack();

                        } else if (res.cancel) {
                            uni.navigateBack();
                        }
                    }
                });
            } else {
                uni.showModal({
                    title: '提示',
                    content: '退出此次编辑？',
                    confirmText: '确定',
                    cancelText: '取消',
                    success: (res) => {
                        if (res.confirm) {
                            uni.removeStorageSync('editCardDraft');
                            uni.navigateBack();
                        }
                        // 取消则不操作
                    }
                });
            }
        },

        showCardNameEdit() {
            this.$refs.cardNamePopup.open()
        },

        handleCardNameConfirm(value) {
            this.cardInfo.name = value
            this.$refs.cardNamePopup.close()
        },

        showCardCityEdit() {
            this.cities = this.provinces[this.regionValue[0]].cities
            console.log('handleCityClick this.provinces:', this.provinces)
            console.log('handleCityClick this.cities:', this.cities)

            this.showAnyPopup = true;
            this.$refs.cardCityPopup.open();
        },

        handleCardCityConfirm(value) {
            this.cardInfo.city = value
            this.$refs.cardCityPopup.close()
        },

        showCardCompanyEdit() {
            this.$refs.cardCompanyPopup.open()
        },
        handleCardCompanyConfirm(value) {
            this.cardInfo.company = value
            this.$refs.cardCompanyPopup.close()
        },

        showCardSalaryEdit() {
            this.$refs.cardSalaryPopup.open()
        },
        handleCardSalaryConfirm(value) {
            this.cardInfo.income = value
            this.$refs.cardSalaryPopup.close()
        },

        showCardRolePicker() {
            this.tempRoleIndex = this.rolePickerIndex;
            this.showAnyPopup = true;
            this.$refs.cardRolePopup.open();
        },

        closeRolePopup() {
            this.showAnyPopup = false;
            this.$refs.cardRolePopup.close();
        },

        onRoleChange(e) {
            if (e && e.detail && Array.isArray(e.detail.value)) {
                this.tempRoleIndex = e.detail.value[0];
            }
            console.log('onRoleChange this.tempRoleIndex', this.tempRoleIndex)
        },

        confirmRole() {
            this.rolePickerIndex = this.tempRoleIndex;
            this.cardInfo.roleName = this.roleOptions[this.rolePickerIndex];
            this.cardInfo.roleId = this.roleIdOptions[this.rolePickerIndex];
            this.closeRolePopup();
        },

        showCardPhoneEdit() {
            this.$refs.cardPhonePopup.open()
        },

        handleCardPhoneConfirm(value) {
            this.cardInfo.phone = value
            this.$refs.cardPhonePopup.close()
        },

        showCardWechatEdit() {
            this.$refs.cardWechatPopup.open()
        },

        handleCardWechatConfirm(value) {
            this.cardInfo.wechatId = value
            this.$refs.cardWechatPopup.close()
        },

        gotoCardIntroEdit() {
            uni.navigateTo({
                url: '/pages/card/EditData?type=description&title=个人简介&value=' + encodeURIComponent(this.cardInfo.description || '')
            })
        },
        gotoCardResourceEdit() {
            uni.navigateTo({
                url: '/pages/card/EditData?type=advantage&title=我的资源&value=' + encodeURIComponent(this.cardInfo.advantage || '')
            })
        },
        gotoCardNeedEdit() {
            uni.navigateTo({
                url: '/pages/card/EditData?type=defect&title=我的需求&value=' + encodeURIComponent(this.cardInfo.defect || '')
            })
        },
        gotoMoreSocial() {
            // 跳转时传递当前socialLinks内容
            const links = encodeURIComponent(JSON.stringify(this.cardInfo.platformIndex));
            uni.navigateTo({
                url: '/pages/card/EditMoreSocial?socialLinks=' + links
            })
        },
        // 监听地区选择变化
        onRegionChange(e) {
            const values = e.detail.value;
            console.log('onRegionChange values:', values);

            // 省份改变时更新城市列表
            if (values[0] !== this.regionValue[0]) {
                this.cities = this.provinces[values[0]].cities;
                values[1] = 0; // 城市重置为第一个
            }

            this.regionValue = values;
            console.log('onRegionChange updated cities:', this.cities);
        },

        confirmCity() {
            console.log('confirmCity this.regionValue ', this.regionValue)
            const province = this.provinces[this.regionValue[0]]
            const city = this.cities[this.regionValue[1]]

            this.selectedRegion = {
                province: province.name,
                city: city.name
            }

            // 更新显示的地区文本
            this.cardInfo.city = `${province.name} ${city.name}`
            console.log('confirmCity this.cardInfo ', this.cardInfo.city)
            // 这里可以调用接口保存地区数据
            // appServerApi.completeUserInfo(this.userInfo).then(() => {

            // 	// 触发用户信息更新事件
            // 	uni.$emit('userInfoUpdated')
            // }).catch(err => {
            // 	console.error('更新地区失败:', err)
            // 	uni.showToast({
            // 	title: '修改失败',
            // 	icon: 'none'
            // 	})
            // })

            this.closeCityPopup()
        },

        uploadFileTest() {
            console.log("当前编辑好的名片内容是：", this.cardInfo)
        },
        uploadAndPublishCard() {
            console.log("当前编辑好的名片内容是：", this.cardInfo)
            this.uploadFilesAndSubmitCard();
        },


        async getUserScore() {
            try {
                if (!this.userInfo || !this.userInfo.userId) {
                    console.log('用户信息未加载，暂不获取评分');
                    return;
                }
                console.log('获取用户ID：', this.userInfo.userId);
                const response = await appServerApi.getUserScore(this.userInfo.userId)
                console.log('获取用户评分：', response.data?.score);
                this.userScore = response.data?.score || 5.0
            } catch (error) {
                console.error("获取用户评分失败:", error)
            }
        },

        async uploadFile() {
            const startUpload = Date.now();
            console.log(`[uploadFile] 开始:`, startUpload);
            console.log("当前要发布的名片内容是：", this.cardInfo)
            if (this.uploading) return;
            this.uploading = true;
            try {
                uni.showLoading({ title: '发布中...' });
                let urlList = [];
                // 只允许图片或视频二选一
                if (this.video) {
                    try {
                        const videoUploadStart = Date.now();
                        console.log(`[uploadFile] 视频上传开始:`, videoUploadStart);
                        urlList = await new Promise((resolve, reject) => {
                            appServerApi.uploadimgFile(
                                this.video,
                                (res) => {
                                    const videoUploadEnd = Date.now();
                                    console.log(`[uploadFile] 视频上传结束:`, videoUploadEnd, '耗时:', videoUploadEnd - videoUploadStart, 'ms');
                                    if (res && res.data) {
                                        resolve([res.data]);
                                    } else {
                                        reject(new Error('视频上传返回数据异常'));
                                    }
                                },
                                (error) => {
                                    const videoUploadEnd = Date.now();
                                    console.log(`[uploadFile] 视频上传失败:`, videoUploadEnd, '耗时:', videoUploadEnd - videoUploadStart, 'ms');
                                    reject(error);
                                }
                            );
                        });
                    } catch (error) {
                        uni.hideLoading();
                        uni.showToast({ title: '视频上传失败', icon: 'none' });
                        this.uploading = false;
                        const failTime = Date.now();
                        console.log(`[uploadFile] 视频上传异常结束:`, failTime, '总耗时:', failTime - startUpload, 'ms');
                        return;
                    }
                } else if (this.cardInfo.url.length > 0) {
                    try {
                        const imgUploadStart = Date.now();
                        console.log(`[uploadFile] 图片上传开始:`, imgUploadStart);
                        urlList = await Promise.all(
                            this.cardInfo.url.map((image, idx) => {
                                const singleImgStart = Date.now();
                                console.log(`[uploadFile] 单张图片上传开始 idx=${idx}:`, singleImgStart);
                                return new Promise((resolve, reject) => {
                                    appServerApi.uploadimgFile(
                                        image,
                                        (res) => {
                                            const singleImgEnd = Date.now();
                                            console.log(`[uploadFile] 单张图片上传结束 idx=${idx}:`, singleImgEnd, '耗时:', singleImgEnd - singleImgStart, 'ms');
                                            if (res && res.data) {
                                                resolve(res.data);
                                            } else {
                                                reject(new Error('图片上传返回数据异常'));
                                            }
                                        },
                                        (error) => {
                                            const singleImgEnd = Date.now();
                                            console.log(`[uploadFile] 单张图片上传失败 idx=${idx}:`, singleImgEnd, '耗时:', singleImgEnd - singleImgStart, 'ms');
                                            reject(error);
                                        }
                                    );
                                });
                            })
                        );
                        const imgUploadEnd = Date.now();
                        console.log(`[uploadFile] 所有图片上传结束:`, imgUploadEnd, '总耗时:', imgUploadEnd - imgUploadStart, 'ms');
                    } catch (error) {
                        uni.hideLoading();
                        uni.showToast({ title: '图片上传失败', icon: 'none' });
                        this.uploading = false;
                        const failTime = Date.now();
                        console.log(`[uploadFile] 图片上传异常结束:`, failTime, '总耗时:', failTime - startUpload, 'ms');
                        return;
                    }
                }
                const beforeSaveCard = Date.now();
                console.log(`[uploadFile] 调用handleSaveCard前:`, beforeSaveCard, '距开始:', beforeSaveCard - startUpload, 'ms');
                // 发布名片
                await this.handleSaveCard(urlList);
                const afterSaveCard = Date.now();
                console.log(`[uploadFile] handleSaveCard结束:`, afterSaveCard, '距开始:', afterSaveCard - startUpload, 'ms');
            } catch (e) {
                uni.hideLoading();
                uni.showToast({ title: '发布失败', icon: 'none' });
                const errorTime = Date.now();
                console.log(`[uploadFile] 异常:`, errorTime, '总耗时:', errorTime - startUpload, 'ms');
            } finally {
                this.uploading = false;
                const endUpload = Date.now();
                console.log(`[uploadFile] 结束:`, endUpload, '总耗时:', endUpload - startUpload, 'ms');
            }
        },

        async handleSaveCard(urlList = []) {
            const startSaveCard = Date.now();
            console.log(`[handleSaveCard] 开始:`, startSaveCard);
            // 组装接口参数
            const gid = this.groupId;
            const userCard = {
                // 如果存在名片id，说明是修改操作，带上id
                ...(this.cardInfo.id && { id: this.cardInfo.id }),
                name: this.cardInfo.name,
                city: this.cardInfo.city,
                company: this.cardInfo.company,
                roleId: this.cardInfo.roleId,
                description: this.cardInfo.description,
                advantage: this.cardInfo.advantage,
                defect: this.cardInfo.defect,
                url: this.cardInfo.url, // 直接使用 this.cardInfo.url
                phone: this.cardInfo.phone,
                wechatId: this.cardInfo.wechatId,
                income: this.cardInfo.income,
                introduction: this.cardInfo.introduction,
                platformIndex: this.cardInfo.platformIndex,
                groups: this.cardInfo.groups.map(group => {
                    // 根据类型构建符合后端期望的社群数据
                    if (group.groupType === 1) { // 地球岛社群
                        return {
                            groupId: group.gid,
                            groupType: group.groupType,
                        };
                    } else if (group.groupType === 2) { // 第三方社群
                        return {
                            groupType: group.groupType,
                            groupId: group.gid, // 第三方群ID用 gid 字段
                            groupName: group.name,
                            avatar: group.avatar || '',
                            qrcode: group.qrcode || '',
                            description: group.intro || '',
                        };
                    }
                    return null; // 未知类型
                }).filter(Boolean), // 过滤掉null值
            };
            console.log(`[handleSaveCard] 组装参数:`, userCard);
            try {
                const beforeApi = Date.now();
                console.log(`[handleSaveCard] 调用publishCardIntoGroup前:`, beforeApi, '距开始:', beforeApi - startSaveCard, 'ms');
                let result = null;
                if (this.cardInfo.id) {
                    // 如果存在名片id，调用修改接口
                    console.log(`[handleSaveCard] 调用修改名片接口:`, userCard);
                    result = await appServerApi.modifyCard(userCard);
                } else if (this.groupId) {
                    // 如果不存在名片id但存在群id，调用新建并发布到群接口
                    console.log(`[handleSaveCard] 调用新建并发布到群接口: gid=${gid}, userCard=`, userCard);
                    result = await appServerApi.publishCardIntoGroup({
                        gid: gid,
                        userCard: userCard
                    });
                } else {
                    // 如果不存在名片id也不存在群id，调用新建名片接口（假设新建名片接口是 saveCard）
                    console.log(`[handleSaveCard] 调用新建名片接口:`, userCard);
                    // 注意：这里假设有一个 saveCard 接口用于新建不关联群组的名片，请根据实际接口调整
                    result = await appServerApi.saveCard(userCard);
                }

                // 根据接口返回判断是否成功
                if (result && result.code === 200) {
                    const afterApi = Date.now();
                    console.log(`[handleSaveCard] 接口调用成功:`, afterApi, '耗时:', afterApi - beforeApi, 'ms', '结果:', result);
                    uni.hideLoading();
                    uni.showToast({ title: '发布成功', icon: 'success' });

                    // 发布成功后清除草稿
                    uni.removeStorageSync('editCardDraft');

                    // 如果是从群组页面进入的，通知群组页面刷新
                    if (this.groupId) {
                        uni.$emit('refreshGroupCard');
                    }

                    setTimeout(() => {
                        uni.navigateBack({ delta: 1 });
                    }, 1500);
                } else {
                    // 接口返回 code 不为 200，处理失败情况
                    console.error(`[handleSaveCard] 接口调用失败: code=${result?.code}, msg=${result?.msg}`);
                    uni.hideLoading();
                    uni.showToast({ title: result?.msg || '发布失败', icon: 'none' });
                }
            } catch (e) {
                uni.hideLoading();
                uni.showToast({ title: '发布失败', icon: 'none' });
                const errorTime = Date.now();
                console.log(`[uploadFile] 异常:`, errorTime, '总耗时:', errorTime - startSaveCard, 'ms');
            } finally {
                this.uploading = false;
                const endSaveCard = Date.now();
                console.log(`[uploadFile] 结束:`, endSaveCard, '总耗时:', endSaveCard - startSaveCard, 'ms');
            }
        },

        async getAllRoles() {
            try {
                const result = await appServerApi.getAllRoles();
                console.log("页面获取到的角色信息：", result)
                if (result && result.data) {
                    // 只取roleName用于显示，id用于后续提交
                    this.roleOptions = result.data.map(item => item.roleName);
                    this.roleIdOptions = result.data.map(item => item.id);
                }
            } catch (e) {
                console.log("获取角色失败")
            }
        },

        // 关闭地区选择器
        closeCityPopup() {
            this.showAnyPopup = false;
            this.$refs.cardCityPopup.close()
        },
        saveDraft() {
            try {
                uni.setStorageSync('editCardDraft', this.cardInfo);
                uni.showToast({
                    title: '草稿已保存',
                    icon: 'success'
                })
                console.log("草稿已保存", this.cardInfo)
            } catch (e) {
                uni.showToast({
                    title: '保存失败',
                    icon: 'none'
                });
            }
        },

        previewCard() {
            this.showPreview = true;
        },

        closePreview() {
            this.showPreview = false;
        },

        publishCard() {
            console.log("this.cardInfo", this.cardInfo)
        },

        async fetchUserInfo() {
            try {
                const result = await appServerApi.getUserInfo()
                this.userInfo = { ...this.userInfo, ...result }
                // const userId = this.userInfo.userId
                // this.qrCodeConfig.text = `EARTHID:${userId}`
                console.log("编辑名片页面获取到的userInfo", this.userInfo)
            } catch (error) {
                console.error('获取用户信息失败:', error)
                uni.showToast({
                    title: '获取用户信息失败',
                    icon: 'none',
                })
                throw error
            }
        },
        showMediaPicker() {
            if (this.video) return; // 已有视频不能再添加
            if (this.cardInfo.url.length >= 9) return;
            uni.showActionSheet({
                itemList: ['拍摄', '从相册选择图片', '从相册选择视频'],
                success: (res) => {
                    if (res.tapIndex === 0) {
                        // 拍摄
                        uni.chooseImage({
                            count: 9 - this.cardInfo.url.length,
                            sourceType: ['camera'],
                            success: (imgRes) => {
                                this.cardInfo.url = this.cardInfo.url.concat(imgRes.tempFilePaths);
                            }
                        });
                    } else if (res.tapIndex === 1) {
                        // 从相册选择图片
                        uni.chooseImage({
                            count: 9 - this.cardInfo.url.length,
                            sourceType: ['album'],
                            success: (imgRes) => {
                                this.cardInfo.url = this.cardInfo.url.concat(imgRes.tempFilePaths);
                            }
                        });
                    } else if (res.tapIndex === 2) {
                        // 从相册选择视频
                        if (this.cardInfo.url.length > 0) {
                            uni.showToast({ title: '图片和视频不能共存', icon: 'none' });
                            return;
                        }
                        uni.chooseVideo({
                            sourceType: ['album'],
                            success: (videoRes) => {
                                this.video = videoRes.tempFilePath;
                            }
                        });
                    }
                }
            });
        },
        deleteImage(index) {
            this.cardInfo.url.splice(index, 1);
        },
        deleteVideo() {
            this.video = '';
        },
        onPopupClose() {
            this.showAnyPopup = false;
        },
        onPopupChange(e) {
            if (e && e.show === false) {
                this.showAnyPopup = false;
            }
        },
        deleteGroupShowcaseImage(index) {
            this.cardInfo.groups.splice(index, 1);
        },
        showGroupShowcasePicker() {
            // 调用接口获取社群
            appServerApi.searchGroups({ pageNo: 1, pageSize: 10, joined: 1 }).then(res => {
                const total = res?.data?.total || 0;
                if (total !== 0) {
                    this.showGroupPicker = true;
                } else {
                    uni.showToast({ title: '暂无可选社群', icon: 'none' });
                }
            });
        },
        onSelectEarthGroup() {

            console.log('点击了选择地球岛社群');
            this.showGroupPicker = false;
            uni.navigateTo({
                url: '/pages/contact/GroupListPage?from=editCard'
            });
        },
        onSelectThirdGroup() {
            console.log('点击了第三方社群');
            this.showGroupPicker = false;
            this.showThirdGroupDialog = true;
        },
        chooseThirdGroupAvatar() {
            uni.chooseImage({
                count: 1,
                sourceType: ['album'],
                success: (res) => {
                    this.thirdGroup.avatar = res.tempFilePaths[0];
                }
            });
        },
        chooseThirdGroupQrcode() {
            uni.chooseImage({
                count: 1,
                sourceType: ['album'],
                success: (res) => {
                    this.thirdGroup.qrcode = res.tempFilePaths[0];
                }
            });
        },
        submitThirdGroup() {
            // 校验（可扩展）
            if (!this.thirdGroup.avatar || !this.thirdGroup.name || !this.thirdGroup.number || !this.thirdGroup.qrcode) {
                uni.showToast({ title: '请完善所有必填项', icon: 'none' });
                return;
            }
            // 添加到groups，并明确类型
            this.cardInfo.groups.push({
                groupType: 2, // 第三方社群
                avatar: this.thirdGroup.avatar,
                name: this.thirdGroup.name,
                gid: this.thirdGroup.number, // 使用gid作为第三方群的ID
                qrcode: this.thirdGroup.qrcode,
                intro: this.thirdGroup.intro
            });
            // 重置并关闭弹窗
            this.thirdGroup = { avatar: '', name: '', number: '', qrcode: '', intro: '' };
            this.showThirdGroupDialog = false;
        },
        // 新增：通用上传本地文件函数
        async uploadLocalFile(filePath) {
            console.log('uploadLocalFile: 开始上传', filePath);
            if (!filePath || (!filePath.startsWith('file://') && !filePath.startsWith('nfile://'))) {
                console.log('uploadLocalFile: 非本地文件，直接返回', filePath);
                return filePath; // 不是本地文件，直接返回
            }
            try {
                const res = await new Promise((resolve, reject) => {
                    appServerApi.uploadimgFile(
                        filePath,
                        (uploadRes) => {
                            console.log('uploadLocalFile: appServerApi.uploadimgFile 成功返回', uploadRes);
                            if (uploadRes && uploadRes.data) {
                                resolve(uploadRes.data);
                            } else {
                                reject(new Error('文件上传返回数据异常'));
                            }
                        },
                        (error) => {
                            console.error('uploadLocalFile: appServerApi.uploadimgFile 失败', error);
                            reject(error);
                        }
                    );
                });
                console.log(`uploadLocalFile: 文件上传成功: ${filePath} -> ${res}`);
                return res; // 返回服务器端的URL
            } catch (error) {
                console.error(`uploadLocalFile: 文件上传失败: ${filePath}`, error);
                uni.showToast({ title: '文件上传失败', icon: 'none' });
                return ''; // 上传失败返回空字符串或原路径，取决于需求
            }
        },

        // 新增：处理富文本内容中的图片/视频URL
        async processRichTextContent(content) {
            console.log('processRichTextContent: 原始内容', content);
            if (!content) return '';

            // Split content by actual newline characters represented as literal '\n'
            const parts = content.split('\\n');

            const processedPartsPromises = parts.map(async (part) => {
                let cleanedPath = part.trim();
                console.log('processRichTextContent: 处理片段', cleanedPath);

                // Handle redundant "file:" prefixes, which might come from copy-pasting or specific input methods
                if (cleanedPath.startsWith('file:file:///')) {
                    cleanedPath = 'file:///' + cleanedPath.substring('file:file:///'.length);
                    console.log('processRichTextContent: 修正 file:file:/// 为', cleanedPath);
                } else if (cleanedPath.startsWith('nfile:nfile:///')) {
                    cleanedPath = 'nfile:///' + cleanedPath.substring('nfile:nfile:///'.length);
                    console.log('processRichTextContent: 修正 nfile:nfile:/// 为', cleanedPath);
                }

                // Check if the cleaned path is a local file path
                if (cleanedPath.startsWith('file://') || cleanedPath.startsWith('nfile://')) {
                    console.log('processRichTextContent: 识别到本地文件路径，开始上传', cleanedPath);
                    const serverUrl = await this.uploadLocalFile(cleanedPath);
                    console.log('processRichTextContent: 上传结果', serverUrl, '原路径', cleanedPath);
                    // If upload is successful, return the server URL.
                    // Otherwise, if serverUrl is empty (upload failed), return the original part
                    // so that the content is not lost, although the display might be broken.
                    return serverUrl || part; // Keep original part if upload fails
                } else {
                    // Not a local file path, return as is
                    console.log('processRichTextContent: 非本地文件路径，保留', part);
                    return part;
                }
            });

            const processedParts = await Promise.all(processedPartsPromises);
            // Join the processed parts back with actual newline characters represented as literal '\n'
            const finalContent = processedParts.join('\\n');
            console.log('processRichTextContent: 最终处理内容', finalContent);
            return finalContent;
        },

        // 新增：统一处理文件上传和提交名片
        async uploadFilesAndSubmitCard() {
            uni.showLoading({ title: '发布中...' });
            this.uploading = true;

            try {
                // 1. 处理风采展示的图片和视频 (现有逻辑)
                let urlList = [];
                if (this.video) {
                    const uploadedVideoUrl = await this.uploadLocalFile(this.video);
                    if (uploadedVideoUrl) {
                        urlList.push(uploadedVideoUrl);
                    }
                } else if (this.cardInfo.url.length > 0) {
                    urlList = await Promise.all(this.cardInfo.url.map(async (image) => {
                        return await this.uploadLocalFile(image);
                    }));
                    urlList = urlList.filter(Boolean); // 过滤掉上传失败的空字符串
                }
                this.cardInfo.url = urlList; // 更新风采展示URL列表
                this.video = urlList.length > 0 && (urlList[0].endsWith('.mp4') || urlList[0].endsWith('.mov')) ? urlList[0] : '';

                // 2. 处理个人简介、我的资源、我的需求中的图片/视频
                console.log("处理之前的this.cardInfo.description", this.cardInfo.description)
                this.cardInfo.description = await this.processRichTextContent(this.cardInfo.description);
                console.log("处理之后的this.cardInfo.description", this.cardInfo.description)
                this.cardInfo.advantage = await this.processRichTextContent(this.cardInfo.advantage);
                this.cardInfo.defect = await this.processRichTextContent(this.cardInfo.defect);

                // 3. 处理社群展示中的头像和二维码
                const processedGroups = await Promise.all(this.cardInfo.groups.map(async (group) => {
                    const newGroup = { ...group };
                    if (newGroup.avatar && (newGroup.avatar.startsWith('file://') || newGroup.avatar.startsWith('nfile://'))) {
                        newGroup.avatar = await this.uploadLocalFile(newGroup.avatar);
                    }
                    if (newGroup.qrcode && (newGroup.qrcode.startsWith('file://') || newGroup.qrcode.startsWith('nfile://'))) {
                        newGroup.qrcode = await this.uploadLocalFile(newGroup.qrcode);
                    }
                    return newGroup;
                }));
                this.cardInfo.groups = processedGroups;
                console.log("名片待发布到后端前完成组装的数据：", this.cardInfo)
                // 4. 调用 handleSaveCard 提交最终数据
                await this.handleSaveCard(); // urlList 已经更新到 this.cardInfo.url，这里可以作为参数传递，也可以不传直接用 this.cardInfo.url

            } catch (e) {
                console.error('文件上传或名片提交失败:', e);
                uni.hideLoading();
                uni.showToast({ title: '发布失败', icon: 'none' });
            } finally {
                this.uploading = false;
                uni.hideLoading();
            }
        },

        /**
         * 过滤掉内容中的本地图片和视频链接，只保留纯文本
         */
        filterMediaLinks(content) {
            if (!content) return '';
            // 支持多行内容，逐行过滤
            const lines = content.split('\\n');
            // 匹配本地图片/视频链接的正则
            const mediaPattern = /^(file:\/\/|nfile:\/\/).+\.(png|jpg|jpeg|gif|bmp|mp4|mov)$/i;
            // 过滤掉匹配的行
            const filteredLines = lines.filter(line => !mediaPattern.test(line.trim()));
            return filteredLines.join('\\n');
        }
    },
    computed: {
        descriptionShort() {
            const val = this.cardInfo.description;
            const filtered = this.filterMediaLinks(val);
            // 只取第一行内容
            const firstLine = filtered.split('\\n')[0] || '';
            return firstLine.length > 10 ? firstLine.slice(0, 10) + '...' : firstLine;
        },
        advantageShort() {
            const val = this.cardInfo.advantage;
            const filtered = this.filterMediaLinks(val);
            const firstLine = filtered.split('\\n')[0]|| '';
            return firstLine.length > 10 ? firstLine.slice(0, 10) + '...' : firstLine;
        },
        defectShort() {
            const val = this.cardInfo.defect;
            const filtered = this.filterMediaLinks(val);
            const firstLine = filtered.split('\\n')[0] || '';
            return firstLine.length > 10 ? firstLine.slice(0, 10) + '...' : firstLine;
        }
    }
}
</script>

<style scoped>
.edit-card-container {
    min-height: 100vh;
    background: #f8f9fb;
    display: flex;
    flex-direction: column;
}

.edit-content {
    flex: 1;
    padding: 80px 18px 80px 18px;
}

.section-title-row {
    display: flex;
    align-items: center;
    margin: 16px 0 10px 0;
}

.section-bar {
    width: 4px;
    height: 20px;
    background: #256DFF;
    border-radius: 2px;
    margin-right: 8px;
}

.section-title {
    font-size: 16px;
    color: #000;
    font-weight: bold;
}

.cardInfo-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fb;
    border-radius: 8px;
    margin-bottom: 10px;
    padding: 2px 10px 0 8px;
    /* min-height: 48px; */
    z-index: 300;
}

.cardInfo-label {
    color: #222;
    font-size: 15px;
    font-weight: 400;
    flex: 0 0 90px;
}

.cardInfo-value {
    flex: 1;
    text-align: right;
    color: #bbb;
    font-size: 15px;
    margin-right: 10px;
    -webkit-line-clamp: 1;
}

.cardInfo-value-avatar {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 10px;
}

.avatar-img {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background: #e6e6e6;
}

.showcase-scroll {
    width: 100%;
    overflow-x: auto;
    white-space: nowrap;
    padding: 10px 0;
    min-height: 100px;
    /* background: #fff; */
    margin-bottom: 16px;
}

.file-block,
.file-block.add-block {
    width: 80px;
    height: 80px;
    border-radius: 5px;
    display: inline-flex;
    vertical-align: top;
    margin-right: 12px;
    position: relative;
    background: #f5f5f5;
    border: 1px dashed #bbb;
    color: #bbb;
    justify-content: center;
    align-items: center;
}

.file-block .delete {
    position: absolute;
    top: -1px;
    right: -1px;
    width: 24px;
    height: 24px;
    background: #FF4D4F;
    border-radius: 0 5px 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
}

.file-block .delete image {
    width: 100%;
    height: 100%;
}

.file-block .avatar {
    width: 100%;
    height: 100%;
    border-radius: 4px;
}

.video-block {
    width: 80px;
    height: 80px;
    position: relative;
}

.preview-video {
    width: 100%;
    height: 100%;
    border-radius: 4px;
}

.required {
    color: #e13c39;
}

.bottom-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 0px 15px 10px 15px;
    z-index: 200;
    box-shadow: none;
    border-top: none;
}

.btn-draft {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    width: 56px;
    height: 56px;
    background: transparent;
    border: none;
    box-shadow: none;
    margin-right: 16px;
    padding: 0;
}

.draft-icon {
    width: 36px;
    height: 45px;
    margin-bottom: 2px;
    padding-top: 5px;
}

.draft-text {
    font-size: 10px;
    color: #222;
    margin-top: 5px;
}

.btn-preview,
.btn-publish {
    height: 48px;
    line-height: 24px;
    border-radius: 12px;
    padding: 12px 0;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    flex: 1;
    margin: 0 12px;
    min-width: 0;
    box-sizing: border-box;
}

.btn-preview {
    background: #e7edff;
    color: #b2c7fa;
    border: none;
    margin-right: 16px;
}

.btn-publish {
    background: #256dff;
    color: #fff;
    border: none;
    margin-right: 0;
}

.icon-ion-ios-arrow-right {
    color: #bbb;
    font-size: 16px;
    margin-left: 6px;
}

.title {
    font-size: 16px;
    font-weight: 500;
}

/* 角色选择弹窗样式 */
.picker-mask {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 9999;
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

.picker-popup {
    width: 100%;
    background: #fff;
    border-radius: 16px 16px 0 0;
    padding: 16px 0 0 0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
}

.picker-item {
    height: 44px;
    line-height: 44px;
    text-align: center;
    font-size: 16px;
    color: #333;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.picker-item.selected {
    color: #256DFF;
    font-weight: bold;
    background: #f0f6ff;
}

.picker-actions {
    display: flex;
    border-top: 1px solid #eee;
    margin-top: 8px;
}

.role-picker {
    background-color: #fff;
    border-radius: 16px 16px 0 0;
    padding-bottom: 10px;
    z-index: 9999;
}

.picker-buttons-top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0px 0 0px;
    border-bottom: 1px solid #eee;
    position: relative;
    height: 44px;
}

.picker-cancel,
.picker-confirm {
    width: 60px;
    text-align: center;
    font-size: 16px;
    color: #888;
    z-index: 2;
}

.picker-confirm {
    color: #256DFF;
    font-weight: bold;
}

.picker-title {
    position: absolute;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 16px;
    color: #333;
    font-weight: bold;
    pointer-events: none;
    z-index: 1;
}

.preview-mask {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: #fff;
    z-index: 3000;
    display: flex;
    flex-direction: column;
}

.preview-header {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #f0f0f0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding-top: 40px;
    box-sizing: border-box;
    z-index: 3100;
}

.preview-back {
    position: absolute;
    left: 16px;
    top: 40px;
    height: 36px;
    display: flex;
    align-items: center;
}

.preview-title {
    font-size: 18px;
    /* font-weight: bold; */
    color: #222;
}

.preview-scroll-content {
    flex: 1;
    /* margin-top: 80px; */
    background: #f8f9fb;
    min-height: 0;
    padding-bottom: 66px;
}

.preview-card-box {
    /* margin: 16px 16px 0 16px; */
    background: transparent;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    /* padding: 16px; */
}

.preview-info-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.preview-avatar {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background: #e6e6e6;
    margin-right: 12px;
}

.preview-info-main {
    display: flex;
    flex-direction: column;
}

.preview-info-name {
    font-size: 16px;
    font-weight: bold;
    color: #222;
}

.preview-info-title {
    font-size: 13px;
    color: #888;
    margin-top: 2px;
}

.preview-section-box {
    margin: 16px 16px 0 16px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    padding: 0 16px 12px 16px;
}

.preview-section-row {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    padding-top: 12px;
}

.preview-section-bar {
    width: 4px;
    height: 16px;
    background: #fd3c3c;
    border-radius: 2px;
    margin-right: 6px;
}

.preview-section-label {
    font-size: 15px;
    color: #222;
    font-weight: 500;
    margin-right: 8px;
}

.preview-section-content {
    font-size: 14px;
    color: #888;
    background: #fff;
    border-radius: 6px;
    min-height: 22px;
    padding: 2px 0 8px 0;
    word-break: break-all;
}

.preview-contact-box {
    margin: 16px 16px 0 16px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    padding: 0 16px 12px 16px;
}

.preview-contact-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 12px;
}

.preview-contact-label {
    font-size: 15px;
    color: #222;
    font-weight: 500;
}

.preview-contact-value {
    font-size: 14px;
    color: #888;
    margin-left: 10px;
}

.preview-bottom-bar {
    background: #f8f9fb;
    box-shadow: none;
    border-top: none;
}

.preview-img-vertical-list {
    margin: 16px 16px 0px 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.preview-img-vertical {
    width: 100%;
    aspect-ratio: 9/16;
    border-radius: 12px;
    background: #f5f5f5;
    object-fit: cover;
}

.region-picker {
    background-color: #fff;

    picker-view {
        width: 100%;
        height: 400rpx;
    }

    .picker-item {
        line-height: 44px;
        text-align: center;
        padding: 0 10px;
    }

    .picker-buttons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx 0;
        border-bottom: 1px solid #eee;
        position: relative;
        width: 100%;

        text {
            font-size: 36rpx;

            &:first-child {
                position: absolute;
                left: 40rpx;
                color: #999;
            }

            &:last-child {
                position: absolute;
                right: 0;
                padding-right: 40rpx;
                color: #007AFF;
            }

            &:active {
                opacity: 0.7;
            }
        }
    }
}

/* 一句话介绍自己输入框优化 */
.intro-textarea-wrap {
    margin-bottom: 10px;
    position: relative;
}

.intro-textarea {
    width: 100%;
    min-height: 72px;
    max-height: 100px;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 10px 12px 26px 12px;
    font-size: 15px;
    background: #f8f9fb;
    resize: none;
    box-sizing: border-box;
    line-height: 1.5;
}

.intro-textarea-count {
    position: absolute;
    right: 16px;
    bottom: 8px;
    color: #bbb;
    font-size: 13px;
    pointer-events: none;
    background: transparent;
}

/* 其他社交媒体标题右侧更多 */
.section-right {
    margin-left: auto;
    color: #4E7CF7;
    font-size: 13px;
    padding-right: 6px;
}

/* 社交媒体输入行优化 */
.social-media-list {
    margin-bottom: 16px;
}

.social-media-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.social-media-icon {
    width: 24px;
    height: 24px;
    background: #eee;
    border-radius: 4px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.social-media-label {
    width: 48px;
    color: #222;
    font-size: 14px;
}

.social-media-input {
    flex: 1;
    height: 34px;
    border: 1px solid #e5e5e5;
    background: #f8f9fb;
    font-size: 14px;
    padding: 6px 8px;
    border-radius: 4px;
    margin-left: 6px;
    box-sizing: border-box;
}

.group-picker-mask {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.2);
    z-index: 4000;
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

.group-picker-popup.bottom-popup {
    width: 100vw;
    border-radius: 16px 16px 0 0;
    margin-bottom: 0;
    box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
    background: #fff;
    overflow: hidden;
    padding-bottom: env(safe-area-inset-bottom);
    /* 兼容iPhone X底部安全区 */
}

.group-picker-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 40px;
    padding: 0 15px;
}

.group-picker-close {
    cursor: pointer;
}

.group-picker-content {
    display: flex;
    flex-direction: column;
    padding-bottom: 20px;
}

.group-picker-option {
    padding: 16px 18px;
    font-size: 16px;
    color: #222;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.group-picker-divider {
    height: 1px;
    background: #e5e5e5;
    margin: 0 12px;
}

.group-picker-arrow {
    color: #bbb;
    font-size: 18px;
}

.group-name {
    position: absolute;
    bottom: 4px;
    left: 0;
    width: 100%;
    text-align: center;
    font-size: 12px;
    color: #333;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 0 0 5px 5px;
    padding: 2px 0;
}

/* 社群展示区优化 */
.group-block,
.group-block.add-block {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    width: 80px;
    margin-right: 12px;
    position: relative;
    background: #f5f5f5;
    border: none;
    color: #bbb;
    box-shadow: none;
    vertical-align: top;
}

.group-avatar {
    width: 80px;
    height: 80px;
    border-radius: 5px;
    object-fit: cover;
    background: #f5f5f5;
}

.group-block-name {
    width: 100%;
    text-align: center;
    font-size: 13px;
    color: #333;
    margin-top: 4px;
    word-break: break-all;
}

.group-delete {
    position: absolute;
    top: -1px;
    right: -1px;
    width: 24px;
    height: 24px;
    background: #FF4D4F;
    border-radius: 0px 5px 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
    z-index: 2;
}

.group-delete image {
    width: 16px;
    height: 16px;
}

.group-block.add-block {
    justify-content: center;
    align-items: center;
    background: #f5f5f5;
    border: 1px dashed #bbb;
    color: #bbb;
    min-height: 80px;
    min-width: 80px;
    border-radius: 5px;
    margin-right: 0;
}

.third-group-mask {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
    z-index: 5000;
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

.third-group-popup {
    width: 340px;
    background: #fff;
    border-radius: 12px 12px 0 0;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    margin-bottom: 0;
    overflow: hidden;
    padding-bottom: 16px;
}

.third-group-close {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 32px;
    padding: 0 12px;
    font-size: 18px;
    cursor: pointer;
}

.third-group-content {
    padding: 0 20px 10px 20px;
}

.third-group-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    position: relative;
}

.third-group-label {
    width: 70px;
    font-size: 14px;
    color: #222;
    flex-shrink: 0;
}

.third-group-avatar-upload {
    width: 36px;
    height: 36px;
    border-radius: 6px;
    background: #f5f5f5;
    border: 1px dashed #bbb;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    cursor: pointer;
    position: relative;
}

.third-group-avatar-img {
    width: 36px;
    height: 36px;
    border-radius: 6px;
    object-fit: cover;
}

.third-group-avatar-add {
    font-size: 28px;
    color: #bbb;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.third-group-avatar-tip {
    font-size: 11px;
    color: #bbb;
    margin-left: 2px;
}

.third-group-input {
    flex: 1;
    height: 32px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    padding: 0 8px;
    font-size: 14px;
    background: #fafbfc;
    margin-left: 4px;
}

.third-group-qrcode-upload {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    background: #f5f5f5;
    border: 1px dashed #bbb;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 4px;
}

.third-group-qrcode-img {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    object-fit: cover;
}

.third-group-qrcode-add {
    font-size: 48px;
    color: #bbb;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.third-group-intro {
    flex: 1;
    height: 40px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    padding: 0 8px;
    font-size: 14px;
    background: #fafbfc;
}

.third-group-intro-count {
    position: absolute;
    right: 8px;
    bottom: 2px;
    color: #bbb;
    font-size: 12px;
}

.third-group-submit-btn {
    width: 100%;
    height: 38px;
    background: #256DFF;
    color: #fff;
    border-radius: 8px;
    text-align: center;
    line-height: 38px;
    font-size: 16px;
    font-weight: bold;
    margin-top: 10px;
    cursor: pointer;
}
</style>