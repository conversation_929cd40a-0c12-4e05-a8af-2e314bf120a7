<template>
    <view class="card-detail-container">
		<canvas canvas-id="qrcodeCanvas" :style="{
            width: width + 'px',
            height: height + 'px',
            position: 'fixed',
            left: '-9999px'
        }"></canvas>
		<!-- 顶部栏 -->
		<view class="card-detail-header">
			<view class="card-detail-back" @click="goBack">
				<uni-icons type="back" size="24" color="#222" />
			</view>
			<text class="card-detail-title">名片详情</text>
			<view class="card-detail-more">
				<!-- <uni-icons color="#386BF6" type="redo" size="24" @click="handleShare(this.card)" style="margin-right: 24px;" /> -->
				<!-- <uni-icons type="more-filled" size="24" color="#222" @click="onMore"/> -->
				<uni-icons color="#386BF6" type="redo" size="24" @click="handleShare(this.card)"/>
			</view>
		</view>
		
		<!-- 可滑动内容区域 -->
		<scroll-view class="card-detail-content" scroll-y>
			<!-- 个人信息和联系方式合并板块 -->
			<view class="info-block block-common">
				<view class="info-section">
					<view class="avatar-container">
						<image :src="card.avatar || defaultAvatar" class="user-avatar" mode="aspectFill"></image>
					</view>
					<view class="gender-dot" :class="cardOwnerInfo.gender === 1 ? 'male' : 'female'"></view>
					<view class="user-info">
						<view class="name-role-container">
							<text class="user-name">{{card.name}}</text>
							<text v-if="card.roleName" class="user-role">{{card.roleName}}</text>
						</view>
						<text class="company-name">{{card.company || '暂无公司'}}</text>
						<view class="rating-container" @click="goComment">
							<text class="rating-text">评分:</text>
							<uni-icons type="star-filled" size="14" color="#FFD700"></uni-icons>
							<text class="rating-text">{{ userScore }}分</text>
						</view>
					</view>
					<!-- <view class="switch-btn">
						<text class="switch-btn-text">切换</text>
					</view> -->
				</view>
				
				<!-- 水平滚动的联系方式 -->
				<scroll-view class="contact-scroll" scroll-x show-scrollbar="false">
					<view class="contact-list">
						<!-- 必须显示的联系方式 -->
						<view class="contact-item" @tap="showContactInfo('phone')">
							<uni-icons type="phone" size="24" color="#007AFF"></uni-icons>
							<text class="platform-name">电话</text>
						</view>
						<view class="contact-item" @tap="showContactInfo('wechat')">
							<uni-icons type="weixin" size="24" color="#07C160"></uni-icons>
							<text class="platform-name">微信</text>
						</view>
						<view class="contact-item" @tap="gotoGroups">
							<uni-icons type="staff" size="24" color="#007AFF"></uni-icons>
							<text class="platform-name">社群</text>
						</view>
						
						<!-- 条件显示的联系方式 -->
						<view class="contact-item" v-if="card.platformIndex?.douyin" @tap="showContactInfo('douyin')">
							<image class="platform-icon" src="/static/image/icon/douyin1.png" mode="widthFix"></image>
							<text class="platform-name">抖音</text>
						</view>
						<view class="contact-item" v-if="card.platformIndex?.xiaohongshu" @tap="showContactInfo('xiaohongshu')">
							<image class="platform-icon" src="/static/image/icon/xiaohongshu.png" mode="widthFix"></image>
							<text class="platform-name">小红书</text>
						</view>
						<view class="contact-item" v-if="card.platformIndex?.kuaishou" @tap="showContactInfo('kuaishou')">
							<image class="platform-icon" src="/static/image/icon/kuaishou.png" mode="widthFix"></image>
							<text class="platform-name">快手</text>
						</view>
						<view class="contact-item" v-if="card.platformIndex?.weibo" @tap="showContactInfo('weibo')">
							<image class="platform-icon" src="/static/image/icon/weibo.png" mode="widthFix"></image>
							<text class="platform-name">微博</text>
						</view>
						<view class="contact-item" v-if="card.platformIndex?.toutiao" @tap="showContactInfo('toutiao')">
							<image class="platform-icon" src="/static/image/icon/toutiao.png" mode="widthFix"></image>
							<text class="platform-name">今日头条</text>
						</view>
						<view class="contact-item" v-if="card.platformIndex?.gongzhonghao" @tap="showContactInfo('gongzhonghao')">
							<image class="platform-icon" src="/static/image/icon/gongzhonghao.png" mode="widthFix"></image>
							<text class="platform-name">公众号</text>
						</view>
					</view>
				</scroll-view>
			</view>
			
			<!-- 信息展示模块 -->
			<view class="info-display-block block-common">
				<!-- 标签切换区域 -->
				<view class="tab-container">
					<view 
						class="tab-item" 
						v-for="(item, index) in tabs" 
						:key="index"
						:class="{ active: currentTab === index }"
						@click="switchTab(index)"
					>
						<text class="tab-text">{{ item }}</text>
						<view class="tab-line" v-if="currentTab === index"></view>
					</view>
				</view>
				
				<!-- 内容区域 -->
				<view class="content-container">
					<template v-for="(item, index) in currentContent" :key="index">
						<!-- 文本内容 -->
						<view v-if="item.type === 'text'" class="content-text">
							{{ item.content }}
						</view>
						<!-- 图片内容 -->
						<image
							v-else-if="item.type === 'image'"
							:src="item.url"
							class="content-image"
							mode="widthFix"
							@tap="previewImage(item.url)"
							@load="onImageLoad"
						/>
						<!-- 视频内容 -->
						<nVideo
							v-else-if="item.type === 'video'"
							:src="item.url"
							:poster="item.url + '?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'"
							:object-fit="'contain'"
						/>
					</template>
				</view>
			</view>
			
			<!-- 风采展示板块 -->
			<view v-if="card.url && card.url.length > 0" class="style-image-list">
				<image 
					v-for="(img, idx) in card.url" 
					:key="idx" 
					:src="img" 
					class="style-image" 
					mode="aspectFill"
					@tap="previewImage(img)"
				/>
			</view>
			
			<!-- 留言区域 -->
			<!-- <view class="message-block block-common">
				
				<view class="message-header">
					<text class="message-title">留言</text>
					<text class="message-record" @click="gotoCommentRecord">记录</text>
				</view>
				
				
				<view class="message-input-container">
					<textarea
						class="message-input"
						placeholder="文明交流，共创绿色生态......请输入留言内容"
						v-model="messageContent"
						:maxlength="500"
					></textarea>
				</view>
			</view>

			<view class="message-actions">
				<text class="action-text submit" @tap="handleSubmitMessage">提交</text>
			</view>
			<view class="message-actions">
				<text class="action-text report" @tap="handleReport">投诉与举报</text>
			</view> -->
		</scroll-view>

		<!-- 添加电话号码弹窗 -->
		<view class="phone-modal" v-if="showPhoneModal">
			<view class="modal-mask" @tap="closePhoneModal"></view>
			<view class="modal-content">
				<view class="modal-title">电话号码</view>
				<view class="modal-phone">{{phoneNumber || '未设置'}}</view>
				<view class="modal-btns">
					<button class="modal-btn primary" @tap="handleCall">联系Ta</button>
					<button class="modal-btn primary" @tap="handleCopy">复制</button>
					<button class="modal-btn" @tap="closePhoneModal">关闭</button>
				</view>
			</view>
		</view>
		<view class="phone-modal" v-if="showWechatModal">
			<view class="modal-mask" @tap="closeWechatModal"></view>
			<view class="modal-content">
				<view class="modal-title">微信</view>
				<view class="modal-phone">{{wechatId || '未设置'}}</view>
				<view class="modal-btns">
					<button class="modal-btn primary" @tap="handleCopy">复制</button>
					<button class="modal-btn" @tap="closeWechatModal">关闭</button>
				</view>
			</view>
		</view>

		<!-- 其他联系方式通用弹窗 -->
		<view class="phone-modal" v-if="showContactModal">
			<view class="modal-mask" @tap="closeContactModal"></view>
			<view class="modal-content">
				<view class="modal-title">{{currentContact.title}}</view>
				<view class="modal-phone">{{currentContact.value || '未设置'}}</view>
				<!-- <a :href="currentContact.value" class="modal-links">{{currentContact.value || '未设置'}}</a> -->
				<view class="other-modal-btns">
					<button class="modal-btn primary" @tap="handleContactCopy">复制</button>
					<button class="modal-btn" @tap="closeContactModal">关闭</button>
				</view>
			</view>
		</view>

		<!-- 弹框 -->
        <uni-popup ref="morePopup" borderRadius='20px 20px 0 0 ' background-color="#fff" @change="handlePopupChange" z-index="1000">
            <view class="top-line"></view>
            <view class="pop-op">
                <view class="col-flex" @click="handleShare(this.card)">
                    <image src="/static/image/icon/fenxiang.png" style="width: 30px; height: 30px;" mode=""></image>
                    <text>分享</text>
                </view>
				<view class="col-flex" @click="handleReport">
					<image src="/static/image/icon/jubao.png" style="width: 30px; height: 30px;" mode=""></image>
					<text>举报</text>
				</view>
            </view>
        </uni-popup>

		<!-- 分享弹窗 -->
        <uni-popup ref="share" type="share" safeArea backgroundColor="#fff">
            <uniPopupShareCard title="名片分享" description="分享你的数字名片，链接更多的人" @select="shareselect"></uniPopupShareCard>
        </uni-popup>

		<view class="rq-tc" v-if="iserweima" @click="closeQRCode">
            <view class="tc" @click.stop>
                <!-- 个人信息区域：与主页面一致 -->
                <view class="info-section" style="width: 100%; margin-top: 24px; margin-bottom: 16px; padding-left: 12px;">
                    <!-- 头像 -->
                    <view class="avatar-container" style="width: 50px; height: 50px;">
                        <image :src="card.avatar || defaultAvatar" class="user-avatar" mode="aspectFill"></image>
                    </view>
                    <!-- 性别 -->
                    <view class="gender-dot" :class="cardOwnerInfo.gender === 1 ? 'male' : 'female'"></view>
                    <!-- 详情 -->
                    <view class="user-info" style="gap: 4px;">
                        <view class="name-role-container">
                            <text class="user-name">{{card.name}}</text>
                            <text v-if="card.roleName" class="user-role">{{card.roleName}}</text>
                        </view>
                        <text class="company-name">{{card.company || '暂无公司'}}</text>
                        <view class="rating-container" style="margin-top: 0px; gap: 0px;">
                            <text class="rating-text">评分:</text>
                            <uni-icons type="star-filled" size="14" color="#FFD700"></uni-icons>
                            <text class="rating-text">{{ userScore }}分</text>
                        </view>
                    </view>
                </view>
                <!-- 二维码区域 -->
                <view>
                    <view class="qr-wrap">
                        <view class="qr-box">
                            <uqrcode ref="uqrcode" canvas-id="qrcode-canvas" :value="qrCodeConfig.text" :options="{
                                margin: 0,
                                size: 180,
                                backgroundColor: qrCodeConfig.backgroundColor,
                                foregroundColor: qrCodeConfig.foregroundColor,
                                foregroundImageSrc: qrCodeConfig.logo,
                                foregroundImagePadding: 10,
                                logo: qrCodeConfig.logo,
                                logoSize: qrCodeConfig.logoSize
                            }">
                            </uqrcode>
                        </view>
                        <text class="scan-tip">扫一扫上面的二维码图案，查看我的名片</text>
                    </view>
                </view>

                <!-- 底部按钮区域 -->
                <view class="action-buttons">
                    <!-- <view class="action-btn" @click="handleScan">
                        <text>扫一扫</text>
                    </view> -->
                    <view class="refresh-btn" @click="showQrcode">
                        <text>换个样式</text>
                    </view>
                    <view class="action-btn" @click="handleSave">
                        <text>保存图片</text>
                    </view>

                </view>
            </view>
        </view>
    </view>
</template>

<script>
import appServerApi from '@/api/appServerApi'
import wfc from "../../wfc/client/wfc";
import nVideo from '@/pages/bar/nVideo.vue'
import uniPopupShareCard from '../../uni_modules/uni-popup/components/uni-popup-share/uni-popup-share-card.vue';
import store from '@/store'
import uQRCode from '@/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue'
import ColorUtil from '@/utils/colorUtil'

export default {
	components: {
		nVideo,
		uniPopupShareCard,
		uqrcode: uQRCode,
	},
	data() {
		return {
		card: {},
		userInfo: {},//当前登录用户的信息，并非是名片主的信息
		cardOwnerInfo: {},//名片主的个人信息
		userScore: 1.0,
		defaultAvatar: '/static/images/user.png',
		isMine: false,
		isFriend : false,
		tabs: ['简介', '资源', '需求'],
		currentTab: 0,
		scrollTop: 0,
		showPhoneModal: false,
		showWechatModal: false,
		showContactModal: false,
		phoneNumber: '',
		wechatId: '',
		currentContact: {
			title: '',
			value: ''
		},
		contactMapping: {
			phone: {
				title: '电话号码',
				field: 'phone'
			},
			wechat: {
				title: '微信号',
				field: 'wechatId'
			},
			douyin: {
				title: '抖音号',
				field: 'platformIndex.douyin'
			},
			group: {
				title: '社群',
				field: 'platformIndex.group'
			},
			xiaohongshu: {
				title: '小红书',
				field: 'platformIndex.xiaohongshu'
			},
			kuaishou: {
				title: '快手',
				field: 'platformIndex.kuaishou'
			},
			weibo: {
				title: '微博',
				field: 'platformIndex.weibo'
			},
			toutiao: {
				title: '今日头条',
				field: 'platformIndex.toutiao'
			},
			gongzhonghao: {
				title: '公众号',
				field: 'platformIndex.gongzhonghao'
			}
		},
		messageContent: '',
		//二维码相关
		iserweima: false,
		qrCodeConfig: {
			text: '',
			size: 200,
			margin: 10,
			backgroundColor: '#ffffff',
			foregroundColor: this.getRandomColor(),
			foregroundImageSrc: '/static/logo.png',
			foregroundImagePadding: 10,
			logo: '/static/logo.png',
			logoSize: 50,
		},
		options: {
			margin: 10,
		},
		systemInfo: uni.getSystemInfoSync(),
		width: 300,
        height: 500,
		}
	},
	computed: {
		currentContent() {
			let content = '';
			switch(this.currentTab) {
				case 0:
					content = this.card.description || '暂无简介';
					break;
				case 1:
					content = this.card.advantage || '暂无资源信息';
					break;
				case 2:
					content = this.card.defect || '暂无需求信息';
					break;
				default:
					content = '';
			}
			return this.parseContent(content);
		}
	},
	onBackPress() {
		// Implementation of onBackPress method
	},
	async onLoad(options) {
		if (options && options.card) {
		try {
			this.card = JSON.parse(decodeURIComponent(options.card));
		} catch (e) {
			this.card = {};
		}
		}
		// 获取系统信息以获取屏幕宽度
        const systemInfo = uni.getSystemInfoSync()
        this.width = systemInfo.windowWidth * 0.95

		console.log("CardDetails.vue页面名片详情页加载的数据是：",this.card)
		await this.fetchUserInfo();
		await this.getUserScore();
		await this.getOtherUserInfo(this.card.userId)

	},
	methods: {
		goBack() {
		uni.navigateBack();
		},

		onMore() {
			uni.showToast({ title: '更多功能加急开发中，敬请期待！', icon: 'none' });
			// this.$refs.morePopup.open('bottom')
		},

		goComment() {
            uni.navigateTo({
                url: '/pages/user/Comment?userId=' + this.card.userId + '&isComment=false&isMyFriend=true'
            })
        },

		gotoGroups() {
			uni.navigateTo({
				url: '/pages/card/Groups?cardId=' + this.card.id
			})
		},

		async fetchUserInfo() {
			try {
				const result = await appServerApi.getUserInfo()
				this.userInfo = { ...this.userInfo, ...result }
				// const userId = this.userInfo.userId
				// this.qrCodeConfig.text = `EARTHID:${userId}`
				console.log("名片详情页面获取到的userInfo",this.userInfo)
			} catch (error) {
				console.error('获取用户信息失败:', error)
				uni.showToast({
					title: '获取用户信息失败',
					icon: 'none',
				})
				throw error
			}
		},

		async getUserScore() {
            try {
                if (!this.card || !this.card.userId) {
                    console.log('用户信息未加载，暂不获取评分');
                    return;
                }
                // console.log('获取用户ID：', this.card.userId);
                const response = await appServerApi.getUserScore(this.card.userId)
                // console.log('获取用户评分：', response.data?.score);
                this.userScore = response.data?.score || 5.0
            } catch (error) {
                console.error("获取用户评分失败:", error)
            }
        },

		
		async getOtherUserInfo(userId) {
			appServerApi.getOtherUserInfo(userId)
				.then(response => {
					console.log('CardDetails.vue获取到的名片主的详细信息为:', response)
					this.cardOwnerInfo = response;
				})
				.catch(error => {
					console.error('获取用户信息失败:', error)
				})
		},

		switchTab(index) {
			this.currentTab = index;
			this.scrollTop = 0;
		},

		previewImage(url) {
			uni.previewImage({
				urls: [url],
				current: url
			});
		},

		onImageLoad() {
			// 图片加载完成后，可能需要重新计算scroll-view的高度
			this.$nextTick(() => {
				this.scrollTop = this.scrollTop;
			});
		},

		parseContent(content) {
			if (!content) return [];
			
			// 分割内容，以\n作为分隔符
			const parts = content.split('\\n');
			
			return parts.map(part => {
				// 判断是否是图片链接
				if (part.includes('.jpg') || part.includes('.png') || part.includes('.jpeg') || part.includes('.gif')) {
					return {
						type: 'image',
						url: part.trim()
					};
				}
				// 判断是否是视频链接
				else if (part.includes('.mp4') || part.includes('.mov') || part.includes('video')) {
					return {
						type: 'video',
						url: part.trim()
					};
				}
				// 普通文本
				else {
					return {
						type: 'text',
						content: part.trim()
					};
				}
			}).filter(item => item.type === 'text' ? item.content : true);
		},

		// 获取嵌套对象属性值的辅助方法
		getNestedValue(obj, path) {
			return path.split('.').reduce((current, key) => 
				current && current[key] !== undefined ? current[key] : '未设置', obj);
		},
		
		// 显示联系方式信息
		showContactInfo(type) {
			const config = this.contactMapping[type];
			if (!config) return;
			
			const value = this.getNestedValue(this.card, config.field);
			
			// 电话号码特殊处理
			if (type === 'phone') {
				this.phoneNumber = value;
				this.showPhoneModal = true;
				return;
			}
			if (type === 'wechat') {
				this.wechatId = value;
				this.showWechatModal = true;
				return;
			}
			

			// 其他联系方式使用通用弹窗
			this.currentContact = {
				title: config.title,
				value: value
			};
			this.showContactModal = true;
		},

		// 关闭电话弹窗
		closePhoneModal() {
			this.showPhoneModal = false;
		},

		// 关闭微信弹窗
		closeWechatModal() {
			this.showWechatModal = false;
		},

		// 关闭联系方式弹窗
		closeContactModal() {
			this.showContactModal = false;
		},

		// 复制电话号码
		handleCopy() {
			if (this.phoneNumber && this.phoneNumber !== '未设置') {
				uni.setClipboardData({
					data: this.phoneNumber,
					success: () => {
						uni.showToast({
							title: '已复制到剪贴板',
							icon: 'success'
						});
						this.closePhoneModal();
					}
				});
			}
		},

		// 复制其他联系方式
		handleContactCopy() {
			if (this.currentContact.value && this.currentContact.value !== '未设置') {
				uni.setClipboardData({
					data: this.currentContact.value,
					success: () => {
						uni.showToast({
							title: '已复制到剪贴板',
							icon: 'success'
						});
						this.closeContactModal();
					}
				});
			}
		},

		// 拨打电话
		handleCall() {
			if (this.phoneNumber && this.phoneNumber !== '未设置') {
				// 使用 plus.runtime.openURL 来打开拨号界面
				// tel: 协议会直接跳转到拨号界面而不是直接拨打
			} else {
				uni.showToast({
					title: '电话号码未设置',
					icon: 'none'
				});
			}
		},

		async handleSubmitMessage() {
			if (!this.messageContent.trim()) {
				uni.showToast({
					title: '请输入留言内容',
					icon: 'none'
				});
				return;
			}
			
			try {
				const params = {
					userId: this.userInfo.userId,
					cardId: this.card.id,
					content: this.messageContent
				};
				
				await appServerApi.publishCardComment(params);
				
				uni.showToast({
					title: '发布成功',
					icon: 'success'
				});
				
				// 清空留言内容
				this.messageContent = '';
				
			} catch (error) {
				console.error('发布留言失败:', error);
				uni.showToast({
					title: '发布失败',
					icon: 'none'
				});
			}
		},

		handleReport() {
			// Implementation of handleReport method
			uni.navigateTo({
                url: `/pages/complaint/ComplaintPage`,
            })
		},

		gotoCommentRecord() {
			
			uni.navigateTo({
				url: `/pages/card/CardComment?isMine=${this.isMine}&cardId=${this.card.id}&cardOwnerId=${this.card.userId}`
			});
		},

		handlePopupChange(e) {
            // 处理弹出框变化逻辑
        },

		//分享操作
		handleShare(card) {
            // this.currentPost = {
            //     topicId: this.article.topicId,
            //     name: this.article.userInfo?.displayName || '用户',
            //     content: this.article.content || '',
            //     avatar: this.article.userInfo?.avatar || '/static/logo.png',
            //     title: this.article.title
            // };
			this.currentCard = card
			console.log("待分享名片信息为:",this.currentCard)
            this.$refs.share.open();
        },

		// 分享选择处理
        shareselect(e) {
            console.log('分享选择:', e);
            console.log('当前名片:', this.currentCard);
            let title = (this.currentCard?.name ? this.currentCard.name : '地球岛') + "的数字名片";
            if (e.index === 0) {
                // 微信好友
                var obj = {
                    provider: 'weixin',
                    type: 0,
                    title: title,
                    summary: this.currentCard?.introduction ? this.currentCard.introduction : "您收到了一张来自地球岛的数字名片！",//使用"一句话介绍自己"占位
                    imageUrl: this.currentCard.avatar,
                    scene: 'WXSceneSession',
                    cardId: this.currentCard.id,
                };
                store.shareCard(obj);
				console.log("点击了分享到微信")
            }
            if (e.index === 1) {
                // 微信朋友圈
                var obj = {
                    provider: 'weixin',
                    type: 0,
                    title: title,
                    summary: this.currentCard?.introduction ? this.currentCard.introduction : "您收到了一张来自地球岛的数字名片！",
                    imageUrl: this.currentCard.avatar,
                    scene: 'WXSceneTimeline',
                    cardId: this.currentCard.id,
                };
                store.shareCard(obj);
				console.log("点击了分享到微信朋友圈")
            }
            if (e.index === 2) {
                // 发送给好友
                // 将分享数据存储到全局store中
                store.setShareData(this.currentCard);
                uni.navigateTo({
                    url: '/pages/conversationList/ChooseChat',
                });
                return;
            }
            // 添加复制链接功能
            if (e.index === 3) {
                this.copyPostLink();
				// console.log("点击了复制链接")
                return;
            }

			// 添加复制链接功能
            if (e.index === 4) {
                // this.copyPostLink();
				this.openQRCode()
				console.log("点击了保存二维码")
                return;
            }
        },

		//复制名片链接
		copyPostLink() {
            if (!this.currentCard || !this.currentCard.id) {
                uni.showToast({
                    title: '名片信息不完整',
                    icon: 'none'
                });
                return;
            }
            
            // 构建名片链接
            const postUrl = `https://web.ykjrhl.com/share/card.html?cardId=${this.currentCard.id}`;
            
            // 使用uni.setClipboardData复制到剪贴板
            uni.setClipboardData({
                data: postUrl,
                success: () => {
                    uni.showToast({
                        title: '链接已复制',
                        icon: 'success',
                        duration: 2000
                    });
                },
                fail: (err) => {
                    console.error('复制失败:', err);
                    uni.showToast({
                        title: '复制失败',
                        icon: 'none'
                    });
                }
            });
        },

		//二维码相关
		openQRCode() {
            this.iserweima = true
            this.qrCodeConfig.text = `https://web.ykjrhl.com/share/card.html?cardId=${this.card.id}`
            // this.updateQRCodeLogo()
            this.showQrcode()
        },
        closeQRCode() {
            this.iserweima = false
        },

		// 更新二维码中的头像
        updateQRCodeLogo() {
            if (this.userInfo.avatar) {
                console.log('card.avatar:', this.card.avatar)
                this.qrCodeConfig.foregroundImageSrc = this.card.avatar
                this.qrCodeConfig.logo = this.card.avatar
            } else {
                console.log('updateQRCodeLogo: 没有头像')
                this.qrCodeConfig.foregroundImageSrc = '/static/logo.png'
                this.qrCodeConfig.logo = '/static/logo.png'
            }
        },

		async showQrcode() {
            console.log('showQrcode')
            return new Promise((resolve) => {
                this.qrCodeConfig.foregroundColor = this.getRandomColor()
                this.$nextTick(() => {
                    if (this.$refs.uqrcode) {
                        this.$refs.uqrcode.make();
                        setTimeout(() => {
                            resolve();
                        }, 500);
                    } else {
                        resolve();
                    }
                });
            });
        },

		// 生成随机颜色
        getRandomColor() {
            return ColorUtil.getSmartColor()
        },

		async handleSave() {
            try {
                // iOS或已获得权限，直接保存
                await this.saveQRCodeToAlbum();
                
            } catch (error) {
                uni.hideLoading();
                console.error('保存二维码失败:', error);
                uni.showToast({
                    title: '保存失败: ' + (error?.message || '未知错误'),
                    icon: 'none',
                    duration: 2000
                });
            }
        },
        
        // 将二维码保存到相册的具体实现
        async saveQRCodeToAlbum() {
            uni.showLoading({ title: '保存中...' });
            try {
                // 等待二维码生成
                await new Promise((resolve) => { setTimeout(resolve, 800); });
                // 画布参数
                const canvasWidth = this.systemInfo.windowWidth * 0.95;
                const padding = 50; // 增大左右留白
                const infoHeight = 50; // 个人信息区高度
                const qrCardMarginTop = 30;//二维码上边距 原本为60
                const qrCardRadius = 32;//二维码圆弧
                const qrCardShadow = 6;//二维码阴影
                const qrSize = 240; // 二维码大小
                const tipHeight = 50;//提示高度
                // // 内容区总高度
                const contentHeight = infoHeight + qrCardMarginTop + qrSize + tipHeight;
                // 画布高度适当增加留白
                const canvasHeight = 500;

                // 内容区起始Y坐标（使内容整体垂直居中）
                const contentStartY = (canvasHeight - contentHeight) / 2;
                const ctx = uni.createCanvasContext('qrcodeCanvas', this);
                // 背景
                ctx.setFillStyle('#fff');
                ctx.fillRect(0, 0, canvasWidth, canvasHeight);
				//个人区域
                // 头像（圆角8px矩形）
                const avatarSize = 50;
                const avatarX = padding;
                const avatarY = contentStartY + (infoHeight - avatarSize) / 2;
                ctx.save();
                const avatarRadius = 8;
                ctx.beginPath();
                ctx.moveTo(avatarX + avatarRadius, avatarY);
                ctx.lineTo(avatarX + avatarSize - avatarRadius, avatarY);
                ctx.arcTo(avatarX + avatarSize, avatarY, avatarX + avatarSize, avatarY + avatarRadius, avatarRadius);
                ctx.lineTo(avatarX + avatarSize, avatarY + avatarSize - avatarRadius);
                ctx.arcTo(avatarX + avatarSize, avatarY + avatarSize, avatarX + avatarSize - avatarRadius, avatarY + avatarSize, avatarRadius);
                ctx.lineTo(avatarX + avatarRadius, avatarY + avatarSize);
                ctx.arcTo(avatarX, avatarY + avatarSize, avatarX, avatarY + avatarSize - avatarRadius, avatarRadius);
                ctx.lineTo(avatarX, avatarY + avatarRadius);
                ctx.arcTo(avatarX, avatarY, avatarX + avatarRadius, avatarY, avatarRadius);
                ctx.closePath();
                ctx.clip();
                ctx.drawImage(this.card.avatar || this.defaultAvatar, avatarX, avatarY, avatarSize, avatarSize);
                ctx.restore();
                // 性别点、姓名、角色 水平居中对齐
                const genderDotSize = 8;
                const genderDotX = avatarX + avatarSize + 14;
                const genderDotY = avatarY + genderDotSize / 2;
                ctx.setFillStyle(this.cardOwnerInfo.gender === 1 ? '#007AFF' : '#FF2D55');
                ctx.beginPath();
                ctx.arc(genderDotX + genderDotSize / 2, genderDotY + genderDotSize / 2, genderDotSize / 2, 0, Math.PI * 2);
                ctx.fill();
                // 姓名、角色
                ctx.setFontSize(16);
                ctx.setFillStyle('#222');
                ctx.setTextAlign('left');
                const nameX = genderDotX + genderDotSize + 8;
                // 姓名Y坐标与性别点垂直居中
                const nameY = genderDotY + genderDotSize / 2 + 5; // 向下微调
                ctx.fillText(this.card.name || '', nameX, nameY);
                // 角色标签
                let roleX = nameX + ctx.measureText(this.card.name || '').width + 10;
                if (this.card.roleName) {
                    ctx.setFontSize(14);
                    ctx.setFillStyle('#386BF6');
                    const roleTextWidth = ctx.measureText(this.card.roleName).width;
                    const roleBgWidth = roleTextWidth + 20;
                    const roleBgHeight = 24;
                    const roleBgX = roleX;
                    const roleBgY = nameY - 16;
                    // 圆角矩形背景
                    ctx.save();
                    ctx.beginPath();
                    const r = 12;
                    ctx.moveTo(roleBgX + r, roleBgY);
                    ctx.lineTo(roleBgX + roleBgWidth - r, roleBgY);
                    ctx.arcTo(roleBgX + roleBgWidth, roleBgY, roleBgX + roleBgWidth, roleBgY + r, r);
                    ctx.lineTo(roleBgX + roleBgWidth, roleBgY + roleBgHeight - r);
                    ctx.arcTo(roleBgX + roleBgWidth, roleBgY + roleBgHeight, roleBgX + roleBgWidth - r, roleBgY + roleBgHeight, r);
                    ctx.lineTo(roleBgX + r, roleBgY + roleBgHeight);
                    ctx.arcTo(roleBgX, roleBgY + roleBgHeight, roleBgX, roleBgY + roleBgHeight - r, r);
                    ctx.lineTo(roleBgX, roleBgY + r);
                    ctx.arcTo(roleBgX, roleBgY, roleBgX + r, roleBgY, r);
                    ctx.closePath();
                    ctx.setFillStyle('#386BF6');
                    ctx.fill();
                    ctx.restore();
                    // 角色文字
                    ctx.setFontSize(14);
                    ctx.setFillStyle('#fff');
                    ctx.fillText(this.card.roleName, roleBgX + 10, nameY);
                }
                // 公司与评分同行
                ctx.setFontSize(14);
                ctx.setFillStyle('#666');
                const companyY = nameY + 25;
                ctx.fillText(this.card.company || '暂无公司', nameX, companyY);
                // 评分
                ctx.setFontSize(14);
                ctx.setFillStyle('#666');
                const scoreLabel = '评分:';
                // const companyWidth = ctx.measureText(this.card.company || '暂无公司').width;
                const scoreX = nameX ;
                const scoreY = companyY + 20
                ctx.fillText(scoreLabel, scoreX, scoreY);
                // 星星
                ctx.drawImage('/static/image/icon/star-filled.png', scoreX + 40, companyY + 8, 14, 14);
                // 分数颜色改为黑色
                ctx.setFillStyle('#222');
                ctx.setFontSize(12);
                ctx.fillText((this.userScore || 5) + '分', scoreX + 60, scoreY);
                // 二维码卡片区域（带圆角阴影）
                const qrCardX = (canvasWidth - qrSize) / 2 - 20;
                const qrCardY = contentStartY + infoHeight + qrCardMarginTop;
                const qrCardW = qrSize + 40;
                const qrCardH = qrSize + 40;
                ctx.save();
                ctx.setShadow && ctx.setShadow(0, 8, qrCardShadow, 'rgba(0,0,0,0.08)');
                ctx.beginPath();
                const r = qrCardRadius;
                ctx.moveTo(qrCardX + r, qrCardY);
                ctx.lineTo(qrCardX + qrCardW - r, qrCardY);
                ctx.arcTo(qrCardX + qrCardW, qrCardY, qrCardX + qrCardW, qrCardY + r, r);
                ctx.lineTo(qrCardX + qrCardW, qrCardY + qrCardH - r);
                ctx.arcTo(qrCardX + qrCardW, qrCardY + qrCardH, qrCardX + qrCardW - r, qrCardY + qrCardH, r);
                ctx.lineTo(qrCardX + r, qrCardY + qrCardH);
                ctx.arcTo(qrCardX, qrCardY + qrCardH, qrCardX, qrCardY + qrCardH - r, r);
                ctx.lineTo(qrCardX, qrCardY + r);
                ctx.arcTo(qrCardX, qrCardY, qrCardX + r, qrCardY, r);
                ctx.closePath();
                ctx.setFillStyle('#fff');
                ctx.fill();
                ctx.restore();
                // 获取二维码图片
                const qrcode = await new Promise((resolve, reject) => {
                    uni.canvasToTempFilePath({
                        canvasId: 'qrcode-canvas',
                        success: (res) => {
                            if (!res.tempFilePath) {
                                reject(new Error('二维码生成失败，未获取到图片路径'));
                            } else {
                                resolve(res.tempFilePath);
                            }
                        },
                        fail: (err) => {
                            reject(new Error('二维码生成失败: ' + (err && err.errMsg ? err.errMsg : '未知错误')));
                        },
                    }, this.$refs.uqrcode);
                });
                // 绘制二维码
                const qrX = (canvasWidth - qrSize) / 2;
                const qrY = qrCardY + 20;
                ctx.drawImage(qrcode, qrX, qrY, qrSize, qrSize);
                // 提示文字
                ctx.setFillStyle('#666666');
                ctx.setFontSize(12);
                ctx.setTextAlign('center');
                ctx.fillText('扫一扫上面的二维码图案，查看我的名片', canvasWidth / 2, qrY + qrSize + 56);
                // 执行绘制
                await new Promise((resolve) => { ctx.draw(false, resolve); });
                // 保存图片
                const tempFilePath = await new Promise((resolve, reject) => {
                    uni.canvasToTempFilePath({
                        canvasId: 'qrcodeCanvas',
                        success: (res) => resolve(res.tempFilePath),
                        fail: reject
                    });
                });
                await new Promise((resolve, reject) => {
                    uni.saveImageToPhotosAlbum({
                        filePath: tempFilePath,
                        success: () => {
                            uni.hideLoading();
                            uni.showToast({
                                title: '保存成功',
                                icon: 'success',
                                duration: 2000
                            });
                            this.iserweima = false;
                            resolve();
                        },
                        fail: (err) => {
                            console.error('保存到相册失败:', err);
                            reject(new Error('保存到相册失败'));
                        }
                    });
                });
            } catch (error) {
                uni.hideLoading();
                throw error;
            }
        },
	},
}
</script>

<style scoped>
.card-detail-container {
  min-height: 100vh;
  background: #f8f9fb;
  display: flex;
  flex-direction: column;
}
.card-detail-header {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding-top: 40px;
  box-sizing: border-box;
  z-index: 3100;
}
.card-detail-back {
  position: absolute;
  left: 16px;
  top: 40px;
  height: 36px;
  display: flex;
  align-items: center;
}
.card-detail-title {
  font-size: 18px;
  color: #222;
}
.card-detail-more {
  position: absolute;
  right: 16px;
  top: 40px;
  height: 36px;
  display: flex;
  align-items: center;
}

.card-detail-content {
  flex: 1;
  margin-top: 80px;
  padding: 10px;
  box-sizing: border-box;
}

.block-common {
  background: #fff;
  border-radius: 12px;
  padding: 8px;
  margin-bottom: 10px;
}

.info-block {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.info-section {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.avatar-container {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar {
  width: 100%;
  height: 100%;
}

.gender-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 6px;
}

.male {
  background: #007AFF;
}

.female {
  background: #FF2D55;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.name-role-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-size: 16px;
  font-weight: 500;
  color: #222;
}

.user-role {
  padding: 2px 8px;
  background: #007AFF;
  border-radius: 10px;
  font-size: 12px;
  color: #fff;
}

.company-name {
  font-size: 14px;
  color: #666;
}

.rating-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-text {
  font-size: 12px;
  color: #666;
}

.switch-btn {
  padding: 4px;
  margin-top: 2px;
  flex-shrink: 0;
}

.contact-scroll {
  width: 100%;
  white-space: nowrap;
}

.contact-list {
  display: inline-flex;
  padding: 4px 0;
}

.contact-item {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  padding: 0 21px;
  min-width: fit-content;
}

.contact-item:not(:last-child) {
  border-right: 1px solid #f0f0f0;
}

.platform-name {
  font-size: 14px;
  color: #333;
}

.info-display-block {
  display: flex;
  flex-direction: column;
  padding: 8px;
  background: #fff;
}

.tab-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 0;
  height: 40px;
  flex-shrink: 0;
}

.tab-item {
  position: relative;
  height: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 80px;
}

.tab-item:first-child {
  align-items: center;
}

.tab-item:last-child {
  align-items: center;
}

.tab-text {
  font-size: 15px;
  color: #333;
  transition: all 0.3s;
}

.active .tab-text {
  color: #007AFF;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  width: 20px;
  height: 2px;
  background: #007AFF;
  border-radius: 1px;
}

.tab-item:first-child .tab-line {
  left: 50%;
  transform: translateX(-50%);
}

.tab-item:nth-child(2) .tab-line {
  left: 50%;
  transform: translateX(-50%);
}

.tab-item:last-child .tab-line {
  right: 50%;
  left: auto;
  transform: translateX(50%);
}

.content-container {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: #F8F8F8;
  border-radius: 8px;
}

.content-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  word-break: break-all;
  white-space: pre-wrap;
}

.content-image {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.content-video {
  width: 100%;
  border-radius: 8px;
}

.style-display-block {
  margin: 10px 16px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  padding: 16px;
}

.style-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.style-bar {
  width: 4px;
  height: 16px;
  background: #fd3c3c;
  border-radius: 2px;
  margin-right: 6px;
}

.style-title {
  font-size: 15px;
  color: #222;
  font-weight: 500;
}

.style-image-list {
  margin: 16px 0 0 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.style-image {
  width: 100%;
  aspect-ratio: 9/16;
  border-radius: 12px;
  background: #f5f5f5;
  object-fit: cover;
}

.platform-icon {
  width: 24px;
  height: 24px;
}

/* 电话号码弹窗样式 */
.phone-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
}

.modal-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
}

.modal-content {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    background: #fff;
    border-radius: 12px;
    padding: 20px;
}

.modal-title {
    font-size: 16px;
    color: #333;
    text-align: center;
    margin-bottom: 16px;
}

.modal-phone {
    font-size: 20px;
    color: #333;
    text-align: center;
    margin-bottom: 24px;
}

.modal-links {
    font-size: 20px;
    color: #333;
    text-align: center;
    margin-bottom: 24px;
    /* text-decoration: none; */
}

.modal-btns {
    display: flex;
    justify-content: space-around;
    gap: 12px;
}

.other-modal-btns {
    display: flex;
    justify-content: space-around;
    gap: 12px;
}

.modal-btn {
    flex: 1;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 20px;
    font-size: 14px;
    background: #f5f5f5;
    color: #333;
    /* border: solid 1px #f5f5f5; */
}

.modal-btn.primary {
    background: #007AFF;
    color: #fff;
}

.message-block {
  margin-top: 16px;
  padding: 16px;
  background: #fff;
  border-radius: 12px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 8px;
}

.message-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.message-record {
  font-size: 14px;
  color: #666;
}

.message-input-container {
  margin-bottom: 16px;
  background: #F8F8F8;
  border-radius: 8px;
  padding: 12px 12px 0px 12px;
}

.message-input {
  width: 100%;
  min-height: 80px;
  background: transparent;
  border: none;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}

.message-input::placeholder {
  color: #999;
  font-size: 14px;
}

.message-actions {
  display: grid;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.action-text {
  font-size: 14px;
  color: #666;
  padding: 0 12px;
}

.action-text.submit {
  color: #007AFF;
}

.action-text.report {
  color: #666;
}

.action-divider {
  font-size: 14px;
  color: #666;
  margin: 0 4px;
}

.top-line {
    width: 38px;
    height: 4px;
    margin: 13px auto 0;
    border-radius: 10px;
    background: #e7e7e7;
}

.pop-op {
    display: flex;
    gap: 30px;
    padding: 23px 20px;
    font-size: 12px;

    .col-flex {
        gap: 8px;
        text-align: center;
    }

    image {
        width: 48px;
        height: 48px;
    }
}

.rq-tc {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tc {
    position: relative;
    width: 80%;
    height: auto !important;
    background: #fff;
    border-radius: 12px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: center;
    .tcc {
        margin-top: 0 !important;
    }
    .tcc1 {
        width: 50px !important;
        height: 50px !important;
    }
}

.qr-section {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;

    .section-title {
        font-size: 16px;
        color: #333;
        margin-bottom: 20px;
        text-align: center;
    }
}

.qr-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;

    .qr-box {
        background: #fff;
        padding: 15px;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        margin-bottom: 15px;
    }

    .scan-tip {
        font-size: 12px;
        color: #999999;
        text-align: center;
    }
}

.action-buttons {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    .action-btn {
        color: #386bf6;
        font-size: 14px;
    }
    .refresh-btn {
        color: #386bf6;
        font-size: 14px;
        /* padding: 0 20px;
        margin: 0 20px; */
		padding-right: 20px;
		margin-right: 20px;
        /* border-left: 1px solid #efefef; */
        border-right: 1px solid #efefef;
    }
}
</style>


