<template>
	<view class="activity-main-bg earnings-wrap">
		<CustomHeader>
			<template v-slot:title>
				<text class="title"> 收益记录 </text>
			</template>
		</CustomHeader>
		<view class="list-block">
			<view class="table-header">
				<text>用户</text>
				<text>活动</text>
				<text>报名费</text>
			</view>
			<view class="list-data">
				<view class="list-item">
					<image src="../../assets/images/find/test.png" mode=""></image>
					<view class="item-right">
						<text>全力詹</text>
						<text>全力詹</text>
						<text>￥200</text>
					</view>
				</view>
				<view class="list-item">
					<image src="../../assets/images/find/test.png" mode=""></image>
					<view class="item-right">
						<text>全力詹</text>
						<text>全力詹</text>
						<text>￥200</text>
					</view>
				</view>
			</view>
			<uni-load-more status="more"></uni-load-more>
		</view>
	</view>
</template>

<script>
	import CustomHeader from '../../components/custom-header'
	export default {
		components: {
			CustomHeader,
		},
		data() {
			return {

			}
		},
		methods: {

		}
	}
</script>

<style lang="scss" scoped>
	.earnings-wrap {
		padding: 20px 12px;
		height: 100vh;
	}

	.list-block {
		background-color: #fff;
		border-radius: 10px 10px 0px 0px;

	}

	.table-header {
		display: flex;
		justify-content: space-between;
		height: 44px;
		padding: 0 16px;
		line-height: 44px;
		font-size: 16px;
		font-weight: bold;
		background: #F5F5F5;


	}

	.list-item {
		display: flex;
		align-items: center;
		gap: 14px;
		padding: 12px 16px;

		image {
			width: 38px;
			height: 38px;
			border-radius: 8px;
			overflow: hidden;
		}

		.item-right {
			flex: auto;
			font-size: 16px;
			padding: 15px 0;

			display: flex;
			justify-content: space-between;
			border-bottom: 1px solid #EFEFEF
		}
	}
</style>