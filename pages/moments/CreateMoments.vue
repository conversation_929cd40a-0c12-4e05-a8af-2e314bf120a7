<template>
  <view class="container">
    <CustomHeader>
      <template v-slot:title>
        <text>发表动态</text>
      </template>
      <template v-slot:right>
          <text 
            v-if="content.trim() || images.length > 0 || video"
            :class="['publish-btn', 'active']" 
            @click="handlePublish"
          >发布</text>
          <text 
            v-else
            class="publish-btn disabled"
          >发布</text>
      </template>
    </CustomHeader>

    <!-- 内容区域 -->
    <view class="content-area">
      <textarea
        class="content-input"
        v-model="content"
        placeholder="这一刻的想法..."
        :maxlength="maxLength"
        auto-height
      />
    </view>

    <!-- 底部功能区 -->
<!--    <view class="bottom-section">-->
<!--      <view class="location-section" @click="handleLocation">-->
<!--        <view class="location-icon">-->
<!--          <image src="/static/location.png" mode="aspectFit" />-->
<!--        </view>-->
<!--        <text class="location-text">{{location || '所在位置' }}</text>-->
<!--        <view class="arrow-icon">></view>-->
<!--      </view>-->
<!--    </view>-->

    <!-- 图片/视频选择区域 -->
    <view class="image-picker">
      <view class="image-list">
        <!-- 视频预览 -->
        <view v-if="video" class="video-container">
          <view class="video-item" :class="getVideoClass()">
            <video 
              :src="video" 
              class="preview-video"
              :style="getVideoStyle()"
              controls
              :show-center-play-btn="true"
              :enable-progress-gesture="true"
              :show-fullscreen-btn="true"
              object-fit="contain"
              :play-btn-position="'center'"
              :enable-play-gesture="true"
              :show-play-btn="true"
              :initial-time="0.1"
              :show-loading="true"
              :custom-cache="false"
              :vslide-gesture="true"
              :vslide-gesture-in-fullscreen="true"
            />
          </view>
          <view class="video-delete-btn" @click="deleteVideo">
            <text class="delete-icon">×</text>
          </view>
        </view>
        
        <!-- 已选择的图片 -->
        <template v-else>
          <view v-for="(image, index) in images" :key="index" class="image-item">
            <image :src="image" mode="aspectFill" class="preview-image" @click="previewImage(images, index)" />
            <view class="delete-btn" @click="deleteImage(index)">
              <text class="delete-icon">×</text>
            </view>
          </view>
        </template>
        
        <!-- 添加媒体按钮 -->
        <view v-if="!video && images.length === 0" class="add-media" @click="showMediaPicker">
          <text class="plus-icon">+</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import appServerApi from '@/api/appServerApi'
import CustomHeader from '@/components/custom-header'
import topMessage from '@/common/topMessageView'

export default {
  components: {
    CustomHeader
  },
  data() {
    return {
      statusBarHeight: 0, // 状态栏高度
      content: '',
      maxLength: 1000,
      location: '',
      locationInfo: {
        address: '',
        longitude: '',
        latitude: ''
      },
      visibility: '公开',
      images: [],
      video: '', // 添加视频URL
      lastGroup: '领导',
      uploading: false,
      videoRatio: null, // 添加视频宽高比存储
    }
  },
  onLoad() {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
  },
  methods: {
    
    async handlePublish() {
      const startTime = Date.now();
      console.log(`[handlePublish] 开始发布:`, startTime);

      if (!this.content.trim() && this.images.length === 0 && !this.video) {
        uni.showToast({
          title: '请输入内容或选择图片/视频',
          icon: 'none'
        })
        return
      }

      if (this.uploading) return
      this.uploading = true

      try {
        uni.showLoading({ title: '发布中...' })

        // 如果有图片，先上传图片
        let imageUrls = []
        if (this.images.length > 0) {
          const uploadStartTime = Date.now();
          console.log(`[handlePublish] 开始上传图片:`, uploadStartTime, '图片数量:', this.images.length);
          
          try {
            imageUrls = await Promise.all(
              this.images.map(async (image) => {
                return new Promise((resolve, reject) => {
                  appServerApi.uploadimgFile(
                    image,
                    (res) => {
                      if (res && res.data) {
                        resolve(res.data)
                      } else {
                        reject(new Error('上传返回数据异常'))
                      }
                    },
                    (error) => {
                      reject(error)
                    }
                  )
                })
              })
            )
            
            const uploadEndTime = Date.now();
            console.log(`[handlePublish] 所有图片上传完成:`, uploadEndTime, 
              '总耗时:', uploadEndTime - uploadStartTime, 'ms',
              '平均每张耗时:', (uploadEndTime - uploadStartTime) / this.images.length, 'ms');
              
          } catch (error) {
            const uploadFailTime = Date.now();
            console.log(`[handlePublish] 图片上传失败:`, uploadFailTime, 
              '总耗时:', uploadFailTime - uploadStartTime, 'ms');
              
            uni.hideLoading()
            uni.showToast({
              title: '图片上传失败',
              icon: 'none'
            })
            return
          }
        }

        // 发布动态
        const publishStartTime = Date.now();
        console.log(`[handlePublish] 开始发布动态:`, publishStartTime);
        
        await appServerApi.publishMoment({
          content: this.content,
          url: this.video ? [this.video] : imageUrls,
          address: this.locationInfo.address,
          longitude: this.locationInfo.longitude,
          latitude: this.locationInfo.latitude
        })

        const publishEndTime = Date.now();
        console.log(`[handlePublish] 动态发布完成:`, publishEndTime,
          '耗时:', publishEndTime - publishStartTime, 'ms');

        uni.hideLoading()
        uni.showToast({
          title: '发布成功',
          icon: 'success'
        })

        const endTime = Date.now();
        console.log(`[handlePublish] 整个发布流程完成:`, endTime,
          '总耗时:', endTime - startTime, 'ms');

        setTimeout(() => {
          uni.hideLoading()
          // 返回到朋友圈页面并刷新数据
          const pages = getCurrentPages()
          const momentsPage = pages.find(page => page.route === 'pages/moments/Moments')
          
          if (momentsPage) {
            // 如果在页面栈中找到了朋友圈页面
            uni.navigateBack({
              success: () => {
                // 返回成功后刷新数据
                const currentPages = getCurrentPages()
                const currentPage = currentPages[currentPages.length - 1]
                if (currentPage && currentPage.$vm.getList) {
                  // 重置页码并刷新列表
                  currentPage.$vm.pageNo = 1
                  currentPage.$vm.momentsList = []
                  currentPage.$vm.getList()
                }
              }
            })
          } else {
            // 如果没找到朋友圈页面，直接跳转
            uni.redirectTo({
              url: '/pages/moments/Moments'
            })
          }
        }, 500)
      } catch (error) {
        const errorTime = Date.now();
        console.log(`[handlePublish] 发布失败:`, errorTime,
          '总耗时:', errorTime - startTime, 'ms');
          
        console.error('发布失败:', error)
        uni.hideLoading()
        uni.showToast({
          title: '发布失败',
          icon: 'none'
        })
      } finally {
        this.uploading = false
      }
    },
    
    handleLocation() {
      uni.chooseLocation({
        success: (res) => {
          console.log('选择位置成功:', res)
          this.locationInfo = {
            address: res.address,
            longitude: res.longitude,
            latitude: res.latitude
          }
          this.location = res.address
        },
        fail: (err) => {
          console.error('选择位置失败:', err)
        }
      })
    },
    

    // 删除图片
    deleteImage(index) {
      this.images.splice(index, 1)
    },
    
    chooseImage() {
      uni.chooseImage({
        count: 9,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          // 过滤重复图片
          const newImages = res.tempFilePaths.filter(newPath => {
            return !this.images.some(existingPath => existingPath == newPath)
          })
          
          if (newImages.length > 0) {
            this.images = [...this.images, ...newImages]
          }
        }
      })
    },

    // 可选：添加一个方法来检查图片是否相同
    isImageDuplicate(path1, path2) {
      // 基础版本：直接比较路径
      return path1 == path2
      
      // 进阶版本：可以添加图片内容比较
      // TODO: 可以添加图片hash比较或其他更复杂的比较方法
    },

    // 预览图片
    previewImage(urls, current) {
      uni.previewImage({
        urls: urls,              // 图片数组
        current: current,        // 当前图片索引
        indicator: 'number',     // 显示页码
        loop: true,             // 支持循环预览
        success: () => {
          console.log('图片预览成功')
        },
        fail: (err) => {
          console.error('图片预览失败:', err)
        }
      })
    },

    // 选择视频
    chooseVideo(sourceType = 'album') {
      if (this.images.length > 0) {
        uni.showToast({
          title: '不能同时选择图片和视频',
          icon: 'none'
        })
        return
      }
      
      uni.chooseVideo({
        count: 1,
        sourceType: [sourceType],
        maxDuration: 30,
        success: (res) => {
          if (res.duration > 30) {
            uni.showToast({
              title: '视频时长不能超过30秒',
              icon: 'none'
            })
            return
          }
          
          if (res.size > 100 * 1024 * 1024) {
            uni.showToast({
              title: '视频大小不能超过100MB',
              icon: 'none'
            })
            return
          }
          
          // 先设置临时视频路径
          this.video = res.tempFilePath
          
          // 计算并设置视频宽高比
          this.videoRatio = res.width / res.height
          console.log('视频宽高比：', this.videoRatio)
          
          // 显示上传中提示
          uni.showLoading({
            title: '视频上传中...'
          })
          
          // 上传视频
          appServerApi.uploadimgFile(
            res.tempFilePath,
            (uploadRes) => {
              console.log('视频上传成功:', uploadRes)
              uni.hideLoading()
              if (uploadRes && uploadRes.data) {
                this.video = uploadRes.data + '?videoRatio=' + this.videoRatio
              } else {
                uni.showToast({
                  title: '视频上传失败',
                  icon: 'none'
                })
                this.video = ''
                this.videoRatio = null
              }
            },
            (error) => {
              console.error('视频上传失败:', error)
              uni.hideLoading()
              uni.showToast({
                title: '视频上传失败',
                icon: 'none'
              })
              this.video = ''
              this.videoRatio = null
            }
          )
        },
        fail: (err) => {
          console.log('选择视频失败', err);
          // 增加更详细的错误反馈
          if (err.errMsg && err.errMsg.indexOf('permission') > -1) {
            uni.showToast({
              title: '无法访问相册或相机，请在系统设置中授权',
              icon: 'none',
              duration: 3000
            });
          }
        }
      })
    },
    
    // 删除视频
    deleteVideo() {
      this.video = ''
    },

    // 添加新方法
    async showMediaPicker() {
      uni.showActionSheet({
        itemList: ['拍摄', '从相册选择图片', '从相册选择视频'],
        success: async (res) => {
          switch(res.tapIndex) {
            case 0: // 拍摄
              uni.showActionSheet({
                itemList: ['拍照', '拍视频'],
                success: async (res2) => {
                  if (res2.tapIndex === 0) {
                    // 拍照
                    /*#ifdef APP-PLUS*/
                    if (plus.os.name !== 'iOS') {
                      var isPermission = plus.navigator.checkPermission('android.permission.CAMERA')
                      if (isPermission != 'authorized') {
                        topMessage.createTopMessage(
                          '相机权限使用说明',
                          '用于拍摄照片发布到动态，与好友分享您的生活瞬间。我们不会将相机用于其他用途。'
                        )
                      }
                      let res = await topMessage.requestPermissions(
                        'CAMERA',
                        '相机权限未获得，此权限用于拍摄照片功能，请前往设置中打开'
                      )
                      setTimeout(() => {
                        topMessage.hideTopMessage()
                      }, 300)
                      if (!res.granted[0]) {
                        // 检查是否被永久拒绝
                        if (res.deniedAlways && res.deniedAlways.includes('android.permission.CAMERA')) {
                          uni.showModal({
                            title: '相机权限已被禁用',
                            content: '请前往系统设置中手动开启相机权限',
                            confirmText: '去设置',
                            success: (modalRes) => {
                              if (modalRes.confirm) {
                                // 引导用户去系统设置
                                if (plus.os.name === 'Android') {
                                  plus.runtime.openURL('package:' + plus.runtime.appid);
                                }
                              }
                            }
                          });
                        }
                        return
                      }
                    }
                    /*#endif*/
                    uni.chooseImage({
                      count: 9,
                      sizeType: ['compressed'],
                      sourceType: ['camera'],
                      success: (res) => {
                        const newImages = res.tempFilePaths.filter(newPath => {
                          return !this.images.some(existingPath => existingPath == newPath)
                        })
                        if (newImages.length > 0) {
                          this.images = [...this.images, ...newImages]
                        }
                      },
                      fail: (err) => {
                        console.log('选择照片失败', err);
                        // 增加更详细的错误反馈
                        if (err.errMsg && err.errMsg.indexOf('permission') > -1) {
                          uni.showToast({
                            title: '无法使用相机，请在系统设置中授权',
                            icon: 'none',
                            duration: 3000
                          });
                        }
                      }
                    })
                  } else {
                    // 拍视频
                    /*#ifdef APP-PLUS*/
                    if (plus.os.name !== 'iOS') {
                      var isPermission = plus.navigator.checkPermission('android.permission.CAMERA')
                      if (isPermission != 'authorized') {
                        topMessage.createTopMessage(
                          '相机权限使用说明',
                          '用于拍摄视频发布到动态，与好友分享您的生活瞬间。我们不会将相机用于其他用途。'
                        )
                      }
                      let res = await topMessage.requestPermissions(
                        'CAMERA',
                        '相机权限未获得，此权限用于拍摄视频功能，请前往设置中打开'
                      )
                      setTimeout(() => {
                        topMessage.hideTopMessage()
                      }, 300)
                      if (!res.granted[0]) {
                        // 检查是否被永久拒绝
                        if (res.deniedAlways && res.deniedAlways.includes('android.permission.CAMERA')) {
                          uni.showModal({
                            title: '相机权限已被禁用',
                            content: '请前往系统设置中手动开启相机权限',
                            confirmText: '去设置',
                            success: (modalRes) => {
                              if (modalRes.confirm) {
                                // 引导用户去系统设置
                                if (plus.os.name === 'Android') {
                                  plus.runtime.openURL('package:' + plus.runtime.appid);
                                }
                              }
                            }
                          });
                        }
                        return
                      }
                    }
                    /*#endif*/
                    this.chooseVideo('camera')
                  }
                }
              })
              break
            case 1: // 从相册选择图片
              /*#ifdef APP-PLUS*/
              if (plus.os.name !== 'iOS') {
                // 获取Android版本
                let androidVersion = parseInt(plus.os.version);
                console.log('Android版本:', androidVersion);
                
                // 根据Android版本选择合适的权限
                let permissionName = 'READ_EXTERNAL_STORAGE';
                
                // Android 13及以上使用细分的媒体权限
                if (androidVersion >= 13) {
                  permissionName = 'READ_MEDIA_IMAGES';
                  console.log('使用READ_MEDIA_IMAGES权限');
                } else {
                  console.log('使用READ_EXTERNAL_STORAGE权限');
                }
                
                // 检查权限状态
                var isPermission = plus.navigator.checkPermission('android.permission.' + permissionName);
                console.log('存储权限状态:', isPermission);
                
                // 如果权限已授权，直接选择图片
                if (isPermission === 'authorized') {
                  console.log('权限已授权，直接选择图片');
                  this.chooseImagesFromAlbum();
                  return;
                }
                
                topMessage.createTopMessage(
                  '权限使用说明',
                  '需要您开启存储权限,以便为您提供从相册选择照片发布到动态的功能。'
                );
                
                let res = await topMessage.requestPermissions(
                  permissionName,
                  '存储权限未获得，此权限用于选择照片功能，请前往设置中打开'
                );
                
                console.log('权限请求结果:', JSON.stringify(res));
                
                setTimeout(() => {
                  topMessage.hideTopMessage();
                }, 300);
                
                // 检查权限结果中是否有重复前缀问题
                const hasDuplicatePrefix = res.deniedAlways && res.deniedAlways.some(perm => 
                  perm.includes('android.permission.android.permission')
                );
                
                if (hasDuplicatePrefix) {
                  console.log('检测到权限前缀重复问题，尝试直接访问相册');
                  this.chooseImagesFromAlbum();
                  return;
                }
                
                // 如果权限请求失败
                if (!res.granted || res.granted.length === 0) {
                  // 检查真实权限状态再次确认
                  const realPermissionStatus = plus.navigator.checkPermission('android.permission.' + permissionName);
                  console.log('再次确认权限状态:', realPermissionStatus);
                  
                  if (realPermissionStatus === 'authorized') {
                    console.log('权限实际已授权，直接选择图片');
                    this.chooseImagesFromAlbum();
                    return;
                  }
                  
                  // 检查是否被永久拒绝
                  if (res.deniedAlways && res.deniedAlways.includes('android.permission.' + permissionName)) {
                    // 显示引导用户去设置的弹窗
                    uni.showModal({
                      title: '存储权限问题',
                      content: '无法访问您的相册，请在设置中开启存储权限',
                      confirmText: '去设置',
                      cancelText: '取消',
                      success: (modalRes) => {
                        if (modalRes.confirm) {
                          // 跳转到应用权限设置页面
                          if (plus.os.name === 'Android') {
                            plus.runtime.openURL('package:' + plus.runtime.appid);
                          }
                        } else {
                          // 尝试直接访问，某些设备可能不需要明确权限
                          this.chooseImagesFromAlbum();
                        }
                      }
                    });
                    return;
                  }
                }
              }
              /*#endif*/
              this.chooseImagesFromAlbum();
              break
            case 2: // 从相册选择视频
              /*#ifdef APP-PLUS*/
              if (plus.os.name !== 'iOS') {
                // 获取Android版本
                let androidVersion = parseInt(plus.os.version);
                console.log('Android版本:', androidVersion);
                
                // 根据Android版本选择合适的权限
                let permissionName = 'READ_EXTERNAL_STORAGE';
                
                // Android 13及以上使用细分的媒体权限
                if (androidVersion >= 13) {
                  permissionName = 'READ_MEDIA_VIDEO';
                  console.log('使用READ_MEDIA_VIDEO权限');
                } else {
                  console.log('使用READ_EXTERNAL_STORAGE权限');
                }
                
                // 检查权限状态
                var isPermission = plus.navigator.checkPermission('android.permission.' + permissionName);
                console.log('存储权限状态:', isPermission);
                
                // 如果权限已授权，直接选择视频
                if (isPermission === 'authorized') {
                  console.log('权限已授权，直接选择视频');
                  this.chooseVideo('album');
                  return;
                }
                
                topMessage.createTopMessage(
                  '权限使用说明',
                  '需要您开启存储权限,以便为您提供从相册选择视频发布到动态的功能。'
                );
                
                let res = await topMessage.requestPermissions(
                  permissionName,
                  '存储权限未获得，此权限用于选择视频功能，请前往设置中打开'
                );
                
                console.log('权限请求结果:', JSON.stringify(res));
                
                setTimeout(() => {
                  topMessage.hideTopMessage();
                }, 300);
                
                // 检查权限结果中是否有重复前缀问题
                const hasDuplicatePrefix = res.deniedAlways && res.deniedAlways.some(perm => 
                  perm.includes('android.permission.android.permission')
                );
                
                if (hasDuplicatePrefix) {
                  console.log('检测到权限前缀重复问题，尝试直接访问相册');
                  this.chooseVideo('album');
                  return;
                }
                
                // 如果权限请求失败
                if (!res.granted || res.granted.length === 0) {
                  // 检查真实权限状态再次确认
                  const realPermissionStatus = plus.navigator.checkPermission('android.permission.' + permissionName);
                  console.log('再次确认权限状态:', realPermissionStatus);
                  
                  if (realPermissionStatus === 'authorized') {
                    console.log('权限实际已授权，直接选择视频');
                    this.chooseVideo('album');
                    return;
                  }
                  
                  // 检查是否被永久拒绝
                  if (res.deniedAlways && res.deniedAlways.includes('android.permission.' + permissionName)) {
                    // 显示引导用户去设置的弹窗
                    uni.showModal({
                      title: '存储权限问题',
                      content: '无法访问您的相册，请在设置中开启存储权限',
                      confirmText: '去设置',
                      cancelText: '取消',
                      success: (modalRes) => {
                        if (modalRes.confirm) {
                          // 跳转到应用权限设置页面
                          if (plus.os.name === 'Android') {
                            plus.runtime.openURL('package:' + plus.runtime.appid);
                          }
                        } else {
                          // 尝试直接访问，某些设备可能不需要明确权限
                          this.chooseVideo('album');
                        }
                      }
                    });
                    return;
                  }
                }
              }
              /*#endif*/
              this.chooseVideo('album')
              break
          }
        }
      })
    },

    // 添加获取视频容器类名的方法
    getVideoClass() {
      if (!this.videoRatio) return ''
      if (this.videoRatio > 1.2) return 'landscape'
      if (this.videoRatio < 0.8) return 'portrait'
      return 'square'
    },
    
    // 修改 getVideoStyle 方法
    getVideoStyle() {
      if (!this.videoRatio) return {}
      
      if (this.videoRatio < 1) {
        // 竖屏视频
        return {
          width: '56.25vw',
          height: `${56.25 / this.videoRatio}vw`,
          margin: '0 auto'
        }
      } else if (this.videoRatio > 1) {
        // 横屏视频
        return {
          width: `${46 * this.videoRatio}vw`,
          height: '46vw',  // 改为自动高度
          aspectRatio: `${this.videoRatio}`,  // 使用视频原始宽高比
        }
      } else {
        // 方形视频
        return {
          width: '100%',
          aspectRatio: '1',
          maxHeight: '750rpx'
        }
      }
    },

    // 从相册选择多张图片的方法
    chooseImagesFromAlbum() {
      uni.chooseImage({
        count: 9,
        sizeType: ['compressed'],
        sourceType: ['album'],
        success: (res) => {
          const newImages = res.tempFilePaths.filter(newPath => {
            return !this.images.some(existingPath => existingPath == newPath)
          })
          if (newImages.length > 0) {
            this.images = [...this.images, ...newImages]
          }
        },
        fail: (err) => {
          console.log('选择图片失败', err);
          // 增加更详细的错误反馈
          if (err.errMsg && err.errMsg.indexOf('permission') > -1) {
            uni.showToast({
              title: '无法访问相册，请在系统设置中授权',
              icon: 'none',
              duration: 3000
            });
          }
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #fff;
}

.status-bar {
  width: 100%;
  background-color: #fff;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
  height: 44px;  /* 导航栏固定高度 */
  box-sizing: border-box;
  
  .left {
    .back-icon {
      font-size: 18px;
      color: #333;
    }
  }
  
  .publish-btn {
    font-size: 16px;
    color: #07c160;
    background: none;
    border: none;
    padding: 5px 15px;
    opacity: 0.5;
    
    &.active {
      opacity: 1;
    }
    
    &::after {
      border: none;
    }
  }
}

.content-area {
  padding: 15px;
  
  .content-input {
    width: 100%;
    min-height: 100px;
    font-size: 16px;
    line-height: 1.5;
  }
}

.bottom-section {
  padding: 0 15px;
  
  .location-section,
  .mention-section,
  .visibility-section {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
    
    image {
      width: 20px;
      height: 20px;
      margin-right: 10px;
    }
    
    .arrow-icon {
      margin-left: auto;
      color: #999;
    }
  }
  
  .visibility-value {
    margin-left: auto;
    margin-right: 5px;
    color: #999;
  }
  
  .last-group {
    padding: 10px 0;
    font-size: 14px;
    color: #999;
    
    .group-value {
      color: #333;
    }
  }
}

.image-picker {
  padding: 15px;
  
  .image-list {
    display: flex;
    flex-wrap: wrap;
    margin-right: -10px;
    margin-bottom: -10px;
    
    .image-item {
      width: 80px;
      height: 80px;
      position: relative;
      margin-right: 10px;
      margin-bottom: 10px;
      
      .preview-image {
        width: 100%;
        height: 100%;
        border-radius: 4px;
        cursor: pointer;
      }
      
      .delete-btn {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 20px;
        height: 20px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .delete-icon {
          color: #fff;
          font-size: 16px;
          line-height: 1;
        }
      }
    }
    
    .add-media {
      width: 80px;
      height: 80px;
      margin-right: 10px;
      margin-bottom: 10px;
      background-color: #f7f7f7;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px dashed #ddd;
      border-radius: 4px;
      
      .plus-icon {
        font-size: 30px;
        color: #999;
      }
    }
    
    .video-container {
      position: relative;
      width: 100%;
      margin-bottom: 10rpx;
      
      .video-item {
        width: 100%;
        border-radius: 8rpx;
        background: #f8f8f8;
        overflow: hidden;
        
        &.landscape {
          width: 100%;
          display: flex;
          justify-content: center;
          background: transparent;
          .preview-video {
            width: 100%;
            aspect-ratio: var(--video-ratio);
            object-fit: contain;
          }
        }
        
        &.portrait {
          width: 56.25vw;
          margin: 0 auto;
          .preview-video {
            width: 100%;
            aspect-ratio: var(--video-ratio);
            object-fit: contain;
          }
        }
        
        &.square {
          aspect-ratio: 1;
          .preview-video {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }
        
        .preview-video {
          width: 100%;
          display: block;
          background: transparent;
        }
      }

      .video-delete-btn {
        position: absolute;
        top: 8rpx;
        right: 8rpx;
        width: 44rpx;
        height: 44rpx;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        pointer-events: auto;
        
        .delete-icon {
          color: #fff;
          font-size: 36rpx;
          line-height: 1;
        }
        
        &:active {
          opacity: 0.8;
        }
      }
    }
  }
}

.camera-icon {
  width: 24px;
  height: 24px;
}

.right {
  padding: 10px 15px;
}

.publish-btn {
  color: #386bf6;
  font-size: 16px;
  white-space: nowrap;
  
  &.disabled {
    color: #999;
  }
  
  &.active {
    color: #386bf6;
  }
}
</style>
