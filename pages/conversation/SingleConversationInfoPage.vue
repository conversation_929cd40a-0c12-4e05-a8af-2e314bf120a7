<template>
    <div class="conversation-info">
        <!-- 头部用户信息区域 -->
        <view class="users-container">
			<view class="users-container-list">
				<view class="users-container-list-item">
					<view class="me3b1" @click.stop="seeUser(targetUserInfo)">
						<image class="img"  :src="targetUserInfo.portrait" ></image>
					</view>
					<view class="me3b2">{{ targetUserInfo.friendAlias || targetUserInfo.displayName }}</view>
				</view>
                <view class="users-container-list-item"  @click="showCreateConversationModal">
					<view class="me3b1">
						<image class="img" src="@/assets/images/group/add.png"></image>
					</view>
				</view>
			</view>
		</view>

        <!-- 设置选项列表 -->
        <div class="settings-list">
            <!-- 消息免打扰 -->
            <div class="setting-item">
                <text class="setting-label">消息免打扰</text>
                <switch :checked="isSilent" @change="toggleMute" color="#4168e0" />
            </div>

            <!-- 置顶聊天 -->
            <div class="setting-item">
                <text class="setting-label">置顶聊天</text>
                <switch :checked="isTop" @change="toggleTop" color="#4168e0" />
            </div>

            <!-- 其他选项 -->
            <div class="setting-item" @click="viewChatHistory">
                <text class="setting-label">查看聊天记录</text>
                <text class="icon-ion-ios-arrow-right"></text>
            </div>

            <!-- <div class="setting-item" @click="viewMediaFiles">
                <text class="setting-label">文件记录</text>
                <text class="icon-ion-ios-arrow-right"></text>
            </div>-->
        </div>
           <!-- 设置选项列表 -->
        <div class="settings-list">
            <div class="setting-item" @click="clearHistory">
                <text class="setting-label">清空聊天记录</text>
            </div>
        </div>
           <!-- 设置选项列表 -->
        <div class="settings-list">
            <div class="setting-item" @click="report">
                <text class="setting-label">更多</text>
            </div>
        </div>
        
        <!-- 添加删除好友选项 -->
        <div class="settings-list delete-container" v-if="!isTempChat">
            <div class="setting-item delete-item" @click="deleteFriendAndExit">
                <text class="setting-label delete-label">删除并退出</text>
            </div>
        </div>
    </div>
</template>

<script>
import store from "@/store";
import Config from '../../config'
import wfc from "../../wfc/client/wfc";
import messageLimitManager from "../../utils/messageLimitManager";

export default {
    name: "SingleConversationInfoPage",
    data() {
        return {
            conversationInfo: null,
            users: null,
            sharedContactState: store.state.contact,
            isSilent: false,
            isTop: false,
            targetUserInfo: null
        }
    },
    computed: {
        // 判断是否为临时会话
        isTempChat() {
            if (!this.conversationInfo || !this.conversationInfo.conversation) {
                return false;
            }
            return messageLimitManager.isTempChat(this.conversationInfo.conversation);
        }
    },
    onLoad(option) {
        console.log('SingleConversationInfoPage onLoad')
        // #ifdef APP-NVUE
        const eventChannel = this.$scope.eventChannel; // 兼容APP-NVUE
        // #endif
        // #ifndef APP-NVUE
        const eventChannel = this.getOpenerEventChannel();
        // #endif
        
        console.log('准备接收事件数据', eventChannel);
        
        eventChannel.on('conversationInfo', (options) => {
            console.log('接收到conversationInfo事件数据', options);
            this.conversationInfo = options.conversationInfo;
            console.log('设置conversationInfo', this.conversationInfo);
            
            try {
                this.users = store.getConversationMemberUsrInfos(this.conversationInfo.conversation);
                console.log('获取到用户信息', this.users);
                
                // 从wfc中再次获取目标用户信息
                const targetUserId = this.conversationInfo.conversation.target;
                this.targetUserInfo = wfc.getUserInfo(targetUserId);
                console.log('设置targetUserInfo', this.targetUserInfo);

                this.isSilent = this.conversationInfo.isSilent;
                this.isTop = !!this.conversationInfo.top;
                
                // 设置固定的导航栏标题
                uni.setNavigationBarTitle({
                    title: "聊天信息",
                });
            } catch (error) {
                console.error('处理会话信息时出错', error);
            }
        });
    },
    
    onShow() {
        // 当页面重新显示时，重新获取用户信息以确保备注等信息是最新的
        if (this.conversationInfo && this.conversationInfo.conversation) {
            const targetUserId = this.conversationInfo.conversation.target;
            this.targetUserInfo = wfc.getUserInfo(targetUserId);
            console.log('onShow更新targetUserInfo', this.targetUserInfo);
        }
    },
    methods: {
        showCreateConversationModal() {
            let canPickUsers = this.sharedContactState.favContactList.concat(
                    this.sharedContactState.friendList
                ).filter(u => {
                    return u.uid !== Config.FILE_HELPER_ID
                })
            this.$pickUsers(
                {
                    users: canPickUsers,
                    initialCheckedUsers: [this.conversationInfo.conversation._target],
                    uncheckableUsers: [this.conversationInfo.conversation._target],
                    confirmTitle: this.$t('common.add'),
                    successCB: (users) => {
                        let newPickedUsers = users;
                        newPickedUsers.push(this.conversationInfo.conversation._target)
                        store.createConversation(newPickedUsers);
                    }
                })
        },
        
        toggleMute(e) {
            this.isSilent = e.detail.value;
            store.setConversationSilent(this.conversationInfo.conversation, e.detail.value);
        },

        toggleTop(e) {
            this.isTop = e.detail.value;
            store.setConversationTop(this.conversationInfo.conversation, e.detail.value ? 1 : 0);
        },

        viewChatHistory() {
            
            // store.searchMessage(this.conversationInfo.conversation, this.keyword)
            uni.navigateTo({
                url: `/pages/search/SearchPortalPage`
            });
        },
        seeUser(user){
            uni.navigateTo({
                url: '/pages/contact/UserDetailPage?userId='+user.uid,
            })
        },
        viewMediaFiles() {
            // TODO: 实现查看文件记录功能
        },

        clearHistory() {
            uni.showModal({
                title: '确认清空',
                content: '是否清空所有聊天记录？此操作不可恢复',
                success: (res) => {
                    if (res.confirm) {
                        store.clearMessages(this.conversationInfo.conversation);
                        uni.showToast({
                            title: '操作成功',
                            icon: 'none'
                        })
                    }
                }
            });
        },

        report() {
            // uni.showToast({
            //     title: '感谢您的反馈，我们将尽快处理',
            //     icon: 'none'
            // })
            uni.navigateTo({
                url: `/pages/complaint/moreComplaint?userId=${this.targetUserInfo.uid}`
            });
        },
        
        // 添加删除好友方法
        deleteFriendAndExit() {
            // 阻止删除文件传输助手
            if (this.targetUserInfo.uid === Config.FILE_HELPER_ID) {
                uni.showToast({
                    title: '不能删除文件传输助手',
                    icon: 'none'
                });
                return;
            }
            
            uni.showModal({
                title: '确认删除',
                content: '确定要删除该好友吗？删除后将无法接收对方消息',
                success: (res) => {
                    if (res.confirm) {
                        const userId = this.targetUserInfo.uid;
                        wfc.deleteFriend(userId, 
                            () => {
                                // 删除成功回调
                                uni.showToast({
                                    title: '删除成功',
                                    icon: 'success'
                                });
                                // 移除会话
                                store.removeConversation(this.conversationInfo.conversation);
                                // 返回上一页
                                setTimeout(() => {
                                    uni.navigateBack();
                                }, 1000);
                            }, 
                            (errorCode) => {
                                // 删除失败回调
                                uni.showToast({
                                    title: '删除失败，错误码：' + errorCode,
                                    icon: 'none'
                                });
                            }
                        );
                    }
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.conversation-info {
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
    min-height: 100vh;
    padding: 20px 12px;
}
 .users-container {
    width: 100%;
    background-color: #ffffff;
    padding: 14px 10px;
    margin-bottom: 10px;
    border-radius: 10px;
    .users-container-list {

        .users-container-list-item {
            float: left;
            margin-right: 18px;
            display: flex;
            justify-content: space-between;
            flex-direction: column;
            align-items: center;

            .me3b1 {
                overflow: hidden;
                image{
                    width: 50px;
                    height: 50px;
                    border-radius: 8px;
                    margin-bottom: 10px;
                    background-color: #f5f5f5;
                }
            }

            .me3b2 {
                max-width: 57px;
                font-size: 12px;
                padding: 0 4px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                color: #4B4B4B;
            }
        }
    }

    
}

.settings-list {
    margin-top: 10px;
    background: white;
    border-radius: 10px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #EFEFEF;
}

.setting-label {
    font-size: 16px;
    color: #000;
}

.icon-ion-ios-arrow-right {
     margin-left: 10px;
     font-size: 16px;
}

/* 删除好友样式 */
.delete-container {
    margin-top: 30px;
}

.delete-item {
    justify-content: center;
}

.delete-label {
    color: #FF3B30;
}
</style>