<template>
	<view>
		<!-- 添加自定义导航栏 -->
		<view v-if="sharedConversationState.currentConversationInfo != null">
			<CustomHeader backgroundColor="#f5f5f5" leftIcon="back" @height-update="onHeaderHeightUpdate">
				<template v-slot:title>
					<view class="title-container">
						<text
							class="title">{{ targetUserOnlineStateDesc ? conversationTitle + `(${targetUserOnlineStateDesc})` : conversationTitle }}</text>
						<!-- 群聊时显示的图标按钮，单聊时宽度为0 -->
						<view class="navbar-button group-profile-button"
							:style="{ width: isGroupConversation ? '40px' : '0px', overflow: 'hidden' }"
							@click="navigateToGroupProfile">
							<text class="iconfont2">&#xe621;</text>
						</view>
					</view>
				</template>
				<template v-slot:right>
					<view class="right-buttons">
						<view class="navbar-button more-button" @click="onMoreButtonClick">
							<text class="wx-iconfont">&#xe623;</text>
						</view>
					</view>
				</template>
			</CustomHeader>
		</view>

		<view v-if="sharedConversationState.currentConversationInfo == null" class="conversation-empty-container">
			<text>^~^</text>
		</view>
		<view v-else ref="conversationContentContainer" class="conversation-content-container"
			:style="{ height: `calc(100vh - ${headerHeight}px)` }" :dummy_just_for_reactive="currentVoiceMessage">
			<!-- 添加群公告弹窗 -->
			<view v-if="showAnnouncementModal" class="announcement-modal" @click="closeAnnouncementModal">
				<view class="announcement-content" @click.stop>

					<view class="announcement-header">
						<text class="announcement-title">群公告</text>
						<text class="close-btn" @click="closeAnnouncementModal">×</text>
					</view>

					<scroll-view class="announcement-body" scroll-y>
						<!-- 使用 rich-text 组件显示HTML内容 -->
						<rich-text v-if="announcementRichTextHtml" :nodes="announcementRichTextHtml"></rich-text>
						<!-- 无内容提示 -->
						<view v-if="!announcementRichTextHtml || announcementRichTextHtml.length === 0" class="no-announcement">
							<text>暂无群公告</text>
						</view>
					</scroll-view>
				</view>
			</view>
			<!-- 修改消息列表容器，添加相对定位 -->
			<view class="message-list-container" :style="{ position: 'relative' }"
				:class="{
					'has-two-notifications': showAddFriendTip && isGroupConversation && hasGroupAnnouncement && !announcementReadLocal,
					'has-add-friend-only': showAddFriendTip && !(isGroupConversation && hasGroupAnnouncement && !announcementReadLocal),
					'has-announcement-only': !showAddFriendTip && isGroupConversation && hasGroupAnnouncement && !announcementReadLocal,
					'has-no-notifications': !showAddFriendTip && !(isGroupConversation && hasGroupAnnouncement && !announcementReadLocal)
				}">
				<!-- 移除了添加好友提示和群公告入口 -->
				<view v-if="ongoingCalls && ongoingCalls.length > 0" class="ongoing-call-container">
					<view v-for="(value, index) in ongoingCalls" :key="index" class="ongoing-call-item">
						<text>{{ value.messageContent.digest(value) }}</text>
						<button @click="joinMultiCall(value)">加入</button>
					</view>
				</view>
				<scroll-view ref="conversationMessageList" class="message-list" scroll-y="true" :scroll-top="scrollTop"
					refresher-enabled="true" :refresher-triggered="triggered" :refresher-threshold="45"
					@refresherpulling="onPulling" @refresherrefresh="onRefresh" @refresherrestore="onRestore"
					@refresherabort="onAbort" @scroll="onScroll"
					:scroll-into-view="scrollIntoView"
					v-if="sharedConversationState.currentConversationMessageList.length>0"
					@touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd">
					<view v-for="(message) in sharedConversationState.currentConversationMessageList"
						:id="'id-'+ message.messageId" :key="message.messageId">
						<!--todo 不同的消息类型 notification in out -->

						<NotificationMessageContentView :message="message" v-if="isNotificationMessage(message)" />
						<RecallNotificationMessageContentView :message="message"
							v-else-if="isRecallNotificationMessage(message)" />
						<ContextableNotificationMessageContentContainerView
							v-else-if="isContextableNotificationMessage(message)"
							@click.native.capture="sharedConversationState.enableMessageMultiSelection? clickMessageItem($event, message) : null"
							:message="message" />
						<NormalOutMessageContentView
							@click.native.capture.stop="sharedConversationState.enableMessageMultiSelection? clickMessageItem($event, message) : null"
							:message="message" @touchstart.native="onTouchStart" @touchmove.native="onTouchMove"
							v-else-if="message.direction === 0 && sharedConversationState.enableMessageMultiSelection" />
						<NormalOutMessageContentView :message="message" @touchstart.native="onTouchStart"
							@touchmove.native="onTouchMove"
							v-else-if="message.direction === 0 && !sharedConversationState.enableMessageMultiSelection" />
						<NormalInMessageContentView
							@click.native.capture.stop="sharedConversationState.enableMessageMultiSelection ? clickMessageItem($event, message) : null"
							:message="message" @touchstart.native="onTouchStart" @touchmove.native="onTouchMove"
							v-else-if="message.direction === 1 && sharedConversationState.enableMessageMultiSelection" />
						<NormalInMessageContentView :message="message" @touchstart.native="onTouchStart"
							@touchmove.native="onTouchMove" v-else />
						
						<!-- 为每条消息添加底部锚点，用于准确定位到消息底部 -->
						<view :id="'bottom-'+ message.messageId" class="message-bottom-anchor"></view>
					</view>
					<!-- 添加消息列表底部锚点，确保能滚动到真正的底部 -->
					<view id="message-list-bottom" class="message-list-bottom-anchor"></view>
				</scroll-view>
			</view>

			<!-- 添加浮动的添加好友提示 -->
			<view v-if="showAddFriendTip" class="add-friend-tip-floating">
				<view class="tip-content">
					<text class="tip-text">添加好友可畅聊，还能解锁多种功能</text>
					<!-- 临时会话提示，显示在第二行 -->
					<view v-if="tempChatTipMessage" class="temp-chat-tip-row">
						<text class="temp-tip-text">{{ tempChatTipMessage }}</text>
					</view>
				</view>
				<view class="add-friend-btn" @click="addFriend">添加</view>
			</view>

			<!-- 添加浮动的群公告入口 -->
			<view v-if="isGroupConversation && hasGroupAnnouncement && !announcementReadLocal"
				class="group-announcement-entry-floating" @click="showGroupAnnouncement">
				<image class="announcement-icon" src="/static/image/conversation/announcement.png" mode="aspectFit">
				</image>
				<view class="announcement-preview">
					<text class="announcement-label">群公告：</text>
					<text class="announcement-content-preview">{{ announcementPreview }}</text>
				</view>
				<text class="view-detail">查看</text>
			</view>

			<view v-if="showContextMenu" class="bottom-menu-mask" @click="showContextMenu = false">
				<view class="bottom-menu" @click.stop>
					<template v-for="(group, groupIndex) in groupedMenuItems" :key="groupIndex">
						<view class="menu-group">
							<view v-for="(item, index) in group" :key="index" class="menu-item"
								@click="onContextMenuItemSelect(item)">
								<image :src="menuIcons[item.tag]" mode="widthFix" class="menu-item-icon"></image>
								<text>{{ item.title }}</text>
							</view>
						</view>
					</template>
				</view>
			</view>
			<MessageInputView 
				v-if="!showliwu && sharedMiscState.canSendMessage" 
				:conversationInfo="sharedConversationState.currentConversationInfo"
				v-show="!sharedConversationState.enableMessageMultiSelection" 
				class="message-input-container"
				ref="messageInputView" />
			
			<!-- 群状态提示 -->
			<view v-if="!sharedMiscState.canSendMessage && sharedMiscState.groupStatusMessage" 
				class="group-status-tip">
				<text class="status-message">{{ sharedMiscState.groupStatusMessage }}</text>
			</view>
			
			<MultiSelectActionView v-show="sharedConversationState.enableMessageMultiSelection" />
		</view>

		<!-- 修改赞赏弹窗，添加遮罩层 -->
		<view v-if="showliwu" class="rq-ds-mask" @click="handleMaskClick">
			<view class="rq-ds" :style="'height: auto;'" @click.stop>
				<view class="dsa">
					<view class="dsa1">礼物</view>
					<view class="dsa2">
						<image class="dsa2a" src="../../static/image/chat/jb.png"></image>
						<view class="dsa2b">{{gold}}</view>
						<view class="dsa2c" @click="handleRecharge">充值</view>
					</view>
				</view>
				<scroll-view :scroll-y="true" class="dsb" :style="'height: ' + (keyboardHeight - 60) + 'px'">
					<view class="dsba">
						<view class="dsba1" @click="onliwu(v)" v-for="(v,i) in lwList" :key="i">
							<image class="dsba1a" :src="v.url"></image>
							<view class="dsba1b dsba1d" v-if="v.price==0">赞赏</view>
							<view class="dsba1c dsba1d" v-else>{{v.price}}金币</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 赞赏确认弹窗 -->
		<view class="rq-tca" v-if="lwdata">
			<view class="tc">
				<view class="tca">
					<image class="tca0" :src="lwdata.url"></image>
					<view class="tca1">请输入数量</view>
					<view class="tca2"><input class="tca2a" type="number" v-model="lwnum" /></view>
					<view class="tca3" @click="onsendliwu">确认赞赏</view>
					<view class="tca4">
						<view class="icon-ion-ios-close-empty tca4a" @click="lwdata = false;"></view>
					</view>
				</view>
			</view>
		</view>

		<!-- 添加支付选择器组件 -->
		<GoldPaymentSelector v-model:visible="paymentVisible" @close="handlePaymentClose"
			@confirm="handlePaymentConfirm" />
		<IosPaymentSelector v-model:visible="iosPaymentVisible" @close="handlePaymentClose"
			@confirm="handlePaymentConfirm" />
	</view>
</template>

<script>
	import MessageInputView from "@/pages/conversation/MessageInputView";
	import NormalOutMessageContentView from "@/pages/conversation/message/NormalOutMessageContentContainerView";
	import NormalInMessageContentView from "@/pages/conversation/message/NormalInMessageContentContainerView";
	import NotificationMessageContentView from "@/pages/conversation/message/NotificationMessageContentView";
	import RecallNotificationMessageContentView from "@/pages/conversation/message/RecallNotificationMessageContentView";
	import NotificationMessageContent from "@/wfc/messages/notification/notificationMessageContent";
	import TextMessageContent from "@/wfc/messages/textMessageContent";
	import store from "@/store";
	import wfc from "../../wfc/client/wfc";
	import {
		numberValue,
		stringValue
	} from "@/wfc/util/longUtil";
	import MultiSelectActionView from "@/pages/conversation/MessageMultiSelectActionView";
	import ForwardType from "@/pages/conversation/message/forward/ForwardType";
	import FileMessageContent from "@/wfc/messages/fileMessageContent";
	import ImageMessageContent from "@/wfc/messages/imageMessageContent";
	import {
		copyImg,
		copyText
	} from "@/pages/util/clipboard";
	import Message from "@/wfc/messages/message";
	import VideoMessageContent from "@/wfc/messages/videoMessageContent";
	import SoundMessageContent from "@/wfc/messages/soundMessageContent";
	import MessageContentType from "@/wfc/messages/messageContentType";
	import FavItem from "@/wfc/model/favItem";
	import ConversationType from "@/wfc/model/conversationType";
	import GroupMemberType from "@/wfc/model/groupMemberType";
	import CompositeMessageContent from "@/wfc/messages/compositeMessageContent";
	import ConnectionStatus from "../../wfc/client/connectionStatus";
	import {
		getItem,
		setItem
	} from "../util/storageHelper";
	import Config from "../../config";
	import RichNotificationMessageContent from "../../wfc/messages/notification/richNotificationMessageContent";
	import ArticlesMessageContent from "../../wfc/messages/articlesMessageContent";
	import ContextableNotificationMessageContentContainerView from "./message/ContextableNotificationMessageContentContainerView.vue";
	import EventType from "../../wfc/client/wfcEvent";
	import MultiCallOngoingMessageContent from "../../wfc/av/messages/multiCallOngoingMessageContent";
	import JoinCallRequestMessageContent from "../../wfc/av/messages/joinCallRequestMessageContent";
	import CustomHeader from "@/components/custom-header/index.vue";
	import GoldPaymentSelector from '@/components/payment/GoldPaymentSelector'
	import IosPaymentSelector from '@/components/payment/IosPaymentSelector'
	import appServerApi from '@/api/appServerApi'
  	import MessageStatus from "@/wfc/messages/messageStatus";
	import messageLimitManager from '../../utils/messageLimitManager'

	// 添加性能优化工具函数
	function throttle(func, wait) {
		let timeout = null;
		let previous = 0;
		return function (...args) {
			const now = Date.now();
			if (now - previous > wait) {
				func.apply(this, args);
				previous = now;
			}
		};
	}

	function debounce(func, wait) {
		let timeout;
		return function (...args) {
			clearTimeout(timeout);
			timeout = setTimeout(() => func.apply(this, args), wait);
		};
	}

	var innerAudioContext;
	export default {
		name: 'ConversationPage',
		components: {
			ContextableNotificationMessageContentContainerView,
			MultiSelectActionView,
			NotificationMessageContentView,
			RecallNotificationMessageContentView,
			NormalInMessageContentView,
			NormalOutMessageContentView,
			MessageInputView,
			CustomHeader,
			GoldPaymentSelector,
			IosPaymentSelector
		},
		// props: ["conversation"],
		data() {
			return {
				conversationInfo: store.state.conversation.currentConversationInfo,
				sharedConversationState: store.state.conversation,
				sharedContactState: store.state.contact,
				sharedPickState: store.state.pick,
				sharedMiscState: store.state.misc,

				savedMessageListViewHeight: -1,
				saveMessageListViewFlexGrow: -1,

				dragAndDropEnterCount: 0,

				showContextMenu: false,
				isScroll: false,
				touchStartX: 0,
				touchStartY: 0,
				contextMenuItems: [],
				lastScrollTop: 0,
				keyboardHeight: 300,
				currentKeyboardHeight: 0,
				scrollTop: 0,
				scrollIntoView: '', // 添加变量用于控制滚动定位

				triggered: false,

				ongoingCalls: [],
				ongoingCallTimer: 0,
				showAddFriendTip: false,
				hasShownKeyboard: false,
				menuIcons: {
					quote: '/static/image/chat/menu/quote.png', //回复
					forward: '/static/image/chat/menu/anniu1.png', //转发
					copy: '/static/image/chat/menu/copy.png', //复制
					multiSelection: '/static/image/chat/menu/multiSelection.png', //多选
					recall: '/static/image/chat/menu/recall.png', //撤回
					delete: '/static/image/chat/menu/delete.png', //删除
					jubao: '/static/image/chat/menu/jubao.png', //举报
					liwu:'/static/image/chat/ds.png'
				},
				showAnnouncementModal: false,
				announcementRichTextHtml: '', // 新增用于 rich-text 的HTML属性
				announcementReadLocal: false,
				headerHeight: 0, // 添加导航栏高度变量

				// 添加赞赏相关数据
				showliwu: false,
				lwdata: false,
				lwnum: 1,
				lwList: getItem('gift_list'),
				gold: 0,
				paymentVisible: false,
				iosPaymentVisible: false,

				// 添加性能优化相关的缓存变量
				cachedShouldShowAddFriendTip: null,
				cachedHasGroupAnnouncement: null,
				cachedAnnouncementPreview: '',
				lastAnnouncementCheck: 0,
				tempChatTip: null, // 临时会话提示信息
			};
		},
		onReady() {
			//动态修改状态栏的颜色
			uni.setNavigationBarColor({
				frontColor: '#000000',
				backgroundColor: '#f5f5f5',
			})
		},
		onLoad() {
		},

		async onShow() {
			// 更新会话标题
			this.updateConversationTitle();

			// 检查是否应该显示添加好友提示
			const shouldShow = this.checkShouldShowAddFriendTip(true); // 强制刷新以获取最新状态
			this.showAddFriendTip = shouldShow;
			
			console.log('onShow: 添加好友提示状态:', shouldShow);

			// 设置事件监听器
			this.setupEventListeners();
		},

		onHide() {
			// 清理事件监听器
			this.removeEventListeners();

		},

		onNavigationBarButtonTap(e) {
			if (this.conversationInfo.conversation.type === ConversationType.Single) {
				uni.navigateTo({
					url: '/pages/conversation/SingleConversationInfoPage',
					success: (res) => {
						res.eventChannel.emit('conversationInfo', {
							conversationInfo: this.conversationInfo
						});
					},
					fail: (err) => {
						console.log('nav to SingleConversationInfoPage err', err);
					}
				});

			} else if (this.conversationInfo.conversation.type === ConversationType.Group) {
				uni.navigateTo({
					url: '/pages/conversation/GroupConversationManagePage',
					success: (res) => {
						res.eventChannel.emit('conversationInfo', {
							conversationInfo: this.conversationInfo
						});
					},
					fail: (err) => {
						console.log('nav to GroupConversationManagePage err', err);
					}
				});
			} else {
				uni.showToast({
					title: 'TODO 暂不支持该会话类型',
					icon: 'none'
				})
			}
		},

		onUnload() {
			store.setCurrentConversationInfo(null);
		},

		methods: {
			toggleMessageMultiSelectionActionView(message) {
				store.toggleMessageMultiSelection(message);
			},

			clickMessageItem(event, message) {
				if (message.messageContent instanceof NotificationMessageContent) {
					return;
				}
				if (this.sharedConversationState.enableMessageMultiSelection) {
					store.selectOrDeselectMessage(message);
					event.stopPropagation();
					event.preventDefault();
				}
			},

			isNotificationMessage(message) {
				return message && message.messageContent instanceof NotificationMessageContent &&
					message.messageContent.type !== MessageContentType.RecallMessage_Notification &&
					message.messageContent.type !== MessageContentType.Rich_Notification;
			},

			isContextableNotificationMessage(message) {
				return message && (message.messageContent instanceof RichNotificationMessageContent || message
					.messageContent instanceof ArticlesMessageContent);
			},


			isRecallNotificationMessage(message) {
				return message && message.messageContent.type === MessageContentType.RecallMessage_Notification;
			},

			reedit(message) {
				this.$refs.messageInputView.insertText(message.messageContent.originalSearchableContent);
			},

			// 优化滚动事件处理，添加节流
			onScroll: throttle(function(e) {
				// 记录滚动位置
				this.lastScrollTop = e.detail.scrollTop;
				
				// 检测是否用户手动滚动
				// 如果滚动到接近底部，则认为是想看最新消息，设置自动滚动标志
				const {scrollTop, scrollHeight} = e.detail;
				// 在uni-app中，通过clientHeight获取可视区域高度
				const height = e.detail.clientHeight || 500; // 使用默认高度以防获取失败
				
				// 计算距离底部的距离
				const distanceFromBottom = scrollHeight - scrollTop - height;
				
				if (distanceFromBottom < 100) {
					// 当距离底部小于100px时，设置自动滚动
					if (!this.sharedConversationState.shouldAutoScrollToBottom) {
						store.setShouldAutoScrollToBottom(true);
					}
				} else if (this.sharedConversationState.shouldAutoScrollToBottom && distanceFromBottom > 100) {
					// 当用户向上滚动超过100px时，关闭自动滚动
					store.setShouldAutoScrollToBottom(false);
				}
				
				// 隐藏上下文菜单
				this.showContextMenu = false;
			}, 16), // 限制为60fps

			onMessageSenderContextMenuClose() {
				console.log('onMessageSenderContextMenuClose')
			},

			// message context menu
			isCopyable(message) {
				return message && message.messageContent instanceof TextMessageContent;
			},
			isDownloadAble(message) {
				return message && (message.messageContent instanceof ImageMessageContent ||
					message.messageContent instanceof FileMessageContent ||
					message.messageContent instanceof VideoMessageContent);
			},

			isForwardable(message) {
				if (message && message.messageContent instanceof SoundMessageContent) {
					return false;
				}
				return true;
			},

			isFavable(message) {
				if (!message) {
					return false;
				}
				return [MessageContentType.VOIP_CONTENT_TYPE_START,
					MessageContentType.CONFERENCE_CONTENT_TYPE_INVITE
				].indexOf(message.messageContent.type) <= -1;
			},

			isRecallable(message) {
				if (message) {
					if (message.conversation.type === ConversationType.Group) {
						let groupInfo = wfc.getGroupInfo(message.conversation.target);
						let selfUserId = wfc.getUserId();
						if (groupInfo && groupInfo.owner === selfUserId) {
							return true;
						}

						let groupMember = wfc.getGroupMember(message.conversation.target, selfUserId);
						if (groupMember && [GroupMemberType.Manager, GroupMemberType.Owner].indexOf(groupMember.type) > -
							1) {
							return true;
						}
					}
					let delta = wfc.getServerDeltaTime();
					let now = new Date().getTime();
					if (message.direction === 0 && now - (numberValue(message.timestamp) - delta) < 60 * 1000) {
						return true;
					}
				}
				return false;
			},

			ifBlockSend(message) {

			},

			ifUserSelfSend(message) {
				return message.from === wfc.getUserId();
			},
			isLocalFile(message) {
				// TODO
				return false;
			},

			isQuotable(message) {
				if (!message) {
					return false;
				}
				return [MessageContentType.VOIP_CONTENT_TYPE_START,
					MessageContentType.Voice,
					MessageContentType.Video,
					MessageContentType.Composite_Message,
					MessageContentType.CONFERENCE_CONTENT_TYPE_INVITE
				].indexOf(message.messageContent.type) <= -1;
			},

			copy(message) {
				let content = message.messageContent;
				console.log('content', content);
				if (content instanceof TextMessageContent) {
					copyText(content.content);
				} else if (content.type === MessageContentType.Image) {
					copyImg(content.remotePath)
				}
			},
			download(message) {
				// TODO
				// if (isElectron()) {
				//     downloadFile(message);
				// } else {
				//     if (!store.isDownloadingMessage(message.messageId)) {
				//         downloadFile(message)
				//         store.addDownloadingMessage(message.messageId)
				//     } else {
				//         // TODO toast 下载中
				//         console.log('file isDownloading')
				//     }
				// }
			},

			openFile(message) {
				let file = message.messageContent;
				// TODO
				//shell.openItem(file.localPath);
			},

			openDir(message) {
				let file = message.messageContent;
				// TODO
				// shell.showItemInFolder(file.localPath);
			},

			recallMessage(message) {
				wfc.recallMessage(message.messageUid, null, null);
			},

			forward(message) {
				this.$forward({
					forwardType: ForwardType.NORMAL,
					messages: [message],
				});
			},

			quoteMessage(message) {
				store.quoteMessage(message);
			},

			// call from child
			favMessages(messages) {
				console.log('fav messages');
				let compositeMessageContent = new CompositeMessageContent();
				let title = '';
				let msgConversation = messages[0].conversation;
				if (msgConversation.type === ConversationType.Single) {
					let users = store.getUserInfos([wfc.getUserId(), msgConversation.target], '');
					title = users[0]._displayName + '和' + users[1]._displayName + '的聊天记录';
				} else {
					title = '群的聊天记录';
				}
				compositeMessageContent.title = title;
				compositeMessageContent.messages = messages;

				let message = new Message(msgConversation, compositeMessageContent);
				message.from = wfc.getUserId();
				this.favMessage(message);
			},

			favMessage(message) {
				// fixme 收藏
				// TODO 收藏
				uni.showToast({
					title: 'TODO ',
					icon: 'none'
				});
				return;
				let favItem = FavItem.fromMessage(message);
				axios.post('/fav/add', {
						messageUid: stringValue(favItem.messageUid),
						type: favItem.favType,
						convType: favItem.conversation.type,
						convTarget: favItem.conversation.target,
						convLine: favItem.conversation.line,
						origin: favItem.origin,
						sender: favItem.sender,
						title: favItem.title,
						url: favItem.url,
						thumbUrl: favItem.thumbUrl,
						data: favItem.data,
					}, {
						withCredentials: true
					})
					.then(response => {
						if (response && response.data && response.data.code === 0) {
							this.$notify({
								// title: '收藏成功',
								text: '收藏成功',
								type: 'info'
							});
						} else {
							this.$notify({
								// title: '收藏成功',
								text: '收藏失败',
								type: 'error'
							});
						}
					})
					.catch(err => {
						this.$notify({
							// title: '收藏失败',
							text: '收藏失败',
							type: 'error'
						});

					})
			},

			multiSelect(message) {
				this.toggleMessageMultiSelectionActionView(message);
			},
			liwu(message) {
				this.liwuTargetMessage = message
				this.toggleliwu();
			},
			jubao(message) {
				uni.navigateTo({
					url: `/pages/complaint/ComplaintPage`,
				})
			},
			// why？
			// 用于控制，同时只能播放一个语言，如果是将逻辑放到 AudioMessageContentView 里面，控制起来会更麻烦
			playVoice(message) {
				if (innerAudioContext) {
					innerAudioContext.stop();
				}
				let voice = message.messageContent;
				let mp3RemotePath = Config.AMR_TO_MP3_SERVER_ADDRESS + voice.remotePath;
				innerAudioContext = uni.createInnerAudioContext();
				innerAudioContext.autoplay = false;
				innerAudioContext.src = mp3RemotePath;
				innerAudioContext.onPlay(() => {
					message._isPlaying = true;
					// 移除以下代码，不再在播放语音时滚动到底部
					// store.setShouldAutoScrollToBottom(true);
					// this.$nextTick(() => {
					// 	this.scrollToBottom();
					// });
				});
				innerAudioContext.onError((res) => {
					message._isPlaying = false;
					// store.playVoice(null)
				});
				innerAudioContext.onEnded(() => {
					message._isPlaying = false;
					store.playVoice(null)
				})
				innerAudioContext.play();
			},
			mentionMessageSenderTitle(message) {
				if (!message) {
					return ''
				}
				let displayName = wfc.getGroupMemberDisplayName(message.conversation.target, message.from);
				return '@' + displayName;
			},

			mentionMessageSender(message) {
				this.$refs.messageInputView.mention(message.conversation.target, message.from);
			},

			onReceiveMessage(message, hasMore) {
				if (this.conversationInfo && this.conversationInfo.conversation.equal(message.conversation) &&
					message.messageContent instanceof MultiCallOngoingMessageContent &&
					Config.ENABLE_MULTI_CALL_AUTO_JOIN
				) {
					// 自己是不是已经在通话中
					// console.log('MultiCallOngoingMessageContent', message.messageContent)
					if (message.messageContent.targets.indexOf(wfc.getUserId()) >= 0) {
						return;
					}
					let index = this.ongoingCalls.findIndex(call => call.messageContent.callId === message.messageContent
						.callId);
					if (index > -1) {
						this.ongoingCalls[index] = message;
					} else {
						this.ongoingCalls.push(message);
					}
					if (!this.ongoingCallTimer) {
						this.ongoingCallTimer = setInterval(() => {
							this.ongoingCalls = this.ongoingCalls.filter(call => {
								return (new Date().getTime() - (numberValue(call.timestamp) - numberValue(
									wfc
									.getServerDeltaTime()))) < 3 * 1000;
							})
							if (this.ongoingCalls.length === 0) {
								clearInterval(this.ongoingCallTimer);
								this.ongoingCallTimer = 0;
							}
							console.log('ongoing calls', this.ongoingCalls.length);
						}, 1000)
					}
				}
				
				// 检查是否为当前会话的新消息
				if (this.conversationInfo && 
					this.conversationInfo.conversation.equal(message.conversation) && 
					!message._isNewlyLoaded) { // 排除加载历史消息
					
					// 特殊处理语音消息，确保其始终能触发滚动
					if (message.messageContent instanceof SoundMessageContent) {
						// 对于语音消息，强制设置自动滚动到底部
						store.setShouldAutoScrollToBottom(true);
						this.$nextTick(() => {
							this.scrollToBottom();
						});
					}
					// 如果用户正在查看靠近底部的消息，则自动滚动到最新消息
					else if (this.sharedConversationState.shouldAutoScrollToBottom) {
						this.$nextTick(() => {
							// 使用智能滚动，根据消息类型和高度选择最佳滚动策略
							this.smartScrollToMessage(message.messageId);
						});
					} else if (!hasMore) {
						// 提示用户有新消息
						// 这里可以添加新消息提示的UI，如"新消息"提示按钮
					}
				}
			},

			joinMultiCall(message) {
				let request = new JoinCallRequestMessageContent(message.messageContent.callId, wfc.getClientId());
				wfc.sendConversationMessage(this.conversationInfo.conversation, request);
			},

			onTouchStart(e) {
				// 添加事件对象的安全检查
				if (!e || !e.touches || !e.touches[0]) {
					console.warn('onTouchStart: 无效的事件对象');
					return;
				}
				this.isScroll = false
				this.touchStartX = e.touches[0].clientX
				this.touchStartY = e.touches[0].clientY
			},

			onTouchMove(e) {
				// 添加事件对象的安全检查
				if (!e || !e.touches || !e.touches[0]) {
					console.warn('onTouchMove: 无效的事件对象');
					return;
				}
				uni.hideKeyboard();

				let delX = e.touches[0].clientX - this.touchStartX
				let delY = e.touches[0].clientY - this.touchStartY
				if (Math.abs(delX) > 5 || Math.abs(delY) > 5) {
					this.isScroll = true
				}
			},

			showMessageContextMenu(e, message) {
				if (this.isScroll) {
					return;
				}

				this.contextMenuItems = [];
				if (this.isQuotable(message)) {
					this.contextMenuItems.push({
						title: '回复',
						message: message,
						tag: 'quote',
					})
				}
				if (this.isForwardable(message)) {
					this.contextMenuItems.push({
						title: '转发',
						message: message,
						tag: 'forward',
					})
				}
				if(this.isGroupConversation){
					this.contextMenuItems.push({
						title: '赞赏',
						message: message,
						tag: 'liwu',
					})
				}

				if (this.isCopyable(message)) {
					this.contextMenuItems.push({
						title: "复制文字",
						message: message,
						tag: 'copy',
					})
				}
				this.contextMenuItems.push({
					title: '多选',
					message: message,
					tag: 'multiSelection',
				})


				if (this.isRecallable(message)) {
					this.contextMenuItems.push({
						title: '撤回',
						message: message,
						tag: 'recall',
					})
				}
				this.contextMenuItems.push({
					title: '删除',
					message: message,
					tag: 'delete',
				})
				if (!this.ifUserSelfSend(message)) {
					this.contextMenuItems.push({
						title: '举报',
						message: message,
						tag: 'jubao',
					})
				}

				if (this.isDownloadAble(message)) {
					this.contextMenuItems.push({
						title: "下载",
						message: message,
						tag: 'copy'
					})
				}


				// this.contextMenuItems.push({
				//     title: '远程删除',
				//     message: message,
				//     tag: 'deleteRemote',
				// })

				this.showContextMenu = true;
			},

			onContextMenuItemSelect(t) {
				switch (t.tag) {
					case 'delete':
						console.log('wfc delete message', t.message.messageId)
						wfc.deleteMessage(t.message.messageId);
						break;
					case 'deleteRemote':
						wfc.deleteRemoteMessageByUid(t.message.messageUid, () => {
							console.log('delete remote message success')
						}, err => {
							console.log('delete remote message fail', err);
						})
						break;
					case 'forward':
						this.forward(t.message)
						break;
					case 'recall':
						this.recallMessage(t.message);
						break;
					case 'quote':
						store.quoteMessage(t.message);
						break;
					case 'multiSelection':
						this.multiSelect(t.message);
						break;
					case 'jubao':
						this.jubao(t.message);
						break;
					case 'copy':
						this.copy(t.message);
						break;
					case 'liwu':
						this.liwu(t.message);
						break;
					default:
						uni.showToast({
							title: 'TODO ' + t.title,
							icon: 'none'
						})
						break;
				}
				this.showContextMenu = false;
				this.$eventBus.$emit('contextMenuClosed')
			},

			updateConversationTitle() {
				uni.setNavigationBarTitle({
					title: this.targetUserOnlineStateDesc ? this.conversationTitle +
						`(${this.targetUserOnlineStateDesc})` : this.conversationTitle
				});
			},

			onPulling(e) {
				// console.log("onpulling", e);
			},
			onRefresh() {
				if (this._freshing) {
					return;
				}

				this._freshing = true;
				this.triggered = true;

				// 保存当前第一条消息的ID，用于后续定位
				let firstMessageId = null;
				if (this.sharedConversationState.currentConversationMessageList.length > 0) {
					firstMessageId = this.sharedConversationState.currentConversationMessageList[0].messageId;
				}

				store.loadConversationHistoryMessages(() => {
					this.triggered = false;
					this._freshing = false;
					
					// 如果加载了新消息，滚动到之前的第一条消息位置
					if (firstMessageId) {
						this.$nextTick(() => {
							setTimeout(() => {
								// 防止加载后直接跳转到最后一条消息
								store.setShouldAutoScrollToBottom(false);
								
								// 直接设置scrollIntoView变量定位到原来的第一条消息
								this.scrollIntoView = `id-${firstMessageId}`;
								
								// 200ms后重置，以便后续操作不受影响
								setTimeout(() => {
									this.scrollIntoView = '';
								}, 200);
							}, 100);
						});
					}
				}, () => {
					this.triggered = false;
					this._freshing = false;
				});
			},
			onRestore() {
				this.triggered = 'restore'; // 需要重置
				console.log("onRestore");
			},
			onAbort() {
				console.log("onAbort");
			},

			scrollToBottom: debounce(function() {
				this.$nextTick(() => {
					// 当需要滚动到底部时，优先使用消息列表底部锚点
					// 这样可以确保滚动到真正的底部，而不是最后一条消息的顶部
					if (this.sharedConversationState.currentConversationMessageList.length > 0) {
						// 使用消息列表底部锚点，确保滚动到最底部
						this.scrollIntoView = 'message-list-bottom';
						
						// 清除scrollIntoView，避免影响后续操作
						setTimeout(() => {
							this.scrollIntoView = '';
						}, 200);
					}
				});
			}, 100),

			// 滚动到指定消息的底部（用于长消息的准确定位）
			scrollToMessageBottom: debounce(function(messageId) {
				if (!messageId) return;
				
				this.$nextTick(() => {
					// 使用消息底部锚点来定位到消息的底部
					this.scrollIntoView = `bottom-${messageId}`;
					
					// 清除scrollIntoView，避免影响后续操作
					setTimeout(() => {
						this.scrollIntoView = '';
					}, 200);
				});
			}, 100),

			// 智能滚动方法 - 根据消息高度选择最佳滚动策略
			smartScrollToMessage: debounce(function(messageId) {
				if (!messageId) {
					this.scrollToBottom();
					return;
				}

				this.$nextTick(() => {
					// 获取消息元素高度来判断是否为长消息
					const query = uni.createSelectorQuery().in(this);
					query.select(`#id-${messageId}`).boundingClientRect((data) => {
						if (data && data.height) {
							// 获取可视区域高度
							const sysInfo = uni.getSystemInfoSync();
							const viewportHeight = sysInfo.windowHeight;
							
							// 如果消息高度超过可视区域的60%，认为是长消息，滚动到底部
							if (data.height > viewportHeight * 0.6) {
								console.log('检测到长消息，滚动到消息底部');
								this.scrollToMessageBottom(messageId);
							} else {
								// 普通消息，滚动到消息列表底部
								this.scrollToBottom();
							}
						} else {
							// 如果无法获取消息高度，默认滚动到底部
							this.scrollToBottom();
						}
					}).exec();
				});
			}, 100),

			addFriend() {
				if (!this.sharedConversationState.currentConversationInfo) {
					console.error('当前没有选中会话，无法添加好友');
					return;
				}

				const conversation = this.sharedConversationState.currentConversationInfo.conversation;
				if (conversation.type !== 0) {
					console.error('只能添加单聊用户为好友');
					return;
				}

				const userId = conversation.target;
				if (!userId) {
					console.error('无法获取用户ID');
					return;
				}

				console.log('准备添加好友:', userId);

				// 跳转到 NewFriendDetailPage 页面，并传递用户ID
				uni.navigateTo({
					url: `/pages/contact/NewFriendDetailPage?userId=${userId}`
				});
			},

			// 发送好友请求
			sendFriendRequest(userId) {
				try {
					// 调用sdk发送好友请求
					wfc.sendFriendRequest(userId, '我想添加你为好友', (success) => {
						if (success) {
							console.log('好友请求发送成功');
							uni.showToast({
								title: '好友请求已发送',
								icon: 'success'
							});

							// 隐藏添加好友提示，防止用户重复点击
							this.showAddFriendTip = false;

							// 更新本地好友请求记录
							try {
								// 获取用户信息
								const userInfo = wfc.getUserInfo(userId);
								if (userInfo) {
									// 标记为已发送好友请求状态
									userInfo.friendRequestStatus = 'sent';

									// 尝试从本地获取已发送的好友请求列表
									let sentFriendRequests = uni.getStorageSync('sentFriendRequests') || [];

									// 检查是否已存在该用户的请求
									const existingIndex = sentFriendRequests.findIndex(req => req.userId ===
										userId);
									if (existingIndex >= 0) {
										// 更新现有记录
										sentFriendRequests[existingIndex] = {
											userId: userId,
											userInfo: userInfo,
											timestamp: Date.now(),
											status: 'sent'
										};
									} else {
										// 添加新记录
										sentFriendRequests.push({
											userId: userId,
											userInfo: userInfo,
											timestamp: Date.now(),
											status: 'sent'
										});
									}

									// 保存回本地
									uni.setStorageSync('sentFriendRequests', sentFriendRequests);
									console.log('已更新本地好友请求记录');

									// 触发全局事件，通知其他页面更新
									uni.$emit('friendRequestSent', {
										userId: userId,
										userInfo: userInfo,
										timestamp: Date.now()
									});
								}
							} catch (e) {
								console.error('更新本地好友请求记录失败:', e);
							}
						} else {
							console.error('好友请求发送失败');
							uni.showToast({
								title: '好友请求发送失败，请稍后再试',
								icon: 'none'
							});
						}
					}, (error) => {
						console.error('发送好友请求出错:', error);
						uni.showToast({
							title: '发送请求失败: ' + (error.message || '未知错误'),
							icon: 'none'
						});
					});
				} catch (error) {
					console.error('发送好友请求异常:', error);
					uni.showToast({
						title: '系统异常，请稍后再试',
						icon: 'none'
					});
				}
			},

			// 标记接收到的消息为已读并发送回执
			markIncomingMessageAsRead(message) {
				try {
					// 如果是自己发送的消息，不处理
					const currentUserId = getItem('userId');
					if (!currentUserId || message.from === currentUserId) {
						return;
					}

					// 只处理当前会话的单聊消息
					if (message.conversation.type !== ConversationType.Single ||
						!this.conversationInfo ||
						!this.conversationInfo.conversation.equal(message.conversation)) {
						return;
					}

					// 消息ID和会话ID验证
					const messageId = message.messageId;
					const conversationId = message.conversation.target;
					if (!messageId || !conversationId) {
						console.error('消息ID或会话ID无效，无法标记为已读:', message);
						return;
					}

					console.log('标记收到的单聊消息为已读:', messageId);

					// 标记消息对象为已读
					message.isRead = true;
					message.readTime = Date.now();

					// 1. 更新store中的状态
					if (this.$store && this.$store.commit) {
						this.$store.commit('UPDATE_MESSAGE_READ_STATUS', {
							messageId: messageId,
							value: 1,
							updateTime: Date.now(),
							serverConfirmed: true
						});
					}

					// 2. 存储在本地缓存
					const readStatusKey = `msg_read_status_${messageId}`;
					uni.setStorageSync(readStatusKey, {
						isRead: true,
						readTime: Date.now(),
						serverConfirmed: true
					});

					// 3. 发送WebSocket已读回执
					if (typeof window !== 'undefined' && window.wfcWebsocket) {
						console.log('通过WebSocket发送已读回执');
						window.wfcWebsocket.sendReadMessage({
							conversation: {
								type: ConversationType.Single,
								target: conversationId
							},
							msgs: [message]
						});
					} else {
						console.log('找不到WebSocket实例，尝试使用websocketClient');
						// 尝试通过store获取WebSocket客户端
						const websocketClient = this.$store && this.$store.state && this.$store.state.websocketClient;
						if (websocketClient && typeof websocketClient.sendReadMessage === 'function') {
							websocketClient.sendReadMessage({
								conversation: {
									type: ConversationType.Single,
									target: conversationId
								},
								msgs: [message]
							});
						} else {
							console.error('无法发送已读回执，无可用的WebSocket客户端');
						}
					}
				} catch (error) {
					console.error('标记收到的消息为已读失败:', error);
				}
			},

			// 在标记会话为已读的方法中添加发送已读回执
			markConversationAsRead() {
				if (!this.conversationInfo) {
					return;
				}

				// 调用已有的标记会话为已读方法
				store.clearConversationUnreadStatus(this.conversationInfo.conversation);

				// 如果是单聊，为所有未读消息发送已读回执
				if (this.conversationInfo.conversation.type === ConversationType.Single) {
					const currentUserId = getItem('userId');
					// 获取当前会话的所有消息
					const messageList = this.sharedConversationState.currentConversationMessageList || [];

					// 筛选出需要发送已读回执的消息（接收到的且未标记为已读的）
					const unreadMessages = messageList.filter(msg =>
						msg.from !== currentUserId && !msg.isRead
					);

					if (unreadMessages.length > 0) {
						console.log('为会话中的未读消息发送已读回执:', unreadMessages.length, '条');

						// 标记这些消息为已读
						unreadMessages.forEach(message => {
							this.markIncomingMessageAsRead(message);
						});
					}
				}
			},

			// 设置事件监听器
			setupEventListeners() {
        // 监听消息已读状态更新事件
				uni.$on('messageReadStatusUpdated', this.handleMessageReadStatusUpdate);
				// 监听批量消息已读状态更新事件
				uni.$on('batchMessageReadStatusUpdated', this.handleBatchMessageReadStatusUpdate);

				// 监听好友状态变化事件
				uni.$on('friendStatusChanged', this.handleFriendStatusChange);
				// 监听好友请求发送事件
				uni.$on('friendRequestSent', this.handleFriendRequestSent);
				// 监听新的好友请求事件
				uni.$on('newFriendRequest', this.handleNewFriendRequest);
				
				// 监听WFC好友列表更新事件
				wfc.eventEmitter.on(EventType.FriendListUpdate, this.onFriendListUpdate);
				
				// 添加其他可能需要的事件监听...
			},

			// 移除事件监听器
			removeEventListeners() {
				// 移除事件监听
				uni.$off('messageReadStatusUpdated', this.handleMessageReadStatusUpdate);
				uni.$off('batchMessageReadStatusUpdated', this.handleBatchMessageReadStatusUpdate);

				// 移除好友相关事件监听
				uni.$off('friendStatusChanged', this.handleFriendStatusChange);
				uni.$off('friendRequestSent', this.handleFriendRequestSent);
				uni.$off('newFriendRequest', this.handleNewFriendRequest);
				
				// 移除WFC好友列表更新事件监听
				wfc.eventEmitter.removeListener(EventType.FriendListUpdate, this.onFriendListUpdate);
				
				// 移除其他可能的事件监听...
			},
			
			// 处理WFC好友列表更新事件
			onFriendListUpdate(updatedFriendIds) {
				console.log('ConversationPage: 收到好友列表更新事件', updatedFriendIds);
				
				// 检查是否与当前会话相关
				if (this.sharedConversationState.currentConversationInfo &&
					this.sharedConversationState.currentConversationInfo.conversation.type === 0) {
					
					const currentUserId = this.sharedConversationState.currentConversationInfo.conversation.target;
					
					// 检查当前会话的用户是否在更新的好友列表中
					if (updatedFriendIds && updatedFriendIds.includes(currentUserId)) {
						console.log('当前会话用户成为了好友，隐藏添加好友提示');
						
						// 强制刷新好友状态检查
						const shouldShow = this.checkShouldShowAddFriendTip(true);
						this.showAddFriendTip = shouldShow;
						
						// 如果确实成为了好友，触发全局事件通知其他可能需要更新的页面
						if (!shouldShow) {
							uni.$emit('friendStatusChanged', {
								userId: currentUserId,
								isFriend: true,
								
								fromServer: true,
								timestamp: Date.now()
							});
							
							// 强制视图更新
							this.$forceUpdate();
						}
					}
				}
			},

			// 处理单条消息已读状态更新
			handleMessageReadStatusUpdate(data) {
				const {
					type,
					messageId,
					isRead = false,
					readCount= 0,
					fromServer = false,
					timestamp
				} = data;
				const messageList = this.sharedConversationState.currentConversationMessageList || [];
				if (!messageList || messageList.length <= 0) {
					console.log('[会话页] 当前会话没有消息，无法更新已读状态');
					return;
				}
        // console.log('[会话页] -----------------------> handleMessageReadStatusUpdate :', messageList[messageList.length - 1])
        //单聊的话 传过来的 messageId 是对方userId
        if (type === ConversationType.Single) {
          const targetUserId = messageList[0].conversation.target
          if (targetUserId !== messageId){
            return;
          }
          messageList.forEach(targetMessage => {
            if (!targetMessage.isRead) {
              // console.log('[会话页] 更新消息的已读状态:', messageId);
              targetMessage.isRead = true;
              if (!targetMessage.readTime || targetMessage.readTime < timestamp) {
                targetMessage.readTime = timestamp;
              }
              if (fromServer) {
                targetMessage.serverConfirmedRead = true;
              }
            }
          });
          // 强制视图更新
          this.$forceUpdate();
          return;
        }
        if (type === ConversationType.Group) {
          const targetMessage = messageList.find(msg => {
            const messageUidStr = `${msg.messageUid.high}-${msg.messageUid.low}`
            return (messageId === messageUidStr)
          });
          // 合并后的对象更新
          const updates = {};
          const cacheUidKey = `group_msg_read_count_uid_${messageId}`;
          const cachedCount = getItem(cacheUidKey);
          if (cachedCount && cachedCount.count > 0) {
            // 按优先级顺序处理不同条件
            if (!targetMessage.isRead) {
              updates.isRead = true;
            }
            if (!targetMessage.readTime || targetMessage.readTime < cachedCount.timestamp) {
              updates.readTime = cachedCount.timestamp;
            }
            if (cachedCount.fromServer) {
              updates.serverConfirmedRead = true;
            }
            if (cachedCount.count > targetMessage.readCount) {
              updates.readCount = cachedCount.count;
            }
            // 一次性合并更新
            Object.assign(targetMessage, updates);
          }
          this.$forceUpdate();
        }
      },

			// 处理批量消息已读状态更新
			handleBatchMessageReadStatusUpdate(data) {
				const {
					messageIds,
					conversationId,
					timestamp,
					serverConfirmed
				} = data;
				console.log('[会话页] 收到批量消息已读状态更新:',
					messageIds ? messageIds.length : 0, '条消息',
					serverConfirmed ? '(服务器确认)' : '(本地标记)');

				// 检查是否为当前会话
				if (conversationId && this.conversationInfo &&
					this.conversationInfo.conversation &&
					this.conversationInfo.conversation.target !== conversationId) {
					console.log('[会话页] 不是当前会话的消息更新，忽略');
					return;
				}
        // 找到对应的消息并更新
        const messageList = this.sharedConversationState.currentConversationMessageList || [];
				// 更新所有对应的消息
				if (messageIds && messageIds.length > 0 &&
					messageList && messageList.length > 0) {

					let updatedCount = 0;

					messageIds.forEach(msgId => {
						const targetMessage = messageList.find(
							msg => (msg.messageId === msgId || msg.id === msgId)
						);

						if (targetMessage) {
							targetMessage.isRead = true;
							targetMessage.readTime = timestamp;

							if (serverConfirmed) {
								targetMessage.serverConfirmedRead = true;
							}

							updatedCount++;
						}
					});

					if (updatedCount > 0) {
						console.log('[会话页] 已更新', updatedCount, '条消息的已读状态');
						// 强制视图更新
						this.$forceUpdate();
					}
				}
			},

			// 处理好友状态变化事件
			handleFriendStatusChange(data) {
				console.log('收到好友状态变化事件:', data);

				// 检查是否与当前会话相关
				if (this.sharedConversationState.currentConversationInfo &&
					this.sharedConversationState.currentConversationInfo.conversation.type === 0) {

					const currentUserId = this.sharedConversationState.currentConversationInfo.conversation.target;
					if (data.userId === currentUserId) {
						console.log('当前会话用户的好友状态发生变化，更新添加好友提示');

						// 强制刷新好友状态检查
						const shouldShow = this.checkShouldShowAddFriendTip(true);
						this.showAddFriendTip = shouldShow;
						
						// 强制视图更新
						this.$forceUpdate();
						
						console.log('好友状态变化处理完成:', { userId: currentUserId, isFriend: data.isFriend, shouldShow });
					}
				}
			},

			// 处理好友请求发送事件
			handleFriendRequestSent(data) {
				console.log('收到好友请求发送事件:', data);

				// 检查是否与当前会话相关
				if (this.sharedConversationState.currentConversationInfo &&
					this.sharedConversationState.currentConversationInfo.conversation.type === 0) {

					const currentUserId = this.sharedConversationState.currentConversationInfo.conversation.target;
					if (data.userId === currentUserId) {
						console.log('已向当前会话用户发送好友请求，隐藏添加好友提示');
						this.showAddFriendTip = false;
					}
				}
			},

			// 处理新的好友请求事件
			handleNewFriendRequest(data) {
				console.log('收到新的好友请求事件:', data);

				// 检查是否与当前会话相关
				if (this.sharedConversationState.currentConversationInfo &&
					this.sharedConversationState.currentConversationInfo.conversation.type === 0) {

					const currentUserId = this.sharedConversationState.currentConversationInfo.conversation.target;
					if (data.userId === currentUserId) {
						console.log('收到来自当前会话用户的好友请求，隐藏添加好友提示');
						this.showAddFriendTip = false;
					}
				}
			},

			// 检查用户的好友状态
			checkFriendStatus(userId) {
				if (!userId) return;

				try {
					// 获取用户信息
					const userInfo = wfc.getUserInfo(userId);

					// 直接通过SDK检查好友关系
					const isFriend = wfc.isMyFriend(userId);
					if (isFriend) {
						this.showAddFriendTip = false;
						return;
					}

					// 检查是否已发送好友请求
					try {
						const sentFriendRequests = uni.getStorageSync('sentFriendRequests') || [];
						const hasSentRequest = sentFriendRequests.some(req =>
							req.userId === userId &&
							req.status === 'sent' &&
							(Date.now() - req.timestamp < 86400000) // 24小时内发送的请求
						);

						if (hasSentRequest) {
							this.showAddFriendTip = false;
							return;
						}
					} catch (err) {
						console.error('检查已发送好友请求失败:', err);
					}

					// 未确认是好友且未发送请求，显示添加好友提示
					this.showAddFriendTip = true;
				} catch (error) {
					console.error('检查好友状态失败:', error);
					// 失败时默认不显示
					this.showAddFriendTip = false;
				}
			},
			async sendConversationMessage(conversation, messageContent, toUsers = [], preparedCB = null, progressCB = null,
				successCB = null, failCB = null) {
				// 检查是否被对方拉黑
				if (conversation.type === ConversationType.Single) {
					const targetUserId = conversation.target;
					if (wfc.isBlackListed(targetUserId)) {
						// 如果被拉黑，显示发送失败状态
						const message = new Message(conversation, messageContent);
						message.status = MessageStatus.SendFailure;
						message.failureReason = '对方已将您拉黑';
						store.insertMessage(conversation, messageContent, MessageStatus.SendFailure);
						if (failCB) {
							failCB(message);
						}
						return;
					}
				}

				// 正常发送消息
				wfc.sendConversationMessage(conversation, messageContent, toUsers, preparedCB, progressCB, successCB,
					failCB);
			},
			showGroupAnnouncement() {
				if (!this.conversationInfo || !this.conversationInfo.conversation._target || !this.conversationInfo.conversation._target.extra) {
					this.announcementRichTextHtml = '';
				} else {
					try {
						const extra = JSON.parse(this.conversationInfo.conversation._target.extra);
						if (extra.groupAnnouncement) {
							// 解析公告中的文本和图片
							const announcement = extra.groupAnnouncement;
							// 匹配图片标记：[图片:URL]
							const imgRegex = /\[图片:(.*?)\]/g;
							const imgMatches = [...announcement.matchAll(imgRegex)];

							if (imgMatches.length > 0) {
								// 提取图片URL
								this.announcementRichTextHtml = announcement.replace(imgRegex, '').trim();
							} else {
								this.announcementRichTextHtml = announcement;
							}

							// 标记公告为已读
							this.markAnnouncementAsRead(announcement);
							// 立即更新本地状态
							this.announcementReadLocal = true;
						} else {
							this.announcementRichTextHtml = '';
						}
					} catch (error) {
						console.error('解析群公告失败:', error);
						this.announcementRichTextHtml = '';
					}
				}
				this.showAnnouncementModal = true;
			},

			// 标记公告为已读的方法
			markAnnouncementAsRead(announcement) {
				try {
					// 获取群组ID
					const groupId = this.conversationInfo.conversation.target;

					// 生成公告的唯一标识
					const announcementId = `${groupId}_${this.getAnnouncementHash(announcement)}`;

					// 从本地存储中获取已读公告列表
					const readAnnouncements = getItem('readAnnouncements') || '{}';
					const readAnnouncementsObj = JSON.parse(readAnnouncements);

					// 标记当前公告为已读
					readAnnouncementsObj[announcementId] = Date.now();

					// 保存回本地存储
					setItem('readAnnouncements', JSON.stringify(readAnnouncementsObj));
				} catch (error) {
					console.error('标记群公告已读失败:', error);
				}
			},

			// 计算公告内容的哈希值，用于生成唯一标识
			getAnnouncementHash(announcement) {
				let hash = 0;
				if (announcement.length === 0) return hash;

				for (let i = 0; i < announcement.length; i++) {
					const char = announcement.charCodeAt(i);
					hash = ((hash << 5) - hash) + char;
					hash = hash & hash; // Convert to 32bit integer
				}

				return Math.abs(hash).toString(16); // 转为16进制正整数
			},
			closeAnnouncementModal() {
				this.showAnnouncementModal = false;
				// 关闭后不需要额外操作，因为已经在showGroupAnnouncement中设置了announcementReadLocal
			},
			previewAnnouncementImage(index) {
				if (!this.announcementRichTextHtml || this.announcementRichTextHtml.length === 0) return;

				uni.previewImage({
					urls: [this.announcementRichTextHtml],
					current: index,
					indicator: 'number',
					loop: true,
					success: () => {
						console.log('图片预览成功');
					},
					fail: (err) => {
						console.error('图片预览失败:', err);
					}
				});
			},
			// 添加导航到群组资料页面的方法
			navigateToGroupProfile() {
				if (this.isGroupConversation) {
					uni.navigateTo({
						url: `/pages/group/GroupProfile?groupId=${this.conversationInfo.conversation.target}`,
						fail: (err) => {
							console.error('导航到GroupProfile失败:', err);
							uni.showToast({
								title: '打开页面失败',
								icon: 'none'
							});
						}
					});
				}
			},

			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 处理导航栏高度更新事件
			onHeaderHeightUpdate(height) {
				console.log('导航栏高度更新:', height);
				this.headerHeight = height;
			},

			// 处理点击更多按钮的事件
			onMoreButtonClick() {
				if (this.conversationInfo.conversation.type === ConversationType.Single) {
					uni.navigateTo({
						url: '/pages/conversation/SingleConversationInfoPage',
						success: (res) => {
							res.eventChannel.emit('conversationInfo', {
								conversationInfo: this.conversationInfo
							});
						},
						fail: (err) => {
							console.log('nav to SingleConversationInfoPage err', err);
						}
					});
				} else if (this.conversationInfo.conversation.type === ConversationType.Group) {
					uni.navigateTo({
						url: '/pages/conversation/GroupConversationManagePage',
						success: (res) => {
							res.eventChannel.emit('conversationInfo', {
								conversationInfo: this.conversationInfo
							});
						},
						fail: (err) => {
							console.log('nav to GroupConversationManagePage err', err);
						}
					});
				} else {
					uni.showToast({
						title: 'TODO 暂不支持该会话类型',
						icon: 'none'
					})
				}
			},

			// 处理触摸开始事件
			handleTouchStart(e) {
				// 添加事件对象的安全检查
				if (!e || !e.touches || !e.touches[0]) {
					console.warn('handleTouchStart: 无效的事件对象');
					return;
				}
				// 仅处理事件，不阻止默认行为
				this.touchStartY = e.touches[0].clientY;
			},

			// 处理触摸移动事件
			handleTouchMove(e) {
				// 添加事件对象的安全检查
				if (!e || !e.touches || !e.touches[0]) {
					console.warn('handleTouchMove: 无效的事件对象');
					return;
				}
				// 仅处理需要的逻辑，不阻止默认行为
				const currentY = e.touches[0].clientY;
				const diff = currentY - this.touchStartY;

				// 可以在这里添加自定义逻辑
				// 例如，检测是否需要下拉刷新
			},

			// 处理触摸结束事件
			handleTouchEnd(e) {
				// 添加事件对象的安全检查
				if (!e || !e.changedTouches || !e.changedTouches[0]) {
					console.warn('handleTouchEnd: 无效的事件对象');
					return;
				}
				// 处理触摸结束的逻辑
			},

			// 添加赞赏相关方法
			toggleliwu() {
				let newState = !this.showliwu;
				if (newState) {
					appServerApi.getWallet().then(response => {
						this.gold = response?.data?.gold || 0
						this.showliwu = newState
					})
				} else {
					this.showliwu = newState
				}
				this.lwdata = false;
			},

			onliwu(e) {
				this.lwdata = e
			},

			onsendliwu() {
				const liwuId = this.lwdata.id;
				const price = this.lwdata.price;
				if (price * this.lwnum > this.gold) {
					uni.showToast({
						title: '金币不足，请先充值',
						icon: 'none',
						duration: 2000
					})
				}else if (this.lwnum < 1) {
					uni.showToast({
						title: '礼物数量至少是1',
						icon: 'none',
						duration: 2000
					})
				}else {
					appServerApi.sendGift({
						userId: this.liwuTargetMessage.from,
						giftId: liwuId,
						num: this.lwnum
					}).then((res) => {
						store.sendGift(this.conversationInfo.conversation, liwuId, this.lwnum,this.liwuTargetMessage.from);
						this.toggleliwu();
					}).catch(err => {
						uni.showToast({
							title: err,
							icon: 'none',
							duration: 2000
						})
					})
				}
			},

			handleRecharge() {
				// 根据平台选择支付方式
				if (uni.getSystemInfoSync().platform === 'ios') {
					this.iosPaymentVisible = true;
				} else {
					this.paymentVisible = true;
				}
			},

			handlePaymentClose() {
				this.paymentVisible = false
				this.iosPaymentVisible = false
			},

			async handlePaymentConfirm({
				status
			}) {
				try {
					if (status) {
						console.log('支付确认', status)
						appServerApi.getWallet().then(response => {
							this.gold = response?.data?.gold || 0
							this.paymentVisible = false
							this.iosPaymentVisible = false
						})
					}
				} catch (error) {
					this.$message.error('支付失败')
				}
			},

			// 添加遮罩层点击处理方法
			handleMaskClick(e) {
				this.toggleliwu();
			},

			// 优化添加好友状态检查，减少重复计算
			checkShouldShowAddFriendTip(forceRefresh = false) {
				try {
					// 如果强制刷新，清除缓存
					if (forceRefresh) {
						this.cachedShouldShowAddFriendTip = null;
					}
					
					// 使用缓存避免重复计算（除非强制刷新）
					if (!forceRefresh && this.cachedShouldShowAddFriendTip !== null) {
						return this.cachedShouldShowAddFriendTip;
					}

					// 检查会话信息是否存在
					if (!this.conversationInfo) {
						this.cachedShouldShowAddFriendTip = false;
						return false;
					}

					// 检查会话对象是否存在
					const conversation = this.conversationInfo.conversation;
					if (!conversation) {
						this.cachedShouldShowAddFriendTip = false;
						return false;
					}

					// 检查目标用户ID是否存在
					const targetUserId = conversation.target;
					if (!targetUserId) {
						this.cachedShouldShowAddFriendTip = false;
						return false;
					}

					// 检查会话类型是否为单聊
					if (conversation.type !== ConversationType.Single) {
						this.cachedShouldShowAddFriendTip = false;
						return false;
					}

					// 检查是否为自己和自己的会话
					if (targetUserId === wfc.getUserId()) {
						this.cachedShouldShowAddFriendTip = false;
						return false;
					}

					// 检查目标用户信息是否存在
					const targetUserInfo = wfc.getUserInfo(targetUserId);
					if (!targetUserInfo) {
						this.cachedShouldShowAddFriendTip = false;
						return false;
					}

					// 检查是否为好友关系
					const isFriend = wfc.isMyFriend(targetUserId);
					const result = !isFriend;
					
					// 只有在非强制刷新时才设置缓存过期时间
					if (!forceRefresh) {
						this.cachedShouldShowAddFriendTip = result;
						// 设置缓存过期时间（5秒后重新检查）
						setTimeout(() => {
							this.cachedShouldShowAddFriendTip = null;
						}, 5000);
					} else {
						// 强制刷新时，立即更新缓存但不设置过期时间
						this.cachedShouldShowAddFriendTip = result;
					}
					
					// console.log('好友状态检查结果:', { targetUserId, isFriend, result, forceRefresh });
					
					return result;
				} catch (error) {
					console.error('Error in checkShouldShowAddFriendTip:', error);
					this.cachedShouldShowAddFriendTip = false;
					return false;
				}
			},

			// 优化群公告检查，减少JSON解析次数
			checkHasGroupAnnouncement() {
				if (!this.conversationInfo || !this.conversationInfo.conversation._target || !this.conversationInfo.conversation._target.extra) {
					this.cachedHasGroupAnnouncement = false;
					return false;
				}

				// 如果缓存存在且未过期，直接返回
				const now = Date.now();
				if (this.cachedHasGroupAnnouncement !== null && 
					now - this.lastAnnouncementCheck < 10000) { // 10秒缓存
					return this.cachedHasGroupAnnouncement;
				}

				try {
					const extra = JSON.parse(this.conversationInfo.conversation._target.extra);
					const result = !!extra.groupAnnouncement;
					this.cachedHasGroupAnnouncement = result;
					this.lastAnnouncementCheck = now;
					
					// 同时缓存预览文本
					if (result && extra.groupAnnouncement) {
						const announcement = extra.groupAnnouncement;
						const pureText = announcement.replace(/\[图片:.*?\]/g, '[图片]').trim();
						this.cachedAnnouncementPreview = pureText.length > 20 ? 
							pureText.substring(0, 20) + '...' : pureText;
					}
					
					return result;
				} catch (error) {
					console.error('解析群公告失败:', error);
					this.cachedHasGroupAnnouncement = false;
					this.lastAnnouncementCheck = now;
					return false;
				}
			},

			// 优化滚动到底部的方法，减少DOM操作
			scrollToBottom: debounce(function() {
				this.$nextTick(() => {
					// 当需要滚动到底部时，优先使用消息列表底部锚点
					// 这样可以确保滚动到真正的底部，而不是最后一条消息的顶部
					if (this.sharedConversationState.currentConversationMessageList.length > 0) {
						// 使用消息列表底部锚点，确保滚动到最底部
						this.scrollIntoView = 'message-list-bottom';
						
						// 清除scrollIntoView，避免影响后续操作
						setTimeout(() => {
							this.scrollIntoView = '';
						}, 200);
					}
				});
			}, 100),

			mentionMessageSender(message) {
				this.$refs.messageInputView.mention(message.conversation.target, message.from);
			},
			
			// 处理长按头像@用户功能
			handleMentionUser(data) {
				// 只在群聊中支持@用户功能
				if (!this.isGroupConversation) {
					return;
				}
				
				// 调用MessageInputView的mention方法
				if (this.$refs.messageInputView) {
					this.$refs.messageInputView.mentionUser(data.userId, data.userInfo);
				}
			},
			// 更新临时会话提示信息
			updateTempChatTip() {
				if (this.sharedConversationState.currentConversationInfo) {
					this.tempChatTip = messageLimitManager.getTempChatTip(
						this.sharedConversationState.currentConversationInfo.conversation
					);
				} else {
					this.tempChatTip = null;
				}
			},
		},

		mounted() {
			uni.setNavigationBarTitle({
				title: this.targetUserOnlineStateDesc ? this.conversationTitle +
						`(${this.targetUserOnlineStateDesc})` : this
						.conversationTitle
			});
			
			// 初始加载时，使用智能滚动定位到最新消息
			if (this.sharedConversationState.currentConversationMessageList.length > 0) {
				const lastMessage = this.sharedConversationState.currentConversationMessageList[
					this.sharedConversationState.currentConversationMessageList.length - 1
				];
				
				// 延迟执行，确保DOM已完全渲染
				this.$nextTick(() => {
					setTimeout(() => {
						this.smartScrollToMessage(lastMessage.messageId);
					}, 100);
				});
			} else {
				this.scrollToBottom();
			}
			
			store.clearConversationUnreadStatus(this.conversationInfo.conversation);

			// 初始化导航栏高度（状态栏+导航栏固定高度）
			if (this.headerHeight === 0) {
				const sysInfo = uni.getSystemInfoSync();
				this.headerHeight = sysInfo.statusBarHeight + 44; // 状态栏高度 + 导航栏固定高度
			}

			// 初始化群公告已读状态
			if (this.isGroupConversation && this.hasGroupAnnouncement) {
				try {
					if (this.conversationInfo.conversation._target && this.conversationInfo.conversation._target.extra) {
						const extra = JSON.parse(this.conversationInfo.conversation._target.extra);
						if (extra.groupAnnouncement) {
							const groupId = this.conversationInfo.conversation.target;
							const announcement = extra.groupAnnouncement;
							const announcementId = `${groupId}_${this.getAnnouncementHash(announcement)}`;

							const readAnnouncements = getItem('readAnnouncements') || '{}';
							const readAnnouncementsObj = JSON.parse(readAnnouncements);

							this.announcementReadLocal = !!readAnnouncementsObj[announcementId];
						}
					}
				} catch (error) {
					console.error('初始化群公告状态失败:', error);
					this.announcementReadLocal = false;
				}
			}

			this.keyboardHeight = getItem('keyboardHeight');
			// 监听输入面板高度变化
			this.$eventBus.$on('inputPanelHeightChanged', (height) => {
				// 当输入面板（表情包、扩展面板等）弹起时，检查用户是否在底部附近
				// 如果在底部附近，则滚动到底部
				
				// 方法1：检查是否应该自动滚动
				const shouldAutoScroll = this.sharedConversationState.shouldAutoScrollToBottom;
				
				// 方法2：检查最后滚动位置是否接近底部（这里简化判断，认为用户刚滚动回底部）
				const wasNearBottom = this.lastScrollTop !== undefined;
				
				// 方法3：检查是否有消息（避免空会话时的错误）
				const hasMessages = this.sharedConversationState.currentConversationMessageList.length > 0;
				
				// 如果满足任一条件，则认为需要滚动到底部
				if (shouldAutoScroll || (wasNearBottom && hasMessages)) {
					// 确保设置自动滚动标志
					store.setShouldAutoScrollToBottom(true);
					// 延迟滚动，确保面板动画完成
					this.$nextTick(() => {
						setTimeout(() => {
							this.scrollToBottom();
						}, 100);
					});
				}
			});
			
			// 监听输入框焦点变化
			this.$eventBus.$on('inputFocusChanged', (focused) => {
				if (focused && this.sharedConversationState.shouldAutoScrollToBottom) {
					// 输入框获得焦点时，如果应该自动滚动，则滚动到底部
					this.$nextTick(() => {
						setTimeout(() => {
							this.scrollToBottom();
						}, 150); // 给键盘动画更多时间
					});
				}
			});
			
			this.$eventBus.$on('openMessageContextMenu', ([event, message]) => {
				this.showMessageContextMenu(event, message)
			});
			
			// 监听长按头像@用户事件
			this.$eventBus.$on('mentionUser', (data) => {
				this.handleMentionUser(data);
			});

			// 设置事件监听器
			this.setupEventListeners();

			// 监听接收消息事件
			wfc.eventEmitter.on(EventType.ReceiveMessage, this.onReceiveMessage);
		},

		beforeDestroy() {
			// 移除事件监听器
			this.removeEventListeners();
			
			// 移除自定义事件监听器
			this.$eventBus.$off('inputPanelHeightChanged');
			this.$eventBus.$off('inputFocusChanged');
			this.$eventBus.$off('openMessageContextMenu');
			this.$eventBus.$off('mentionUser');

			// 移除接收消息监听
			wfc.eventEmitter.removeListener(EventType.ReceiveMessage, this.onReceiveMessage);

			// 清理其他资源
			if (this.ongoingCallTimer) {
				clearInterval(this.ongoingCallTimer);
				this.ongoingCallTimer = null;
			}
		},

		beforeUpdate() {},
		updated() {
			if (!this.sharedConversationState.currentConversationInfo) {
				return;
			}

			// 如果消息列表需要自动滚动到底部
			if (this.sharedConversationState.shouldAutoScrollToBottom) {
				this.scrollToBottom();
			}

			// 处理未读消息
			if (this.sharedConversationState.currentConversationInfo) {
				let unreadCount = this.sharedConversationState.currentConversationInfo.unreadCount;
				if (unreadCount.unread > 0) {
					store.clearConversationUnreadStatus(this.sharedConversationState.currentConversationInfo.conversation);
				}
			}

			this.conversationInfo = this.sharedConversationState.currentConversationInfo;

			// 更新导航栏标题，确保显示最新的备注名
			this.updateConversationTitle();
		},

		computed: {
			conversationTitle() {
				let info = this.sharedConversationState.currentConversationInfo;
				if (!info) return '';

				let isGroup = info.conversation.type === ConversationType.Group;
				if (isGroup) {
					return info.conversation._target._displayName + '(' + info.conversation._target.memberCount + ')';
				} else {
					// 单聊优先使用备注名(alias)
					let target = info.conversation._target;
					if (target.alias) {
						return target.alias;
					} else if (target.friendAlias) {
						return target.friendAlias;
					} else {
						return target._displayName;
					}
				}
			},
			isGroupConversation() {
				return this.conversationInfo && this.conversationInfo.conversation &&
					this.conversationInfo.conversation.type === ConversationType.Group;
			},
			hasGroupAnnouncement() {
				// 使用优化后的检查方法
				return this.checkHasGroupAnnouncement();
			},
			announcementPreview() {
				// 使用缓存的预览文本
				// if (this.cachedAnnouncementPreview && 
				// 	Date.now() - this.lastAnnouncementCheck < 10000) {
				// 	return this.cachedAnnouncementPreview;
				// }

				if (!this.conversationInfo || !this.conversationInfo.conversation._target || !this.conversationInfo.conversation._target.extra) {
					return '';
				}
				try {
					const extra = JSON.parse(this.conversationInfo.conversation._target.extra);
					if (!extra.groupAnnouncement) return '';

					// 移除图片标记以显示纯文本
					const announcement = extra.groupAnnouncement;
					const pureText = announcement.replace(/<[^>]+>/g, '');

					// 替换 [图片:URL] 为 [图片]
					const announcementText = pureText.replace(/\\[图片:.*?\\]/g, '[图片]').trim();

					// 返回截断的预览文本
					return announcementText.length > 20 ? announcementText.substring(0, 20) + '...' : announcementText;
				} catch (error) {
					console.error('解析群公告预览失败:', error);
					return '';
				}
			},
			targetUserOnlineStateDesc() {
				let info = this.sharedConversationState.currentConversationInfo;
				return info ? info.conversation._targetOnlineStateDesc : null;
			},
			loadingIdentifier() {
				let conversation = this.sharedConversationState.currentConversationInfo.conversation;
				return conversation.type + '-' + conversation.target + '-' + conversation.line;
			},
			currentVoiceMessage() {
				let voice = this.sharedConversationState.currentVoiceMessage;
				if (voice) {
					this.playVoice(voice);
				} else {
					if (innerAudioContext) {
						innerAudioContext.stop();
						innerAudioContext = null;
					}
				}
				return null;
			},

			lastMessageId() {
				return this.conversationInfo.lastMessage ? this.conversationInfo.lastMessage.messageId : '';
			},
			shouldShowAddFriendTip() {
				// 检查当前会话信息
				if (!this.sharedConversationState.currentConversationInfo) {
					return false;
				}
				
				const conversation = this.sharedConversationState.currentConversationInfo.conversation;
				
				// 只对单聊类型进行处理
				if (!conversation || conversation.type !== ConversationType.Single) {
					return false;
				}
				
				const targetUserId = conversation.target;
				
				// 自己和自己的会话不显示添加好友提示
				if (!targetUserId || targetUserId === wfc.getUserId()) {
					return false;
				}
				
				// 使用优化后的检查方法，但在计算属性中不使用缓存（始终获取最新状态）
				return this.checkShouldShowAddFriendTip(false);
			},
			groupedMenuItems() {
				const groups = [];
				for (let i = 0; i < this.contextMenuItems.length; i += 2) {
					groups.push(this.contextMenuItems.slice(i, i + 2));
				}
				return groups;
			},
			// 判断群公告是否已读
			isAnnouncementRead() {
				if (!this.conversationInfo || !this.conversationInfo.conversation._target || !this.conversationInfo.conversation._target.extra) {
					return true; // 无公告时视为已读
				}

				try {
					const extra = JSON.parse(this.conversationInfo.conversation._target.extra);
					if (!extra.groupAnnouncement) return true; // 无公告时视为已读

					// 获取群组ID和公告内容用于生成唯一标识
					const groupId = this.conversationInfo.conversation.target;
					const announcement = extra.groupAnnouncement;

					// 生成公告的唯一标识
					const announcementId = `${groupId}_${this.getAnnouncementHash(announcement)}`;

					// 检查本地存储中是否已标记为已读
					const readAnnouncements = getItem('readAnnouncements') || '{}';
					const readAnnouncementsObj = JSON.parse(readAnnouncements);

					return !!readAnnouncementsObj[announcementId];
				} catch (error) {
					console.error('检查群公告阅读状态失败:', error);
					return false;
				}
			},
			// 临时会话提示信息
			tempChatTipMessage() {
				if (!this.sharedConversationState.currentConversationInfo) {
					return null;
				}
				
				const conversation = this.sharedConversationState.currentConversationInfo.conversation;
				return messageLimitManager.getTempChatTip(conversation);
			},
		},

		watch: {
			shouldShowAddFriendTip: {
				immediate: true,
				handler(newVal) {
					this.showAddFriendTip = newVal;
				}
			},
			// 优化会话信息变化监听，添加防抖
			'conversationInfo.conversation.target': {
				handler: debounce(function() {
					// 清除缓存，强制重新计算
					this.cachedHasGroupAnnouncement = null;
					this.cachedAnnouncementPreview = '';
					this.lastAnnouncementCheck = 0;
					
					if (this.isGroupConversation && this.hasGroupAnnouncement) {
						try {
							if (this.conversationInfo.conversation._target && this.conversationInfo.conversation._target.extra) {
								const extra = JSON.parse(this.conversationInfo.conversation._target.extra);
								if (extra.groupAnnouncement) {
									const groupId = this.conversationInfo.conversation.target;
									const announcement = extra.groupAnnouncement;
									const announcementId = `${groupId}_${this.getAnnouncementHash(announcement)}`;

									const readAnnouncements = getItem('readAnnouncements') || '{}';
									const readAnnouncementsObj = JSON.parse(readAnnouncements);

									this.announcementReadLocal = !!readAnnouncementsObj[announcementId];
								}
							}
						} catch (error) {
							console.error('更新群公告状态失败:', error);
							this.announcementReadLocal = false;
						}
					}
				}, 300)
			},
			// 优化添加好友提示状态变化监听，添加防抖
			showAddFriendTip: {
				handler: debounce(function(newVal) {
					// 当添加好友提示显示或隐藏时，重新计算滚动位置
					this.$nextTick(() => {
						// 如果是最新消息，则滚动到底部
						if (this.sharedConversationState.shouldAutoScrollToBottom) {
							this.scrollToBottom();
						}
					});
				}, 100)
			},
			// 优化群公告已读状态变化监听，添加防抖
			announcementReadLocal: {
				handler: debounce(function(newVal) {
					// 当群公告已读状态变化时，重新计算滚动位置
					this.$nextTick(() => {
						// 如果是最新消息，则滚动到底部
						if (this.sharedConversationState.shouldAutoScrollToBottom) {
							this.scrollToBottom();
						}
					});
				}, 100)
			},
			// 监听会话变化，更新临时会话提示
			'sharedConversationState.currentConversationInfo'(newVal) {
				this.updateTempChatTip();
			},
		},

		directives: {
			// ClickOutside
		},

		// 添加onBackPress生命周期方法
		onBackPress() {
			// 如果显示上下文菜单，先关闭菜单
			if (this.showContextMenu) {
				this.showContextMenu = false;
				return true; // 返回true表示自己处理返回事件
			}

			// 如果群公告弹窗显示中，关闭弹窗
			if (this.showAnnouncementModal) {
				this.closeAnnouncementModal();
				return true;
			}

			// 返回false表示不拦截，由系统处理返回事件
			return false;
		},
	};
</script>

<style lang="scss" scoped>
	/* 添加标题样式 */
	.title-container {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		width: 100%;
		flex: 1;
		min-width: 0; /* 允许flex子元素收缩 */
	}

	.title {
		font-size: 17px;
		font-weight: 500;
		color: #333;
		flex: 1;
		text-align: center;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-right: 4px;
		min-width: 0; /* 允许文本收缩 */
		/* 添加右边距，让群名片按钮离标题更近 */
	}

	/* 群名片按钮样式 */
	.group-profile-button {
		margin-left: 0;
		padding: 0;
		width: 40px;
	}

	/* 右侧按钮样式 */
	.right-buttons {
		display: flex;
		align-items: center;
	}

	.navbar-button {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 44px;
		transition: width 0.2s ease;
	}

	.more-button {
		width: 40px;
	}

	/* 导入自定义图标字体 */
	@font-face {
		font-family: 'iconfont2';
		src: url('/static/iconfonts/iconfont2.ttf') format('truetype');
	}

	@font-face {
		font-family: 'wx-iconfont';
		src: url('/static/iconfonts/wx_iconfont.ttf') format('truetype');
	}

	.iconfont2 {
		font-family: 'iconfont2';
		font-size: 24px;
		color: #333;
	}

	.wx-iconfont {
		font-family: 'wx-iconfont';
		font-size: 24px;
		color: #333;
	}

	.conversation-empty-container {
		height: 100%;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		/*border-left: 1px solid #e6e6e6;*/
	}

	.conversation-content-container {
		position: relative;
		display: flex;
		/* 移除固定height计算，改为使用内联样式动态计算 */
		overflow: hidden;
		flex-direction: column;
		background-color: #f5f5f5;
		/*padding: 0 12px;*/
	}


	.viewider-handler::before {
		cursor: row-resize;
		content: '';
		display: block;
		width: 100%;
		height: 3px;
		border-top: 1px solid #e2e2e2;
		margin: 0 auto;
	}

	.conversation-content-container .ongoing-call-container {
		width: 100%;
		display: flex;
		flex-direction: column;
		background: white;
	}

	.ongoing-call-item {
		padding: 10px 20px;
		display: flex;
		border-bottom: 1px solid lightgrey;
		align-items: center;
	}

	.ongoing-call-item text {
		flex: 1;
	}

	.ongoing-call-item button {
		//padding: 5px 10px;
		border: 1px solid #e5e5e5;
		border-radius: 3px;
	}

	.ongoing-call-item button:active {
		border: 1px solid #4168e0;
	}

	.message-list-container {
		min-height: 100px;
		flex: 1 1 auto;
		overflow: hidden;
	}

	.message-list {
		height: 100%;
		overflow: auto;
	}

	>>>.uni-scroll-view-refresher {
		max-height: 100px;
		/* 设置下拉刷新区域的最大高度为200像素 */
	}

	/* 添加浮动的添加好友提示 */
	.add-friend-tip-floating {
		position: fixed;
		top: calc(var(--status-bar-height, 20px) + 44px + 8px);
		left: 16px;
		right: 16px;
		background: linear-gradient(to right, #F8F9FF, #EEF2FF);
		border-radius: 12px;
		padding: 12px 16px;
		box-shadow: 0 2px 8px rgba(56, 107, 246, 0.08);
		border: 1px solid rgba(56, 107, 246, 0.1);
		z-index: 100;
		display: flex;
		justify-content: space-between;
		align-items: center;
		animation: fadeIn 0.3s ease-in-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(-10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.add-friend-tip-floating .tip-content {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.add-friend-tip-floating .tip-text {
		font-size: 14px;
		color: #333333;
		font-weight: 500;
		margin-bottom: 0;
	}

	/* 临时会话提示行样式 */
	.temp-chat-tip-row {
		display: flex;
		align-items: center;
		margin-top: 6px;
	}

	.temp-chat-tip-row .temp-tip-text {
		font-size: 13px;
		color: #666;
		line-height: 1.4;
	}

	.add-friend-tip-floating .add-friend-btn {
		padding: 8px 20px;
		background: linear-gradient(135deg, #4E7CF7, #386BF6);
		color: white;
		border-radius: 20px;
		font-size: 14px;
		font-weight: 500;
		margin-left: 16px;
		box-shadow: 0 4px 8px rgba(56, 107, 246, 0.2);
		transition: all 0.2s ease;
		flex-shrink: 0;
	}

	.add-friend-tip-floating .add-friend-btn:active {
		transform: scale(0.96);
		box-shadow: 0 2px 4px rgba(56, 107, 246, 0.15);
	}

	/* 添加浮动的群公告入口 */
	.group-announcement-entry-floating {
		position: fixed;
		top: calc(var(--status-bar-height, 20px) + 44px + 8px);
		left: 16px;
		right: 16px;
		background-color: #F8F9FF;
		padding: 12px 16px;
		border-radius: 12px;
		border: 1px solid rgba(56, 107, 246, 0.08);
		z-index: 100;
		display: flex;
		align-items: center;
		animation: fadeIn 0.3s ease-in-out;
		box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
	}

	.group-announcement-entry-floating:active {
		background-color: #EEF2FF;
	}

	/* 调整两个通知同时存在时的位置 */
	.add-friend-tip-floating + .group-announcement-entry-floating {
		top: calc(var(--status-bar-height, 20px) + 44px + 78px); /* 调整第二个通知的位置，避免重叠 */
	}

	.announcement-icon {
		width: 18px;
		height: 18px;
		margin-right: 8px;
		flex-shrink: 0;
	}

	.announcement-preview {
		flex: 1;
		display: flex;
		overflow: hidden;
		align-items: center;
	}

	.announcement-label {
		font-size: 14px;
		color: #4E7CF7;
		font-weight: 500;
		flex-shrink: 0;
	}

	.announcement-content-preview {
		font-size: 14px;
		color: #666666;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	.view-detail {
		font-size: 13px;
		color: #4E7CF7;
		margin-left: 8px;
		padding: 4px 8px;
		border-radius: 10px;
		background-color: rgba(78, 124, 247, 0.1);
	}

	.bottom-menu-mask {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		background: rgba(0, 0, 0, 0.4);
		z-index: 999;
	}

	.bottom-menu {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(180deg, #E7F0FF 0%, #F5F5F5 100%);
		border-radius: 16px 16px 0 0;
		padding-bottom: env(safe-area-inset-bottom);
		transform: translateY(0);
		transition: transform 0.3s;
		padding: 30px 13px 2px 13px;
	}

	.menu-group {
		display: flex;
		flex-direction: column;
		background: #ffffff;
		margin-bottom: 8px;
		border-radius: 10px;
		overflow: hidden;
		padding: 0 15px;
	}

	.menu-item {
		height: 50px;
		display: flex;
		align-items: center;
		background: #ffffff;
		position: relative;

		text {
			font-size: 16px;
			color: #333333;
		}

		&:active {
			background: #f5f5f5;
		}

		&:not(:last-child)::after {
			content: '';
			position: absolute;
			left: 0;
			right: 0;
			bottom: 0;
			height: 1px;
			background: #E6E6E6;
			transform: scaleY(0.5);
		}

		.menu-item-icon {
			width: 22px;
			height: 22px;
			margin-right: 12px;
		}
	}

	.menu-cancel {
		height: 56px;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #ffffff;
		margin-top: 8px;
		border-radius: 12px;

		text {
			font-size: 16px;
			color: #999999;
		}

		&:active {
			background: #f5f5f5;
		}
	}

	.message-failed-icon {
		width: 16px;
		height: 16px;
		margin-right: 4px;
		color: #ff4d4f;
	}

	.announcement-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.6);
		z-index: 999;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.announcement-content {
		width: 80%;
		// padding-bottom: 20px;
		max-height: 70vh;
		background: #fff;
		border-radius: 12px;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		animation: modalFadeIn 0.3s ease;
	}

	@keyframes modalFadeIn {
		from {
			opacity: 0;
			transform: scale(0.9);
		}

		to {
			opacity: 1;
			transform: scale(1);
		}
	}

	.announcement-header {
		padding: 16px;
		border-bottom: 1px solid #eee;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.announcement-title {
		font-size: 18px;
		font-weight: 500;
		color: #333;
	}

	.close-btn {
		position: absolute;
		right: 16px;
		top: 50%;
		transform: translateY(-50%);
		font-size: 24px;
		color: #999;
		width: 30px;
		height: 30px;
		line-height: 30px;
		text-align: center;
		border-radius: 15px;

		&:active {
			background-color: #f5f5f5;
		}
	}

	.announcement-body {
		// padding: 10px 10px 50px 10px; /* 增加底部内边距 */
		// background-color: #0ff;
		margin-top: 15px;
		// padding-top: 15px;
		padding-left: 12px;
		padding-right: 10px;
		margin-bottom: 20px;
		max-height: calc(70vh - 80px);
		/* 为 rich-text 内部元素添加样式，确保换行和图片显示正常 */
		::v-deep p {
		margin-bottom: 0.5em; /* 减小段落下边距 */
		margin-top: 0; /* 段落上边距 */
		white-space: pre-wrap; /* 保留文本中的空格和换行 */
		word-break: break-word; /* 长单词换行 */
		line-height: 1.5; /* 调整段落内的行高 */
		}
		::v-deep div {
			white-space: pre-wrap; /* 对于某些编辑器可能使用div包裹，也保留空格和换行 */
			word-break: break-word; /* 长单词换行 */
		}
		::v-deep br {
			content: ''; /* 确保换行符正常显示 */
			display: block;
			// margin-bottom: 5px; /* 可选：调整换行间距 */
			margin-bottom: 0.2em; /* 尝试给 br 添加一点下边距 */
		}
		::v-deep img {
			max-width: 100% !important; /* 强制最大宽度为父容器的100% */
			height: auto !important; /* 强制高度自动，保持图片比例 */
			display: block; /* 图片作为块级元素 */
			margin: 10px 10px 15px auto !important; /* 上右下左外边距，强调下边距 */
			object-fit: contain; /* 确保图片内容适应容器，不被拉伸 */
		}
		
		.no-content {
			text-align: center;
			color: #999;
			padding: 50px 0;
			font-size: 15px;
		}
	}

	.announcement-text {
		font-size: 16px;
		color: #333;
		line-height: 1.6;
		margin-bottom: 16px;
		white-space: pre-wrap;
		word-break: break-all;
	}

	.announcement-images {
		display: flex;
		flex-direction: column;
		gap: 10px;
	}

	.announcement-image {
		width: 100%;
		border-radius: 8px;
		background-color: #f5f5f5;
	}

	.no-announcement {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 30px 0;

		text {
			color: #999;
			font-size: 14px;
		}
	}

	.rq-tca {
		position: fixed;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.70);

		.tc {
			width: 234px;
			height: 257px;
			border-radius: 20px;
			background: #FEF9F3;
			position: absolute;
			top: 50%;
			left: 50%;
			-webkit-transform: translate(-50%, -50%);
			-moz-transform: translate(-50%, -50%);
			-ms-transform: translate(-50%, -50%);
			-o-transform: translate(-50%, -50%);
			transform: translate(-50%, -50%);

			.tca {
				position: relative;
				width: 100%;
				height: 100%;

				.tca0 {
					width: 134px;
					height: 134px;
					position: absolute;
					left: 50px;
					top: -67px;
				}

				.tca1 {
					width: 100%;
					position: absolute;
					left: 0;
					top: 83px;
					text-align: center;
					color: #9F513A;
					font-family: MiSans;
					font-size: 16px;
				}

				.tca2 {
					width: 100%;
					position: absolute;
					left: 0;
					top: 124px;
					text-align: center;

					.tca2a {
						width: 141px;
						height: 45px;
						border-radius: 10px;
						border: 1px solid rgba(159, 81, 58, 0.20);
						background: #FFF;
						margin: 0 auto;
					}
				}

				.tca3 {
					width: 157px;
					height: 44px;
					line-height: 44px;
					position: absolute;
					left: 39px;
					top: 193px;
					text-align: center;
					border-radius: 100px;
					background: #FD3D58;
					color: #FFF;
					font-family: MiSans;
					font-size: 16px;
				}

				.tca4 {
					width: 20px;
					height: 20px;
					position: absolute;
					right: 10px;
					top: 10px;
					font-size: 28px;
				}

			}
		}

	}

	// 添加遮罩层样式
	.rq-ds-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);

		display: flex;
		align-items: flex-end;
	}

	// 修改赞赏弹窗样式
	.rq-ds {
		position: relative;
		width: 100%;
		height: 245px;
		background-color: #ffffff;
		border-radius: 20px 20px 0px 0px;
		padding: 10px;

		.dsb {
			padding: 6px 10px;

			.dsba {
				display: flex;
				flex-direction: row;
				flex-wrap: wrap;

				.dsba1 {
					width: 25%;
					display: flex;
					align-items: center;
					flex-direction: column;
					margin-bottom: 10px;

					.dsba1a {
						width: 72px;
						height: 72px;
						border-radius: 10px 10px 0px 0px;
					}

					.dsba1d {
						width: 72px;
						height: 24px;
						text-align: center;
						line-height: 24px;
						color: #000;
						font-size: 12px;
						border-radius: 0px 0px 10px 10px;
					}

					.dsba1b {
						color: #ffffff;
						background-color: #386BF6;
					}

					.dsba1c {}
				}
			}
		}

		.dsa {
			height: 32px;
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.dsa2 {
				width: 116px;
				height: 32px;
				border-radius: 20px;
				padding: 6px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 14px;
				color: #000;
				background: #F5F5F5;

				.dsa2a {
					width: 26px;
					height: 26px;
				}

				.dsa2b {
					width: 44px;
					overflow: hidden;
					height: 20px;
					line-height: 20px;
				}

				.dsa2c {
					color: #386BF6;
					height: 20px;
					line-height: 20px;
				}
			}
		}
	}

	.rq_liwu {
		width: 48px;
		height: 38px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 0;

		.rq_liwuimg {
			width: 28px;
			height: 28px;
		}
	}

	/* 如果两个通知都显示，则需要更大的内边距 */
	.message-list-container.has-two-notifications .message-list {
		padding-top: 140px; /* 两个通知的高度加上一些额外空间 */
	}

	/* 如果只有群公告通知显示 */
	.message-list-container.has-announcement-only .message-list {
		padding-top: 70px;
	}

	/* 如果只有添加好友通知显示 */
	.message-list-container.has-add-friend-only .message-list {
		padding-top: 70px;
	}

	/* 消息底部锚点样式 - 用于精确定位到消息底部 */
	.message-bottom-anchor {
		height: 1px;
		width: 100%;
		visibility: hidden;
		pointer-events: none;
	}

	/* 消息列表底部锚点样式 - 用于滚动到最底部 */
	.message-list-bottom-anchor {
		height: 1px;
		width: 100%;
		visibility: hidden;
		pointer-events: none;
	}

	/* 群状态提示 */
	.group-status-tip {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: #f5f5f5;
		padding: 16px 20px;
		border-top: 1px solid #e0e0e0;
		z-index: 100;
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 60px;
		/* 适配安全区域 */
		padding-bottom: calc(16px + env(safe-area-inset-bottom));
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.group-status-tip .status-message {
		font-size: 14px;
		color: #999999;
		text-align: center;
		line-height: 1.4;
	}
</style>