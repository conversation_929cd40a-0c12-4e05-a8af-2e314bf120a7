<template>
    <div class="conversation-info" v-if="conversationInfo">
        <CustomHeader>
            <template v-slot:title>
                <text class="title">群聊({{memberCount}})</text>
            </template>
        </CustomHeader>
        <div class="conversation-info-content">
            <!-- 添加canvas元素用于保存二维码图片 -->
            <canvas canvas-id="qrcodeCanvas" :style="{
                width: width + 'px',
                height: height + 'px',
                position: 'fixed',
                left: '-9999px'
            }"></canvas>
            <view class="users-container">
                <view class="users-container-header" @click="showGroupUsers">
                    <view class="header-left">群成员</view>
                    <view class="header-right">全部<i class="icon-ion-ios-arrow-right"></i></view>
                </view>
                <view class="users-container-list">
                    <view class="users-container-list-item" v-for="(user,index) in users" :key="index" @click.stop="seeUser(user)">
                        <view class="me3b1">
                            <image class="img" :src="user.portrait"></image>
                        </view>
                        <view class="me3b2">{{ user._displayName ? user._displayName : user.displayName }}</view>
                    </view>
                    <view v-if="enableAddGroupMember && !filterQuery" class="users-container-list-item" @click="showCreateConversationModal">
                        <view class="me3b1">
                            <image class="img" src="@/assets/images/group/add.png"></image>
                        </view>
                        <view class="me3b2">
                            邀请好友
                        </view>
                    </view>
                    <view v-if="enableRemoveGroupMember && !filterQuery" @click="showRemoveGroupMemberModal" class="users-container-list-item">
                        <view class="me3b1">
                            <image class="img" src="@/assets/images/group/min.png"></image>
                        </view>
                        <view class="me3b2">
                            移出好友
                        </view>
                    </view>
                </view>
            </view>
            <view class="group-info-container">
                <view class="info-item" @click="showGroupNameEdit">
                    <view class="item-label">群聊名称</view>
                    <view class="item-content">
                        <text class="item-text">{{ conversationInfo.conversation._target._displayName }}</text>
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
                <!-- <view class="info-item" @click="chooseAndUploadAvatar">
                    <view class="item-label">群头像</view>
                    <view class="item-content">
                        <image class="qr-code" :src="conversationInfo.conversation._target.portrait"></image>
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view> -->
                <view class="info-item" @click="openGroupQRCode">
                    <view class="item-label">群二维码</view>
                    <view class="item-content">
                        <!-- <image class="qr-code" src="@/assets/images/icon.png"></image> -->
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
                <view class="info-item" @click="showGroupAnnouncementEdit">
                    <view class="item-label">群公告</view>
                    <view class="item-content">
                        <text v-if="getAnnouncementTextOnly()" class="announcement-text">{{ getLimitedAnnouncementText() }}</text>
                        <text v-else class="announcement-text">暂无公告</text>
                        <view v-if="hasAnnouncementImages()" class="has-images-indicator">
                            <text class="images-count">[{{ getAnnouncementImagesCount() }}张图片]</text>
                        </view>
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
                <view class="info-item" @click="gotoGroupSettings">
                    <view class="item-label">群管理</view>
                    <view class="item-content">
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
                <view class="info-item" @click="showGroupDescriptionEdit">
                    <view class="item-label">群介绍</view>
                    <view class="item-content">
                        <text v-if="getDescriptionTextOnly()">{{ getLimitedDescriptionText() }}</text>
                        <text v-else>暂无介绍</text>
                        <view v-if="hasDescriptionImages()" class="has-images-indicator">
                            <text class="images-count">[{{ getDescriptionImagesCount() }}张图片]</text>
                        </view>
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
                <view class="info-item" @click="showGroupRemarkEdit">
                    <view class="item-label">群备注</view>
                    <view class="item-content">
                        <text>{{ userExtra.groupRemark }}</text>
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
            </view>
            <view class="group-info-container">
                <view class="info-item" @click="viewChatHistory">
                    <view class="item-label">查找聊天记录</view>
                    <view class="item-content">
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
                <!--<view class="info-item">
                    <view class="item-label">查找文件记录</view>
                    <view class="item-content">
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view> -->
            </view>
            <view class="group-info-container">
                <view class="info-item">
                    <view class="item-label">消息免打扰</view>
                    <view class="item-content">
                        <switch :checked="isSilent" @change="toggleMute" color="#4168e0" />
                    </view>
                </view>
                <view class="info-item">
                    <view class="item-label">消息置顶</view>
                    <view class="item-content">
                        <switch :checked="isTop" @change="toggleTop" color="#4168e0" />
                    </view>
                </view>
            </view>
            <view class="group-info-container">
                <view class="info-item" @click="showGroupCategoryEdit">
                    <view class="item-label">群分类</view>
                    <view class="item-content">
                        <text>{{ extra.groupCategory }}</text>
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
                <view class="info-item" @click="showGroupNicknameEdit">
                    <view class="item-label">我在群里的昵称</view>
                    <view class="item-content">
                        <text>{{ userExtra.groupNickname }}</text>
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
                <view class="info-item">
                    <view class="item-label">显示群成员名称</view>
                    <view class="item-content">
                        <switch :checked="userExtra.groupShowNickname" @change="onGroupShowNicknameChange" />
                    </view>
                </view>
                <view class="info-item">
                    <view class="item-label">我在群内的身份</view>
                    <view class="item-content">
                        <text>{{ userExtra.groupRole }}</text>
                    </view>
                </view>
                <view class="info-item">
                    <view class="item-label">显示群内身份</view>
                    <view class="item-content">
                        <switch :checked="userExtra.groupShowRole" @change="onGroupShowRoleChange" />
                    </view>
                </view>
            </view>
            <view class="group-info-container">
                <view class="info-item" @click="showGroupPropertyEdit">
                    <view class="item-label">群属性</view>
                    <view class="item-content">
                        <!-- <text>{{ extra.groupProperty }}</text> -->
                        <text> <span v-if="this.groupdetail?.price">付费:<span>￥{{this.groupdetail?.price}}</span></span><span v-else>免费</span> </text>
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
                <view class="info-item" @click="clearGroupHistory">
                    <view class="item-label">清空群聊天记录</view>
                    <view class="item-content">
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
                <view class="info-item" @click="groupReport">
                    <view class="item-label">投诉</view>
                    <view class="item-content">
                        <text class="icon-ion-ios-arrow-right"></text>
                    </view>
                </view>
            </view>
            <view class="group-info-container">
                <view class="info-item" v-if="isOwner" @click="quitGroup(true)">
                    <view class="item-label">解散群组</view>
                </view>
            </view>
            <view class="bottom-btn" @click="quitGroup(false)">
                {{ $t('conversation.quit_group') }}
            </view>

            <!--<div class="search-item">
                <input type="text" v-model="filterQuery" :placeholder="$t('common.search')">
                <i class="icon-ion-ios-search"></i>
            </div>
            <div class="member-container">
                <div v-if="enableAddGroupMember && !filterQuery" @click="showCreateConversationModal" class="action-item">
                    <div class="icon">+</div>
                    <p>{{ $t('conversation.add_member') }}</p>
                </div>
                <div v-if="enableRemoveGroupMember && !filterQuery" @click="showRemoveGroupMemberModal" class="action-item">
                    <div class="icon">-</div>
                    <p>{{ $t('conversation.remove_member') }}</p>
                </div>
                <UserListView :users="users" :show-category-label="false" :showNavigationbar="false" :padding-left="'20px'" />
            </div> -->

            <!-- 群名称编辑弹窗 -->
            <uni-popup ref="groupNamePopup" type="dialog">
                <uni-popup-dialog mode="input" title="修改群聊名称" v-model="newGroupName" placeholder="请输入群聊名称" maxlength="96" @confirm="handleGroupNameConfirm">
                </uni-popup-dialog>
            </uni-popup>

            <!-- 群公告编辑弹窗 -->
            <uni-popup ref="groupAnnouncementPopup" type="dialog">
                <uni-popup-dialog mode="input" title="修改群公告" :value="newGroupAnnouncement" placeholder="请输入群公告" @confirm="handleAnnouncementConfirm">
                </uni-popup-dialog>
            </uni-popup>
            <!-- 群编辑弹窗 -->

            <!-- 群备注编辑弹窗 -->
            <uni-popup ref="groupRemarkPopup" type="dialog">
                <uni-popup-dialog mode="input" title="修改群备注" :value="userExtra.groupRemark" placeholder="请输入群备注" @confirm="handleGroupRemarkConfirm">
                </uni-popup-dialog>
            </uni-popup>
            <!-- 群介绍编辑弹窗 -->
            <uni-popup ref="groupDescriptionPopup" type="center">
                <view class="rich-text-popup">
                    <view class="rich-text-popup-header">
                        <text class="rich-text-popup-title">修改群介绍</text>
                    </view>
                    <view class="rich-text-popup-content">
                        <textarea class="rich-text-textarea" 
                            v-model="newGroupDescription" 
                            placeholder="请输入群介绍内容，让大家更好地了解这个群" 
                            :auto-height="false"
                            maxlength="500"
                        />
                        
                        <!-- 添加图片预览区域 -->
                        <view class="image-picker" v-if="descriptionImages.length > 0">
                            <view class="image-list">
                                <view v-for="(image, index) in descriptionImages" :key="index" class="image-item">
                                    <image :src="image" mode="aspectFill" class="preview-image" @click="previewDescriptionImage(descriptionImages, index)" />
                                    <view class="delete-btn" @click="deleteDescriptionImage(index)">
                                        <text class="delete-icon">×</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                        
                        <!-- 添加图片按钮 -->
                        <view class="add-image-btn" @click="chooseDescriptionImage">
                            <text class="add-image-text">添加图片</text>
                        </view>
                    </view>
                    <view class="rich-text-popup-footer">
                        <text class="text-length-indicator">{{newGroupDescription ? newGroupDescription.length : 0}}/500</text>
                        <view class="rich-text-buttons">
                            <button class="cancel-btn" @click="$refs.groupDescriptionPopup.close()">取消</button>
                            <button class="confirm-btn" @click="handleGroupDescriptionConfirm">确定</button>
                        </view>
                    </view>
                </view>
            </uni-popup>
            <!-- 群内昵称编辑弹窗 -->
            <uni-popup ref="groupNicknamePopup" type="dialog">
                <uni-popup-dialog mode="input" title="修改群内昵称" :value="userExtra.groupNickname" placeholder="请输入群内昵称" @confirm="handleGroupNicknameConfirm">
                </uni-popup-dialog>
            </uni-popup>

            <!-- 添加群分类选择弹窗 -->
            <uni-popup ref="groupCategoryPopup" type="bottom">
                <view class="category-picker">
                    <view class="picker-header">
                        <text class="cancel" @click="$refs.groupCategoryPopup.close()">取消</text>
                        <text class="title">选择群分类</text>
                        <text class="confirm" @click="handleGroupCategoryConfirm">确定</text>
                    </view>
                    <picker-view class="picker-view" :value="[selectedCategoryIndex]" @change="onCategoryChange">
                        <picker-view-column>
                            <view class="picker-item" v-for="(category, index) in categories" :key="index">
                                {{ category.name }}
                            </view>
                        </picker-view-column>
                    </picker-view>
                </view>
            </uni-popup>

            <!-- 群二维码弹窗 -->
            <view class="rq-tc" v-if="isGroupQRCodeVisible" @click="closeGroupQRCode">
                <view class="tc" @click.stop>
                    <!-- 添加关闭按钮 -->
                    <view class="close-btn" @click="closeGroupQRCode">×</view>
                    <view class="tcc">
                        <image class="tcc1" :src="conversationInfo.conversation._target.portrait || '/static/logo.png'"></image>
                    </view>
                    <view class="tcd">{{ conversationInfo.conversation._target._displayName || '群聊' }}</view>
                    <!-- 二维码区域 -->
                    <view class="qr-wrap">
                        <view class="qr-box">
                            <uqrcode ref="uqrcode" canvas-id="qrcode-canvas" :value="qrCodeConfig.text" :options="{
                                margin: 10,
                                size: 180,
                                backgroundColor: qrCodeConfig.backgroundColor,
                                foregroundColor: qrCodeConfig.foregroundColor,
                                foregroundImageSrc: qrCodeConfig.logo,
                                foregroundImagePadding: 10,
                                logo: qrCodeConfig.logo,
                                logoSize: qrCodeConfig.logoSize,
                                correctLevel: 3,
                                componentInstance: true
                            }">
                            </uqrcode>
                        </view>
                        <text class="scan-tip">扫一扫上面的二维码图案，加入群聊</text>
                    </view>

                    <!-- 底部按钮区域 -->
                    <view class="action-buttons">
                        <view class="action-btn" @click="handleScan">
                            <text>扫一扫</text>
                        </view>
                        <view class="refresh-btn" @click="refreshQRCodeStyle">
                            <text>换个样式</text>
                        </view>
                        <view class="action-btn" @click="handleSave">
                            <text>保存图片</text>
                        </view>
                    </view>
                </view>
            </view>
        </div>
    </div>
</template>

<script>
import UserListView from '@/pages/user/UserListView'
import store from '@/store'
import wfc from '@/wfc/client/wfc'
import GroupMemberType from '@/wfc/model/groupMemberType'
import GroupType from '@/wfc/model/groupType'
import ModifyGroupInfoType from '@/wfc/model/modifyGroupInfoType'
import Config from '../../config'
import EventType from '../../wfc/client/wfcEvent'
import appServerApi from '../../api/appServerApi'
import CustomHeader from '../../components/custom-header'
import uQRCode from '@/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue'
import ColorUtil from '@/utils/colorUtil'
import topMessage from '@/common/topMessageView'
import GroupIdentityCache from '@/utils/GroupIdentityCache'

export default {
    name: 'GroupConversationManagePage',
    components: {
        UserListView,
        CustomHeader,
        uqrcode: uQRCode,
    },
    data() {
        return {
            conversationInfo: null,
            groupMemberUserInfos: null,
            filterQuery: '',
            sharedContactState: store.state.contact,
            newGroupName: '',
            newGroupAnnouncement: '',
            extra: {
                groupAnnouncement: '',
                groupCategory: '',
                groupProperty: '免费',
            },
            userExtra: {
                groupRemark: '',
                groupNickname: '',
                groupRole: '成员',
                groupShowRole: false,
                groupShowNickname: false,
            },
            isSilent: false,
            isTop: false,
            categories: [],
            selectedCategoryIndex: 0,
            selectedCategory: null,
            groupdetail: {},
            isGroupQRCodeVisible: false,
            qrCodeConfig: {
                text: '',
                size: 200,
                margin: 10,
                backgroundColor: '#ffffff',
                foregroundColor: '#4168e0',
                foregroundImageSrc: '/static/logo.png',
                foregroundImagePadding: 10,
                logo: '/static/logo.png',
                logoSize: 50,
            },
            width: 300,
            height: 500,
            newGroupDescription: '',
            descriptionImages: [],
            descriptionWithImages: '',
        }
    },
    onLoad(option) {
        console.log('GroupConversationManagePage onLoad')
        // #ifdef APP-NVUE
        const eventChannel = this.$scope.eventChannel // 兼容APP-NVUE
        // #endif
        // #ifndef APP-NVUE
        const eventChannel = this.getOpenerEventChannel()
        // #endif
        eventChannel.on('conversationInfo', (options) => {
            this.conversationInfo = options.conversationInfo
            console.log(
                'GroupConversationManagePage mounted',
                this.conversationInfo
            )
            
            // 强制刷新群成员信息，确保获取到完整的成员列表
            this.refreshGroupMembers()
            
            this.isSilent = this.conversationInfo.isSilent
            this.isTop = !!this.conversationInfo.top
            uni.setNavigationBarTitle({
                title: this.conversationInfo.conversation._target._displayName,
            })
            this.getGroupExtra()
            this.getCurrentGroupMember()
            this.getGroupinfo()
            
            // 初始化二维码配置
            this.qrCodeConfig.text = `wildfirechat://group/${this.conversationInfo.conversation.target}`
        })
    },
    onShow() {
        this.getGroupinfo()
        // 页面显示时也刷新群成员信息，确保数据是最新的
        if (this.conversationInfo) {
            this.refreshGroupMembers()
        }
    },
    mounted() {
        wfc.eventEmitter.on(
            EventType.UserInfosUpdate,
            this.onConversationMembersUpdate
        )
        wfc.eventEmitter.on(
            EventType.GroupMembersUpdate,
            this.onConversationMembersUpdate
        )
    },

    beforeUnmount() {
        wfc.eventEmitter.removeListener(
            EventType.UserInfosUpdate,
            this.onConversationMembersUpdate
        )
        wfc.eventEmitter.removeListener(
            EventType.GroupMembersUpdate,
            this.onConversationMembersUpdate
        )
    },

    methods: {
        // 强制刷新群成员信息
        async refreshGroupMembers() {
            try {
                const groupId = this.conversationInfo.conversation.target
                const cachedMemberIds = wfc.getGroupMemberIds(groupId, false)
                await new Promise((resolve, reject) => {
                    wfc.getGroupMembersEx(groupId, true, (members) => {
                        this.groupMemberUserInfos = store.getConversationMemberUsrInfos(
                            this.conversationInfo.conversation,
                            true // 强制刷新
                        )
                        resolve()
                    }, (error) => {
                        this.groupMemberUserInfos = store.getConversationMemberUsrInfos(
                            this.conversationInfo.conversation,
                            false // 使用缓存
                        )
                        reject(error)
                    })
                })
            } catch (error) {
                this.groupMemberUserInfos = store.getConversationMemberUsrInfos(
                    this.conversationInfo.conversation,
                    false // 使用缓存
                )
            }
        },
        
        async getGroupinfo() {
            const response = await appServerApi.getGroupDetail(
                this.conversationInfo.conversation.target
            )
            console.log('群信息', response)
            this.groupdetail = response.data
        },
        showGroupPropertyEdit() {
            if (!this.enableEditGroupNameOrAnnouncement) {
                uni.showToast({
                    title: '您没有修改权限',
                    icon: 'none',
                })
                return
            }
            uni.navigateTo({
                url:
                    '/pages/conversation/GroupAttribute?groupId=' +
                    this.conversationInfo.conversation.target +
                    '&price=' +
                    (this.groupdetail?.price??0) +
                    '&duration=' +
                    (this.groupdetail?.duration??0),
                success: (res) => {
                    // res.eventChannel.emit('conversationInfo', {
                    //     conversationInfo: this.conversationInfo,
                    // })
                },
                fail: (e) => {
                    console.log('to group users error', e)
                },
                complete: () => {
                    console.log('switch tab complete')
                },
            })
        },
        switch1Change: function (e) {
            console.log('switch1 发生 change 事件，携带值为', e.detail.value)
        },
        toggleMute(e) {
            this.isSilent = e.detail.value
            store.setConversationSilent(
                this.conversationInfo.conversation,
                e.detail.value
            )
        },

        toggleTop(e) {
            this.isTop = e.detail.value
            store.setConversationTop(
                this.conversationInfo.conversation,
                e.detail.value ? 1 : 0
            )
        },
        onConversationMembersUpdate() {
            console.log('群成员信息更新，重新获取成员列表')
            // 重新获取群成员用户信息
            this.groupMemberUserInfos = store.getConversationMemberUsrInfos(
                this.conversationInfo.conversation
            )
        },
        seeUser(user){
            uni.navigateTo({
                url: '/pages/contact/UserDetailPage?userId='+user.uid,
            })
        },
        showCreateConversationModal() {
            let beforeClose = (users) => {
                let ids = users.map((u) => u.uid)
                wfc.addGroupMembers(
                    this.conversationInfo.conversation.target,
                    ids,
                    null,
                    [0],
                    null,
                    () => {
                        this.groupMemberUserInfos =
                            store.getConversationMemberUsrInfos(
                                this.conversationInfo.conversation
                            )
                    },
                    (err) => {
                        uni.showToast({
                            title: '邀请新成员失败 ' + err,
                        })
                    }
                )
            }
            let groupMemberUserInfos = store.getGroupMemberUserInfos(
                this.conversationInfo.conversation.target,
                false
            )
            let canPickUsers = this.sharedContactState.favContactList
                .concat(this.sharedContactState.friendList)
                .filter((u) => {
                    return u.uid !== Config.FILE_HELPER_ID
                })
            this.$pickUsers({
                users: canPickUsers,
                initialCheckedUsers: groupMemberUserInfos,
                uncheckableUsers: groupMemberUserInfos,
                confirmTitle: this.$t('common.add'),
                successCB: beforeClose,
            })
        },

        showRemoveGroupMemberModal() {
            let beforeClose = (users) => {
                let ids = users.map((u) => u.uid)
                wfc.kickoffGroupMembers(
                    this.conversationInfo.conversation.target,
                    ids,
                    [0],
                    null,
                    () => {
                        this.groupMemberUserInfos =
                            store.getConversationMemberUsrInfos(
                                this.conversationInfo.conversation
                            )
                    },
                    (err) => {
                        uni.showToast({
                            title: '踢除群成员失败' + err,
                        })
                    }
                )
            }
            let groupMemberUserInfos = store.getGroupMemberUserInfos(
                this.conversationInfo.conversation.target,
                false,
                false
            )
            this.$pickUsers({
                users: groupMemberUserInfos,
                confirmTitle: this.$t('common.remove'),
                showCategoryLabel: false,
                successCB: beforeClose,
            })
        },

        showUserInfo(user) {
            console.log('todo show userInfo', user)
        },

        updateGroupName() {
            let groupId = this.conversationInfo.conversation.target
            if (
                !this.newGroupName ||
                this.newGroupName ===
                    this.conversationInfo.conversation._target._displayName
            ) {
                return
            }

            wfc.modifyGroupInfo(
                groupId,
                ModifyGroupInfoType.Modify_Group_Name,
                this.newGroupName,
                [0],
                null,
                () => {
                    this.conversationInfo.conversation._target._displayName =
                        this.newGroupName

                    appServerApi.updateGroupName({
                        gid: this.conversationInfo.conversation.target,
                        name: this.newGroupName,
                    })
                    uni.setNavigationBarTitle({
                        title: this.conversationInfo.conversation._target
                            ._displayName,
                    })
                },
                (err) => {
                    // do nothing
                    console.log('err', err)
                }
            )
        },

        updateGroupExtra(type) {
            let groupId = this.conversationInfo.conversation.target
            console.log('updateGroupExtra', type, this.extra)
            wfc.modifyGroupInfo(
                groupId,
                ModifyGroupInfoType.Modify_Group_Extra,
                JSON.stringify(this.extra),
                [0],
                null,
                () => {
                    switch (type) {
                        case 'announcement':
                            break
                        case 'description':
                            appServerApi.updateGroupDescriotion({
                                gid: this.conversationInfo.conversation.target,
                                description: this.extra.groupDescription,
                            })
                            break
                        default:
                            break
                    }
                },
                (err) => {
                    // do nothing
                    console.log('updateGroupAnnouncement err', err)
                }
            )
        },

        quitGroup(dismiss) {
            if (dismiss) {
                uni.showModal({
                    title: '确认解散群组',
                    content: '是否确认解散群组？此操作不可恢复',
                    success: (res) => {
                        if (res.confirm) {
                            store.dismissGroup(
                                this.conversationInfo.conversation.target
                            )
                            uni.switchTab({
                                url: '/pages/conversationList/ConversationListPage',
                            })
                        }
                    },
                    complete: () => {},
                })
            } else {
                uni.showModal({
                    title: '确认退出',
                    content: '是否确认退出该群？此操作不可恢复',
                    success: (res) => {
                        if (res.confirm) {
                            store.quitGroup(
                                this.conversationInfo.conversation.target
                            )
                            uni.switchTab({
                                url: '/pages/conversationList/ConversationListPage',
                            })
                        }
                    },
                    complete: () => {},
                })
            }
        },
        showGroupUsers() {
            uni.navigateTo({
                 url:`/pages/group/GroupUsersPage?groupId=${this.conversationInfo.conversation.target}`,
                success: (res) => {
                    res.eventChannel.emit('conversationInfo', {
                        conversationInfo: this.conversationInfo
                    })
                },
                fail: (e) => {
                    console.log('to group users error', e)
                }
            })
        },

        setFavGroup(groupId, fav) {
            console.log('setFavGroup', groupId, fav)
            wfc.setFavGroup(
                groupId,
                fav,
                () => {
                    this.conversationInfo.conversation._target._isFav = fav
                    store.reloadFavGroupList()
                },
                (err) => {
                    console.log('setFavGroup error', err)
                }
            )
        },

        showGroupNameEdit() {
            if (!this.enableEditGroupNameOrAnnouncement) {
                uni.showToast({
                    title: '您没有修改权限',
                    icon: 'none',
                })
                return
            }
            // 判断是否有群聊名称
            const groupName = this.conversationInfo.conversation._target._displayName
            console.log(this.conversationInfo.conversation._target)
            if (!groupName || groupName.trim() === '') {
                this.newGroupName = ''
            } else {
                this.newGroupName = groupName
            }
            console.log("groupName:",groupName)
            console.log("判断结果：",!groupName || groupName.trim() === '')
            console.log("this.newGroupName:",this.newGroupName)
            this.$refs.groupNamePopup.open()
        },

        handleGroupNameConfirm(value) {
            if (
                !value ||
                value ===
                    this.conversationInfo.conversation._target._displayName
            ) {
                return
            }
            this.newGroupName = value
            this.updateGroupName()
            this.$refs.groupNamePopup.close()
        },

        showGroupAnnouncementEdit() {
            // 删除权限检查，直接跳转到群公告编辑/查看页面
            // 让编辑页面根据权限决定是只读还是可编辑模式
            uni.navigateTo({
                url: `/pages/conversation/GroupAnnouncementEdit?groupId=${this.conversationInfo.conversation.target}`,
                events: {
                    // 监听页面返回事件，返回时刷新数据
                    refreshData: () => {
                        this.getGroupExtra();
                    }
                },
                success: (res) => {
                    // 设置页面返回时的回调
                    const eventChannel = res.eventChannel;
                    // 页面即将返回时触发刷新
                    eventChannel.on('beforeBack', () => {
                        setTimeout(() => {
                            this.getGroupExtra();
                        }, 500);
                    });
                },
                fail: (err) => {
                    console.error('跳转失败:', err);
                    // 跳转失败时仍使用原来的弹窗方式
                    this.newGroupAnnouncement = this.extra.groupAnnouncement;
                    this.$refs.groupAnnouncementPopup.open();
                }
            });
        },
        showGroupDescriptionEdit() {
            // 跳转到独立的群介绍编辑/查看页面
            uni.navigateTo({
                url: `/pages/conversation/GroupDescriptionEdit?groupId=${this.conversationInfo.conversation.target}`,
                events: {
                    // 监听页面返回事件，返回时刷新数据
                    refreshData: () => {
                        this.getGroupExtra();
                    }
                },
                success: (res) => {
                    // 设置页面返回时的回调
                    const eventChannel = res.eventChannel;
                    // 页面即将返回时触发刷新
                    eventChannel.on('beforeBack', () => {
                        setTimeout(() => {
                            this.getGroupExtra();
                        }, 500);
                    });
                },
                fail: (err) => {
                    console.error('跳转失败:', err);
                    // 跳转失败时仍使用原来的弹窗方式
                    this.parseGroupDescription();
                    this.$refs.groupDescriptionPopup.open();
                }
            });
        },

        // 解析群介绍中的文本和图片
        parseGroupDescription() {
            const description = this.extra.groupDescription || '';
            
            // 匹配图片标记：[图片:URL]
            const imgRegex = /\[图片:(.*?)\]/g;
            const imgMatches = [...description.matchAll(imgRegex)];
            
            if (imgMatches.length > 0) {
                // 提取图片URL
                this.descriptionImages = imgMatches.map(match => match[1]);
                
                // 提取纯文本部分
                this.newGroupDescription = description.replace(imgRegex, '');
            } else {
                // 没有图片，直接显示文本
                this.newGroupDescription = description;
                this.descriptionImages = [];
            }
        },

        handleAnnouncementConfirm(value) {
            if (!value || value === this.extra.groupAnnouncement) {
                return
            }
            this.newGroupAnnouncement = value
            this.extra.groupAnnouncement = value
            this.updateGroupExtra('announcement')
            this.$refs.groupAnnouncementPopup.close()
        },
        // 更改群头像
        // chooseAndUploadAvatar() {
        //     if (!this.enableEditGroupNameOrAnnouncement) {
        //         uni.previewImage({
        //             urls: [this.conversationInfo.conversation._target.portrait], // 需要预览的图片链接列表
        //             current: 0, // 当前显示图片的索引
        //             indicator: 'number', // 显示页码
        //             loop: true, // 是否循环预览
        //             success: () => {
        //                 console.log('图片预览成功')
        //             },
        //             fail: (err) => {
        //                 console.error('图片预览失败:', err)
        //             },
        //         })
        //         return
        //     } else {
        //         uni.navigateTo({
        //             url:
        //                 '/pages/me/AvatarUpload?groupId=' +
        //                 this.conversationInfo.conversation.target,
        //             fail: (err) => {
        //                 console.error('跳转失败:', err)
        //             },
        //         })
        //     }
        // },
        updateGroupMemberExtra() {
            console.log(
                '修改群内成员个人',
                this.conversationInfo.conversation.target,
                wfc.getUserId(),
                this.userExtra
            )
            wfc.modifyGroupMemberExtra(
                this.conversationInfo.conversation.target,
                wfc.getUserId(),
                JSON.stringify(this.userExtra),
                [0],
                null,
                () => {
                    console.log('updateGroupMemberExtra success')
                },
                (err) => {
                    console.log('updateGroupMemberExtra error', err)
                }
            )
        },

        // 显示群备注编辑弹窗
        showGroupRemarkEdit() {
            this.$refs.groupRemarkPopup.open()
        },

        // 显示群内昵称编辑弹窗
        showGroupNicknameEdit() {
            this.$refs.groupNicknamePopup.open()
        },

        // 处理群备注确认
        handleGroupRemarkConfirm(value) {
            this.userExtra.groupRemark = value
            this.updateGroupMemberExtra()
            this.$refs.groupRemarkPopup.close()
        },

        // 处理群介绍确认
        handleGroupDescriptionConfirm() {
            this.saveGroupDescriptionWithImages();
            this.$refs.groupDescriptionPopup.close();
        },

        // 选择群介绍图片
        async chooseDescriptionImage() {
            // 限制最多9张图片
            if (this.descriptionImages.length >= 9) {
                uni.showToast({
                    title: '最多只能添加9张图片',
                    icon: 'none'
                });
                return;
            }
            
            try {
                /*#ifdef APP-PLUS*/
                if (plus.os.name !== 'iOS') {
                    var isPermission = plus.navigator.checkPermission('android.permission.READ_EXTERNAL_STORAGE');
                    if (isPermission != 'authorized') {
                        topMessage.createTopMessage(
                            '相册权限使用说明',
                            '用于从相册选择照片作为群介绍图片。我们不会访问您相册中的其他照片。'
                        );
                    }
                    let res = await topMessage.requestPermissions(
                        'READ_EXTERNAL_STORAGE',
                        '相册权限未获得，此权限用于选择照片功能，请前往设置中打开'
                    );
                    setTimeout(() => {
                        topMessage.hideTopMessage();
                    }, 300);
                    if (!res.granted[0]) {
                        // 无权限
                        return;
                    }
                }
                /*#endif*/
                
                uni.chooseImage({
                    count: 9 - this.descriptionImages.length, // 最多还能选几张
                    sizeType: ['compressed'],
                    sourceType: ['album', 'camera'],
                    success: (res) => {
                        // 过滤重复图片
                        const newImages = res.tempFilePaths.filter(newPath => {
                            return !this.descriptionImages.some(existingPath => existingPath == newPath);
                        });
                        
                        if (newImages.length > 0) {
                            this.descriptionImages = [...this.descriptionImages, ...newImages];
                        }
                    }
                });
            } catch (error) {
                console.error('选择图片失败:', error);
            }
        },

        // 删除群介绍图片
        deleteDescriptionImage(index) {
            this.descriptionImages.splice(index, 1);
        },

        // 预览群介绍图片
        previewDescriptionImage(urls, current) {
            uni.previewImage({
                urls: urls,              // 图片数组
                current: current,        // 当前图片索引
                indicator: 'number',     // 显示页码
                loop: true,              // 支持循环预览
                success: () => {
                    console.log('图片预览成功');
                },
                fail: (err) => {
                    console.error('图片预览失败:', err);
                }
            });
        },

        // 保存带图片的群介绍
        async saveGroupDescriptionWithImages() {
            try {
                if (this.descriptionImages.length > 0) {
                    uni.showLoading({ title: '上传图片中...' });
                    
                    // 上传图片并获取URL
                    const imageUrls = await Promise.all(
                        this.descriptionImages.map(async (image) => {
                            return new Promise((resolve, reject) => {
                                appServerApi.uploadimgFile(
                                    image,
                                    (res) => {
                                        if (res && res.data) {
                                            resolve(res.data);
                                        } else {
                                            reject(new Error('上传返回数据异常'));
                                        }
                                    },
                                    (error) => {
                                        reject(error);
                                    }
                                );
                            });
                        })
                    );
                    
                    // 将图片URL与文本结合
                    const imageUrlsText = imageUrls.map(url => `[图片:${url}]`).join('');
                    this.descriptionWithImages = this.newGroupDescription + imageUrlsText;
                    
                    // 将组合后的内容保存到群介绍
                    if (this.descriptionWithImages !== this.extra.groupDescription) {
                        this.extra.groupDescription = this.descriptionWithImages;
                        this.updateGroupExtra('description');
                    }
                    
                    uni.hideLoading();
                } else {
                    // 只有文本的情况
                    if (this.newGroupDescription !== this.extra.groupDescription) {
                        this.extra.groupDescription = this.newGroupDescription;
                        this.updateGroupExtra('description');
                    }
                }
            } catch (error) {
                uni.hideLoading();
                console.error('保存群介绍失败:', error);
                uni.showToast({
                    title: '保存图片失败',
                    icon: 'none'
                });
            }
        },

        // 处理群内昵称确认
        handleGroupNicknameConfirm(value) {
            this.userExtra.groupNickname = value
            this.updateGroupMemberExtra()
            this.$refs.groupNicknamePopup.close()
        },

        // 处理显示群内身份开关变化
        onGroupShowRoleChange(e) {
            this.userExtra.groupShowRole = e.detail.value
            this.updateGroupMemberExtra()
        },

        // 处理显示群成员昵称开关变化
        onGroupShowNicknameChange(e) {
            this.userExtra.groupShowNickname = e.detail.value
            console.log(
                '处理显示群成员昵称开关变化',
                this.userExtra.groupShowNickname
            )
            this.updateGroupMemberExtra()
        },

        // 获取当前用户在群内的信息
        getCurrentGroupMember() {
            console.log('获取用户在群内的信息')
            const groupId = this.conversationInfo.conversation.target
            const currentUserId = wfc.getUserId()
            const memberInfo = wfc.getGroupMember(groupId, currentUserId)
            console.log('用户在群内的信息', memberInfo)
            
            // 首先设置基本信息
            if (memberInfo) {
                // 获取群成员扩展信息
                if (memberInfo.extra) {
                    try {
                        let extraObj = {}
                        // 确保 extra 是有效的 JSON 字符串
                        if (
                            typeof memberInfo.extra === 'string' &&
                            memberInfo.extra.trim()
                        ) {
                            extraObj = JSON.parse(memberInfo.extra)
                        }

                        // 设置默认值
                        this.userExtra = {
                            groupRemark: extraObj.groupRemark || '',
                            groupNickname: extraObj.groupNickname || '',
                            groupShowRole: !!extraObj.groupShowRole,
                            groupShowNickname: !!extraObj.groupShowNickname,
                        }
                    } catch (error) {
                        console.error('解析群成员扩展信息失败', error)
                        // 设置默认值
                        this.userExtra = {
                            groupRemark: '',
                            groupNickname: '',
                            groupShowRole: false,
                            groupShowNickname: false,
                        }
                    }
                } else {
                    // 没有扩展信息时设置默认值
                    this.userExtra = {
                        groupRemark: '',
                        groupNickname: '',
                        groupShowRole: false,
                        groupShowNickname: false,
                    }
                }
                
                // 先根据成员类型设置基本角色
                const memberRole = this.getMemberRoleText(memberInfo.type || 0)
                this.userExtra.groupRole = memberRole
                
                // 如果是普通成员，则尝试获取自定义身份
                if (memberInfo.type === 0) {
                    // 获取自定义身份
                    this.getCustomGroupIdentity(groupId, currentUserId)
                }
            }
        },
        
        // 获取自定义身份
        async getCustomGroupIdentity(groupId, userId) {
            try {
                // 使用全局缓存管理器获取群成员身份
                const customIdentity = await GroupIdentityCache.getUserIdentity(groupId, userId);
                
                if (customIdentity) {
                    console.log('找到当前用户自定义身份:', customIdentity);
                    // 更新用户身份显示
                    this.userExtra.groupRole = customIdentity;
                }
            } catch (error) {
                console.error('获取自定义身份出错:', error);
            }
        },

        // 获取成员角色文本
        getMemberRoleText(memberType) {
            switch (memberType) {
                case 0:
                    return '普通成员'
                case 2:
                    return '群主'
                case 1:
                    return '管理员'
                case 3:
                    return '已禁言'
                default:
                    return '成员'
            }
        },
        getGroupExtra() {
            // 从store中获取最新的群组信息
            const groupInfo = wfc.getGroupInfo(this.conversationInfo.conversation._target.target);
            if (groupInfo) {
                try {
                    // 优先使用store中的群组信息
                    this.extra = groupInfo.extra ? JSON.parse(groupInfo.extra) : {};
                } catch (error) {
                    console.error('解析群组扩展信息失败:', error);
                    this.extra = {};
                }
            } else {
                // 如果store中没有找到群组信息，则使用conversation中的信息作为备选
                this.extra = this.conversationInfo.conversation._target.extra
                    ? JSON.parse(this.conversationInfo.conversation._target.extra)
                    : {};
            }
            console.log('群组扩展信息', this.extra);
        },
        viewChatHistory() {
            // store.searchMessage(this.conversationInfo.conversation, this.keyword)
            uni.navigateTo({
                url: `/pages/search/SearchPortalPage`,
            })
        },
        gotoGroupSettings() {
            if (!this.enableEditGroupNameOrAnnouncement) {
                uni.showToast({
                    title: '您没有修改权限',
                    icon: 'none',
                })
                return
            }
            uni.navigateTo({
                url: `/pages/group/GroupSettings?groupId=${this.conversationInfo.conversation.target}`,
            })
        },

        viewMediaFiles() {
            // TODO: 实现查看文件记录功能
        },

        clearGroupHistory() {
            uni.showModal({
                title: '确认清空',
                content: '是否清空所有聊天记录？此操作不可恢复',
                success: (res) => {
                    if (res.confirm) {
                        store.clearMessages(this.conversationInfo.conversation)
                        uni.showToast({
                            title: '操作成功',
                            icon: 'none',
                        })
                    }
                },
                complete: () => {},
            })
        },

        groupReport() {
            // uni.showToast({
            //     title: '感谢您的反馈，我们将尽快处理',
            //     icon: 'none'
            // })
            // return
            uni.navigateTo({
                url: `/pages/complaint/ComplaintPage`,
            })
        },

        // 显示群分类编辑弹窗
        async showGroupCategoryEdit() {
            if (!this.enableEditGroupNameOrAnnouncement) {
                uni.showToast({
                    title: '您没有修改权限',
                    icon: 'none',
                })
                return
            }

            try {
                const response = await appServerApi.getAllGroupCategories()
                if (response.data && response.data.length > 0) {
                    this.categories = response.data
                    // 找到当前分类的索引
                    const currentIndex = this.categories.findIndex(
                        (category) => category.name === this.extra.groupCategory
                    )
                    this.selectedCategoryIndex =
                        currentIndex >= 0 ? currentIndex : 0
                    this.selectedCategory =
                        this.categories[this.selectedCategoryIndex]
                    this.$refs.groupCategoryPopup.open()
                } else {
                    uni.showToast({
                        title: '暂无可选分类',
                        icon: 'none',
                    })
                }
            } catch (error) {
                console.error('获取群分类失败:', error)
                uni.showToast({
                    title: '获取群分类失败',
                    icon: 'none',
                })
            }
        },

        // 处理分类选择变化
        onCategoryChange(e) {
            const index = e.detail.value[0]
            this.selectedCategoryIndex = index
            this.selectedCategory = this.categories[index]
        },

        // 处理群分类确认
        handleGroupCategoryConfirm() {
            if (!this.selectedCategory) return

            try {
                // 更新extra信息
                this.extra.groupCategory = this.selectedCategory.name
                this.updateGroupExtra('category')

                // 调用更新群分类API
                appServerApi.updateGroupCategory({
                    gid: this.conversationInfo.conversation.target,
                    categoryId: this.selectedCategory.id,
                })

                uni.showToast({
                    title: '更新成功',
                    icon: 'success',
                })
                this.$refs.groupCategoryPopup.close()
            } catch (error) {
                console.error('更新群分类失败:', error)
                uni.showToast({
                    title: '更新失败',
                    icon: 'none',
                })
            }
        },

        // 打开群二维码
        openGroupQRCode() {
            const groupId = this.conversationInfo.conversation.target;
            // 使用新的URL格式
            this.qrCodeConfig.text = `http://admin.ykjrhl.com/?GroupID=${groupId}`;
            
            // 完整更新配置
            this.$nextTick(() => {
                this.qrCodeConfig = {
                    ...this.qrCodeConfig,
                    text: `http://admin.ykjrhl.com/?GroupID=${groupId}`,
                    foregroundColor: this.getRandomColor()
                };
                this.updateQRCodeLogo();
                this.isGroupQRCodeVisible = true;
                
                // 在下一个渲染周期执行二维码生成
                setTimeout(() => {
                    if (this.$refs.uqrcode) {
                        try {
                            this.$refs.uqrcode.make();
                            console.log('二维码生成成功');
                        } catch (error) {
                            console.error('二维码生成失败:', error);
                        }
                    }
                }, 300);
            });
        },
        
        // 关闭群二维码
        closeGroupQRCode() {
            this.isGroupQRCodeVisible = false;
        },
        
        // 更新二维码中的头像
        updateQRCodeLogo() {
            const portrait = this.conversationInfo.conversation._target.portrait;
            if (portrait && portrait.trim() !== '') {
                this.qrCodeConfig.foregroundImageSrc = portrait;
                this.qrCodeConfig.logo = portrait;
            } else {
                this.qrCodeConfig.foregroundImageSrc = '/static/logo.png';
                this.qrCodeConfig.logo = '/static/logo.png';
            }
        },
        
        // 刷新二维码样式（更换颜色）
        async refreshQRCodeStyle() {
            return new Promise((resolve) => {
                this.qrCodeConfig.foregroundColor = this.getRandomColor();
                this.$nextTick(() => {
                    if (this.$refs.uqrcode) {
                        this.$refs.uqrcode.make();
                        // 给二维码生成一些时间
                        setTimeout(() => {
                            resolve();
                        }, 500);
                    } else {
                        resolve();
                    }
                });
            });
        },
        
        // 生成随机颜色
        getRandomColor() {
            return ColorUtil.getSmartColor();
        },
        
        // 处理扫描二维码
        async handleScan() {
            this.isGroupQRCodeVisible = false;
            
            /*#ifdef APP-PLUS*/
            if (plus.os.name !== 'iOS') {
                var isPermission = plus.navigator.checkPermission(
                    'android.permission.CAMERA'
                );
                if (isPermission != 'authorized') {
                    topMessage.createTopMessage(
                        '相机、相册权限使用说明',
                        '用于扫描二维码、条形码等，以便快速访问相关内容或服务。我们不会将相机用于其他用途。'
                    );
                }
                let res = await topMessage.requestPermissions(
                    'CAMERA',
                    '相机权限未获得，此权限用于扫码功能，请前往设置中打开'
                );
                setTimeout(() => {
                    topMessage.hideTopMessage();
                }, 300);
                if (!res.granted[0]) {
                    // 无权限
                    return;
                }
            }
            /*#endif*/

            uni.scanCode({
                success: (res) => {
                    console.log('扫码结果：', res);
                    const scanResult = res.result;

                    // 处理 URL 类型
                    if (scanResult.startsWith('http://') || scanResult.startsWith('https://')) {
                        // 新增：处理贴吧URL格式
                        if (scanResult.includes('bar.html?BarID=')) {
                            const barIdPart = scanResult.split('BarID=')[1];
                            const barId = barIdPart.split('&')[0]; // 处理可能的额外URL参数
                            
                            console.log('扫描到贴吧链接, 贴吧ID:', barId);
                            uni.navigateTo({
                                url: `/pages/bar/BarHome?id=${barId}`,
                                fail: (err) => {
                                    console.error('跳转到贴吧主页失败:', err);
                                    uni.showToast({
                                        title: '跳转贴吧失败',
                                        icon: 'none'
                                    });
                                }
                            });
                            return; // 处理完毕，不再继续执行后续判断
                        }

                        // 处理新的群组URL格式
                        if (scanResult.includes('?GroupID=')) {
                            const groupIdPart = scanResult.split('GroupID=')[1];
                            // 处理可能的额外URL参数
                            const groupId = groupIdPart.split('&')[0];
                            
                            console.log('扫描到群组链接, 群ID:', groupId);
                            // 跳转到群组详情页面
                            uni.navigateTo({
                                url: `/pages/group/GroupDetailPage?groupId=${groupId}`,
                                fail: (err) => {
                                    console.error('跳转失败:', err);
                                    uni.showToast({
                                        title: '跳转失败',
                                        icon: 'none'
                                    });
                                }
                            });
                            return;
                        }

                        // 处理新的用户URL格式
                        if (scanResult.includes('?UserID=')) {
                            const userIdPart = scanResult.split('UserID=')[1];
                            // 处理可能的额外URL参数
                            const userId = userIdPart.split('&')[0];
                            
                            console.log('扫描到用户链接, 用户ID:', userId);
                            // 跳转到用户详情页面
                            uni.navigateTo({
                                url: `/pages/contact/UserDetailPage?userId=${userId}`,
                                fail: (err) => {
                                    console.error('跳转失败:', err);
                                    uni.showToast({
                                        title: '跳转失败',
                                        icon: 'none'
                                    });
                                }
                            });
                            return;
                        }

                        // 其他网址直接跳转到WebPage
                        uni.navigateTo({
                            url: `/pages/misc/WebPage?url=${encodeURIComponent(scanResult)}`,
                            fail: (err) => {
                                console.error('跳转失败:', err);
                                uni.showToast({
                                    title: '跳转失败',
                                    icon: 'none'
                                });
                            }
                        });
                        return;
                    }

                    // 处理地球号
                    if (scanResult.startsWith('EARTHID:')) {
                        const userId = scanResult.substring(8);
                        console.log('userId:', userId);
                        uni.navigateTo({
                            url: `/pages/contact/UserDetailPage?userId=${userId}`,
                            fail: (err) => {
                                console.error('跳转失败:', err);
                                uni.showToast({
                                    title: '跳转失败',
                                    icon: 'none'
                                });
                            }
                        });
                        return;
                    }

                    // 处理其他字符串
                    uni.navigateTo({
                        url: `/pages/misc/StringPage?content=${encodeURIComponent(scanResult)}`,
                        fail: (err) => {
                            console.error('跳转失败:', err);
                            uni.showToast({
                                title: '跳转失败',
                                icon: 'none'
                            });
                        }
                    });
                },
                fail: (err) => {
                    console.error('扫码失败:', err);
                }
            });
        },
        
        // 保存二维码图片
        async handleSave() {
            try {

            } catch (error) {
                uni.hideLoading();
                console.error('保存群二维码失败:', error);
                uni.showToast({
                    title: '保存失败: ' + (error?.message || '未知错误'),
                    icon: 'none',
                    duration: 2000
                });
            }
        },
        
        // 将二维码保存到相册的具体实现
        async saveQRCodeToAlbum() {
            uni.showLoading({ title: '保存中...' });
            
            try {
                // 确保二维码生成完成
                await new Promise((resolve, reject) => {
                    setTimeout(resolve, 500);
                });
                
                // 获取二维码图片
                const qrcodeResult = await new Promise((resolve, reject) => {
                    if (!this.$refs.uqrcode) {
                        reject(new Error('二维码组件未初始化'));
                        return;
                    }
                    
                    uni.canvasToTempFilePath({
                        canvasId: 'qrcode-canvas',
                        success: (res) => resolve(res.tempFilePath),
                        fail: (err) => {
                            console.error('获取二维码图片失败:', err);
                            reject(new Error('获取二维码图片失败'));
                        },
                    }, this.$refs.uqrcode);
                });
                
                // 保存图片到相册
                await new Promise((resolve, reject) => {
                    uni.saveImageToPhotosAlbum({
                        filePath: qrcodeResult,
                        success: () => {
                            uni.hideLoading();
                            uni.showToast({
                                title: '保存成功',
                                icon: 'success',
                                duration: 2000
                            });
                            resolve();
                        },
                        fail: (err) => {
                            console.error('保存到相册失败:', err);
                            reject(new Error('保存到相册失败'));
                        }
                    });
                });
            } catch (error) {
                uni.hideLoading();
                throw error; // 向上传递错误
            }
        },

        // 获取有限长度的群介绍文本（最多3个字，后面用省略号）
        getLimitedDescriptionText() {
            const text = this.getDescriptionTextOnly();
            if (!text) return '';
            
            // 获取前3个字符（中英文都算一个字符）
            return text.length <= 3 ? text : text.substring(0, 3) + '...';
        },

        // 获取群介绍纯文本部分
        getDescriptionTextOnly() {
            const description = this.extra.groupDescription || '';
            // 移除图片标记
            const textOnly = description.replace(/\[图片:.*?\]/g, '');
            return textOnly || ''; // 返回空字符串而不是undefined
        },

        // 检查是否有图片
        hasDescriptionImages() {
            const description = this.extra.groupDescription || '';
            return description.includes('[图片:');
        },

        // 获取图片数量
        getDescriptionImagesCount() {
            const description = this.extra.groupDescription || '';
            const matches = description.match(/\[图片:/g);
            return matches ? matches.length : 0;
        },

        // 获取群公告的文本部分
        getAnnouncementTextOnly() {
            const announcement = this.extra.groupAnnouncement || '';
            // 移除图片标记
            const textOnly = announcement.replace(/\[图片:.*?\]/g, '');
            return textOnly || ''; // 返回空字符串而不是undefined
        },

        // 检查是否有群公告图片
        hasAnnouncementImages() {
            const announcement = this.extra.groupAnnouncement || '';
            return announcement.includes('[图片:');
        },

        // 获取群公告图片数量
        getAnnouncementImagesCount() {
            const announcement = this.extra.groupAnnouncement || '';
            const matches = announcement.match(/\[图片:/g);
            return matches ? matches.length : 0;
        },
        
        // 获取有限长度的群公告文本（最多3个字，后面用省略号）
        getLimitedAnnouncementText() {
            const text = this.getAnnouncementTextOnly();
            if (!text) return '';
            
            // 使用正则表达式去除所有HTML标签
            let textOnly = text.replace(/<[^>]+>/g, '');

            // 替换 [图片:URL] 为 [图片]
            textOnly = textOnly.replace(/\\[图片:.*?\\]/g, '[图片]');

            // 将多个连续换行符替换为单个空格或省略
            // 注意：如果想保留部分换行，需要更复杂的逻辑，这里简单替换
            textOnly = textOnly.replace(/\\n+/g, ' ');

            // 移除首尾空白
            textOnly = textOnly.trim();

            // 获取前3个字符（中英文都算一个字符）
            // 注意：uni-app 的 substring 对中文字符的处理可能需要额外考虑，这里按字符数截取
            if (!textOnly) return '';
            return textOnly.length <= 3 ? textOnly : textOnly.substring(0, 3) + '...';
        },

        // 跳转到群名片页面
        goToGroupProfile() {
            uni.navigateTo({
                url: '/pages/group/GroupProfile?groupId=' + this.conversationInfo.conversation.target
            });
        },
    },

    computed: {
        memberCount() {
            if (this.conversationInfo && this.conversationInfo.conversation && this.conversationInfo.conversation._target) {
                return this.conversationInfo.conversation._target.memberCount || this.users.length || 0;
            }
            return 0;
        },
        enableAddGroupMember() {
            let selfUid = wfc.getUserId()
            let groupInfo = this.conversationInfo.conversation._target
            //在group type为Restricted时，0 开放加入权限（群成员可以拉人，用户也可以主动加入）；1 只能群成员拉人入群；2 只能群管理拉人入群
            if (groupInfo.type === GroupType.Restricted) {
                if (groupInfo.joinType === 0 || groupInfo.joinType === 1) {
                    return true
                } else if (groupInfo.joinType === 2) {
                    let groupMember = wfc.getGroupMember(
                        this.conversationInfo.conversation.target,
                        selfUid
                    )
                    return (
                        [
                            GroupMemberType.Manager,
                            GroupMemberType.Owner,
                        ].indexOf(groupMember.type) >= 0
                    )
                }
            }
            return true
        },

        enableRemoveGroupMember() {
            let selfUid = wfc.getUserId()
            let groupMember = wfc.getGroupMember(
                this.conversationInfo.conversation.target,
                selfUid
            )
            let t = false
            if (groupMember) {
                t =
                    [GroupMemberType.Manager, GroupMemberType.Owner].indexOf(
                        groupMember.type
                    ) >= 0
            }
            return t
        },

        enableEditGroupNameOrAnnouncement() {
            let selfUid = wfc.getUserId()
            let groupMember = wfc.getGroupMember(
                this.conversationInfo.conversation.target,
                selfUid
            )
            if (!groupMember) {
                return false
            }
            
            // 检查群设置中的限制开关
            let groupExtra = {}
            try {
                if (this.conversationInfo.conversation._target.extra) {
                    groupExtra = JSON.parse(this.conversationInfo.conversation._target.extra)
                }
            } catch (error) {
                console.error('解析群设置失败:', error)
            }
            
            // 如果limitChangeName为false，则所有群成员都可以修改群名称
            // 如果limitChangeName未设置或为true，则只有群主和管理员可以修改
            const limitChangeName = groupExtra.limitChangeName !== false // 默认为true
            
            if (!limitChangeName) {
                // 开关关闭时，所有群成员都可以修改
                return true
            } else {
                // 开关开启时，只有群主和管理员可以修改
                return (
                    [GroupMemberType.Manager, GroupMemberType.Owner].indexOf(
                        groupMember.type
                    ) >= 0
                )
            }
        },

        users() {
            if (this.filterQuery) {
                return store.filterUsers(
                    this.groupMemberUserInfos,
                    this.filterQuery
                )
            } else {
                return this.groupMemberUserInfos
            }
        },

        isOwner() {
            let selfUid = wfc.getUserId()
            let groupInfo = this.conversationInfo.conversation._target
            return groupInfo.owner === selfUid
        },
    },
    // 拦截返回按钮事件
    onBackPress() {
        // 如果二维码弹窗正在显示，拦截返回事件并关闭弹窗
        if (this.isGroupQRCodeVisible) {
            this.closeGroupQRCode();
            return true; // 返回true表示自己处理返回事件
        }
        // 返回false表示不拦截，由系统处理返回事件
        return false;
    },
}
</script>

<style lang="scss" scoped>
.conversation-info {
    height: 100vh;
    overflow: hidden;
    .conversation-info-content {
        display: flex;
        flex-direction: column;
        position: relative;
        justify-content: flex-start;
        overflow: auto;
        background-color: #f5f5f5;
        padding: 10px 10px 50px 10px;
        height: 100vh;
    }
    .users-container {
        width: 100%;
        background-color: #ffffff;
        padding: 14px;
        margin-bottom: 10px;
        border-radius: 10px;
        .users-container-header {
            display: flex;
            justify-content: space-between;
            width: 100%;
            align-items: center;

            .header-left {
                font-weight: 500;
                font-size: 16px;
            }

            .header-right {
                font-size: 12px;
                color: #999999;
                i {
                    margin-left: 10px;
                }
            }
        }
        .users-container-list {
            padding: 24px 0 5px;

            .users-container-list-item {
                float: left;
                width: 20%;
                display: flex;
                justify-content: space-between;
                flex-direction: column;
                align-items: center;
                margin-bottom: 10px;

                .me3b1 {
                    overflow: hidden;
                    image {
                        width: 50px;
                        height: 50px;
                        border-radius: 8px;
                        margin-bottom: 10px;
                        background-color: #f5f5f5;
                    }
                }

                .me3b2 {
                    max-width: 57px;
                    font-size: 12px;
                    padding: 0 4px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    color: #4b4b4b;
                }
            }
        }
    }
}

header {
    padding-left: 20px;
    padding-right: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

header label {
    width: 100%;
    display: flex;
    margin-top: 15px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    font-size: 14px;
    color: #999999;
}

header label:last-of-type {
    padding-bottom: 15px;
    border-bottom: 1px solid #ececec;
}

header label input {
    flex: 1;
    margin-top: 5px;
    border: none;
    outline: none;
    width: 100%;
    font-size: 13px;
    background-color: transparent;
}

.member-container {
    flex: 1;
    overflow: auto;
}

.search-item {
    position: relative;
    padding: 10px 20px;
}

.search-item input {
    width: 100%;
    padding: 0 10px 0 20px;
    height: 25px;
    border-radius: 3px;
    border: 1px solid #ededed;
    background-color: white;
    text-align: left;
    outline: none;
}

.search-item input:active {
    border: 1px solid #4168e0;
}

.search-item input:focus {
    border: 1px solid #4168e0;
}

.search-item i {
    position: absolute;
    left: 25px;
    top: 15px;
}

.action-item {
    height: 50px;
    display: flex;
    padding-left: 20px;
    align-items: center;
}

.action-item .icon {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 3px;
    border: 1px dashed #d6d6d6;
}

.action-item img {
    width: 40px;
    height: 40px;
}

.action-item p {
    margin-left: 10px;
    font-size: 13px;
}

.action-item:active {
    background-color: #d6d6d6;
}

.quit-group-item {
    display: flex;
    color: red;
    align-items: center;
    justify-content: center;
    height: 50px;
    max-height: 50px;
    border-top: 1px solid #ececec;
}

.quit-group-item:active {
    background: #d6d6d6;
}

.switch {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
}

.switch checkbox {
    margin-left: 20px;
}

.group-info-container {
    width: 100%;
    background-color: #ffffff;
    padding: 0 14px;
    margin-bottom: 10px;
    border-radius: 10px;

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
            border-bottom: none;
        }

        .item-label {
            font-size: 16px;
            color: #333;
            white-space: nowrap;
        }

        .item-content {
            display: flex;
            align-items: center;
            color: #999;
            font-size: 14px;
            .item-text {
                white-space: nowrap; /* 不换行 */
                overflow: hidden; /* 隐藏超出的内容 */
                text-overflow: ellipsis; /* 用省略号表示被隐藏的部分 */
                max-width: 200px; /* 设置最大宽度以限制文本的显示长度 */
            }

            .announcement-text {
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            
            .has-images-indicator {
                margin-left: 5px;
                
                .images-count {
                    color: #4168e0;
                    font-size: 12px;
                }
            }

            .qr-code {
                width: 20px;
                height: 20px;
                margin-right: 5px;
            }

            .icon-ion-ios-arrow-right {
                margin-left: 10px;
                font-size: 16px;
            }
        }
    }
}
.bottom-btn {
    width: 100%;
    background-color: #ffffff;
    margin-bottom: 30px;
    border-radius: 10px;
    color: #ff2222;
    height: 48px;
    line-height: 48px;
    text-align: center;
}

.category-picker {
    background-color: #fff;
    border-radius: 16px 16px 0 0;

    .picker-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid #eee;

        .cancel {
            color: #999;
            font-size: 14px;
        }

        .title {
            font-size: 16px;
            font-weight: 500;
        }

        .confirm {
            color: #4168e0;
            font-size: 14px;
        }
    }

    .picker-view {
        width: 100%;
        height: 200px;

        .picker-item {
            line-height: 34px;
            text-align: center;
        }
    }
}

// 添加群二维码相关样式
.rq-tc {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tc {
    position: relative;
    width: 80%;
    height: auto !important;
    background: #fff;
    border-radius: 12px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    
    .tcc {
        margin-top: 0 !important;
    }
    
    .tcc1 {
        width: 50px !important;
        height: 50px !important;
        border-radius: 50%;
    }
    
    .tcd {
        margin-top: 10px;
        font-size: 16px;
        font-weight: 500;
        color: #333;
    }
    
    .close-btn {
        position: absolute;
        top: 10px;
        right: 15px;
        font-size: 24px;
        color: #999;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        border-radius: 15px;
        z-index: 10;
        
        &:active {
            background-color: #f5f5f5;
        }
    }
}

.qr-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 20px 0;

    .qr-box {
        background: #fff;
        padding: 15px;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        margin-bottom: 15px;
    }

    .scan-tip {
        font-size: 12px;
        color: #999999;
        text-align: center;
    }
}

.action-buttons {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    .action-btn {
        color: #4168e0;
        font-size: 14px;
    }
    
    .refresh-btn {
        color: #4168e0;
        font-size: 14px;
        padding: 0 20px;
        margin: 0 20px;
        border-left: 1px solid #efefef;
        border-right: 1px solid #efefef;
    }
}

// 添加深度选择器修改弹窗样式
:deep(.uni-popup-dialog) {
    margin-bottom: 20vh;
}

/* 群介绍富文本编辑器样式 */
.rich-text-popup {
    background-color: #fff;
    border-radius: 12px;
    width: 300px;
    padding: 0;
    box-sizing: border-box;
    overflow: hidden;
    
    .rich-text-popup-header {
        position: relative;
        text-align: center;
        padding: 20px 16px 15px;
        
        .rich-text-popup-title {
            font-size: 18px;
            font-weight: 500;
            color: #333;
        }
    }
    
    .rich-text-popup-content {
        padding: 0 15px;
        
        .rich-text-textarea {
            width: 100%;
            height: 160px; 
            border: none;
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 10px;
            font-size: 15px;
            line-height: 1.5;
            box-sizing: border-box;
            color: #333;
            
            &:focus {
                outline: none;
            }
        }
        
        /* 图片选择器样式 */
        .image-picker {
            margin-top: 10px;
            
            .image-list {
                display: flex;
                flex-wrap: wrap;
                margin-right: -10px;
                margin-bottom: -10px;
                
                .image-item {
                    width: 60px;
                    height: 60px;
                    position: relative;
                    margin-right: 10px;
                    margin-bottom: 10px;
                    
                    .preview-image {
                        width: 100%;
                        height: 100%;
                        border-radius: 4px;
                    }
                    
                    .delete-btn {
                        position: absolute;
                        top: -8px;
                        right: -8px;
                        width: 20px;
                        height: 20px;
                        background: rgba(0, 0, 0, 0.5);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        
                        .delete-icon {
                            color: #fff;
                            font-size: 16px;
                            line-height: 1;
                        }
                    }
                }
            }
        }
        
        .add-image-btn {
            display: inline-block;
            margin-top: 15px;
            padding: 8px 15px;
            background-color: #f5f5f5;
            border-radius: 4px;
            color: #4168e0;
            font-size: 14px;
            
            &:active {
                background-color: #e8e8e8;
            }
        }
    }
    
    .rich-text-popup-footer {
        padding: 15px;
        
        .text-length-indicator {
            font-size: 12px;
            color: #999;
            text-align: right;
            margin-bottom: 20px;
        }
        
        .rich-text-buttons {
            display: flex;
            justify-content: space-between;
            
            button {
                width: 120px;
                height: 44px;
                line-height: 44px;
                text-align: center;
                font-size: 16px;
                border-radius: 22px;
                border: none;
                
                &:active {
                    opacity: 0.9;
                }
            }
            
            .cancel-btn {
                background-color: #f2f2f2;
                color: #666;
                border: 1px solid #cccccc;
            }
            
            .confirm-btn {
                background-color: #4168e0;
                color: #fff;
            }
        }
    }
}
</style>
