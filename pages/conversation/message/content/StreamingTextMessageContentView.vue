<template>
    <div class="streaming-text-message-container"
         v-bind:class="{out:message.direction === 0}">
        <p class="text" v-html="this.textContent" ></p>
        <LoadingView v-if="message.messageContent.type === 14"/>
    </div>
</template>

<script>
import Message from "@/wfc/messages/message";
import {parser as emojiParse} from "@/emoji/emoji";
import LoadingView from "../../../common/LoadingView.vue";

export default {
    name: "StreamingTextMessageContentView",
    components: {LoadingView},
    props: {
        message: {
            type: Message,
            required: true,
        }
    },
    data() {
        return {
            textSelected: false,
        }
    },
    mounted() {
    },

    methods: {
        escapeHtml(text) {
            return text.replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/ /g, '&nbsp;')
                .replace(/<script/gi, "&lt;script")
                .replace(/<iframe/gi, "&lt;iframe");
        }
    },

    computed: {
        textContent() {
            let content = this.message.messageContent.digest(this.message);
            let lines = content.split('\n');
            if (lines.length > 1) {
                content = lines.map(line => `<span>${this.escapeHtml(line)}</span>\n`).reduce((total, cv, ci, arr) => total + cv, '');
            } else {
               content = this.escapeHtml(content)
                }
            content = emojiParse(content);
            // tmp = marked.parse(tmp);
            if (content.indexOf('<img') >= 0) {
                content = content.replace(/<img/g, '<img style="max-width:400px;"')
                return content;
            }
            return content;
        }
    }
}
</script>

<style lang="css" scoped>
.streaming-text-message-container {
    margin: 0 10px;
    padding: 10px;
    background-color: white;
    position: relative;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
}

.streaming-text-message-container p {
    user-select: text;
    white-space: pre-line;
}

.streaming-text-message-container >>> .loading {
    margin: 5px 0 0;
}

.streaming-text-message-container.out {
    background-color: #98ea70;
}

.streaming-text-message-container .text {
    color: #050505;
    font-size: 16px;
    line-height: 25px;
    /*max-width: 600px;*/
    word-break: break-word;
    display: inline-block;
    user-select: none;
}

/*style for v-html */
.streaming-text-message-container .text >>> img {
    max-width: 200px !important;
    display: inline-block;
}

.streaming-text-message-container .text >>> a{
    white-space: normal;
}

</style>
