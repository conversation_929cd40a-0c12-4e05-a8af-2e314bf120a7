<template>
    <div class="sticker-content-container">
        <img v-show="imageLoaded === false" @click="preview(message)"
             v-bind:src="'data:video/jpeg;base64,' + message.messageContent.thumbnail"
             class="sticker-image">
        <img v-show="imageLoaded" @click="preview(message)" @load="onImageLoaded"
             v-bind:src="message.messageContent.remotePath"
             class="sticker-image">
    </div>
</template>

<script>
import Message from "@/wfc/messages/message";

export default {
    name: "StickerMessageContentView",
    props: {
        message: {
            type: Message,
            required: true,
        }
    },
    data() {
        return {
            imageLoaded: false,
        }
    },
    methods: {
        preview(message) {
            // TODO
            console.log('TODO, preview sticker collection');
        },
        onImageLoaded() {
            this.imageLoaded = true
        }
    }
}
</script>

<style lang="css" scoped>
.sticker-content-container {
    margin: 0 10px;
    position: relative;
    border-radius: 5px;
    width: 100px;
    height: 100px;
    display: block;
    text-align: left;
}

.sticker-image {
    width: 100px;
    height: 100px;
    max-height: 100px;
    max-width: 100px;
    border-radius: 5px;
    overflow: hidden;
    object-fit: cover;
    display: block;
    margin: 0;
}

.right-arrow:before {
    border-left-color: white;
}

.left-arrow:before {
    border-left-color: white;
}

</style>
