<template>
	<view class="card-message-container" @click="goToCardDetail">
		<view class="card-info-container">
			<view>
				<image :src="card.avatar" class="avatar" mode="aspectFill"></image>
			</view>
			<view class="gender-dot" :class="card.gender === 1 ? 'male' : 'female'"></view>
			<view class="user-info">
				<view class="name-role-container">
					<text class="user-name">{{card.name}}</text>
					<text v-if="card.roleName" class="user-role">{{card.roleName}}</text>
				</view>
				<text class="company-name">{{card.company || '暂无公司'}}</text>
				<view class="rating-container">
					<text class="rating-text">评分:</text>
					<uni-icons type="star-filled" size="14" color="#FFD700"></uni-icons>
					<text class="rating-text">{{ card.score }}分</text>
				</view>
			</view>
		</view>
		<view class="divider"></view>
		<view class="introduction">
			{{ introductionText }}
		</view>
	</view>
</template>

<script>
import appServerApi from '@/api/appServerApi';
export default {
	name: 'CardCustomMessageContentView',
	props: {
		message: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			card: {},
		};
	},
	computed: {
		cardId() {
			return this.message.messageContent.cardId;
		},
		genderClass() {
			// gender: 1 男，0 女
			if (this.card.gender === 1) return 'male';
			if (this.card.gender === 0) return 'female';
			return '';
		},
		introductionText() {
			const intro = this.card.introduction || this.card.description || '';
			if (intro.length > 45) {
				return intro.slice(0, 50) + '...';
			}
			return intro;
		}
	},
	async mounted() {
		if (this.cardId) {
			try {
				const res = await appServerApi.getCardDetailById(this.cardId);
				if (res && res.data) {
					this.card = res.data;
				}
			} catch (e) {
				console.error('获取名片详情失败', e);
			}
		}
	},
	methods: {
		goToCardDetail() {
			console.log("点击了跳转名片详情页",this.card,this.cardId)
			if (!this.card || !this.cardId) return;
			const cardStr = encodeURIComponent(JSON.stringify(this.card));
			uni.navigateTo({
				url: `/pages/card/CardDetails?card=${cardStr}`
			});
		}
	}
}
</script>

<style scoped>
.card-message-container {
	background: #fff;
	border-radius: 12px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
	padding: 10px 10px 10px 10px;
	width: 250px;
	/* height: 150px; */
	margin: 5px 10px 5px 0px;
}

.card-info-container {
	display: flex;
	flex-direction: row;
  	align-items: flex-start;
  	gap: 12px;
	/* margin: 20px; */
	/* background-color: #f0f; */
}

.avatar {
	width: 50px;
	height: 50px;
	margin-top: 5px;
	border-radius: 8px;
	object-fit: cover;
	/* margin-right: 8px; */
	
}

.gender-dot {
	width: 8px;
	height: 8px;
	border-radius: 50%;
	margin-top: 6px;
	flex-shrink: 0;
}

.gender-dot.male {
	background: #007AFF;
}

.gender-dot.female {
	background: #FF2D55;
}

.introduction {
	/* margin-top: 12px; */
	/* height: 80px; */
	color: #444;
	font-size: 14px;
	line-height: 1.5;
	background: #fff;
	border-radius: 8px;
	padding: 10px 0px;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.name-role-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-size: 16px;
  font-weight: 500;
  color: #222;
}

.user-role {
  padding: 2px 8px;
  background: #007AFF;
  border-radius: 10px;
  font-size: 12px;
  color: #fff;
}

.company-name {
  font-size: 14px;
  color: #666;
}

.rating-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-text {
  font-size: 12px;
  color: #666;
}

.divider {
  height: 1px;
  background: linear-gradient(to right, #f0f0f0, #e0e0e0, #f0f0f0);
  margin: 8px 0 0 0;
  border: none;
}
</style>