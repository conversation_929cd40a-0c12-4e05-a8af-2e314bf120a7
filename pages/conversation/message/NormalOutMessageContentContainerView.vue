<template>
    <div>
        <div class="message-time-container" v-bind:class="{checked:sharedPickState.messages.indexOf(message) >= 0}">
            <p v-if="this.message._showTime" class="time">{{ message._timeStr }}</p>

            <div class="message-content-container" v-bind:class="{checked:sharedPickState.messages.indexOf(message) >= 0}">
                <checkbox id="checkbox" v-if="sharedConversationState.enableMessageMultiSelection" type="checkbox"
                    class="checkbox" :checked="isMessageChecked" :value="message" placeholder="" />

                <div class="message-avatar-content-container">
                    <LoadingView v-if="message.status === 0 || isDownloading" />
                    <i v-if="message.status === 2" class="icon-ion-close-circled" style="color: red" @click="resend" />
                    <div class="flex-column flex-align-end">
                        <MessageContentContainerView :message="message" class="message-content-container-view"
                            @contextmenu.native.prevent="openMessageContextMenu($event, message)"
                            @longpress="handleLongPress($event, message)" />
                        <QuoteMessageView v-if="quotedMessage" style="padding: 5px 0; max-width: 100%" :message="message"
                            :quoted-message="quotedMessage" :message-digest="this.message.messageContent.quoteInfo.messageDigest"
                            :show-close-button="false" />
                    </div>

                    <img class="avatar" @click="onClickUserPortrait(message.from)" @longpress="onLongPressUserPortrait(message.from)" draggable="false" :src="message._from.portrait?message._from.portrait:Config.DEFAULT_PORTRAIT_URL" alt="" @error="imgUrlAlt" >
                </div>
            </div>
            <!-- 已读/未读状态标签，移到消息内容之后 -->
            <p class="receipt" @click="showMessageReceiptDetail">
                {{ messageReceipt }}
            </p>
        </div>
    </div>

</template>

<!--suppress LanguageDetectionInspection -->
<script>
    import UserCardView from "@/pages/user/UserCardView";
    import Message from "@/wfc/messages/message";
    import MessageContentContainerView from "@/pages/conversation/message/MessageContentContainerView";
    import store from "@/store";
    import LoadingView from "@/pages/common/LoadingView";
    import wfc from "@/wfc/client/wfc";
    import ConversationType from "@/wfc/model/conversationType";
    import QuoteMessageView from "@/pages/conversation/message/QuoteMessageView";
    import Config from "@/config";
    import appServerApi from '@/api/appServerApi'
    import cacheManager from "@/utils/CacheManager";

    export default {
        name: "NormalOutMessageContentView",
        props: {
            message: {
                type: Message,
                required: true,
            },
        },
        data() {
            return {
                sharedConversationState: store.state.conversation,
                sharedPickState: store.state.pick,
                highLight: false,
                quotedMessage: null,
                localReadCount: 0, // 添加本地已读计数
                messageReadUsers: [], // 确保初始化为空数组
                isUpdatingReadStatus: false, // 标记是否正在更新读状态
                lastUpdateTime: 0, // 上次更新时间
                lastReadStatusUpdateTime: 0, // 添加最后一次更新时间
            }
        },
        components: {
            QuoteMessageView,
            LoadingView,
            MessageContentContainerView,
            UserCardView,

        },
        mounted() {
            this.checkInitMessageReadStatus();
            // 设置事件监听器 - 移到这里
            this.setupEventListeners();
            
            // 加载引用的消息
            if (this.message.messageContent.quoteInfo) {
                let messageUid = this.message.messageContent.quoteInfo.messageUid;
                let msg = store.getMessageByUid(messageUid);
                if (!msg) {
                    wfc.loadRemoteMessage(messageUid, (ms) => {
                        msg = store._patchMessage(ms[0]);
                        this.quotedMessage = msg;
                    }, err => {
                        console.log('load remote message error', messageUid, err)
                    })
                } else {
                    this.quotedMessage = msg;
                }
            }
            // 如果是群聊消息，初始化已读状态并设置定时刷新
            if (this.message.conversation.type === ConversationType.Group) {
                // console.log('初始化群聊消息已读状态:', this.message.messageId);
                // 获取当前用户ID
                const currentUserId = cacheManager.getItem('userId');
                // 如果不是自己发送的消息，立即标记为已读
                if (this.message.from !== currentUserId) {
                    // 使用 nextTick 确保组件已完全渲染
                    this.$nextTick(() => {
                        console.log('组件挂载时标记群聊消息已读');
                        this.markGroupMessageAsRead();
                    });
                }
            }
            this.syncAllDataSources();
        },
        beforeDestroy() {
            // 移除事件监听器 - 添加到这里
            this.removeEventListeners();
            // 移除可见性变化监听
            // document.removeEventListener("visibilitychange", this.handleVisibilityChange);

            // 移除uni-app监听
            // uni.offAppShow();
        },
        methods: {
              // 设置事件监听器
            setupEventListeners() {
              uni.$on("messageReadStatusUpdated", this.handleMessageReadCountUpdate);
              uni.$on("batchMessageReadStatusUpdated", this.handleBatchMessageReadStatusUpdate);
            },
            // 移除事件监听器
            removeEventListeners() {
              console.log('移除事件监听器');
              // 修正方法名，确保与setupEventListeners中的一致
              uni.$off('messageReadStatusUpdated', this.handleMessageReadCountUpdate);
              uni.$off('batchMessageReadStatusUpdated', this.handleBatchMessageReadStatusUpdate);
            },
            imgUrlAlt(e) {
				      e.target.src = Config.DEFAULT_PORTRAIT_URL;
			      },
            onClickUserPortrait(userId) {
                store.setCurrentFriend(this.message._from);
                uni.navigateTo({
                    url: '/pages/contact/UserDetailPage',
                    success: () => {
                        console.log('nav to UserDetailPage success');

                    },
                    fail: (err) => {
                        console.log('nav to UserDetailPage err', err);
                    }
                })
            },
            onLongPressUserPortrait(userId) {
                // 只在群聊中支持长按@用户功能
                if (this.message.conversation.type !== ConversationType.Group) {
                    return;
                }
                
                // 不能@自己
                if (userId === wfc.getUserId()) {
                    return;
                }
                
                // 触发@用户事件，通过事件总线通知父组件
                this.$eventBus.$emit('mentionUser', {
                    userId: userId,
                    userInfo: this.message._from,
                    conversationTarget: this.message.conversation.target
                });
            },
            resend() {
                wfc.deleteMessage(this.message.messageId);
                wfc.sendMessage(this.message);
            },
            openMessageContextMenu(event, message) {
                this.$eventBus.$emit('openMessageContextMenu', [event, message])
                this.highLight = true;
            },

            // 更新群聊消息已读状态
            updateGroupMessageReadStatus(force = false, silentUpdate = false) {
                if (!this.message || !this.message.messageId) {
                    return;
                }

                // 防止频繁更新
                const now = Date.now();

                // 如果正在更新中，取消本次请求
                if (this.isUpdatingReadStatus) {
                    return;
                }

                // 获取messageUid
                let messageUidCombined = null;
                try {
                    // 尝试获取messageUid (用于新接口)
                    if (this.message.messageUid) {
                        const conversationId = this.message.conversation ? this.message.conversation.target : '';
                        const messageUid = this.message.messageUid.toString();

                        if (messageUid && conversationId) {
                            messageUidCombined = `${messageUid}–${conversationId}`;
                        }
                    }
                } catch (e) {
                    console.error('获取messageUid失败:', e);
                }

                // 性能优化: 先检查全局变量中是否有最新的已读计数
                if (typeof window !== 'undefined' && window._messageReadCountsByUid) {
                    // 优先使用服务器推送的已读计数
                    // const messageIdKey = this.message.messageId;
                    const messageUidKey = messageUidCombined;
                    // 尝试两种key获取已读计数
                    let serverReadCount = 0;
                    if (messageUidKey && window._messageReadCountsByUid[messageUidKey]) {
                        serverReadCount = window._messageReadCountsByUid[messageUidKey];
                    }

                    // 如果服务器有更新的已读计数
                    if (serverReadCount > 0 && serverReadCount > (this.localReadCount || 0)) {
                        console.log(`使用服务器缓存的已读计数: ${serverReadCount}人 (原计数: ${this.localReadCount || 0}人)`);

                        // 更新本地计数
                        this.localReadCount = serverReadCount;

                        // 如果有combined messageUid，也保存一份
                        if (messageUidCombined) {
                            const uidCacheKey = `group_msg_read_count_uid_${messageUidCombined}`;
                            cacheManager.setItem(uidCacheKey, {
                                count: serverReadCount,
                                timestamp: now,
                                fromServer: true
                            });
                        }

                        // 如果不是强制更新，可以直接返回，不用再请求API
                        if (!force) {
                            return;
                        }
                    }
                }

                // 如果不是强制更新和静默更新，进行性能优化检查
                if (!force && !silentUpdate) {
                    // 尝试获取群成员总数，用于优化请求频率
                    try {
                        if (this.message.conversation && this.message.conversation.type === ConversationType.Group) {
                            // 获取群ID
                            const groupId = this.message.conversation.target;

                            if (groupId) {
                                // 获取群成员数量
                                const groupMemberIds = wfc.getGroupMemberIds(groupId, false);
                                const groupMemberCount = groupMemberIds.length;

                                if (groupMemberCount > 0) {
                                    // 智能控制刷新频率
                                    if (!force && this.localReadCount > 0) {
                                        // 获取发送者ID
                                        const senderId = this.message.from;
                                        // 计算有效的群成员数量（排除发送者）
                                        const effectiveMemberCount = groupMemberCount > 1 && senderId ? (groupMemberCount - 1) : groupMemberCount;

                                        // 如果当前已读数已达到最大值，则不再请求更新
                                        if (this.localReadCount >= effectiveMemberCount) {
                                            return;
                                        }

                                        // 检查是否距离上次更新不足3分钟，如果是且已有合理的已读计数，则不更新
                                        const timeSinceLastUpdate = now - this.lastReadStatusUpdateTime;
                                        if (timeSinceLastUpdate < 180000 && this.localReadCount > 0) { // 3分钟 = 180000毫秒
                                            return;
                                        }
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        console.error('获取群成员数量失败:', e);
                    }
                }

                this.isUpdatingReadStatus = true;
                this.lastReadStatusUpdateTime = now;

                // 只有在需要详细用户信息时才请求完整的已读用户列表
                // 例如强制更新或用户点击查看已读人员时
                if (force) {
                    // 调用API获取已读用户列表
                    appServerApi.getReadUsers(this.message.messageId)
                        .then(readUsers => {
                            this.processReadUsersList(readUsers, messageUidCombined, true);
                            this.isUpdatingReadStatus = false;
                        })
                        .catch(error => {
                            console.error('获取已读用户列表失败:', error);
                            this.isUpdatingReadStatus = false;
                        });
                } else {
                    // 使用本地缓存的已读计数，不请求API
                    this.recoverCachedReadCount(messageUidCombined);
                    this.isUpdatingReadStatus = false;
                }
            },

            // 处理已读用户列表数据
            processReadUsersList(readUsers, messageUidCombined, isForDetailView = false) {
                let newReadCount = 0;

                if (readUsers && readUsers.length > 0) {
                    // 检查返回的是具体用户列表还是占位符
                    const hasPlaceholders = readUsers.some(user => user.isPlaceholder);

                    if (hasPlaceholders) {
                        // 获取占位符中的计数信息
                        const placeholderData = readUsers.find(user => user.isPlaceholder);
                        if (placeholderData) {
                            const placeholderCount = placeholderData.count || 0;
                            newReadCount = placeholderCount;
                            this.messageReadUsers = []; // 清空，表示没有具体用户数据
                        }
                    } else {
                        // 处理实际用户列表
                        this.messageReadUsers = readUsers;

                        // 计算实际已读人数，排除发送者
                        const senderId = this.message.from;
                        const uniqueUserIds = new Set();

                        readUsers.forEach(user => {
                            const userId = user.userId || user.uid;
                            if (userId && userId !== senderId) uniqueUserIds.add(userId);
                        });

                        newReadCount = uniqueUserIds.size;
                    }
                } else {
                    // API返回空列表
                    if (this.localReadCount && this.localReadCount > 0) {
                        newReadCount = this.localReadCount;
                    } else {
                        newReadCount = 0;
                    }
                }

                // 只有在非详情查看模式下才更新本地计数
                if (!isForDetailView) {
                    // 确保读计数不会减少
                    newReadCount = Math.max(this.localReadCount || 0, newReadCount);

                    // 更新本地计数
                    this.localReadCount = newReadCount;

                    // 保存到本地存储
                    const cacheKey = `group_msg_read_count_${messageUidCombined}`;
                    cacheManager.setItem(cacheKey, {
                        count: newReadCount,
                        timestamp: Date.now()
                    });

                    // 如果有combined messageUid，也保存一份
                    if (messageUidCombined) {
                        const uidCacheKey = `group_msg_read_count_uid_${messageUidCombined}`;
                        cacheManager.setItem(uidCacheKey, {
                            count: newReadCount,
                            timestamp: Date.now()
                        });
                    }

                    console.log(`更新消息 ${this.message.messageId} 的已读计数: ${newReadCount}人`);
                } else {
                    // 详情查看模式下，只更新用户列表，不改变计数
                    console.log(`详情查看模式：获取到 ${readUsers ? readUsers.length : 0} 个已读用户，保持原有计数 ${this.localReadCount || 0}人`);
                }
            },

            // 标记群聊消息为已读
            markGroupMessageAsRead() {
                const groupId = this.message.conversation.target;
                const currentUserId = cacheManager.getItem('userId');
                const originalSenderId = this.message.from;

                // 获取完整的messageUid（high-low组合格式）作为API的msgId
                let messageUidCombined = null;
                try {
                    if(!this.message.messageUid){
                      console.warn('无法获取 messageUid，无法发送 GROUP_READ 回执');
                      return; // 如果没有 UID，无法发送正确的 GROUP_READ
                    }
                    if (typeof this.message.messageUid === 'object') {
                      if (this.message.messageUid.high !== undefined && this.message.messageUid.low !== undefined) {
                        // 使用high-low组合格式
                        messageUidCombined = `${this.message.messageUid.high}-${this.message.messageUid.low}`;
                      } else if (this.message.messageUid.low !== undefined) {
                        // 只有low部分可用
                        messageUidCombined = `${this.message.messageUid.low}`;
                      }
                    } else {
                      // 如果messageUid是字符串，直接使用
                      messageUidCombined = this.message.messageUid.toString();
                    }
                    console.log('获取到用于发送 GROUP_READ 的 msgId (uid combined):', messageUidCombined);
                } catch (e) {
                    console.error('获取 messageUid 失败:', e);
                    return;
                }

                if (!messageUidCombined || !groupId || !currentUserId || !originalSenderId) {
                    console.error('标记群聊消息已读参数不完整 (GROUP_READ):', { msgId: messageUidCombined, from: currentUserId, to: originalSenderId, groupId });
                    return;
                }

                // 仅当接收者不是消息发送者时才发送已读回执
                if (originalSenderId !== currentUserId) {
                    console.log(`准备发送 GROUP_READ: msgId=${messageUidCombined}, from=${currentUserId}, to=${originalSenderId}`);

                    // 构建符合 API 规范的 payload
                    const payload = {
                        type: "GROUP_READ",
                        msgId: messageUidCombined,      // 使用 messageUid 组合格式
                        from: currentUserId,      // 当前用户（读者）的 ID
                        to: originalSenderId      // 原始消息发送者的 ID
                    };

                    // 通过WebSocket发送已读状态
                    try {
                        let wsInstance = null;
                        let sendMessageMethod = null;

                        // 优先获取 WebSocket 实例和发送方法
                        if (typeof window !== 'undefined' && window.wfcWebsocket) {
                            wsInstance = window.wfcWebsocket;
                            // 假设发送方法是 sendMessage 或类似名称，需要根据实际情况调整
                            if (typeof wsInstance.sendMessage === 'function') {
                                sendMessageMethod = wsInstance.sendMessage;
                            } else if (typeof wsInstance.send === 'function') { // 备选方法名
                                sendMessageMethod = wsInstance.send;
                            }
                        } else if (typeof uni !== 'undefined' && uni.wfcWebsocket) {
                            wsInstance = uni.wfcWebsocket;
                             if (typeof wsInstance.sendMessage === 'function') {
                                sendMessageMethod = wsInstance.sendMessage;
                            } else if (typeof wsInstance.send === 'function') {
                                sendMessageMethod = wsInstance.send;
                            }
                        }

                        if (sendMessageMethod) {
                            // 直接调用发送方法，传递 JSON 字符串
                            sendMessageMethod(JSON.stringify(payload));
                            console.log('已发送 GROUP_READ 状态 (Payload): ', JSON.stringify(payload));

                            // 发送已读标记后，主动更新本地已读计数
                            // 默认自增1，因为我们刚刚将自己标记为已读
                            try {
                                if (this.localReadCount !== undefined) {
                                    // 增加计数并保证至少为1
                                    this.localReadCount = Math.max(1, (this.localReadCount || 0) + 1);

                                    // 同步到本地存储
                                    const cacheKey = `group_msg_read_count_${this.message.messageId}`;
                                    cacheManager.setItem(cacheKey, {
                                        count: this.localReadCount,
                                        timestamp: Date.now()
                                    });

                                    // 如果有 combined messageUid，也保存一份
                                    if (messageUidCombined) {
                                        const uidCacheKey = `group_msg_read_count_uid_${messageUidCombined}`;
                                        cacheManager.setItem(uidCacheKey, {
                                            count: this.localReadCount,
                                            timestamp: Date.now()
                                        });
                                    }

                                    console.log(`主动更新已读计数(标记已读后): ${this.localReadCount}人`);
                                    this.message.readCount = this.localReadCount;
                                    // 强制更新视图
                                    // this.$forceUpdate();
                                }
                            } catch (e) {
                                console.error('更新本地已读计数失败:', e);
                            }
                        } else {
                             console.warn('未找到可用的WebSocket实例或发送方法，尝试触发事件发送 GROUP_READ');
                             // 触发事件，让中心化的 WebSocket 管理器处理发送
                             // uni.$emit('sendWebSocketPayload', payload);
                        }
                    } catch (e) {
                         console.error('发送 GROUP_READ 状态时出错:', e);
                    }
                } else {
                    // console.log('当前用户是消息发送者，无需发送 GROUP_READ 回执');
                }
            },

            showMessageReceiptDetail() {
                // 单聊消息不需要显示已读人员列表
                let conversation = this.message.conversation;
                if (conversation.type === ConversationType.Single) {
                    return;
                }

                // 如果是群聊消息
                if (conversation.type === ConversationType.Group) {
                    console.log('点击查看已读人员列表，当前已读计数:', this.localReadCount || 0, '，将获取最新数据');

                    // 创建一个加载指示器
                    // uni.showLoading({
                    //     title: '正在加载...',
                    //     mask: true
                    // });

                    // 获取messageUid (用于API调用)
                    let messageUidForApi = null;
                    try {
                        if (this.message.messageUid) {
                            if (typeof this.message.messageUid === 'object') {
                                if (this.message.messageUid.high !== undefined && this.message.messageUid.low !== undefined) {
                                    messageUidForApi = `${this.message.messageUid.high}-${this.message.messageUid.low}`;
                                } else if (this.message.messageUid.low !== undefined) {
                                    messageUidForApi = `${this.message.messageUid.low}`;
                                }
                            } else {
                                messageUidForApi = this.message.messageUid.toString();
                            }
                        }
                    } catch (e) {
                        console.error('获取messageUid失败:', e);
                        // 如果获取messageUid失败，使用messageId
                        messageUidForApi = this.message.messageId;
                    }

                    // 确保我们有用于查询的ID
                    const idForQuery = messageUidForApi;
                    console.log('准备查询消息已读人员列表, msgId =', idForQuery);

                    // 调用API获取实际已读用户列表
                    appServerApi.getReadUsers(idForQuery)
                        .then(readUsers => {
                            // 隐藏加载指示器
                            // uni.hideLoading();

                            console.log('获取到实际已读用户列表:', readUsers);

                            // 确保我们有有效的已读用户列表
                            let validReadUsers = [];

                            if (readUsers && readUsers.length > 0) {
                                // 去重并排除发送者
                                const uniqueUserIds = new Set();
                                const senderId = this.message.from;

                                validReadUsers = readUsers.filter(user => {
                                    if (!user.userId && !user.uid) return false;
                                    const userId = user.userId || user.uid;
                                    // 排除发送者和重复用户
                                    if (userId === senderId || uniqueUserIds.has(userId)) return false;
                                    uniqueUserIds.add(userId);
                                    return true;
                                });

                                console.log('已处理的已读用户列表(去重且排除发送者):', validReadUsers.length, '人');

                                // 注意：这里不再修改localReadCount，保持已读计数的稳定性
                                // 只更新已读用户列表用于显示详情
                                this.messageReadUsers = validReadUsers;
                                
                                console.log(`查看已读详情：获取到 ${validReadUsers.length} 个已读用户，保持原有计数 ${this.localReadCount || 0}人`);
                            } else {
                                console.log('API返回的已读用户列表为空');
                                // 使用空数组表示没有人阅读过消息
                                validReadUsers = [];
                                this.messageReadUsers = [];
                            }

                            // 准备导航到已读人员列表页面
                            this.navigateToReadUsersList(validReadUsers);
                        })
                        .catch(error => {
                            // 隐藏加载指示器
                            // uni.hideLoading();
                            console.error('获取已读用户列表失败:', error);

                            // 导航到空的已读列表
                            this.navigateToReadUsersList([]);

                            // 显示错误提示
                            // uni.showToast({
                            //     title: '获取已读用户列表失败',
                            //     icon: 'none',
                            //     duration: 2000
                            // });
                        });
                }
            },

            // 导航到已读人员列表页面的辅助方法
            navigateToReadUsersList(readUsers) {
                const that = this; // 保存当前this引用供回调使用
                uni.navigateTo({
                    url: '/pages/conversation/message/ReadUsersListView',
                    events: {
                        close: () => {
                            console.log('已读人员列表已关闭');
                        }
                    },
                    success: function(res) {
                        // 确保读用户列表不为null
                        if (!readUsers) {
                            readUsers = [];
                        }

                        // 将数据传递给已读人员列表页面
                        res.eventChannel.emit('setReadUsersData', {
                            message: that.message,
                            readUsers: readUsers
                        });
                    },
                    fail: (err) => {
                        console.error('跳转到已读人员列表页面失败:', err);
                    }
                });
            },
          /**
           *  uni.$emit('batchMessageReadStatusUpdated', {
           *      messageIds: msg.data.messageIds,
           *      conversationId: msg.data.userId,
           *      timestamp: Date.now(),
           *      serverConfirmed: true
           *  });
           * @param data
           */
          handleBatchMessageReadStatusUpdate(data){
            if (data.messageIds && data.messageIds.length > 0) {
                data.messageIds.forEach((msgId, index) => {
                  let messageUidStr = `${this.message.messageUid.high}-${this.message.messageUid.low}`;
                  // 检查是否是本消息的已读更新
                  let isCurrentMessage = (data.messageId === messageUidStr) ||
                      (data.msgId === messageUidStr);
                  if(!isCurrentMessage){
                    return
                  }
                  const uidCacheKey = `group_msg_read_count_uid_${messageUidStr}`;
                  let item = cacheManager.getItem(uidCacheKey);
                  this.handleMessageReadCountUpdate({
                        type: ConversationType.Group,
                        messageId: msgId,
                        isRead: true,
                        readCount: item.count,
                        fromServer: true,
                        timestamp: Date.now()
                      }
                  )
              });
            }
          },
          /**
           *   // 直接触发全局事件通知组件更新
           *   uni.$emit('messageReadStatusUpdated', {
           *       type: ConversationType.Group,
           *       messageId: msgId,
           *       isRead: true,
           *       readCount: readCount,
           *       fromServer: true,
           *       timestamp: Date.now()
           *   });
           * @param data
           */
            // 处理消息读取计数更新事件
            handleMessageReadCountUpdate(data) {
                // 检查是否是本消息的已读更新
                // console.log('handleMessageReadCountUpdate   接收到消息已读计数更新:', data);
                if (!data) return;
                let messageUidStr = `${this.message.messageUid.high}-${this.message.messageUid.low}`;
                // 检查是否是本消息的已读更新
                let isCurrentMessage = (data.messageId === messageUidStr) ||
                    (data.msgId === messageUidStr);

                if (!isCurrentMessage) {
                    return; // 不是本消息的更新
                }

                console.log('接收到当前消息的已读计数更新:', data);

                // 尝试从不同来源获取计数值
                let newCount = 0;
                if (data.count !== undefined) {
                    newCount = parseInt(data.count);
                } else if (data.readCount !== undefined) {
                    newCount = parseInt(data.readCount);
                }

                if (isNaN(newCount) || newCount < 0) {
                    newCount = 0;
                }

                // 只有当新计数大于当前计数时才更新
                if (newCount > (this.localReadCount || 0)) {
                    console.log(`使用服务器推送的已读计数: ${newCount}人 (原计数: ${this.localReadCount || 0}人)`);

                    // 更新已读计数
                    this.localReadCount = newCount;

                    // 保存到缓存
                        const cacheKey = `group_msg_read_count_${messageUidStr}`;
                        cacheManager.setItem(cacheKey, {
                            count: newCount,
                            timestamp: Date.now(),
                            fromServer: data.fromServer || false
                        });
                      const uidCacheKey = `group_msg_read_count_uid_${messageUidStr}`;
                      cacheManager.setItem(uidCacheKey, {
                        count: newCount,
                        timestamp: Date.now(),
                        fromServer: data.fromServer || false
                      });
                    // 强制刷新视图
                    this.$forceUpdate();
                }
            },

            // 检查单聊消息的已读状态
            checkSingleChatMessageReadStatus(forceCheck = false, printLog = true) {
                // 如果消息不是由当前用户发送的，无需检查已读状态
                if (this.message.from !== cacheManager.getItem('userId')) {
                    return;
                }
                try {
                    // // 1. 检查本地isRead标记（这个标记应该只有在收到真实已读确认时才会设置）
                    // if (this.message.isRead) {
                    //   return "已读";
                    // }
                    // 获取该会话的最后已读确认时间
                    const targetUserId = this.message.conversation.target;
                    let timestamp = this.message.timestamp;
                    const readStaKey = `msg_read_status_` + targetUserId;
                    const cachedSta = cacheManager.getItem(readStaKey);
                    if (cachedSta && cachedSta.serverConfirmed) {
                      if(cachedSta.readTime >= timestamp){
                        // if(!this.message.isRead){
                        //   this.message.isRead = true;
                        // }
                        return "已读";
                      }else{
                        return "未读";
                      }
                    }
					          const messageUid = `${this.message.messageUid.high}-${this.message.messageUid.low}`;
                    // 3. 检查消息读取状态Map
                    let readStatus = store.state.messageReadStatus.get(messageUid);
                    if (readStatus && readStatus > 0) {
                      // 不立即更新本地标记，等待服务器确认
                      console.log('消息在store中标记为已读，但等待服务器确认:messageUid ', messageUid);
                      return "发送中..."; // 显示为发送中状态
                    }
                    // 方法1: 检查本地存储的服务器确认的已读状态
                    const readStatusKey = `msg_read_status_${messageUid}`;
                    const cachedStatus = cacheManager.getItem(readStatusKey);
                    console.log('通过本地存储 readStatusKey:', JSON.stringify(cachedStatus));
                    if (cachedStatus && cachedStatus.isRead) {
                        if (cachedStatus.serverConfirmed) {
                            // 这是最可靠的：有服务器确认的已读状态
                            if (printLog) {
                                console.log('通过本地存储的服务器确认判断消息已读:', messageUid);
                            }
                            this.markAsRead(true); // 传递serverConfirmed=true
                            return;
                        } else if (forceCheck) {
                            // 只在强制检查时才考虑本地标记但未经服务器确认的状态
                            if (printLog) {
                                console.log('通过本地存储的本地标记判断消息已读:', messageUid);
                            }
                            this.markAsRead(false); // 传递serverConfirmed=false
                            return;
                        }
                    }

                    // 方法2: 检查全局变量中的服务器确认状态
                    if (typeof window !== 'undefined' && window._serverConfirmedReadMessages) {
                        if (window._serverConfirmedReadMessages[this.message.messageId]) {
                            // 全局变量中有服务器确认的状态
                            if (printLog) {
                                console.log('通过全局缓存的服务器确认判断消息已读:', this.message.messageId);
                            }
                            this.markAsRead(true); // 传递serverConfirmed=true
                        }
                    }
                } catch (error) {
                    console.error('检查单聊消息已读状态出错:', error);
                }
            },

            // 标记消息为已读
            markAsRead(serverConfirmed = false) {
                console.log('标记消息为已读:', this.message.messageId, serverConfirmed ? '(服务器确认)' : '(本地标记)');
                // 更新本地状态
                this.localReadCount = 1;
                this.lastUpdateTime = Date.now();

                // 更新消息对象本身，确保下次检查时直接通过
                // this.message.isRead = true;
                // this.message.readTime = this.lastUpdateTime;

                // 更新store中的状态
                // try {
                //     if (store.commit) {
                //         store.commit('UPDATE_MESSAGE_READ_STATUS', {
                //             messageId: this.message.messageId,
                //             value: 1,
                //             updateTime: this.lastUpdateTime,
                //             serverConfirmed: serverConfirmed
                //         });
                //     }
                // } catch (e) {
                //     console.error('无法更新store状态:', e);
                // }

                // 全局缓存 (确保window对象存在)
                if (typeof window !== 'undefined') {
                    if (!window._lastReadMessages) window._lastReadMessages = {};
                    window._lastReadMessages[this.message.messageId] = this.lastUpdateTime;

                    // 如果是服务器确认的，特别记录
                    if (serverConfirmed) {
                        if (!window._serverConfirmedReadMessages) window._serverConfirmedReadMessages = {};
                        window._serverConfirmedReadMessages[this.message.messageId] = this.lastUpdateTime;
                    }
                }
                let messageUid = this.message.messageContent.quoteInfo.messageUid;
                // 本地缓存状态
                const readStatusKey = `msg_read_status_${messageUid}`;
                cacheManager.setItem(readStatusKey, {
                    isRead: true,
                    readTime: this.lastUpdateTime,
                    serverConfirmed: serverConfirmed
                });

                // 立即更新视图
                this.$forceUpdate();
                // 触发全局更新事件，确保其他相关组件也能及时更新
                // uni.$emit('messageReadStatusUpdated', {
                //     messageId: this.message.messageId,
                //     isRead: true,
                //     timestamp: this.lastUpdateTime,
                //     serverConfirmed: serverConfirmed // 传递服务器确认状态
                // });
                console.log('消息已标记为已读:', this.message.messageId, serverConfirmed ? '(服务器确认)' : '(本地标记)');
            },

            // 初始检查消息已读状态，在组件挂载时调用
            checkInitMessageReadStatus() {
                try {
                    // 检查消息对象是否有isRead标记
                    // if (this.message.isRead) {
                    //   return;
                    // }
                    // 快速判断消息已读状态
                    if (this.message.conversation.type === ConversationType.Single) {
                        // 检查本地存储中的读取状态 (尽量避免同步IO操作)
                        try {
                            const readStatusKey = `msg_read_status_${this.message.conversation.target}`;
                            const cachedStatus = cacheManager.getItem(readStatusKey);
                            if (cachedStatus && cachedStatus.serverConfirmed && cachedStatus.readTime > this.message.timestamp) {
                                // 由于已标记为已读，不需要设置定时检查
                                return;
                            }
                        } catch (e) {
                            console.error('检查本地缓存消息状态出错:', e);
                        }

                        // 在组件挂载完成后，再进行全面检查（不打印日志）
                        this.$nextTick(() => {
                            this.checkSingleChatMessageReadStatus(false, false);
                        });
                    }
                } catch (error) {
                    console.error('初始检查消息已读状态出错:', error);
                }
            },

            // 当组件挂载时同步所有数据源
            syncAllDataSources() {
                // 实现同步所有数据源的逻辑
                try {
                    // console.log('同步消息所有数据源:', this.message.messageId);
                    // 1. 同步单聊消息的已读状态
                    if (this.message.conversation.type === ConversationType.Single) {
                        this.checkSingleChatMessageReadStatus(false, true);
                    }

                    // 2. 同步群聊消息的已读状态
                    else if (this.message.conversation.type === ConversationType.Group) {
                        // 强制更新群聊消息已读状态
                        this.updateGroupMessageReadStatus(false, true);

                        // 确保群聊消息已被标记为已读（如果当前用户不是发送者）
                        const currentUserId = cacheManager.getItem('userId');
                        if (this.message.from !== currentUserId) {
                            this.markGroupMessageAsRead();
                        }
                    }

                    // 3. 同步引用消息数据
                    if (this.message.messageContent && this.message.messageContent.quoteInfo && !this.quotedMessage) {
                        let messageUid = this.message.messageContent.quoteInfo.messageUid;
                        let msg = store.getMessageByUid(messageUid);
                        if (!msg) {
                            wfc.loadRemoteMessage(messageUid, (ms) => {
                                if (ms && ms.length > 0) {
                                    msg = store._patchMessage(ms[0]);
                                    this.quotedMessage = msg;
                                }
                            }, err => {
                                console.log('无法加载引用消息:', messageUid, err);
                            });
                        } else {
                            this.quotedMessage = msg;
                        }
                    }

                    // 4. 同步本地存储和store中的状态
                    // this.syncLocalStorageWithStore();
                } catch (error) {
                    console.error('同步数据源时出错:', error);
                }
            },

            // 同步本地存储和store中的状态
            // syncLocalStorageWithStore() {
            //     try {
            //       console.log('syncLocalStorageWithStore 同步本地存储和store中的状态:', this.message)
            //         const messageUid = `${this.message.messageUid.high}-${this.message.messageUid.low}`;
            //         // 单聊消息处理
            //         if (this.message.conversation.type === ConversationType.Single) {
            //             const readStatusKey = `msg_read_status_${messageUid}`;
            //             const cachedStatus = cacheManager.getItem(readStatusKey);
            //             console.log('syncLocalStorageWithStore cachedStatus:', cachedStatus);
            //             const storeStatus = store.state.messageReadStatus.get(messageUid);
            //              console.log('syncLocalStorageWithStore storeStatus:', storeStatus);
            //             // 如果store中有状态但本地存储没有，更新本地存储
            //             if (storeStatus && storeStatus > 0 && (!cachedStatus || !cachedStatus.isRead)) {
            //                 cacheManager.setItem(readStatusKey, {
            //                     isRead: true,
            //                     timestamp: Date.now(),
            //                     serverConfirmed: false
            //                 });
            //             }
            //             // 如果本地存储有确认的状态但store没有，更新store
            //             else if (cachedStatus && cachedStatus.isRead && cachedStatus.serverConfirmed && (!storeStatus || storeStatus <= 0)) {
            //                 store.state.messageReadStatus.set(messageUid, 1);
            //             }
            //         }
            //
            //         // 群聊消息处理
            //         else if (this.message.conversation.type === ConversationType.Group) {
            //             const readCountKey = `group_msg_read_count_${messageUid}`;
            //             const cachedCount = cacheManager.getItem(readCountKey);
            //
            //             // 如果本地有读取计数但组件状态没有，更新组件状态
            //             if (cachedCount && cachedCount.count > 0 && (!this.localReadCount || this.localReadCount < cachedCount.count)) {
            //                 this.localReadCount = cachedCount.count;
            //             }
            //             // 如果组件状态有计数但本地没有，更新本地存储
            //             else if (this.localReadCount && this.localReadCount > 0 && (!cachedCount || cachedCount.count < this.localReadCount)) {
            //                 cacheManager.setItem(readCountKey, {
            //                     count: this.localReadCount,
            //                     timestamp: Date.now()
            //                 });
            //             }
            //         }
            //     } catch (error) {
            //         console.error('同步本地存储和store状态时出错:', error);
            //     }
            // },
            handleLongPress(event, message) {
                // 在iOS上使用长按事件
                if (uni.getSystemInfoSync().platform === 'ios') {
                    // 添加安全检查，确保event对象及其方法存在
                    try {
                        // 只有在事件对象存在且有必要的方法时才调用
                        if (event && typeof event.stopPropagation === 'function') {
                            event.stopPropagation();
                        }
                        if (event && typeof event.preventDefault === 'function') {
                            event.preventDefault();
                        }

                        // 使用nextTick确保DOM更新后再显示菜单
                        this.$nextTick(() => {
                            this.openMessageContextMenu(event || {}, message);
                        });
                    } catch (error) {
                        console.error('处理长按事件出错:', error);
                        // 如果事件处理出错，仍然尝试显示菜单
                        this.openMessageContextMenu(event || {}, message);
                    }
                }
            },
            // 从缓存中恢复已读计数
            recoverCachedReadCount(messageUidCombined) {
                try {
                    // 先尝试使用 combined messageUid查找
                    if (messageUidCombined) {
                        const uidCacheKey = `group_msg_read_count_uid_${messageUidCombined}`;
                        const cachedCount = cacheManager.getItem(uidCacheKey);
                        if (cachedCount && cachedCount.count) {
                            console.log(`从messageUid缓存恢复已读计数: ${cachedCount.count}人`);
                            this.localReadCount = Math.max(this.localReadCount || 0, cachedCount.count);
                            return;
                        }
                    }

                    // 再尝试使用messageId查找
                    const cacheKey = `group_msg_read_count_${messageUidCombined}`;
                    const cachedCount = cacheManager.getItem(cacheKey);
                    if (cachedCount && cachedCount.count) {
                        // console.log(`从messageId缓存恢复已读计数: ${cachedCount.count}人`);
                        this.localReadCount = Math.max(this.localReadCount || 0, cachedCount.count);
                    } else {
                        // 如果没有缓存数据，保持当前值或设为0
                        if (!this.localReadCount) {
                            this.localReadCount = 0;

                            // 保存到本地存储
                            cacheManager.setItem(cacheKey, {
                                count: 0,
                                timestamp: Date.now()
                            });

                            // 如果有 combined messageUid，也保存一份
                            if (messageUidCombined) {
                                const uidCacheKey = `group_msg_read_count_uid_${messageUidCombined}`;
                                cacheManager.setItem(uidCacheKey, {
                                    count: 0,
                                    timestamp: Date.now()
                                });
                            }
                        }
                    }
                } catch (e) {
                    console.error('从缓存恢复已读计数失败:', e);
                    // 设置为0，如果当前没有值
                    if (!this.localReadCount) {
                        this.localReadCount = 0;
                    }
                }
            },

            async updateReadStatus(messageUidStr,readCount) {
              const now = Date.now();
              const msgTime = this.message.timestamp || 0;
              if (readCount !== 0 || now - msgTime < 7 * 24 * 60 * 60 * 1000) {
                return
              }
              readCount = 1;
              try {
                // 如果有messageUid，更新对应的缓存
                const uidCacheKey = `group_msg_read_count_uid_${messageUidStr}`;
                cacheManager.setItem(uidCacheKey, {
                  count: readCount,
                  timestamp: now
                });
              } catch (e) {
                console.error('更新本地已读计数缓存失败:', e);
              }
            }
          },

        computed: {
            messageReceipt() {
                // console.log('messageReceipt------------------->', this.message)
                // 消息发送者不是当前用户时，不显示已读状态
                const currentUserId = cacheManager.getItem('userId');
                const conversation = this.message.conversation;
                const targetUserId = this.message.conversation.target;
                let readCount = 0;
                const timestamp = this.message.timestamp;
                const from = this.message.from;
                let receiptDesc = '';
                if (conversation.type === ConversationType.Single) {
                    if (from !== currentUserId) {
                        return '';
                    }

                    // 获取该会话的最后已读确认时间
                    const readStatusKey = `msg_read_status_` + targetUserId;
                    const cachedStatus = cacheManager.getItem(readStatusKey);
                    if (cachedStatus && cachedStatus.serverConfirmed) {
                      if (cachedStatus.readTime >= timestamp) {
                        this.message.isRead = true;
                        return "已读";
                      } else {
                        return "未读"
                      }
                    }
                    return "未读";
                }
                // 群聊消息处理逻辑，显示已读人数与群成员总数的比例
                let groupMembers = wfc.getGroupMemberIds(conversation.target, false);
                if (!groupMembers || groupMembers.length === 0) {
                  return '';
                }
                let messageUidStr = '';
                if (this.message.messageUid && typeof this.message.messageUid === 'object') {
                  if (this.message.messageUid.high !== undefined && this.message.messageUid.low !== undefined) {
                    messageUidStr = `${this.message.messageUid.high}-${this.message.messageUid.low}`;
                  } else if (this.message.messageUid.low !== undefined) {
                    messageUidStr = `${this.message.messageUid.low}`;
                  }
                } else {
                  messageUidStr = this.message.messageUid.toString();
                }
                const cacheUidKey = `group_msg_read_count_uid_${messageUidStr}`;
                const cacheUidKeyStu = cacheManager.getItem(cacheUidKey);
                if(cacheUidKeyStu && cacheUidKeyStu.count && cacheUidKeyStu.count > readCount){
                  readCount = cacheUidKeyStu.count;
                }
                // 获取群成员总数，排除发送者
                let memberCount = groupMembers.length;
                const senderId = this.message.from;
                // 使用消息读取用户列表的实际长度作为已读人数
                if (this.messageReadUsers && this.messageReadUsers.length > 0 && readCount < this.messageReadUsers.length) {
                  // 计算不重复的实际已读用户数，排除发送者
                  const uniqueUserIds = new Set();
                  this.messageReadUsers.forEach(user => {
                    const userId = user.userId || user.uid;
                    if (userId && userId !== senderId) uniqueUserIds.add(userId);
                  });
                  readCount = uniqueUserIds.size;
                } else {
                  // 如果没有读取用户列表，使用localReadCount
                  readCount = readCount || this.localReadCount || 0;
                }
                // 计算有效的群成员数量（排除发送者）
                const effectiveMemberCount = memberCount > 1 ? (memberCount - 1) : memberCount;
                // 确保不超过有效群成员总数，但必须至少为0
                readCount = Math.min(Math.max(0, readCount), effectiveMemberCount);
                receiptDesc = `已读 ${readCount}/${effectiveMemberCount}`;
                this.updateReadStatus(messageUidStr,readCount);
                return receiptDesc;
            },

            isDownloading() {
                return store.isDownloadingMessage(this.message.messageId);
            },

            // shouldShowMessageReceipt() {
            //     return this.sharedConversationState.isMessageReceiptEnable && ["FireRobot", Config.FILE_HELPER_ID].indexOf(this
            //         .message.conversation.target) < 0;
            // },

            isMessageChecked() {
                return this.sharedPickState.messages.findIndex(m => m.messageId === this.message.messageId) >= 0;
            }
        },

    }
</script>

<style lang="css" scoped>
    .message-time-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
    }

    .message-time-container.checked {
        background-color: #e7e7e7;
    }

    .message-time-container .time {
        width: 100%;
        margin-bottom: 20px;
        text-align: center;
        color: #b4b4b4;
        font-size: 10px;
    }

    .message-time-container .receipt {
        padding-right: 60px;
        font-size: 11px;
        padding-top: 2px;
        padding-bottom: 5px;
        color: #b4b4b4;
        clear: both;
    }

    .message-content-container {
        width: 100%;
        display: flex;
        padding: 8px 12px;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }


    .message-avatar-content-container {
        display: flex;
        max-width: calc(100% - 40px);
        /* overflow: hidden; */
        /*max-height: 800px;*/
        margin-left: auto;
        text-overflow: ellipsis;
        align-items: flex-start;
        position: relative;
    }
    .isweidu{
        width: 20px;
        position: absolute;
        bottom: 0;
        left: -20px;
        color: #999999;
        font-size: 10px;
    }
    .isyidu{
        color: #386BF6;
    }
    .message-avatar-content-container .avatar {
        width: 40px;
        height: 40px;
        border-radius: 8px;
    }

    .message-content-container-view.highlight {
        background-color: #dadada;
        opacity: 0.5;
        --out-arrow-color: #dadada !important;
    }
</style>