<template>
	<view>
		<view class="wf-message-input-container">
			<!-- 将引用消息框移到工具栏外部，作为整个输入区域的顶部 -->
			 <!-- 引用消息框 -->
			<view v-if="sharedConversationState.quotedMessage" class="quote-message-container">
				<view class="quoted-message single-line">
					<text class="quoted-sender">{{ getQuotedSenderName() }}：</text>
					{{ sharedConversationState.quotedMessage.messageContent.digest(sharedConversationState.quotedMessage) }}
				</view>
				<view class="cancel icon-ion-close" @click="cancelQuote"></view>
			</view>
			<!-- 内容输入框 -->
			<view class="wf-message-input-toolbar">
				<!-- 语音按钮还是键盘按钮 -->
				<view class="wf-input-button-icon wxfont" @click="toggleVoice"
					:class="showVoice ? 'keyboard' : 'voice'"></view>
				<view class="wf-input-button-icon wxfont" v-if="isPttEnable" @click="togglePtt"
					:class="showPtt ? 'keyboard' : 'voice_playing'"></view>
				<!-- 显示按住说话 -->
				<view v-if="showVoice" style="width: 100%">
					<view class="wf-input-voice-container">
						<AudioInputView :conversation-info="conversationInfo"></AudioInputView>
					</view>
				</view>
				<view class="wf-input-voice-container" v-else-if="showPtt">
					<PttAudioInputView :conversation-info="conversationInfo"></PttAudioInputView>
				</view>
				<!-- 显示输入框 -->
				<view v-else class="input-area-container">
					<view class="input-area-wrapper">
						<!-- 移除了这里的引用消息框 -->
						<scroll-view 
							ref="scrollView"
							scroll-y 
							class="wf-input-textarea" 
							:scroll-top="scrollTop"
							:scroll-into-view="scrollIntoView"
							:scroll-with-animation="false"
							@scroll="onScroll"
							@scrolltoupper="onScrolltoupper"
							@scrolltolower="onScrolltolower"
						>
							<view class="input-content">
								<textarea 
									ref="textarea" 
									@focus="onInputFocus" 
									class="wf-input-textarea-inner" 
									@input="onInput"
									:value="text" 
									:focus="inputFocus"
									placeholder="" 
									hold-keyboard 
									:confirm-type="confirmType"
									@confirm="onConfirm"
									:maxlength="-1" 
									auto-height 
								/>
							</view>
						</scroll-view>
					</view>
				</view>
				<!-- 礼物按钮 -->
				<view v-if="conversationInfo?.conversation?.type == 0" @click.prevent="toggleliwu" class=" rq_liwu">
					<image class="rq_liwuimg" src="../../static/image/chat/ds.png"></image>
				</view>
				<!-- 表情按钮 -->
				<view @click.prevent="toggleEmoji" class="wf-input-button-icon wxfont emoji"></view>
				<!-- 发送按钮 -->
				<view v-if="(!hideSendButton || showEmoji) && text !== ''" class="wf-input-text-send-button" @touchstart.prevent=""
					@touchmove.prevent="" @touchend.prevent="send(text)"
					:style="{ background: text !== '' ? '#4168e0' : '#F7F7F7', color: text !== '' ? '#fff' : '#ddd', 'border-color': text !== '' ? '#1BC418' : '#ddd' }">
					发送
				</view>
				<!-- 更多按钮 -->
				<view v-if="hideSendButton || text === ''" @click="toggleExt" class="wf-input-button-icon wxfont add2">
				</view>
			</view>

			<view v-if="showExt" class="wf-ext-container">
				<view class="wf-ext-item" v-for="(v, i) in extList" @click="onClickExt(v)" :key="i">
					<view class="wf-ext-item-icon">
						<view class="wxfont" :class="v.icon"></view>
					</view>
					<view class="wf-ext-item-text">{{ v.title }}</view>
				</view>
			</view>
			<!-- 表情搜索面板 -->
			<view v-if="fullScreenSearch" class="emoji-fullscreen-search" :style="'height: ' + (keyboardHeight + 80) + 'px'">
				<view class="emoji-search-header">
					<view class="search-input-wrap">
						<view class="search-icon wxfont search"></view>
						<input type="text" v-model="emojiSearchKeyword" placeholder="搜索表情" @input="onEmojiInput"
							class="search-input" focus />
						<view v-if="emojiSearchKeyword" class="clear-icon wxfont close" @click="clearSearch"></view>
					</view>
					<view class="cancel-search" @click="toggleEmojiSearch">取消</view>
				</view>
				
				<scroll-view scroll-y class="emoji-search-content" @scrolltolower="onSearchScrollToLower">
					<!-- 搜索历史和推荐 -->
					<view v-if="!emojiSearchKeyword" class="emoji-search-suggestions">
						<!-- 推荐搜索 -->
						<view class="search-section">
							<view class="search-section-header">
								<text class="section-title">推荐搜索</text>
							</view>
							<view class="search-tags">
								<view class="search-tag" v-for="(keyword, index) in recommendKeywords" :key="index" 
									@click="searchWithKeyword(keyword)">{{keyword}}</view>
							</view>
						</view>
						
						<!-- 热门表情展示区域 -->
						<view v-if="searchResults.length > 0" class="search-section">
							<view class="search-section-header">
								<text class="section-title">{{ currentRecommendKeyword ? `"${currentRecommendKeyword}" 相关表情` : '热门表情' }}</text>
							</view>
							<view class="search-results-grid">
								<view class="search-result-item" v-for="(item, index) in searchResults" :key="index"
									@click="onClickSearchEmoji(item)">
									<image :src="item" mode="aspectFit" class="emoji-image" />
								</view>
							</view>
							<view v-if="hasMore" class="loading-more" @click="loadMoreRecommendedEmojis">加载更多...</view>
						</view>
					</view>
					
					<!-- 搜索结果 -->
					<view v-if="emojiSearchKeyword && searchResults.length > 0" class="search-results-grid">
						<view class="search-result-item" v-for="(item, index) in searchResults" :key="index"
							@click="onClickSearchEmoji(item)">
							<image :src="item" mode="aspectFit" class="emoji-image" />
						</view>
					</view>
					
					<!-- 加载更多 -->
					<view v-if="emojiSearchKeyword && hasMore && searchResults.length > 0" class="loading-more" @click="loadMoreResults">加载更多...</view>
					
					<!-- 无搜索结果 -->
					<view v-if="emojiSearchKeyword && searchResults.length === 0" class="no-result">
						<text>没有找到「{{emojiSearchKeyword}}」相关表情</text>
					</view>
				</scroll-view>
			</view>
			<!-- 赞赏 -->
			<view v-if="showliwu" class="rq-ds-mask" @click="showliwu = false">
				<view class="rq-ds" :style="'height: auto;'" @click.stop>
					<view class="dsa">
						<view class="dsa1">礼物</view>
						<view class="dsa2">
							<image class="dsa2a" src="../../static/image/chat/jb.png"></image>
							<view class="dsa2b">{{gold}}</view>
							<view class="dsa2c" @click="handleRecharge">充值</view>
						</view>
					</view>
					<scroll-view v-if="currentEmojiStickerIndex === 0" :scroll-y="true" class="dsb"
						:style="'height: ' + (keyboardHeight - 60) + 'px'">
						<view class="dsba">
							<view class="dsba1" @click="onliwu(v)" v-for="(v,i) in lwList" :key="i">
								<image class="dsba1a" :src="v.url"></image>
								<view class="dsba1b dsba1d" v-if="v.price==0">赞赏</view>
								<view class="dsba1c dsba1d" v-else>{{v.price}}金币</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
			<!-- 表情面板遮罩层 - 用于点击空白处关闭 -->
			<view v-if="showEmoji" class="emoji-close-mask" 
				:style="'bottom: ' + (keyboardHeight + 60) + 'px'" 
				@click="closeEmojiPanel"></view>
			
			<!-- Emoji -->
			<view v-if="showEmoji" class="wf-stickers-container" :style="'height: ' + keyboardHeight + 'px'">
				<view class="category-container">
					<view class="search-category" @click="toggleEmojiSearch">
						<image class="search-icon-img" src="../../static/image/chat/search.svg" mode="aspectFit" />
					</view>
					<scroll-view scroll-x class="category-scroll">
						<view class="category" v-for="(v, i) in emojiStickerList" :key="i" @click="onCategoryClick(i)">
							<img :src="v.poster" v-bind:class="{active: i === currentEmojiStickerIndex}">
						</view>
					</scroll-view>
				</view>

				<!-- 搜索面板 -->
				<view v-if="showEmojiSearch" class="emoji-search-panel">
					<view class="emoji-search-header">
						<view class="search-input-wrap">
							<view class="search-icon wxfont search"></view>
							<input type="text" v-model="emojiSearchKeyword" placeholder="搜索表情" @input="onEmojiInput"
								class="search-input" focus />
							<view v-if="emojiSearchKeyword" class="clear-icon wxfont close" @click="clearSearch"></view>
						</view>
						<view class="cancel-search" @click="toggleEmojiSearch">取消</view>
					</view>

					<!-- 搜索结果 -->
					<scroll-view v-if="emojiSearchKeyword && searchResults.length > 0" scroll-y class="search-results"
						:style="'height: ' + (keyboardHeight - 110) + 'px'" @scrolltolower="loadMoreResults">
						<view class="search-results-grid">
							<view class="search-result-item" v-for="(item, index) in searchResults" :key="index"
								@click="onClickSearchEmoji(item)">
								<image :src="item" mode="aspectFit" class="emoji-image" />
							</view>
						</view>
						<view class="loading-more" v-if="hasMore">加载更多...</view>
					</scroll-view>

					<!-- 无搜索结果提示 -->
					<view v-else-if="emojiSearchKeyword && searchResults.length === 0" class="no-result">
						<text>暂无相关表情</text>
					</view>
				</view>

				<!-- 默认表情内容 -->
				<view v-else>
					<scroll-view v-if="currentEmojiStickerIndex === 0" :scroll-y="true" class="wf-emoji-container"
						:style="'height: ' + (keyboardHeight - 60) + 'px'">
						<view class="wf-emoji-content">
							<view class="emoji-item" @click="onClickEmoji(v)"
								v-for="(v,i) in emojiStickerList[0].emojis" :key="i">
								{{ v }}
							</view>
						</view>
					</scroll-view>

					<scroll-view v-else :scroll-y="true" class="wf-sticker-container"
						:style="'height: ' + (keyboardHeight - 60) + 'px'">
						<view class="wf-sticker-content">
							<img :src="s" class="sticker-item" @click="onClickSticker(s)"
								v-for="(s,j) in emojiStickerList[currentEmojiStickerIndex].stickers" :key="j" />
						</view>
					</scroll-view>
				</view>
				
				<!-- 悬浮删除按钮 -->
				<view v-if="text.length > 0" class="emoji-delete-button" @click="deleteLastCharacter">
					<view class="delete-icon">⌫</view>
				</view>
			</view>
			<!-- 赞赏弹窗 -->
			<view class="rq-tca" v-if="lwdata">
				<view class="tc">
					<view class="tca">
						<image class="tca0" :src="lwdata.url"></image>
						<view class="tca1">请输入数量</view>
						<view class="tca2"><input class="tca2a" type="number" v-model="lwnum" /></view>
						<view class="tca3" @click="onsendliwu">确认赞赏</view>
						<view class="tca4">
							<view class="icon-ion-ios-close-empty tca4a" @click="lwdata = false;"></view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<IosPaymentSelector v-model:visible="iosPaymentVisible" @close="handlePaymentClose" @confirm="handlePaymentConfirm" />
		<GoldPaymentSelector v-model:visible="paymentVisible" @close="handlePaymentClose" @confirm="handlePaymentConfirm" />
	</view>
</template>

<script>
	import TextMessageContent from "../../wfc/messages/textMessageContent";
	import ConversationInfo from "../../wfc/model/conversationInfo";
	import wfc from "../../wfc/client/wfc";
	import store from "../../store";
	import ConversationType from "../../wfc/model/conversationType";
	import emojiStickerConfig from "./emojiStickerConfig";
	import StickerMessageContent from "../../wfc/messages/stickerMessageContent";
	import Config from "../../config";
	import QuoteInfo from "../../wfc/model/quoteInfo";
	import AudioInputView from "./message/AudioInputView.vue";
	import PttAudioInputView from "./message/PttAudioInputView.vue";
	import Draft from "../util/draft";
	import pttClient from "../../wfc/ptt/pttClient";
	import avengineKit from "../../wfc/av/engine/avengineKit";
	import permision from "../../common/permission";
	import checkVoipPermissions from "../voip/voipUtil";
	import appServerApi from '@/api/appServerApi'
	import GoldPaymentSelector from '@/components/payment/GoldPaymentSelector'
	import IosPaymentSelector from '@/components/payment/IosPaymentSelector'
	import messageLimitManager from '../../utils/messageLimitManager'
	// 添加防抖函数实现
	function debounce(func, wait) {
		let timeout;
		return function executedFunction(...args) {
			const later = () => {
				clearTimeout(timeout);
				func.apply(this, args);
			};
			clearTimeout(timeout);
			timeout = setTimeout(later, wait);
		};
	}
	import {
		getItem,
		setItem
	} from '../util/storageHelper'
	import topMessage from '@/common/topMessageView'
	import SoundMessageContent from "@/wfc/messages/soundMessageContent";
	import antiScamNotificationHelper from "../../utils/antiScamNotificationHelper";
	export default {
		name: "MessageInputView",
		components: {
			AudioInputView,
			PttAudioInputView,
			GoldPaymentSelector,
			IosPaymentSelector
		},
		props: {
			conversationInfo: {
				type: ConversationInfo,
				required: true,
				default: null,
			},
		},
		data() {
			return {
				lwnum: 1,
				lwdata: false,
				lwList: getItem('gift_list'),
				emojiStickerList: emojiStickerConfig,
				currentEmojiStickerIndex: 0,
				hideSendButton: Config.getWFCPlatform() === 1 || Config.getWFCPlatform() === 8,
				showRecorder: false,
				showVoice: false,
				showPtt: false,
				paymentVisible: false,
				iosPaymentVisible: false,
				isPttEnable: pttClient.isPttClientEnable(),
				extList: [{
						title: '相册',
						tag: 'image',
						icon: 'image'
					},
					{
						title: '拍摄',
						tag: 'shot',
						icon: 'camera'
					},
					{
						title: '语音通话',
						tag: 'voip_a',
						icon: 'voip'
					},
					{
						title: '视频通话',
						tag: 'voip_v',
						icon: 'voip_v'
					},
					{
						title: '文件',
						tag: 'file',
						icon: 'file'
					},
					// {
					//     title: '位置',
					//     tag: 'location',
					//     icon: 'location'
					// },
					{
						title: '名片',
						tag: 'userCard',
						icon: 'user_card'
					},
					// {
					//     title: '赞赏',
					//     tag: 'liwu',
					//     icon: 'liwu'
					// },
				],
				msgFocus: false,
				showExt: false,
				showliwu: false,
				showEmoji: false,
				lastInputFocusState: undefined,
				text: '',
				timer: '',
				talkTo: '',
				keyboardHeight: 300,
				currentKeyboardHeight: 0,
				windowHeight: 0,
				longTapItemKey: '',
				// chatWindowData:[],
				localData: {},
				sharedMiscState: store.state.misc,
				sharedConversationState: store.state.conversation,
				mentions: [],
				groupMemberUserInfos: [],
				emojiSearchKeyword: '',
				searchResults: [],
				currentPage: 1,
				pageSize: 20,
				hasMore: true,
				// 在data中创建防抖的搜索函数
				debouncedSearch: null,
				showEmojiSearch: false,
				gold: 0,
				draftSaveTimer: null, // 添加定时保存草稿的计时器
				// 添加滚动相关的数据
				scrollTop: 0,
				scrollIntoView: '',
				autoScroll: true,
				oldScrollTop: 0,
				scrollHeight: 0,
				isDraftRestored: false,
				fullScreenSearch: false, // 添加全屏搜索状态控制
				recommendKeywords: ['开心', '生气', '可爱', '委屈', '流泪', '惊讶'], // 添加推荐关键词
				currentRecommendKeyword: '', // 添加当前推荐关键词字段
				shouldFocusOnQuote: false, // 添加引用消息时是否需要聚焦的标志
				inputFocus: false, // 将inputFocus从computed移到data中
			};
		},
		created() {
			// 在created中初始化防抖函数
			this.debouncedSearch = debounce((value) => {
				this.emojiSearchKeyword = value;
				this.currentPage = 1;
				this.searchEmoji();
			}, 300);
			
			// 检查表情搜索API是否可用
			// this.checkEmojiApiAvailable();
		},
		mounted() {
			console.log('mounted', this.conversationInfo);
			if (this.conversationInfo.conversation.type === ConversationType.Group) {
				this.groupMemberUserInfos = store.getGroupMemberUserInfos(this.conversationInfo.conversation.target, false,
					false);
			} else {
				this.extList.push({
					title: '赞赏',
					tag: 'liwu',
					icon: 'liwu'
				});
			}
			this.restoreDraft();
			
			// 初始化输入框焦点状态
			this.updateInputFocus();
			
			// 监听引用消息变化
			this.$watch('sharedConversationState.quotedMessage', (newVal) => {
				if (newVal) {
					// 如果有新的引用消息，设置标志并在下一帧聚焦输入框
					this.shouldFocusOnQuote = true;
					this.$nextTick(() => {
						this.focusInputOnQuote();
					});
				}
			});
			
			// 页面初始化时滚动到底部
			this.$nextTick(() => {
				setTimeout(() => {
					this.scrollToBottom();
				}, 100);
			});
		},
		beforeUnmount() {
			this.storeDraft(this.conversationInfo)
		},
		methods: {
			// 处理键盘确认事件
			onConfirm() {
				try {
					const systemInfo = uni.getSystemInfoSync();
					if (systemInfo.platform === 'ios') {
						// iOS：发送消息
						this.send(this.text);
					} else {
						// Android：换行
						this.text = this.text + '\n';
						// 确保光标在最后并滚动到可见位置
						this.$nextTick(() => {
							this.scrollToBottom();
						});
					}
				} catch (error) {
					// 如果获取系统信息失败，默认为换行行为
					console.warn('获取系统信息失败，执行换行操作:', error);
					this.text = this.text + '\n';
					this.$nextTick(() => {
						this.scrollToBottom();
					});
				}
			},
			// 更新输入框焦点状态
			updateInputFocus() {
				// 只有在用户主动操作时才设置焦点，避免页面初始化时自动弹起键盘
				this.inputFocus = (!this.showExt && !this.showEmoji && !this.showVoice && this.shouldFocusOnQuote);
			},
			onsendliwu() {
				const liwuId = this.lwdata.id;
				const price = this.lwdata.price;
				if (price * this.lwnum > this.gold) {
					uni.showToast({
						title: '金币不足，请先充值',
						icon: 'none',
						duration: 2000
					})
				} else if (this.lwnum < 1) {
					uni.showToast({
						title: '礼物数量至少是1',
						icon: 'none',
						duration: 2000
					})
				} else {
					appServerApi.sendGift({
						userId: this.conversationInfo.conversation._target.uid,
						giftId: liwuId,
						num: this.lwnum
					}).then((res) => {
						store.sendGift(this.conversationInfo.conversation, liwuId, this.lwnum);
						this.toggleliwu();
					}).catch(err => {
						uni.showToast({
							title: err,
							icon: 'none',
							duration: 2000
						})
					})
				}
			},
			onliwu(e) {
				this.lwdata = e
			},
			scrollToBottom() {
				try {
					this.$nextTick(() => {
						// 检查组件是否还存在
						if (!this.$refs || !this.$refs.scrollView) {
							return;
						}
						
						// 使用更精确的方式滚动到底部
						// 先设置一个较大的值，然后在下一帧获取实际的滚动高度
						this.scrollTop = 99999;
						
						// 在下一帧获取实际的滚动高度并设置精确位置
						this.$nextTick(() => {
							try {
								const query = uni.createSelectorQuery().in(this);
								if (query) {
									// 查询scroll-view的滚动信息
									query.select('.wf-input-textarea-inner').boundingClientRect((rect) => {
										if (rect && rect.height) {
											// 设置到真正的底部
											this.scrollTop = rect.height + 100;
										}
									}).exec();
								}
							} catch (error) {
								// 如果查询失败，保持之前设置的大值
								console.warn('获取精确滚动高度失败，使用默认值:', error);
							}
						});
						
						// 确保光标在最后
						setTimeout(() => {
							try {
								this.setCursorToEnd();
							} catch (error) {
								console.warn('设置光标位置时出错:', error);
							}
						}, 0); // 从10ms改为0ms
					});
				} catch (error) {
					console.warn('scrollToBottom 执行出错:', error);
				}
			},
			onInput(event) {
				let inserting = false;
				if (event.detail.value.length > this.text.length) {
					inserting = true;
				}
				this.text = event.detail.value;
				const cursor = event.detail.cursor;
				
				// 确保光标在最后并滚动到可见位置
				this.scrollToBottom();

				if (this.conversationInfo.conversation.type === ConversationType.Group) {
					if (inserting) {
						if (inserting && this.text.length > 0 && this.text.charAt(cursor - 1) === '@') {
							const onPickUser = user => {
								this.text = this.text.substring(0, cursor - 1) + `@${user.displayName} ` + this.text
									.substring(cursor);
								this.mentions.push(user);
								this.inputFocus = true;
								// 确保状态一致性
								this.$nextTick(() => {
									this.updateInputFocus();
								});
							};
							let atAll = {
								uid: '@all',
								displayName: '所有人',
								portrait: this.conversationInfo.conversation._target.portrait,
							}
							this.$pickUser({
								users: [atAll, ...this.groupMemberUserInfos],
								showCategoryLabel: false,
								successCB: onPickUser,
							})
						}
					}
				} else {
					// deleting
				}
				// 添加自动保存草稿的逻辑
				this.autosaveDraft();
				//this.inputFocus = true;	
				// 在输入后滚动到底部
				//if (this.autoScroll) {
				//	this.scrollToBottom();
				//}
			},

			mention() {

			},
			
			// 新增：处理长按头像@用户功能
			mentionUser(userId, userInfo) {
				// 只在群聊中支持@用户功能
				if (this.conversationInfo.conversation.type !== ConversationType.Group) {
					return;
				}
				
				// 不能@自己
				if (userId === wfc.getUserId()) {
					return;
				}
				
				// 获取用户显示名称
				let displayName = userInfo._displayName || userInfo.displayName;
				if (!displayName) {
					// 如果没有显示名称，尝试从群成员信息中获取
					displayName = wfc.getGroupMemberDisplayName(this.conversationInfo.conversation.target, userId);
				}
				
				// 在输入框中添加@用户
				const mentionText = `@${displayName} `;
				this.text = this.text + mentionText;
				
				// 添加到mentions数组中
				if (!this.mentions.find(user => user.uid === userId)) {
					this.mentions.push({
						uid: userId,
						displayName: displayName,
						portrait: userInfo.portrait
					});
				}
				
				// 设置输入框焦点
				this.inputFocus = true;
				this.updateInputFocus();
				
				// 滚动到底部确保输入内容可见
				this.$nextTick(() => {
					this.scrollToBottom();
				});
				
				// 自动保存草稿
				this.autosaveDraft();
			},
			send() {
				if (!this.conversationInfo) {
					return;
				}
				
				// 检查临时会话消息发送限制
				const limitResult = messageLimitManager.checkCanSendMessage(this.conversationInfo.conversation);
				if (!limitResult.canSend) {
					uni.showToast({
						title: limitResult.reason,
						icon: 'none',
						duration: 3000
					});
					return;
				}
				
				// 在发送新消息前，确保将会话标记为已读
				if (this.conversationInfo.conversation.type === ConversationType.Single) {
					uni.$emit('markConversationAsReadBeforeSend', {
						conversationId: this.conversationInfo.conversation.target
					});
				}
				
				if (this.text) {
					// 清理文本中的零宽字符
					const cleanText = this.text.replace(/\u200B/g, '');
					
					let textMessageContent = new TextMessageContent(cleanText)
					let quotedMessage = this.sharedConversationState.quotedMessage;
					if (quotedMessage) {
						let quoteInfo = QuoteInfo.initWithMessage(quotedMessage);
						textMessageContent.setQuoteInfo(quoteInfo);
						store.quoteMessage(null);
					}
					if (this.conversationInfo.conversation.type === ConversationType.Group && this.mentions.length > 0) {
						const regex = /@\S+(\s|$)/g;
						const matches = cleanText.match(regex);
						if (matches && matches.length > 0) {
							for (let i = 0; i < matches.length; i++) {
								const match = matches[i].trim();
								if (match === '@所有人') {
									let index = this.mentions.findIndex(user => user.uid === '@all')
									if (index >= 0) {
										textMessageContent.mentionedType = 2;
										break
									}
								} else {
									let index = this.mentions.findIndex(user => user.displayName === match.substring(1));
									if (index >= 0) {
										let uid = this.mentions[index].uid;
										if (!textMessageContent.mentionedTargets) {
											textMessageContent.mentionedTargets = [];
										}
										if (textMessageContent.mentionedTargets.indexOf(uid) === -1) {
											textMessageContent.mentionedType = 1;
											textMessageContent.mentionedTargets.push(uid);
										}
									}
								}
							}
						}
					}

					// 发送消息
					wfc.sendConversationMessage(this.conversationInfo.conversation, textMessageContent, [], 
						// preparedCB - 消息准备完成回调
						async (messageId, timestamp) => {
							// 记录临时会话消息发送
							messageLimitManager.recordMessageSent(this.conversationInfo.conversation);
							
							// 检查是否为临时会话的第一次发送消息，如果是则发送防诈骗提醒
							try {
								const conversation = this.conversationInfo.conversation;
								if (conversation.type === ConversationType.Single) {
									// 检查是否为临时会话（非好友关系）
									const isFriend = wfc.isMyFriend(conversation.target);
									if (!isFriend) {
										// 检查是否已经发送过防诈骗提醒
										const hasSentNotification = antiScamNotificationHelper.hasShownTempChatNotification(conversation.target);
										if (!hasSentNotification) {
											console.log('临时会话第一次发送消息，准备发送防诈骗提醒');
											// 延迟发送防诈骗提醒，确保用户消息先显示
											setTimeout(async () => {
												try {
													await antiScamNotificationHelper.sendTempChatNotification(conversation);
													console.log('临时会话防诈骗提醒发送完成');
												} catch (error) {
													console.error('发送临时会话防诈骗提醒失败:', error);
												}
											}, 1000);
										}
									}
								}
							} catch (error) {
								console.error('处理防诈骗提醒时发生错误:', error);
							}
							
							// 如果是临时会话，显示剩余消息数提示
							const tipMessage = messageLimitManager.getTempChatTip(this.conversationInfo.conversation);
							if (tipMessage) {
								setTimeout(() => {
									uni.showToast({
										title: tipMessage,
										icon: 'none',
										duration: 2000
									});
								}, 500);
							}
						},
						// progressCB - 进度回调
						null,
						// successCB - 成功回调
						null,
						// failCB - 失败回调
						(error) => {
							console.error('消息发送失败:', error);
							uni.showToast({
								title: '消息发送失败',
								icon: 'none'
							});
						}
					);
					
					this.text = '';
					this.mentions = [];
					// 确保清除草稿
					Draft.clearConversationDraft(this.conversationInfo.conversation);
				}
			},

			minimizeMessageInputView() {
				this.showEmoji = false;
				this.showExt = false;
				this.showliwu = false;
				// 没有 blur 这个方法，奇怪。。。
				// this.$refs.textarea.blur();
				//uni.hideKeyboard();
			},

			onInputFocus() {
				this.showliwu = false;
				this.showEmoji = false;
				this.showExt = false;
				
				// 如果是由于引用消息导致的聚焦，保持聚焦状态
				if (this.shouldFocusOnQuote) {
					this.shouldFocusOnQuote = false;
				}
			},

			async toggleVoice() {
				/*#ifdef APP-PLUS*/
				if (plus.os.name !== 'iOS') {
					var isPermission = plus.navigator.checkPermission('android.permission.RECORD_AUDIO')
					if (isPermission != 'authorized') {
						topMessage.createTopMessage(
							'麦克风权限使用说明',
							'用于录制语音消息功能。我们不会在未经您同意的情况下使用麦克风。'
						)
					}
					let res = await topMessage.requestPermissions(
						'RECORD_AUDIO',
						'麦克风权限未获得，此权限用于录音功能，请前往设置中打开'
					)
					setTimeout(() => {
						topMessage.hideTopMessage()
					}, 300)
					if (!res.granted[0]) {
						return
					}
				}
				/*#endif*/
				this.showVoice = !this.showVoice;
				this.showPtt = false;
				this.showEmoji = false;
				this.showExt = false;
				this.showliwu = false;
			},

			togglePtt() {
				this.showPtt = !this.showPtt;
				this.showVoice = false;
				this.showEmoji = false;
				this.showExt = false;
				this.showliwu = false;
			},

			toggleEmoji() {
				console.log('------------- toggleEmoji')
				this.showEmoji = !this.showEmoji;
				this.showExt = false;
				this.showVoice = false;
				this.showPtt = false;
				this.showliwu = false;
				if (this.showEmoji) {
					this.$eventBus.$emit('inputPanelHeightChanged');
				}
			},
			toggleExt() {
				this.showExt = !this.showExt;
				this.showEmoji = false;
				this.showVoice = false;
				this.showPtt = false;
				this.showliwu = false;
				if (this.showExt) {
					this.$eventBus.$emit('inputPanelHeightChanged');
				}
			},
			toggleliwu() {
				let newState = !this.showliwu;
				if (newState) {
					appServerApi.getWallet().then(response => {
						console.log('------------- toggleliwu', response?.data)
						this.gold = response?.data?.gold || 0
						this.showliwu = newState
					})
				} else {
					this.showliwu = newState
				}
				this.showExt = false;
				this.showEmoji = false;
				this.showVoice = false;
				this.showPtt = false;
				this.lwdata = false;
			},
			handleRecharge() {
				// 根据平台选择支付方式
				if (uni.getSystemInfoSync().platform === 'ios') {
					this.iosPaymentVisible = true;
				} else {
					this.paymentVisible = true;
				}
			},
			onClickExt(ext) {
				console.log('onClick ext', ext);
				switch (ext.tag) {
					case 'image':
						this.showMediaPicker();
						break;
					case 'shot':
						this.chooseShot();
						break;
					case 'file':
						this.chooseFile();
						break;
					case 'voip_a':
						this.voip(true);
						break;
					case 'voip_v':
						this.voip(false);
						break;
					case 'liwu':
						this.toggleliwu();
						break;
					case 'userCard':
						this.toggleUserCard();
						break;
						// case 'location':
						//     this.toggleLocation();
						//     break;
					default:
						uni.showToast({
							title: 'TODO ' + ext.title,
							icon: 'none'
						})
						break;
				}
			},

			onCategoryClick(i) {
				this.currentEmojiStickerIndex = i;
			},

			onClickEmoji(emoji) {
				console.log('onClick emoji', emoji)
				// 方案1：在表情后添加零宽字符，确保光标有位置
				this.text = this.text + emoji + '\u200B'; // 添加零宽空格
				
				// 方案2：等待DOM更新后再滚动
				this.$nextTick(() => {
					// 增加延迟确保textarea内容已经更新
					setTimeout(() => {
						this.scrollToBottom();
					}, 100);
				});
			},
			
			// 新增：设置光标到文本末尾
			setCursorToEnd() {
				try {
					// 在uni-app中，使用inputFocus属性来控制焦点
					this.$nextTick(() => {
						// 检查组件是否还存在
						if (this.$data && typeof this.inputFocus !== 'undefined') {
							this.inputFocus = true;
							// 确保状态一致性
							this.updateInputFocus();
						}
					});
				} catch (error) {
					console.warn('setCursorToEnd 执行出错:', error);
				}
			},
			
			onClickSticker(sticker) {
				console.log('onClick sticker', sticker)
				let stickerMsg = new StickerMessageContent('', sticker, 200, 200)
				wfc.sendConversationMessage(this.conversationInfo.conversation, stickerMsg);
			},

			async chooseImage() {
				/*#ifdef APP-PLUS*/
				if (plus.os.name !== 'iOS') {
					var isPermission = plus.navigator.checkPermission('android.permission.CAMERA')
					if (isPermission != 'authorized') {
						topMessage.createTopMessage(
							'相机、相册权限使用说明',
							'用于拍摄照片、选择照片发送给好友。我们不会将相机用于其他用途。'
						)
					}
					let res = await topMessage.requestPermissions(
						'CAMERA',
						'相机权限未获得，此权限用于拍摄照片功能，请前往设置中打开'
					)
					setTimeout(() => {
						topMessage.hideTopMessage()
					}, 300)
					if (!res.granted[0]) {
						return
					}
				}
				/*#endif*/
				uni.chooseImage({
					// count: _self.limit ? _self.limit  - _self.fileList.length : 999,
					sourceType: ['album', 'camera'],
					sizeType: ['original', 'compressed'],
					success: (e) => {
						console.log('choose image', e.tempFilePaths);
						e.tempFilePaths.forEach(async path => {
							let filePath;
							// #ifdef APP-PLUS
							if (path.startsWith('file://')) {
								filePath = path.substring('file://'.length);
							} else {
								filePath = plus.io.convertLocalFileSystemURL(path)
							}
							// #endif
							// #ifdef H5
							filePath = await fetch(path).then(res => res.blob())
								.then(blob => {
									let name =
										`${new Date().getTime()}.${blob.type.substring(blob.type.lastIndexOf('/') + 1)}`;
									return new File([blob], name)
								})
							// #endif
							console.log('chooseImage', filePath)
							store.sendFile(this.conversationInfo.conversation, filePath);
						})
					}
				})
			},

			async voip(audioOnly) {
				/*#ifdef APP-PLUS*/
				if (plus.os.name !== 'iOS') {
					var isPermission = plus.navigator.checkPermission('android.permission.RECORD_AUDIO')
					if (isPermission != 'authorized') {
						topMessage.createTopMessage(
							'麦克风权限使用说明',
							'用于语音通话、视频通话功能。我们不会在未经您同意的情况下使用麦克风。'
						)
					}
					let res = await topMessage.requestPermissions(
						'RECORD_AUDIO',
						'麦克风权限未获得，此权限用于语音通话功能，请前往设置中打开'
					)
					setTimeout(() => {
						topMessage.hideTopMessage()
					}, 300)
					if (!res.granted[0]) {
						return
					}
				}
				/*#endif*/
				if (!await checkVoipPermissions(audioOnly)) {
					return;
				}
				let session = avengineKit.currentCallSession()
				if (session && session.state !== 0) {
					uni.showToast({
						title: '音视频通话正在进行中...',
						icon: 'none'
					})
					return;
				}

				if (this.conversationInfo.conversation.type === ConversationType.Single) {
					let callSession = avengineKit.startSingleCall(this.conversationInfo.conversation.target, audioOnly)
					if (callSession) {
						let url = `/pages/voip/Single?session=${JSON.stringify(callSession)}`
						this.$navigateToPage(url);
					}
				} else if (this.conversationInfo.conversation.type === ConversationType.Group) {
					this.showPickGroupMemberToVoipModal(audioOnly)
				}
			},

			showPickGroupMemberToVoipModal(audioOnly) {
				let beforeClose = (users) => {
					let ids = users.map(u => u.uid);
					let callSession = avengineKit.startMultiCall(this.conversationInfo.conversation.target, ids,
						audioOnly);
					// 不加延时的话，不能正常切换页面，会报莫名其妙的错误
					setTimeout(() => {
						if (callSession) {
							let url = `/pages/voip/Multi?session=${JSON.stringify(callSession)}`
							this.$navigateToPage(url);
						}
					}, 50)
				}
				this.$pickUsers({
					users: this.groupMemberUserInfos,
					confirmTitle: this.$t('common.confirm'),
					showCategoryLabel: false,
					successCB: beforeClose,
				})

			},

			async chooseVideo(sourceType = ['camera']) {
				/*#ifdef APP-PLUS*/
				if (plus.os.name !== 'iOS') {
					var isPermission = plus.navigator.checkPermission('android.permission.CAMERA')
					if (isPermission != 'authorized') {
						topMessage.createTopMessage(
							'相机权限使用说明',
							'用于拍摄视频发送给好友。我们不会将相机用于其他用途。'
						)
					}
					let res = await topMessage.requestPermissions(
						'CAMERA',
						'相机权限未获得，此权限用于拍摄视频功能，请前往设置中打开'
					)
					setTimeout(() => {
						topMessage.hideTopMessage()
					}, 300)
					if (!res.granted[0]) {
						return
					}
				}
				/*#endif*/
				uni.chooseVideo({
					// count: _self.limit ? _self.limit  - _self.fileList.length : 999,
					sourceType: sourceType,
					sizeType: ['original', 'compressed'],
					success: async (e) => {
						console.log('choose video', e);
						let duration = e.duration;
						let path = e.tempFilePath;
						let filePath;
						
						// #ifdef APP-PLUS
						if (path.startsWith('file://')) {
							filePath = path.substring('file://'.length);
						} else {
							filePath = plus.io.convertLocalFileSystemURL(path)
						}
						// #endif
						// #ifdef H5
						filePath = await fetch(path).then(res => res.blob())
							.then(blob => {
								let name =
									`${new Date().getTime()}.${blob.type.substring(blob.type.lastIndexOf('/') + 1)}`;
								return new File([blob], name)
							})
						// #endif
						
						// 立即发送文件，不显示loading，让用户快速返回聊天界面
						store.sendFile(this.conversationInfo.conversation, filePath, duration);
					},
					fail: (err) => {
						console.log('选择视频失败', err);
						if (err.errMsg && err.errMsg.indexOf('cancel') > -1) {
							// 用户取消录制，不显示错误提示
							console.log('用户取消录制视频');
						} else {
							uni.showToast({
								title: '录制视频失败',
								icon: 'none',
								duration: 2000
							});
						}
					}
				})
			},

			async chooseShot() {
				// 准备 ActionSheet 选项
				const itemList = ['拍照', '录像'];
				
				// 显示 ActionSheet
				uni.showActionSheet({
					itemList: itemList,
					title: uni.getSystemInfoSync().platform === 'ios' ? '选择拍摄类型' : '',
					success: async (res) => {
						const tapIndex = res.tapIndex;
						
						if (tapIndex === 0) { // 拍照
							/*#ifdef APP-PLUS*/
							if (plus.os.name !== 'iOS') {
								var isPermission = plus.navigator.checkPermission('android.permission.CAMERA')
								if (isPermission != 'authorized') {
									topMessage.createTopMessage(
										'相机权限使用说明',
										'用于拍摄照片发送给好友。我们不会将相机用于其他用途。'
									)
								}
								let res = await topMessage.requestPermissions(
									'CAMERA',
									'相机权限未获得，此权限用于拍摄照片功能，请前往设置中打开'
								)
								setTimeout(() => {
									topMessage.hideTopMessage()
								}, 300)
								if (!res.granted[0]) {
									// 检查是否被永久拒绝
									if (res.deniedAlways && res.deniedAlways.includes('android.permission.CAMERA')) {
										uni.showModal({
											title: '相机权限已被禁用',
											content: '请前往系统设置中手动开启相机权限',
											confirmText: '去设置',
											success: (modalRes) => {
												if (modalRes.confirm) {
													// 引导用户去系统设置
													if (plus.os.name === 'Android') {
														plus.runtime.openURL('package:' + plus.runtime.appid);
													}
												}
											}
										});
									}
									return
								}
							}
							/*#endif*/
							// 拍照
							this.chooseImageFromSource(['camera']);
						} else if (tapIndex === 1) { // 录像
							// 录像
							this.chooseVideo(['camera']);
						}
					},
					fail: (err) => {
						console.log('选择拍摄类型操作取消或失败', err);
					}
				});
			},

			async showMediaPicker() {
				// 准备 ActionSheet 选项
				const itemList = ['从相册选择图片', '从相册选择视频'];
				
				// 使用更简洁的方式显示 ActionSheet
				uni.showActionSheet({
					itemList: itemList,
					// iOS 上添加 title 属性，可能会减少空白区域的出现
					title: uni.getSystemInfoSync().platform === 'ios' ? '选择媒体' : '',
					success: async (res) => {
						// 获取选择的索引
						const tapIndex = res.tapIndex;
						
						if (tapIndex === 0) { // 从相册选择图片
							/*#ifdef APP-PLUS*/
							if (plus.os.name !== 'iOS') {
								// 获取Android版本
								let androidVersion = parseInt(plus.os.version);
								console.log('Android版本:', androidVersion);
								
								// 根据Android版本选择合适的权限
								let permissionName = 'READ_EXTERNAL_STORAGE';
								
								// Android 13及以上使用细分的媒体权限
								if (androidVersion >= 13) {
									permissionName = 'READ_MEDIA_IMAGES';
									console.log('使用READ_MEDIA_IMAGES权限');
								} else {
									console.log('使用READ_EXTERNAL_STORAGE权限');
								}
								
								// 检查权限状态
								var isPermission = plus.navigator.checkPermission('android.permission.' + permissionName);
								console.log('存储权限状态:', isPermission);
								
								// 如果权限已授权，直接选择图片
								if (isPermission === 'authorized') {
									console.log('权限已授权，直接选择图片');
									this.chooseImageFromSource(['album']);
									return;
								}
								
								topMessage.createTopMessage(
									'权限使用说明',
									'需要您开启存储权限,以便为您提供从相册选择照片发送给好友的功能。'
								);
								
								let res = await topMessage.requestPermissions(
									permissionName,
									'存储权限未获得，此权限用于选择照片功能，请前往设置中打开'
								);
								
								console.log('权限请求结果:', JSON.stringify(res));
								
								setTimeout(() => {
									topMessage.hideTopMessage();
								}, 300);
								
								// 检查权限结果中是否有重复前缀问题
								const hasDuplicatePrefix = res.deniedAlways && res.deniedAlways.some(perm => 
									perm.includes('android.permission.android.permission')
								);
								
								if (hasDuplicatePrefix) {
									console.log('检测到权限前缀重复问题，尝试直接访问相册');
									this.chooseImageFromSource(['album']);
									return;
								}
								
								// 如果权限请求失败
								if (!res.granted || res.granted.length === 0) {
									// 检查真实权限状态再次确认
									const realPermissionStatus = plus.navigator.checkPermission('android.permission.' + permissionName);
									console.log('再次确认权限状态:', realPermissionStatus);
									
									if (realPermissionStatus === 'authorized') {
										console.log('权限实际已授权，直接选择图片');
										this.chooseImageFromSource(['album']);
										return;
									}
									
									// 检查是否被永久拒绝
									if (res.deniedAlways && res.deniedAlways.includes('android.permission.' + permissionName)) {
										// 显示引导用户去设置的弹窗
										uni.showModal({
											title: '存储权限问题',
											content: '无法访问您的相册，请在设置中开启存储权限',
											confirmText: '去设置',
											cancelText: '取消',
											success: (modalRes) => {
												if (modalRes.confirm) {
													// 跳转到应用权限设置页面
													if (plus.os.name === 'Android') {
														plus.runtime.openURL('package:' + plus.runtime.appid);
													}
												} else {
													// 尝试直接访问，某些设备可能不需要明确权限
													this.chooseImageFromSource(['album']);
												}
											}
										});
										return;
									}
								}
							}
							/*#endif*/
							// 选择图片
							this.chooseImageFromSource(['album']);
						} else if (tapIndex === 1) { // 从相册选择视频
							/*#ifdef APP-PLUS*/
							if (plus.os.name !== 'iOS') {
								// 获取Android版本
								let androidVersion = parseInt(plus.os.version);
								console.log('Android版本:', androidVersion);
								
								// 根据Android版本选择合适的权限
								let permissionName = 'READ_EXTERNAL_STORAGE';
								
								// Android 13及以上使用细分的媒体权限
								if (androidVersion >= 13) {
									permissionName = 'READ_MEDIA_VIDEO';
									console.log('使用READ_MEDIA_VIDEO权限');
								} else {
									console.log('使用READ_EXTERNAL_STORAGE权限');
								}
								
								// 检查权限状态
								var isPermission = plus.navigator.checkPermission('android.permission.' + permissionName);
								console.log('存储权限状态:', isPermission);
								
								// 如果权限已授权，直接选择视频
								if (isPermission === 'authorized') {
									console.log('权限已授权，直接选择视频');
									this.chooseVideoFromSource(['album']);
									return;
								}
								
								topMessage.createTopMessage(
									'权限使用说明',
									'需要您开启存储权限,以便为您提供从相册选择视频发送给好友的功能。'
								);
								
								let res = await topMessage.requestPermissions(
									permissionName,
									'存储权限未获得，此权限用于选择视频功能，请前往设置中打开'
								);
								
								console.log('权限请求结果:', JSON.stringify(res));
								
								setTimeout(() => {
									topMessage.hideTopMessage();
								}, 300);
								
								// 检查权限结果中是否有重复前缀问题
								const hasDuplicatePrefix = res.deniedAlways && res.deniedAlways.some(perm => 
									perm.includes('android.permission.android.permission')
								);
								
								if (hasDuplicatePrefix) {
									console.log('检测到权限前缀重复问题，尝试直接访问相册');
									this.chooseVideoFromSource(['album']);
									return;
								}
								
								// 如果权限请求失败
								if (!res.granted || res.granted.length === 0) {
									// 检查真实权限状态再次确认
									const realPermissionStatus = plus.navigator.checkPermission('android.permission.' + permissionName);
									console.log('再次确认权限状态:', realPermissionStatus);
									
									if (realPermissionStatus === 'authorized') {
										console.log('权限实际已授权，直接选择视频');
										this.chooseVideoFromSource(['album']);
										return;
									}
									
									// 检查是否被永久拒绝
									if (res.deniedAlways && res.deniedAlways.includes('android.permission.' + permissionName)) {
										// 显示引导用户去设置的弹窗
										uni.showModal({
											title: '存储权限问题',
											content: '无法访问您的相册，请在设置中开启存储权限',
											confirmText: '去设置',
											cancelText: '取消',
											success: (modalRes) => {
												if (modalRes.confirm) {
													// 跳转到应用权限设置页面
													if (plus.os.name === 'Android') {
														plus.runtime.openURL('package:' + plus.runtime.appid);
													}
												} else {
													// 尝试直接访问，某些设备可能不需要明确权限
													this.chooseVideoFromSource(['album']);
												}
											}
										});
										return;
									}
								}
							}
							/*#endif*/
							// 选择视频
							this.chooseVideoFromSource(['album']);
						}
					},
					fail: (err) => {
						console.log('选择媒体操作取消或失败', err);
					}
				});
			},
			
			async chooseImageFromSource(sourceType = ['album']) {
				// 为 iOS 平台添加不同的参数配置
				const isIOS = uni.getSystemInfoSync().platform === 'ios';
				
				uni.chooseImage({
					sourceType: sourceType,
					sizeType: ['original'], // 只使用原图，不压缩
					// 允许所有平台多选，最多20张图片
					count: 20,
					success: (e) => {
						console.log('choose image', e.tempFilePaths);
						
						e.tempFilePaths.forEach(async (path, index) => {
							let filePath;
							// #ifdef APP-PLUS
							if (path.startsWith('file://')) {
								filePath = path.substring('file://'.length);
							} else {
								filePath = plus.io.convertLocalFileSystemURL(path)
							}
							// #endif
							// #ifdef H5
							filePath = await fetch(path).then(res => res.blob())
								.then(blob => {
									let name =
										`${new Date().getTime()}_${index}.${blob.type.substring(blob.type.lastIndexOf('/') + 1)}`;
									return new File([blob], name)
								})
							// #endif
							console.log('chooseImage', filePath)
							// 立即发送文件，不显示loading，让用户快速返回聊天界面
							store.sendFile(this.conversationInfo.conversation, filePath);
						});
					},
					fail: (err) => {
						console.log('选择图片失败', err);
						// 增加更详细的错误反馈
						if (err.errMsg && err.errMsg.indexOf('permission') > -1) {
							uni.showToast({
								title: '无法访问相册，请在系统设置中授权',
								icon: 'none',
								duration: 3000
							});
						} else if (err.errMsg && err.errMsg.indexOf('cancel') > -1) {
							// 用户取消选择，不显示错误提示
							console.log('用户取消选择图片');
						} else {
							uni.showToast({
								title: '选择图片失败',
								icon: 'none',
								duration: 2000
							});
						}
					}
				})
			},
			
			async chooseVideoFromSource(sourceType = ['album']) {
				// 为 iOS 平台添加不同的参数配置
				const isIOS = uni.getSystemInfoSync().platform === 'ios';
				
				uni.chooseVideo({
					sourceType: sourceType,
					compressed: false, // 禁用压缩，减少系统处理时间
					maxDuration: 300, // 限制视频最大时长5分钟，减少处理时间
					// iOS 上添加额外的参数
					camera: isIOS ? 'back' : undefined,
					success: async (e) => {
						console.log('choose video', e);
						let duration = e.duration;
						let path = e.tempFilePath;
						let filePath;
						
						// #ifdef APP-PLUS
						if (path.startsWith('file://')) {
							filePath = path.substring('file://'.length);
						} else {
							filePath = plus.io.convertLocalFileSystemURL(path)
						}
						// #endif
						// #ifdef H5
						filePath = await fetch(path).then(res => res.blob())
							.then(blob => {
								let name =
									`${new Date().getTime()}.${blob.type.substring(blob.type.lastIndexOf('/') + 1)}`;
								return new File([blob], name)
							})
						// #endif
						
						// 立即发送文件，不显示loading，让用户快速返回聊天界面
						store.sendFile(this.conversationInfo.conversation, filePath, duration);
					},
					fail: (err) => {
						console.log('选择视频失败', err);
						// 增加更详细的错误反馈
						if (err.errMsg && err.errMsg.indexOf('permission') > -1) {
							uni.showToast({
								title: '无法访问相册，请在系统设置中授权',
								icon: 'none',
								duration: 3000
							});
						} else if (err.errMsg && err.errMsg.indexOf('cancel') > -1) {
							// 用户取消选择，不显示错误提示
							console.log('用户取消选择视频');
						} else {
							uni.showToast({
								title: '选择视频失败',
								icon: 'none',
								duration: 2000
							});
						}
					}
				})
			},

			chooseFile() {
				wfc.chooseFile('all', async (file) => {
					console.log('choose file', file);
					let path = file.path;
					let filePath;
					// #ifdef APP-PLUS
					if (path.startsWith('file://')) {
						filePath = path.substring('file://'.length);
					} else {
						filePath = plus.io.convertLocalFileSystemURL(path)
					}
					// #endif
					// #ifdef H5
					filePath = await fetch(path).then(res => res.blob())
						.then(blob => {
							let name =
								`${new Date().getTime()}.${blob.type.substring(blob.type.lastIndexOf('/') + 1)}`;
							return new File([blob], name)
						})
					// #endif
					file.path = filePath;
					store.sendFile(this.conversationInfo.conversation, file);
				})
			},

			onKeyboardHeightChange(keyboardHeight, currentKeyboardHeight) {
				this.keyboardHeight = keyboardHeight;
				this.currentKeyboardHeight = currentKeyboardHeight;
			},

			cancelQuote() {
				store.quoteMessage(null);
			},

			restoreDraft() {
				let draft = Draft.getConversationDraftEx(this.conversationInfo);
				if (!draft || !draft.text) {
					return;
				}
				console.log('restore draft', this.conversationInfo, draft);
				store.quoteMessage(draft.quotedMessage);
				if (this.text) {
					console.log('inputting, ignore', draft.text)
				} else {
					this.text = draft.text;
					this.isDraftRestored = true;
					this.$nextTick(() => {
						// 恢复草稿后只滚动到底部，不设置焦点，不弹起键盘
						if (this.text) {
							setTimeout(() => {
								// 恢复草稿后滚动到底部
								this.scrollToBottom();
							}, 50);
						}
					});
				}
			},

			storeDraft(conversationInfo) {
				if (!conversationInfo) return;
				
				let quotedMessage = this.sharedConversationState.quotedMessage;
				// 清理草稿文本中的零宽字符
				let draftText = this.text.replace(/\u200B/g, '').trim();
				let quoteInfo = quotedMessage ? QuoteInfo.initWithMessage(quotedMessage) : null;
				
				if (!draftText && !quoteInfo) {
					Draft.clearConversationDraft(conversationInfo.conversation);
				} else {
					Draft.setConversationDraft(conversationInfo.conversation, draftText, quoteInfo);
				}
			},
			toggleUserCard() {
				const onPickUser = user => {
					console.log('选择名片', user);
					store.sendCardMessage(this.conversationInfo.conversation, user.uid);
				};
				let sharedContactState = store.state.contact;
				let users = sharedContactState.favContactList.concat(sharedContactState.friendList);
				users = users.filter((u) => {
					return u.uid !== Config.FILE_HELPER_ID;
				});

				this.$pickUser({
					users: users,
					showCategoryLabel: false,
					successCB: onPickUser,
				});
			},
			async toggleLocation() {
				// 在需要发送定位的地方调用
				try {
					await store.sendLocationMessage(this.conversationInfo.conversation);
				} catch (error) {
					console.error('发送定位消息失败:', error);
					// 处理错误
				}
			},
			toggleEmojiSearch() {
				this.fullScreenSearch = !this.fullScreenSearch;
				if (!this.fullScreenSearch) {
					this.clearSearch();
				} else {
					// 随机选择一个关键词加载推荐表情
					if (this.recommendKeywords.length > 0) {
						const randomIndex = Math.floor(Math.random() * this.recommendKeywords.length);
						const keyword = this.recommendKeywords[randomIndex];
						// 设置当前推荐关键词
						this.currentRecommendKeyword = keyword;
						// 获取热门表情但不设置搜索框关键词
						this.loadRecommendedEmojis(keyword);
					}
				}
			},
			clearSearch() {
				this.emojiSearchKeyword = '';
				this.searchResults = [];
				this.currentPage = 1;
				this.hasMore = true;
				this.currentRecommendKeyword = '';
			},
			
			// 使用推荐关键词进行搜索
			searchWithKeyword(keyword) {
				this.emojiSearchKeyword = keyword;
				this.currentPage = 1;
				this.searchEmoji();
			},
			
			async searchEmoji() {
				if (!this.emojiSearchKeyword.trim()) {
					this.searchResults = [];
					return;
				}

				try {
					const token = getItem('token');
					console.log('搜索表情请求参数:', {
						url: Config.APP_SERVER + '/img',
						token: token ? token.substring(0, 10) + '...' : 'null',
						pageNo: this.currentPage,
						pageSize: this.pageSize,
						keyword: this.emojiSearchKeyword
					});
					
					const response = await uni.request({
						url: Config.APP_SERVER + '/img', // 拼接基地址
						method: 'GET',
						header: {
							'content-type': 'application/json',
							'Authorization': 'Bearer ' + token
						},
						data: {
							pageNo: this.currentPage,
							pageSize: this.pageSize,
							keyword: this.emojiSearchKeyword
						}
					});
					
					console.log('搜索表情响应结果:', response);

					if (response.statusCode === 200 && response.data) {
						// 记录完整的响应数据结构
						console.log('响应数据结构:', JSON.stringify(response.data));
						
						// 尝试从不同位置获取结果数组
						let newResults = [];
						
						// 基于API实际返回的数据结构进行解析
						if (response.data.code === 200 && response.data.data && response.data.data.result) {
							// 标准API响应格式: {code: 200, msg: null, data: {pageNo, pageSize, total, result: []}}
							newResults = response.data.data.result;
							console.log('使用标准API响应格式');
						} else if (Array.isArray(response.data)) {
							// 如果响应直接是数组
							newResults = response.data;
							console.log('响应是数组格式');
						} else if (response.data.result && Array.isArray(response.data.result)) {
							// 如果响应包含result字段且是数组
							newResults = response.data.result;
							console.log('响应包含result字段');
						} else if (response.data.data && Array.isArray(response.data.data)) {
							// 如果响应包含data字段且是数组
							newResults = response.data.data;
							console.log('响应包含data字段');
						} else if (response.data.list && Array.isArray(response.data.list)) {
							// 如果响应包含list字段且是数组
							newResults = response.data.list;
							console.log('响应包含list字段');
						} else {
							// 尝试在响应中查找第一个是数组的字段
							for (const key in response.data) {
								if (Array.isArray(response.data[key])) {
									newResults = response.data[key];
									console.log('从响应中找到数组字段:', key);
									break;
								}
							}
						}
						
						console.log('解析后的结果数组:', newResults);

						if (this.currentPage === 1) {
							this.searchResults = newResults;
						} else {
							this.searchResults = [...this.searchResults, ...newResults];
						}

						this.hasMore = newResults.length === this.pageSize;
					} else {
						// 添加对非200状态码或无效数据的处理
						console.error('搜索表情失败: 无效响应', response);
						uni.showToast({
							title: '搜索失败: 无效响应',
							icon: 'none'
						});
						if (this.currentPage === 1) {
							this.searchResults = [];
						}
						this.hasMore = false;
					}
				} catch (error) {
					console.error('搜索表情失败:', error);
					uni.showToast({
						title: '搜索失败',
						icon: 'none'
					});
					// 网络错误等情况
					 if (this.currentPage === 1) {
						this.searchResults = [];
					}
					this.hasMore = false;
				}
			},
			onEmojiInput(event) {
				this.debouncedSearch(event.detail.value);
			},
			onClickSearchEmoji(item) {
				if (!item) return;

				// item本身就是URL字符串
				let stickerMsg = new StickerMessageContent('', item, 200, 200);
				wfc.sendConversationMessage(this.conversationInfo.conversation, stickerMsg);
				this.fullScreenSearch = false;
				this.showEmoji = false;
				this.clearSearch();
			},
			handlePaymentClose() {
				this.paymentVisible = false
				this.iosPaymentVisible = false
			},
			async handlePaymentConfirm({ status }) {
				// this.paymentStyle = method
				// this.paymentVisible = false
				try {
					if(status){
						console.log('支付确认', status)
						appServerApi.getWallet().then(response => {
							this.gold = response?.data?.gold || 0
							this.paymentVisible = false
							this.iosPaymentVisible = false
						})
					}
				} catch (error) {
					this.$message.error('支付失败')
				}
			},
			// 添加自动保存草稿的方法
			autosaveDraft() {
				if (this.draftSaveTimer) {
					clearTimeout(this.draftSaveTimer);
				}
				
				this.draftSaveTimer = setTimeout(() => {
					this.storeDraft(this.conversationInfo);
				}, 500);
			},
			// 添加滚动相关的方法
			onScroll(e) {
				this.oldScrollTop = e.detail.scrollTop;
			},
			onScrolltoupper() {
				// 滚动到顶部时的处理
			},
			onScrolltolower() {
				// 滚动到底部时的处理
			},
			// 检查表情搜索API是否可用
			async checkEmojiApiAvailable() {
				try {
					const token = getItem('token');
					console.log('检查表情API可用性，token:', token ? token.substring(0, 10) + '...' : 'null');
					
					const response = await uni.request({
						url: Config.APP_SERVER + '/img',
						method: 'GET',
						header: {
							'content-type': 'application/json',
							'Authorization': 'Bearer ' + token
						},
						data: {
							pageNo: 1,
							pageSize: 1,
							keyword: 'test'
						},
						timeout: 5000 // 设置5秒超时
					});
					
					// console.log('表情API检查结果:', response);
					
					// 检查API响应是否符合预期格式
					if (response.statusCode === 200 && 
						response.data && 
						response.data.code === 200 && 
						response.data.data && 
						Array.isArray(response.data.data.result)) {
						// console.log('表情搜索API可用，响应格式正确');
					} else if (response.statusCode === 200) {
						console.warn('表情搜索API响应格式不符合预期:', response.data);
						// API可达但格式不符合预期，仍然允许使用，代码会尝试适应
					} else {
						console.warn('表情搜索API返回非200状态码:', response.statusCode);
						// 静默处理，不显示toast提示
					}
				} catch (error) {
					console.error('表情搜索API不可用:', error);
					// 静默处理，不显示toast提示
				}
			},
			async loadRecommendedEmojis(keyword) {
				try {
					const token = getItem('token');
					
					const response = await uni.request({
						url: Config.APP_SERVER + '/img',
						method: 'GET',
						header: {
							'content-type': 'application/json',
							'Authorization': 'Bearer ' + token
						},
						data: {
							pageNo: keyword === this.currentRecommendKeyword ? this.currentPage + 1 : 1,
							pageSize: this.pageSize,
							keyword: keyword
						}
					});
					
					if (response.statusCode === 200 && response.data) {
						// 尝试从不同位置获取结果数组
						let recommendedResults = [];
						
						// 基于API实际返回的数据结构进行解析
						if (response.data.code === 200 && response.data.data && response.data.data.result) {
							recommendedResults = response.data.data.result;
						} else if (Array.isArray(response.data)) {
							recommendedResults = response.data;
						} else if (response.data.result && Array.isArray(response.data.result)) {
							recommendedResults = response.data.result;
						} else if (response.data.data && Array.isArray(response.data.data)) {
							recommendedResults = response.data.data;
						} else if (response.data.list && Array.isArray(response.data.list)) {
							recommendedResults = response.data.list;
						} else {
							for (const key in response.data) {
								if (Array.isArray(response.data[key])) {
									recommendedResults = response.data[key];
									break;
								}
							}
						}
						
						// 如果是同一个关键词的下一页，追加结果；否则重置结果
						if (keyword === this.currentRecommendKeyword && this.currentPage > 0) {
							this.searchResults = [...this.searchResults, ...recommendedResults];
							this.currentPage += 1;
						} else {
							this.searchResults = recommendedResults;
							this.currentPage = 1;
							this.currentRecommendKeyword = keyword;
						}
						
						this.hasMore = recommendedResults.length === this.pageSize;
					}
				} catch (error) {
					console.error('加载推荐表情失败:', error);
				}
			},
			
			// 专门用于加载更多推荐表情
			async loadMoreRecommendedEmojis() {
				if (!this.hasMore) return;
				
				try {
					const token = getItem('token');
					
					const response = await uni.request({
						url: Config.APP_SERVER + '/img',
						method: 'GET',
						header: {
							'content-type': 'application/json',
							'Authorization': 'Bearer ' + token
						},
						data: {
							pageNo: this.currentPage + 1,
							pageSize: this.pageSize,
							keyword: this.currentRecommendKeyword
						}
					});
					
					if (response.statusCode === 200 && response.data) {
						// 尝试从不同位置获取结果数组
						let recommendedResults = [];
						
						// 基于API实际返回的数据结构进行解析
						if (response.data.code === 200 && response.data.data && response.data.data.result) {
							recommendedResults = response.data.data.result;
						} else if (Array.isArray(response.data)) {
							recommendedResults = response.data;
						} else if (response.data.result && Array.isArray(response.data.result)) {
							recommendedResults = response.data.result;
						} else if (response.data.data && Array.isArray(response.data.data)) {
							recommendedResults = response.data.data;
						} else if (response.data.list && Array.isArray(response.data.list)) {
							recommendedResults = response.data.list;
						} else {
							for (const key in response.data) {
								if (Array.isArray(response.data[key])) {
									recommendedResults = response.data[key];
									break;
								}
							}
						}
						
						// 追加结果
						this.searchResults = [...this.searchResults, ...recommendedResults];
						this.currentPage += 1;
						this.hasMore = recommendedResults.length === this.pageSize;
					}
				} catch (error) {
					console.error('加载更多推荐表情失败:', error);
				}
			},
			onSearchScrollToLower() {
				// 处理滚动到底部的事件
				this.loadMoreRecommendedEmojis();
			},
			// 加载更多搜索结果
			async loadMoreResults() {
				if (!this.hasMore) return;
				
				this.currentPage++;
				await this.searchEmoji();
			},
			getQuotedSenderName() {
				if (!this.sharedConversationState.quotedMessage) {
					return '';
				}
				
				const message = this.sharedConversationState.quotedMessage;
				
				// 如果消息有_from属性，直接使用它的显示名称
				if (message._from) {
					// 优先使用备注名
					if (message._from.alias) {
						return message._from.alias;
					}
					// 其次使用好友备注
					if (message._from.friendAlias) {
						return message._from.friendAlias;
					}
					// 最后使用显示名
					if (message._from._displayName) {
						return message._from._displayName;
					}
				}
				
				// 如果没有_from属性，尝试通过from ID获取用户信息
				if (message.from) {
					const userInfo = wfc.getUserInfo(message.from);
					if (userInfo) {
						// 优先使用备注名
						if (userInfo.alias) {
							return userInfo.alias;
						}
						// 其次使用好友备注
						if (userInfo.friendAlias) {
							return userInfo.friendAlias;
						}
						// 最后使用显示名
						if (userInfo._displayName || userInfo.displayName) {
							return userInfo._displayName || userInfo.displayName;
						}
					}
				}
				
				// 如果是自己发送的消息
				if (message.from === wfc.getUserId()) {
					return '我';
				}
				
				// 如果都没有，返回默认值
				return '对方';
			},
			// 添加新方法：在引用消息时聚焦输入框
			focusInputOnQuote() {
				if (!this.shouldFocusOnQuote) return;
				
				// 确保其他面板都关闭
				this.showliwu = false;
				this.showEmoji = false;
				this.showExt = false;
				this.showVoice = false;
				this.showPtt = false;
				
				// 获取textarea元素
				const textarea = this.$refs.textarea;
				if (textarea) {
					// 先隐藏键盘，避免弹跳
					uni.hideKeyboard();
					
					// 延迟一帧后设置焦点并显示键盘
					setTimeout(() => {
						uni.showKeyboard();
						//textarea.focus();
						this.shouldFocusOnQuote = false;
					}, 100);
				}
			},
			deleteLastCharacter() {
				if (this.text.length === 0) return;
				
				try {
					let newText = this.text;
					
					// 优化删除逻辑：处理零宽字符和表情
					if (newText.length > 0) {
						const lastChar = newText.charAt(newText.length - 1);
						
						// 检查最后一个字符是否是零宽空格
						if (lastChar === '\u200B') {
							// 删除零宽空格
							newText = newText.slice(0, -1);
							
							// 继续检查是否需要删除表情
							if (newText.length > 0) {
								const prevChar = newText.charAt(newText.length - 1);
								const prevPrevChar = newText.length > 1 ? newText.charAt(newText.length - 2) : '';
								
								// 检查是否是表情的代理对
								if (prevChar.charCodeAt(0) >= 0xDC00 && prevChar.charCodeAt(0) <= 0xDFFF && 
									prevPrevChar.charCodeAt(0) >= 0xD800 && prevPrevChar.charCodeAt(0) <= 0xDBFF) {
									// 删除表情代理对
									newText = newText.slice(0, -2);
								} else {
									// 检查是否是单字符表情
									const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
									if (emojiRegex.test(prevChar)) {
										// 删除单字符表情
										newText = newText.slice(0, -1);
									}
								}
							}
						} else {
							// 正常删除逻辑
							const secondLastChar = newText.length > 1 ? newText.charAt(newText.length - 2) : '';
							
							// 处理代理对（surrogate pairs）- 用于表示某些emoji
							if (lastChar.charCodeAt(0) >= 0xDC00 && lastChar.charCodeAt(0) <= 0xDFFF && 
								secondLastChar.charCodeAt(0) >= 0xD800 && secondLastChar.charCodeAt(0) <= 0xDBFF) {
								// 删除代理对
								newText = newText.slice(0, -2);
							} else {
								// 删除单个字符
								newText = newText.slice(0, -1);
							}
						}
					}
					
					this.text = newText;
					
					// 自动保存草稿
					this.autosaveDraft();
					
					// 确保光标在最后并滚动到可见位置 - 添加延迟避免DOM查询错误
					setTimeout(() => {
						try {
							this.scrollToBottom();
						} catch (error) {
							console.warn('删除字符后更新UI时出错:', error);
						}
					}, 50);
				} catch (error) {
					console.error('deleteLastCharacter 执行出错:', error);
				}
			},
			
			// 添加insertText方法，用于重新编辑撤回的消息
			insertText(text) {
				if (text) {
					this.text = text;
					// 确保输入框获得焦点
					this.$nextTick(() => {
						// 关闭其他面板
						this.showliwu = false;
						this.showEmoji = false;
						this.showExt = false;
						this.showVoice = false;
						this.showPtt = false;
						
						// 设置焦点
						this.inputFocus = true;
						// 确保状态一致性
						this.updateInputFocus();
						
						// 滚动到底部确保文本可见
						this.scrollToBottom();
					});
				}
			},
			closeEmojiPanel() {
				this.showEmoji = false;
			}
		},
		computed: {
			// quotedMessage() {
			//     lastQuotedMessage = this.sharedConversationState.quotedMessage;
			//     return this.sharedConversationState.quotedMessage;
			// },
			confirmType() {
				try {
					const systemInfo = uni.getSystemInfoSync();
					return systemInfo.platform === 'ios' ? 'send' : '';
				} catch (error) {
					// 如果获取系统信息失败，默认不设置confirm-type
					console.warn('获取系统信息失败，使用默认confirm-type:', error);
					return '';
				}
			}
		},
		watch: {
			// 监听相关状态变化来更新inputFocus
			showExt(newVal) {
				this.updateInputFocus();
			},
			showEmoji(newVal) {
				this.updateInputFocus();
			},
			showVoice(newVal) {
				this.updateInputFocus();
			},
			isDraftRestored(newVal) {
				this.updateInputFocus();
			},
			shouldFocusOnQuote(newVal) {
				this.updateInputFocus();
			},
			'sharedConversationState.quotedMessage'(newVal) {
				this.updateInputFocus();
			}
		},
		onHide() {
			// 页面隐藏时保存草稿
			if (this.conversationInfo) {
				if (this.text.trim()) {
					this.storeDraft(this.conversationInfo);
				} else {
					Draft.clearConversationDraft(this.conversationInfo.conversation);
				}
			}
		},
		onUnload() {
			// 页面卸载时保存草稿
			if (this.conversationInfo) {
				if (this.text.trim()) {
					this.storeDraft(this.conversationInfo);
				} else {
					Draft.clearConversationDraft(this.conversationInfo.conversation);
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.rq-tca {
		position: fixed;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.70);
		z-index: 9999;

		.tc {
			width: 234px;
			height: 257px;
			border-radius: 20px;
			background: #FEF9F3;
			position: absolute;
			top: 50%;
			left: 50%;
			-webkit-transform: translate(-50%, -50%);
			-moz-transform: translate(-50%, -50%);
			-ms-transform: translate(-50%, -50%);
			-o-transform: translate(-50%, -50%);
			transform: translate(-50%, -50%);

			.tca {
				position: relative;
				width: 100%;
				height: 100%;

				.tca0 {
					width: 134px;
					height: 134px;
					position: absolute;
					left: 50px;
					top: -67px;
				}

				.tca1 {
					width: 100%;
					position: absolute;
					left: 0;
					top: 83px;
					text-align: center;
					color: #9F513A;
					font-family: MiSans;
					font-size: 16px;
				}

				.tca2 {
					width: 100%;
					position: absolute;
					left: 0;
					top: 124px;
					text-align: center;

					.tca2a {
						width: 141px;
						height: 45px;
						border-radius: 10px;
						border: 1px solid rgba(159, 81, 58, 0.20);
						background: #FFF;
						margin: 0 auto;
					}
				}

				.tca3 {
					width: 157px;
					height: 44px;
					line-height: 44px;
					position: absolute;
					left: 39px;
					top: 193px;
					text-align: center;
					border-radius: 100px;
					background: #FD3D58;
					color: #FFF;
					font-family: MiSans;
					font-size: 16px;
				}

				.tca4 {
					width: 20px;
					height: 20px;
					position: absolute;
					right: 10px;
					top: 10px;
					font-size: 28px;
				}

			}
		}

	}

	.rq-ds {
		position: fixed;
		width: 100%;
		height: 245px;
		left: 0;
		bottom: 100rpx;
		background-color: #ffffff;
		border-radius: 20px 20px 0px 0px;
		padding: 10px;

		.dsb {
			padding: 6px 10px;

			.dsba {
				display: flex;
				flex-direction: row;
				flex-wrap: wrap;

				.dsba1 {
					width: 25%;
					display: flex;
					align-items: center;
					flex-direction: column;
					margin-bottom: 10px;

					.dsba1a {
						width: 72px;
						height: 72px;
						border-radius: 10px 10px 0px 0px;
					}

					.dsba1d {
						width: 72px;
						height: 24px;
						text-align: center;
						line-height: 24px;
						color: #000;
						font-size: 12px;
						border-radius: 0px 0px 10px 10px;
					}

					.dsba1b {
						color: #ffffff;
						background-color: #386BF6;
					}

					.dsba1c {}
				}
			}
		}

		.dsa {
			height: 32px;
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.dsa2 {
				width: 116px;
				height: 32px;
				border-radius: 20px;
				padding: 6px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 14px;
				color: #000;
				background: #F5F5F5;

				.dsa2a {
					width: 26px;
					height: 26px;
				}

				.dsa2b {
					width: 44px;
					overflow: hidden;
					height: 20px;
					line-height: 20px;
				}

				.dsa2c {
					color: #386BF6;
					height: 20px;
					line-height: 20px;
				}
			}
		}
	}

	.rq_liwu {
		width: 38px;
		height: 38px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 0;

		.rq_liwuimg {
			width: 28px;
			height: 28px;
		}
	}

	.wf-message-input-container {
		width: 100%;
		position: relative;
		z-index: 2;
	}

	.input-area-container {
		flex: 1;
		display: flex;
		position: relative;
		max-width: calc(100% - 120px); /* 增加右侧预留空间 */
		overflow: hidden; /* 确保内容不会溢出 */
	}

	.input-area-wrapper {
		flex: 1;
		display: flex;
		flex-direction: column;
		width: 100%;
		align-items: center; /* 居中对齐子元素 */
	}

	.wf-ext-container {
		width: 100%;
		background-color: #f7f7f7;
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		align-items: center;
	}

	.wf-ext-item {
		padding: 35rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.wf-ext-item-icon {
		background-color: #fff;
		width: 110rpx;
		height: 110rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		border-radius: 12rpx;
	}

	.wf-ext-item-icon .wxfont {
		color: #181818;
		font-size: 64rpx;
	}

	.wf-ext-item-text {
		font-size: 24rpx;
		color: #666;
		margin-top: 8rpx;
	}

	.wf-message-input-toolbar {
		position: relative;
		z-index: 3;
		padding: 16rpx 6rpx;
		box-sizing: border-box;
		display: flex;
		width: 100%;
		flex-direction: row;
		align-items: flex-end;
		justify-content: space-between; /* 修改为space-between以确保发送按钮始终在右侧 */
		border: 1rpx #ddd solid;
		border-left: none;
		border-right: none;
		background: #ffffff;
	}

	.wf-input-button-icon {
		font-size: 38px;
		color: #333;
		height: 38px;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0; /* 防止图标被压缩 */
		min-width: 38px; /* 确保图标有最小宽度 */
	}

	.wf-input-text-send-button {
		white-space: nowrap;
		padding: 10rpx 24rpx;
		border-radius: 12rpx;
		border: 1rpx #ddd solid;
		background: #f7f7f7;
		color: #ddd;
		height: 38px;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0; /* 防止发送按钮被压缩 */
		min-width: 60px; /* 确保发送按钮有最小宽度 */
		margin-left: 4px; /* 从8px减小到4px */
	}

	.wf-input-textarea {
		flex: 1;
		margin: 0 5px;
		padding: 8px 12px;
		box-sizing: border-box !important;
		width: calc(100% - 10px);
		min-height: 16px;
		max-height: 200px;
		box-sizing: border-box;
		background: #EBEFEF;
		color: #222325;
		font-family: MiSans;
		font-size: 16px;
		font-style: normal;
		font-weight: 400;
		line-height: 1.5;
		border-radius: 5px;
		background: #F5F5F5;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;
		word-break: break-all;
		white-space: pre-wrap;
		scroll-behavior: smooth;
		position: relative;
	}

	.input-content {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		padding-bottom: 0;
	}

	.wf-input-textarea-inner {
		width: 100%;
		height: 100%;
		background: transparent;
		border: none;
		padding: 0;
		margin: 0;
		font-size: inherit;
		font-family: inherit;
		line-height: inherit;
		color: inherit;
		display: flex;
		align-items: center;
		min-height: 16px;
		padding-bottom: 0;
	}

	.quote-message-container {
		overflow: hidden;
		display: flex;
		align-items: center;
		position: relative;
		margin: 0;
		padding: 10px 16px;
		border-radius: 0;
		width: 100%;
		box-sizing: border-box;
		flex-shrink: 0;
		background-color: #F8F9FF;
		border-left: 4px solid #386BF6;
		border-bottom: 1px solid #E6EAF5;
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
	}

	.quote-message-container .quoted-message {
		max-width: calc(100% - 36px);
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		font-size: 14px;
		color: #666;
		padding: 3px 0;
		flex: 1;
		line-height: 1.4;
	}

	.quote-message-container .quoted-sender {
		color: #386BF6;
		font-weight: 600;
		margin-right: 6px;
	}

	.quote-message-container .cancel {
		position: absolute;
		right: 12px;
		top: 50%;
		transform: translateY(-50%);
		padding: 8px;
		color: #999;
		font-size: 16px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: rgba(248, 249, 255, 0.8);
		border-radius: 50%;
		width: 16px;
		height: 16px;
		transition: all 0.2s ease;
	}
	
	.quote-message-container .cancel:active {
		background-color: rgba(230, 234, 245, 0.8);
	}

	.wf-input-voice-container {
		box-sizing: border-box;
		margin: 0 5px;
		width: calc(100% - 10px);
		height: 38px;
		border-radius: 45px;
		display: flex;
		flex: 1;
		flex-direction: row;
		align-items: center;
		background: #EBEFEF;
	}

	.wf-input-voice-button {
		text-align: center;
		font-size: 24rpx;
		line-height: 75rpx;
		flex: 1;
	}

	.wf-input-voice-button:nth-child(1) {
		border-right: 1rpx #eee solid;
	}

	.wf-voice-recorder {
		width: 250rpx;
		height: 250rpx;
		left: 50%;
		transform: translateX(-50%);
		bottom: 680rpx;
		box-sizing: border-box;
		text-align: center;
		position: fixed;
		border-radius: 50%;
		background-color: #f8f8f8;
		box-shadow: 0rpx 4rpx 10rpx rgba(0, 0, 0, 0.05);
		padding: 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
	}

	.popsendCard {
		display: flex;
		background-color: #fff;
		overflow: auto;
	}

	.popsendCard-close {
		width: 100%;
		text-align: center;
		height: 70rpx;
		line-height: 70rpx;
		font-size: 42rpx;
		background-color: #fff;
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 9999;
	}

	.wf-emoji-container {}

	.wf-emoji-content {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
	}

	.emoji-item {
		font-size: 44rpx;
		width: 93rpx;
		height: 93rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
	}

	.wf-stickers-container {
		min-height: 60rpx;
		width: 100%;
		flex-direction: column;
		position: relative;
		z-index: 10;
	}

	.wf-stickers-container .category-container {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 8px 0;
		border-bottom: 1px solid #e6e6e6;
		background: #f7f7f7;
	}

	.wf-stickers-container .category {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.wf-stickers-container .category img {
		width: 40px;
		height: 40px;
		padding: 5px;
		margin: 0 5px;
		border-radius: 5px;
		object-fit: contain;
	}

	.wf-stickers-container .category img.active {
		background: lightgrey;
	}

	.wf-sticker-container {}

	.wf-sticker-content {
		height: 100%;
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		justify-content: space-around;
		align-content: flex-start;
		padding: 10px 0;
	}

	.sticker-item {
		height: 33%;
		aspect-ratio: 1/1;
		padding: 8rpx;
		border-radius: 5px;
	}

	.sticker-item:active {
		background: lightgrey;
	}

	.category-container {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 8px 0;
		border-bottom: 1px solid #e6e6e6;
		background: #f7f7f7;
	}

	.search-category {
		width: 40px;
		height: 40px;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-shrink: 0;

		.search-icon-img {
			width: 32px;
			height: 32px;
		}
	}

	.category-scroll {
		flex: 1;
		white-space: nowrap;
		overflow-x: auto;

		.category {
			display: inline-flex;
			justify-content: center;
			align-items: center;
			padding: 0 4px;

			img {
				width: 40px;
				height: 40px;
				padding: 5px;
				margin: 0 5px;
				border-radius: 5px;
				object-fit: contain;

				&.active {
					background: #e6e6e6;
				}
			}
		}
	}

	.emoji-search-panel {
		position: absolute;
		top: 60px;
		left: 0;
		right: 0;
		bottom: 0;
		background: #fff;
		z-index: 20;
		border-top: 1px solid #e6e6e6;
		height: calc(100% - 60px); /* 确保面板不会被底部元素遮挡 */
		overflow: hidden;
	}

	.emoji-search-header {
		display: flex;
		align-items: center;
		padding: 8px 12px;
		background: #fff;
		border-bottom: 1px solid #e6e6e6;
	}

	.search-input-wrap {
		flex: 1;
		display: flex;
		align-items: center;
		background: #f5f5f5;
		padding: 6px 12px;
		border-radius: 4px;
		margin-right: 8px;
	}

	.search-icon {
		font-size: 16px;
		color: #999;
		margin-right: 6px;
	}

	.search-input {
		flex: 1;
		height: 20px;
		border: none;
		outline: none;
		font-size: 14px;
		background: transparent;
	}

	.clear-icon {
		font-size: 16px;
		color: #999;
		padding: 4px;
	}

	.cancel-search {
		color: #386bf6;
		font-size: 14px;
		padding: 4px 0;
	}

	.search-results {
		background: #f5f5f5;
		padding: 10px;
		height: calc(100% - 60px);
		overflow-y: auto;
	}

	.search-results-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 10px;
		padding: 5px;
	}

	.search-result-item {
		aspect-ratio: 1;
		background: white;
		border-radius: 4px;
		overflow: hidden;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	}

	.emoji-image {
		width: 85%;
		height: 85%;
		object-fit: contain;
	}

	.loading-more {
		text-align: center;
		padding: 12px;
		color: #999;
		font-size: 12px;
	}

	.no-result {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
		color: #999;
		font-size: 13px;
	}

	.emoji-fullscreen-search {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background: #fff;
		z-index: 9999;
		border-top-left-radius: 10px;
		border-top-right-radius: 10px;
		box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);

		.emoji-search-header {
			display: flex;
			align-items: center;
			padding: 8px 12px;
			background: #fff;
			border-bottom: 1px solid #e6e6e6;
		}

		.search-input-wrap {
			flex: 1;
			display: flex;
			align-items: center;
			background: #f5f5f5;
			padding: 8px 12px;
			border-radius: 20px;
			margin-right: 8px;
		}

		.search-icon {
			font-size: 16px;
			color: #999;
			margin-right: 6px;
		}

		.search-input {
			flex: 1;
			height: 20px;
			border: none;
			outline: none;
			font-size: 14px;
			background: transparent;
		}

		.clear-icon {
			font-size: 16px;
			color: #999;
			padding: 4px;
		}

		.cancel-search {
			color: #386bf6;
			font-size: 16px;
			padding: 4px 0;
		}

		.emoji-search-content {
			height: calc(100% - 60px);
			background: #fff;
			overflow: auto;
		}

		.emoji-search-suggestions {
			padding: 10px;
		}

		.search-section {
			margin-bottom: 15px;
		}

		.search-section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 5px;
			margin-bottom: 8px;
			
			.section-title {
				font-size: 14px;
				font-weight: 500;
				color: #333;
			}
		}

		.search-tags {
			display: flex;
			flex-wrap: wrap;
		}

		.search-tag {
			padding: 6px 12px;
			margin: 4px;
			background: #f5f5f5;
			border-radius: 20px;
			font-size: 13px;
			color: #333;
		}
		
		.search-results-grid {
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			gap: 8px;
			padding: 8px;
		}

		.search-result-item {
			aspect-ratio: 1;
			background: white;
			border-radius: 8px;
			overflow: hidden;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
		}

		.emoji-image {
			width: 85%;
			height: 85%;
			object-fit: contain;
		}

		.loading-more {
			text-align: center;
			padding: 10px;
			color: #999;
			font-size: 13px;
			background: #f9f9f9;
		}

		.no-result {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			height: 120px;
			color: #999;
			font-size: 13px;
			
			text {
				margin-top: 10px;
			}
		}
	}

	.rq-ds-mask {
		position: fixed;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		width: 100vw;
		height: 100vh;
		background: transparent;
		z-index: 999;
		display: flex;
		align-items: flex-end;
		justify-content: center;
	}

	.emoji-delete-button {
		position: absolute;
		bottom: 15px;
		right: 15px;
		background-color: rgba(255, 255, 255, 0.95);
		border-radius: 8px;
		width: 56px;
		height: 56px;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
		border: 1px solid rgba(0, 0, 0, 0.1);
		z-index: 100;
		transition: all 0.2s ease;
		
		&:active {
			transform: scale(0.95);
			background-color: rgba(240, 240, 240, 0.95);
		}
		
		.delete-icon {
			font-size: 24px;
			color: #666;
			font-weight: bold;
			user-select: none;
		}
	}

	.emoji-delete-button.fullscreen-delete {
		position: absolute;
		top: 15px;
		right: 15px;
		background-color: rgba(255, 255, 255, 0.95);
		border-radius: 8px;
		width: 56px;
		height: 46px;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
		border: 1px solid rgba(0, 0, 0, 0.1);
		z-index: 100;
		transition: all 0.2s ease;
		
		&:active {
			transform: scale(0.95);
			background-color: rgba(240, 240, 240, 0.95);
		}
		
		.delete-icon {
			font-size: 24px;
			color: #666;
			font-weight: bold;
			user-select: none;
		}
	}

	.emoji-close-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		/* bottom值现在通过内联样式动态设置 */
		background: transparent;
		z-index: 9;
	}
</style>