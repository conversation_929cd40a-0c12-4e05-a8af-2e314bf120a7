<template>
    <div class="group-attribute">
        <!-- 顶部导航栏 -->
        <CustomHeader 
            title="群属性" 
            :showBack="true"
            :titleStyle="{ textAlign: 'center', width: '100%' }"
            @back="onBack"
        ></CustomHeader>

        <div class="content">
            <!-- 公开设置开关 -->
            <div class="cell-group">
                <div class="cell">
                    <div class="cell-title">社群公开</div>
                    <div class="cell-value">
                        <div class="switch" :class="{ active: isPublic }" @click="isPublic = !isPublic">
                            <div class="switch-button"></div>
                        </div>
                    </div>
                </div>

                <!-- 收费规则开关 -->
                <div class="cell">
                    <div class="cell-content">
                        <div class="cell-title">社群收费规则</div>
                        <div class="cell-label">注：默认关闭，即免费群</div>
                    </div>
                    <div class="cell-value">
                        <div class="switch" :class="{ active: hasFee }" @click="toggleHasFee">
                            <div class="switch-button"></div>
                        </div>
                    </div>
                </div>

                <!-- 仅在开启收费规则时显示以下内容 -->
                <template v-if="hasFee">
                    <!-- 加入费用输入 -->
                    <div class="cell">
                        <div class="cell-title">设置加入费用</div>
                        <div class="cell-value">
                            <div class="input-container">
                                <input type="number" v-model="joinFee" placeholder="1-6000的数" class="input" @input="validateFee">
                                <span class="currency-symbol">¥</span>
                            </div>
                        </div>
                    </div>

                    <!-- 有效期选择 -->
                    <div class="cell clickable" @click="showPeriodPicker = true">
                        <div class="cell-content">
                            <div class="cell-title">选择有效期类型</div>
                            <div class="cell-label warning">成员截止日期：{{ validityPeriod }}</div>
                        </div>
                        <div class="cell-value">
                            {{ validityPeriod }}
                            <text class="icon-ion-ios-arrow-right"></text>
                        </div>
                    </div>
                </template>
            </div>

            <!-- 协议确认 -->
            <div class="agreement">
                <div class="checkbox" :class="{ checked: agreementChecked }" @click="agreementChecked = !agreementChecked">
                    <!-- <i  class="ion-ios-checkmark-empty"></i> -->
                    <text style="color: #fff;  font-size: 35rpx;" v-if="agreementChecked" class="icon-ion-ios-checkmark-empty"></text>
                </div>
                <span>我已阅读并同意</span>
                <span class="link" @click="showAgreement">《社群管理规则》</span>
            </div>

            <!-- 完成按钮 -->
            <div class="submit">
                <button class="submit-btn" :class="{ disabled: !isValid }" :disabled="!isValid" @click="onSubmit">
                    完成
                </button>
            </div>
        </div>

        <!-- 有效期选择弹窗 -->
        <div v-if="showPeriodPicker" class="popup-mask" @click="showPeriodPicker = false">
            <div class="popup-content" @click.stop>
                <div class="picker">
                    <div class="picker-header">
                        <span class="cancel" @click="showPeriodPicker = false">取消</span>
                        <span class="confirm" @click="confirmPeriod">确定</span>
                    </div>
                    <div class="picker-body">
                        <div v-for="(item, index) in periodColumns" :key="index" class="picker-item" :class="{ active: selectedPeriod === item }" @click="selectedPeriod = item">
                            {{ item }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import appServerApi from '@/api/appServerApi'
import CustomHeader from '@/components/custom-header'
export default {
    name: 'GroupAttribute',
    components: {
        CustomHeader
    },

    data() {
        return {
            isPublic: true,
            hasFee: false,
            joinFee: '',
            groupId: '',
            validityPeriod: '一年',
            validityDays: 365,
            agreementChecked: false,
            showPeriodPicker: false,
            selectedPeriod: '',
            periodColumns: [ '一个月', '三个月', '半年', '一年'],
            // periodColumns: ['永久有效'],
            price: '',
            duration: 0,
        }
    },

    computed: {
        isValid() {
            if (this.hasFee) {
                // 当开启收费时，验证费用输入
                if (!this.joinFee || parseFloat(this.joinFee) < 1 || parseFloat(this.joinFee) > 6000) {
                    return false
                }
                // 验证有效期选择
                if (this.validityPeriod === '请选择') {
                    return false
                }
            }
            return this.agreementChecked
        },
    },
    onLoad(option) {
        console.log(option)
        this.groupId = option.groupId
        this.price = option.price
        this.duration = option.duration
        if (this.price == 0) {
            this.hasFee = false
        } else {
            this.hasFee = true            
            this.joinFee = this.price
            this.validityDays = this.duration

            // 根据 duration 设置对应的有效期类型
            switch (parseInt(this.duration, 10)) {
                case 30:
                    this.validityPeriod = '一个月'
                    this.selectedPeriod = '一个月'
                    break
                case 90:
                    this.validityPeriod = '三个月'
                    this.selectedPeriod = '三个月'
                    break
                case 180:
                    this.validityPeriod = '半年'
                    this.selectedPeriod = '半年'
                    break
                case 365:
                    this.validityPeriod = '一年'
                    this.selectedPeriod = '一年'
                    break
                default:
                    this.validityPeriod = '一个月'
                    this.selectedPeriod = '一个月'
            }
        }
    },

    methods: {
        onBack() {
            this.$router.back()
        },

        toggleHasFee() {
            this.hasFee = !this.hasFee
            if (!this.hasFee) {
                // 关闭收费时重置相关字段
                this.joinFee = ''
                this.validityPeriod = '一年'
                this.selectedPeriod = '一年'
                this.validityDays = 365
            }
        },

        validateFee() {
            let value = this.joinFee

            // 如果输入为空，不做处理
            if (value === '') return

            // 转换为数字进行比较
            let numValue = parseFloat(value)

            // 处理非法输入
            if (isNaN(numValue)) {
                this.joinFee = '1'
                return
            }

            // 处理超出范围的情况
            if (numValue > 6000) {
                this.joinFee = '6000'
            } else if (numValue < 1) {
                this.joinFee = '1'
            }
        },

        confirmPeriod() {
            if (this.selectedPeriod) {
                this.validityPeriod = this.selectedPeriod
                // 根据选择设置天数
                switch (this.selectedPeriod) {
                    case '一个月':
                        this.validityDays = 30
                        break
                    case '三个月':
                        this.validityDays = 90
                        break
                    case '半年':
                        this.validityDays = 180
                        break
                    case '一年':
                        this.validityDays = 365
                        break
                    default:
                        this.validityDays = 0 
                }
                this.showPeriodPicker = false
            }
        },

        showAgreement() {
            // 显示社群管理规则
        },

        async onSubmit() {
            try {
                // TODO: 提交数据到后端
                // this.$router.back()
                var p = 0
                var d = 0
                if (this.hasFee) {      //是否开启收费
                    p = this.joinFee
                    d = this.validityDays
                } else {
                    p = 0
                    d = 0
                }
                const response = await appServerApi.setGroupPrice({      //给后端发送请求
                    gid: this.groupId,     //群组id
                    price: p,
                    duration: parseInt(d, 10),           //有效期时长（天数）
                })
                console.log('response', response)
                uni.navigateBack()
                // (`/wallet/charge`, {}, false, false, "form")
                /*       if (response.data.code == 200) {
                    console.log('成功')
                } else {
                    uni.showToast({
                        title: '设置失败',
                        icon: 'none',
                    })
                } */
            } catch (error) {
                console.error(error)
            }
        },
    },
}
</script>

<style lang="scss" scoped>
.group-attribute {
    min-height: 100vh;
    background: #f7f8fa;
    position: relative;

    .content {
        padding: 12px;
        padding-top: 10px; // 减少顶部间距，只保留导航栏高度
    }

    .cell-group {
        background: #fff;
        border-radius: 8px;
        margin-bottom: 12px;
    }

    .cell {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        border-bottom: 1px solid #f5f5f5;

        &.clickable {
            cursor: pointer;
        }

        &:last-child {
            border-bottom: none;
        }

        .cell-content {
            flex: 1;
        }

        .cell-title {
            font-size: 14px;
            color: #333;
        }

        .cell-label {
            font-size: 12px;
            color: #999;
            margin-top: 4px;

            &.warning {
                color: #ee0a24;
            }
        }

        .cell-value {
            display: flex;
            align-items: center;
        }
    }

    .switch {
        width: 44px;
        height: 24px;
        background: #dcdee0;
        border-radius: 12px;
        position: relative;
        transition: all 0.3s;
        cursor: pointer;

        &.active {
            background: #1989fa;

            .switch-button {
                transform: translateX(20px);
            }
        }

        .switch-button {
            width: 20px;
            height: 20px;
            background: #fff;
            border-radius: 50%;
            position: absolute;
            left: 2px;
            top: 2px;
            transition: all 0.3s;
        }
    }

    .input-container {
        position: relative;
        width: 120px;

        .currency-symbol {
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 14px;
            pointer-events: none;
        }
    }

    .input {
        width: 100%;
        height: 32px;
        border: 1px solid #dcdee0;
        border-radius: 4px;
        padding: 0 8px 0 20px;
        font-size: 14px;

        &:focus {
            border-color: #1989fa;
            outline: none;
        }
    }

    .agreement {
        margin: 16px;
        display: flex;
        align-items: center;
        font-size: 13px;

        .checkbox {
            width: 20px;
            height: 20px;
            border: 1px solid #dcdee0;
            border-radius: 50%;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            &.checked {
                background: #1989fa;
                border-color: #1989fa;
            }

            i {
                color: #fff;
                font-size: 14px;
            }
        }

        .link {
            color: #1989fa;
            cursor: pointer;
        }
    }

    .submit {
        margin: 16px;

        .submit-btn {
            width: 100%;
            height: 44px;
            background: #1989fa;
            border-radius: 10px;
            color: #fff;
            font-size: 16px;
            border: none;
            cursor: pointer;

            &.disabled {
                background: #dcdee0;
                cursor: not-allowed;
            }
        }
    }

    .popup-mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 100;

        .popup-content {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            background: #fff;
            border-radius: 16px 16px 0 0;
        }
    }

    .picker {
        .picker-header {
            display: flex;
            justify-content: space-between;
            padding: 12px 16px;
            border-bottom: 1px solid #f5f5f5;

            span {
                font-size: 14px;
                cursor: pointer;

                &.confirm {
                    color: #1989fa;
                }
            }
        }

        .picker-body {
            padding: 12px 0;
            max-height: 300px;
            overflow-y: auto;

            .picker-item {
                padding: 12px 16px;
                font-size: 14px;
                cursor: pointer;

                &.active {
                    color: #1989fa;
                }

                &:active {
                    background: #f5f5f5;
                }
            }
        }
    }
}
</style>
