<template>
	<view class="file-preview-container">
		<!-- 头部区域（状态栏 + 导航栏） -->
		<view class="header-container">
			<!-- 状态栏占位 -->
			<view class="status-bar-placeholder" :style="{height: statusBarHeight + 'px'}"></view>
			
			<!-- 导航栏 -->
			<view class="nav-bar">
				<view class="nav-left" @click="goBack">
					<text class="back-icon">‹</text>
				</view>
				<view class="nav-center">
					<text class="nav-title">文件预览</text>
				</view>
				<view class="nav-right" @click="showMoreOptions">
					<text class="more-icon">⋯</text>
				</view>
			</view>
		</view>
		
		<!-- 文件内容区域 -->
		<view class="file-content">
			<!-- 文件图标 -->
			<view class="file-icon-container">
				<image :src="fileIcon" class="file-icon" mode="aspectFit"></image>
			</view>
			
			<!-- 文件名 -->
			<view class="file-name-container">
				<text class="file-name">{{ fileName }}</text>
			</view>
			
			<!-- 下载状态区域 -->
			<view class="download-status" v-if="!isDownloaded">
				<text class="download-text">{{ downloadStatusText }}</text>
				
				<!-- 下载进度圆形指示器 -->
				<view class="progress-container" v-if="isDownloading">
					<view class="progress-circle">
						<view class="progress-bar" :style="progressStyle"></view>
						<view class="pause-button" @click="pauseDownload" v-if="!isPaused">
							<view class="pause-icon"></view>
							<view class="pause-icon"></view>
						</view>
						<view class="play-button" @click="resumeDownload" v-else>
							<view class="play-icon"></view>
						</view>
					</view>
				</view>
				
				<!-- 重试按钮 -->
				<view class="retry-button" @click="retryDownload" v-if="downloadFailed">
					<text class="retry-text">重试</text>
				</view>
			</view>
			
			<!-- 下载完成状态 -->
			<view class="download-complete" v-if="isDownloaded">
				<view class="open-button" @click="openFile">
					<text class="open-text">打开</text>
				</view>
			</view>
		</view>
		
		<!-- 更多选项弹窗 -->
		<view v-if="showOptions" class="options-overlay" @click="hideOptions">
			<view class="options-container" @click.stop>
				<view class="option-item" @click="forwardFile">
					<text class="option-text">转发</text>
				</view>
				<view class="option-item" @click="saveToLocal">
					<text class="option-text">保存到手机</text>
				</view>
				<view class="option-item cancel" @click="hideOptions">
					<text class="option-text">取消</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Message from "@/wfc/messages/message";
import helper from "@/pages/util/helper";
import store from "@/store";
import ForwardType from "@/pages/conversation/message/forward/ForwardType";
import wfc from "@/wfc/client/wfc";

export default {
	name: "FileReceivePreviewPage",
	data() {
		return {
			message: null,
			fileName: '',
			fileIcon: '',
			fileUrl: '',
			isDownloading: false,
			isDownloaded: false,
			downloadProgress: 0,
			showOptions: false,
			statusBarHeight: 0,
			localFilePath: '',
			messageId: null,
			downloadTask: null,
			isPaused: false,
			downloadFailed: false,
			fileHash: '',
			cachedFilePath: ''
		};
	},
	
	computed: {
		downloadStatusText() {
			if (this.downloadFailed) {
				return '下载失败';
			}
			if (this.isDownloading) {
				if (this.isPaused) {
					return '下载已暂停';
				}
				return '正在接收文件';
			}
			if (this.isDownloaded) {
				return '下载完成';
			}
			return '等待下载';
		},
		
		progressStyle() {
			const percentage = this.downloadProgress;
			return {
				background: `conic-gradient(#07C160 0% ${percentage}%, #E5E5E5 ${percentage}% 100%)`
			};
		}
	},
	
	onLoad(options) {
		// 获取系统状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;
		
		// 清理过期缓存（可选）
		this.cleanExpiredCache();
		
		console.log('FileReceivePreviewPage 接收到的参数:', options);
		
		// 从参数中获取文件信息
		if (options.fileName && options.fileUrl) {
			this.fileName = decodeURIComponent(options.fileName);
			this.fileUrl = decodeURIComponent(options.fileUrl);
			
			if (options.messageId) {
				this.messageId = options.messageId;
				// 尝试从store获取完整的消息对象
				try {
					console.log('[FileReceivePreviewPage] 尝试获取消息，messageId:', options.messageId, '类型:', typeof options.messageId);
					const message = store.getMessageById && store.getMessageById(options.messageId);
					if (message && message.messageContent) {
						console.log('[FileReceivePreviewPage] 成功获取到消息对象:', message);
						this.message = message;
						this.fileName = message.messageContent.name;
					} else {
						console.log('[FileReceivePreviewPage] 未获取到消息对象，使用参数中的文件信息');
					}
				} catch (error) {
					console.warn('从store获取消息失败，使用参数中的信息:', error);
				}
			}
			
			this.initFileInfo();
		} else {
			console.error('缺少必要的文件信息参数:', options);
			uni.showToast({
				title: '缺少文件信息',
				icon: 'none'
			});
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		}
	},
	
	onUnload() {
		// 页面卸载时取消下载任务
		if (this.downloadTask) {
			this.downloadTask.abort();
		}
	},
	
	methods: {
		goBack() {
			uni.navigateBack();
		},
		
		showMoreOptions() {
			this.showOptions = true;
		},
		
		hideOptions() {
			this.showOptions = false;
		},
		
		initFileInfo() {
			// 获取文件图标
			const fileExtension = this.fileName.substring(this.fileName.lastIndexOf('.') + 1).toLowerCase();
			this.fileIcon = this.getFileIcon(fileExtension);
			
			// 生成文件唯一标识（基于URL和文件名）
			this.fileHash = this.generateFileHash(this.fileUrl, this.fileName);
			
			// 检查文件是否已下载或正在下载
			this.checkFileStatus();
			
			// 只有在文件未下载且未在下载中时才自动开始下载
			if (!this.isDownloaded && !this.isDownloading && this.fileUrl) {
				setTimeout(() => {
					this.startDownload();
				}, 500);
			}
		},
		
		getFileIcon(extension) {
			try {
				const icon = helper.getFiletypeIcon(extension);
				return "/static/image/filetypes/" + icon;
			} catch (error) {
				console.warn('获取文件图标失败，使用默认图标', error);
				return "/static/image/filetypes/default.png";
			}
		},
		
		generateFileHash(url, fileName) {
			// 生成简单的文件哈希（基于URL和文件名）
			const str = url + '_' + fileName;
			let hash = 0;
			if (str.length === 0) return hash.toString();
			for (let i = 0; i < str.length; i++) {
				const char = str.charCodeAt(i);
				hash = ((hash << 5) - hash) + char;
				hash = hash & hash; // 转换为32位整数
			}
			return Math.abs(hash).toString();
		},
		
		checkFileStatus() {
			// 1. 检查本地缓存
			this.checkLocalFileCache();
			
			// 2. 检查是否正在下载
			if (this.message && this.message.messageId) {
				this.isDownloading = store.isDownloadingMessage(this.message.messageId);
				if (this.isDownloading) {
					const downloadStatus = store.getDownloadingMessageStatus(this.message.messageId);
					if (downloadStatus && downloadStatus.totalBytes > 0) {
						this.downloadProgress = Math.round((downloadStatus.receivedBytes / downloadStatus.totalBytes) * 100);
					}
				}
			}
		},
		
		checkLocalFileCache() {
			try {
				// 从本地存储获取缓存的文件路径
				const cachedFiles = uni.getStorageSync('cachedFiles') || {};
				const cachedInfo = cachedFiles[this.fileHash];
				
				console.log('检查文件缓存，哈希值:', this.fileHash);
				console.log('缓存信息:', cachedInfo);
				
				if (cachedInfo && cachedInfo.filePath) {
					console.log('找到缓存记录，验证文件是否存在...');
					// 验证缓存文件是否仍然存在
					this.verifyCachedFile(cachedInfo.filePath);
				} else {
					console.log('未找到缓存记录');
				}
			} catch (error) {
				console.warn('检查本地文件缓存失败:', error);
			}
		},
		
		verifyCachedFile(filePath) {
			console.log('验证缓存文件:', filePath);
			// 在实际项目中，这里应该验证文件是否存在
			// uni-app 中可以使用 uni.getFileInfo 来检查文件
			uni.getFileInfo({
				filePath: filePath,
				success: (res) => {
					console.log('找到缓存文件，路径:', filePath);
					this.localFilePath = filePath;
					this.cachedFilePath = filePath;
					this.isDownloaded = true;
					this.downloadProgress = 100;
				},
				fail: (err) => {
					console.log('缓存文件不存在或已失效，错误:', err, '路径:', filePath);
					// 缓存文件不存在，清理缓存记录
					this.clearFileCache(this.fileHash);
				}
			});
		},
		
		saveFileToCache(filePath) {
			try {
				// 保存文件路径到本地缓存
				const cachedFiles = uni.getStorageSync('cachedFiles') || {};
				cachedFiles[this.fileHash] = {
					filePath: filePath,
					fileName: this.fileName,
					fileUrl: this.fileUrl,
					cacheTime: Date.now()
				};
				uni.setStorageSync('cachedFiles', cachedFiles);
				console.log('文件已缓存:', this.fileHash);
			} catch (error) {
				console.warn('保存文件缓存失败:', error);
			}
		},
		
		clearFileCache(fileHash) {
			try {
				const cachedFiles = uni.getStorageSync('cachedFiles') || {};
				delete cachedFiles[fileHash];
				uni.setStorageSync('cachedFiles', cachedFiles);
			} catch (error) {
				console.warn('清理文件缓存失败:', error);
			}
		},
		
		startDownload() {
			// 防止重复下载
			if (this.isDownloading || this.isDownloaded) {
				console.log('文件已在下载中或已下载完成，跳过下载');
				return;
			}
			
			// 验证文件URL
			if (!this.fileUrl) {
				console.warn('无效的文件URL:', this.fileUrl);
				uni.showToast({
					title: '文件链接无效',
					icon: 'none'
				});
				return;
			}
			
			console.log('开始下载文件:', this.fileName);
			this.isDownloading = true;
			this.downloadProgress = 0;
			this.downloadFailed = false;
			this.isPaused = false;
			
			// 如果有messageId，添加到下载队列
			if (this.message && this.message.messageId) {
				store.addDownloadingMessage(this.message.messageId);
			}
			
			// 开始下载到临时路径
			this.downloadTask = uni.downloadFile({
				url: this.fileUrl,
				success: (res) => {
					console.log('文件下载响应', res);
					
					if (res.statusCode === 200 && res.tempFilePath) {
						// 下载成功后，将文件移动到持久化目录
						this.saveFileToSavedFiles(res.tempFilePath);
					} else {
						this.handleDownloadError('下载失败');
					}
				},
				fail: (err) => {
					console.error('文件下载失败', err);
					let errorMsg = '下载失败，请重试';
					if (err.errMsg) {
						if (err.errMsg.includes('url not in domain list')) {
							errorMsg = '文件链接不在域名白名单中';
						} else if (err.errMsg.includes('timeout')) {
							errorMsg = '下载超时，请重试';
						}
					}
					this.handleDownloadError(errorMsg);
				}
			});
			
			// 监听下载进度
			if (this.downloadTask.onProgressUpdate) {
				this.downloadTask.onProgressUpdate((res) => {
					this.downloadProgress = res.progress || 0;
					
					// 更新store中的下载进度
					if (this.message && this.message.messageId) {
						const downloadStatus = store.getDownloadingMessageStatus(this.message.messageId);
						if (downloadStatus) {
							downloadStatus.receivedBytes = res.totalBytesWritten || 0;
							downloadStatus.totalBytes = res.totalBytesExpectedToWrite || 1;
						}
					}
				});
			}
		},
		
		pauseDownload() {
			if (this.downloadTask && this.isDownloading) {
				this.downloadTask.abort();
				this.isPaused = true;
				this.isDownloading = false;
			}
		},
		
		resumeDownload() {
			if (this.isPaused && !this.isDownloaded) {
				this.isPaused = false;
				this.startDownload();
			}
		},
		
		retryDownload() {
			this.downloadFailed = false;
			this.startDownload();
		},
		
		handleDownloadError(message) {
			this.isDownloading = false;
			this.downloadFailed = true;
			this.removeFromDownloadQueue();
			
			uni.showToast({
				title: message,
				icon: 'none'
			});
		},
		
		removeFromDownloadQueue() {
			if (this.message && this.message.messageId && store.state && store.state.conversation) {
				// 从下载队列移除
				const downloadingMessages = store.state.conversation.downloadingMessages;
				const index = downloadingMessages.findIndex(dm => dm.messageId === this.message.messageId);
				if (index >= 0) {
					downloadingMessages.splice(index, 1);
				}
			}
		},
		
		openFile() {
			if (this.localFilePath) {
				console.log('准备打开文件:', this.localFilePath);
				uni.openDocument({
					filePath: this.localFilePath,
					showMenu: true,
					success: (res) => {
						console.log('打开文档成功:', res);
					},
					fail: (res) => {
						console.log('打开文档失败:', res);
						// 如果打开失败，可能是文件路径问题，尝试重新下载
						if (res.errMsg && res.errMsg.includes('文件不存在')) {
							uni.showModal({
								title: '提示',
								content: '文件可能已失效，是否重新下载？',
								success: (modalRes) => {
									if (modalRes.confirm) {
										// 清理缓存并重新下载
										this.clearFileCache(this.fileHash);
										this.isDownloaded = false;
										this.localFilePath = '';
										this.startDownload();
									}
								}
							});
						} else {
							uni.showToast({
								title: '无法打开此文件',
								icon: 'none',
							});
						}
					}
				});
			} else {
				uni.showToast({
					title: '文件尚未下载完成',
					icon: 'none',
				});
			}
		},
		
		forwardFile() {
			this.hideOptions();
			console.log('[FileReceivePreviewPage] 尝试转发文件');
			console.log('[FileReceivePreviewPage] message对象:', this.message);
			console.log('[FileReceivePreviewPage] messageId:', this.messageId, '类型:', typeof this.messageId);
			
			if (this.message) {
				// 使用现有的转发系统
				console.log('[FileReceivePreviewPage] 使用完整message对象转发');
				this.$forward({
					forwardType: ForwardType.NORMAL,
					messages: [this.message],
				});
			} else if (this.messageId) {
				// 尝试使用wfc客户端获取消息
				console.log('[FileReceivePreviewPage] 尝试使用wfc客户端获取消息:', this.messageId);
				try {
					// 尝试数字类型的messageId
					const numericMessageId = Number(this.messageId);
					console.log('[FileReceivePreviewPage] 转换后的数字messageId:', numericMessageId);
					
					let message = wfc.getMessageById(this.messageId);
					if (!message && numericMessageId !== this.messageId) {
						console.log('[FileReceivePreviewPage] 字符串ID未找到，尝试数字ID');
						message = wfc.getMessageById(numericMessageId);
					}
					
					if (message) {
						console.log('[FileReceivePreviewPage] 通过wfc客户端找到消息，开始转发:', message);
						this.$forward({
							forwardType: ForwardType.NORMAL,
							messages: [message],
						});
					} else {
						console.log('[FileReceivePreviewPage] wfc客户端也无法找到消息');
						this.showForwardError();
					}
				} catch (error) {
					console.error('[FileReceivePreviewPage] 使用wfc客户端获取消息失败:', error);
					this.showForwardError();
				}
			} else {
				console.log('[FileReceivePreviewPage] 没有messageId，无法转发');
				this.showForwardError();
			}
		},
		
		showForwardError() {
			uni.showToast({
				title: '无法转发此文件，请从聊天页面尝试转发',
				icon: 'none',
				duration: 2000
			});
		},
		
		saveToLocal() {
			this.hideOptions();
			if (this.localFilePath) {
				// 获取文件扩展名
				const fileExtension = this.fileName.substring(this.fileName.lastIndexOf('.') + 1).toLowerCase();
				const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
				
				if (imageExtensions.includes(fileExtension)) {
					// 保存图片到相册
					this.saveImageToPhotosAlbum();
				} else {
					// 保存其他文件到本地
					this.saveFileToLocal();
				}
			} else {
				uni.showToast({
					title: '请先下载文件',
					icon: 'none'
				});
			}
		},
		
		saveImageToPhotosAlbum() {
			uni.saveImageToPhotosAlbum({
				filePath: this.localFilePath,
				success: () => {
					uni.showToast({
						title: '已保存到相册',
						icon: 'success'
					});
				},
				fail: (err) => {
					console.error('保存图片到相册失败:', err);
					if (err.errMsg && err.errMsg.includes('auth deny')) {
						uni.showModal({
							title: '权限不足',
							content: '需要相册权限才能保存图片，请在设置中开启权限',
							showCancel: false
						});
					} else {
						uni.showToast({
							title: '保存失败',
							icon: 'none'
						});
					}
				}
			});
		},
		
		saveFileToLocal() {
			// 对于非图片文件，我们可以尝试复制到用户可访问的目录
			// 或者提示用户文件已下载到应用目录
			uni.showModal({
				title: '文件已下载',
				content: `文件"${this.fileName}"已下载到应用目录，您可以通过"打开"按钮查看文件`,
				showCancel: false,
				confirmText: '知道了'
			});
		},
		
		checkIfFileExists() {
			// 保留原方法用于兼容性，但现在由checkFileStatus替代
			this.checkFileStatus();
		},
		
		// 清理过期缓存（可选：在应用启动时调用）
		cleanExpiredCache() {
			try {
				const cachedFiles = uni.getStorageSync('cachedFiles') || {};
				const now = Date.now();
				const CACHE_EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000; // 7天
				
				Object.keys(cachedFiles).forEach(hash => {
					const cacheInfo = cachedFiles[hash];
					if (now - cacheInfo.cacheTime > CACHE_EXPIRE_TIME) {
						delete cachedFiles[hash];
					}
				});
				
				uni.setStorageSync('cachedFiles', cachedFiles);
			} catch (error) {
				console.warn('清理过期缓存失败:', error);
			}
		},
		
		saveFileToSavedFiles(tempFilePath) {
			// 生成持久化文件名
			const fileExtension = this.fileName.substring(this.fileName.lastIndexOf('.'));
			const savedFileName = `file_${this.fileHash}${fileExtension}`;
			
			// 将文件保存到本地持久化目录
			uni.saveFile({
				tempFilePath: tempFilePath,
				filePath: savedFileName,
				success: (res) => {
					console.log('文件保存成功:', res.savedFilePath);
					this.localFilePath = res.savedFilePath;
					this.isDownloaded = true;
					this.isDownloading = false;
					this.downloadProgress = 100;
					
					// 保存到缓存
					this.saveFileToCache(res.savedFilePath);
					
					// 移除下载状态
					this.removeFromDownloadQueue();
					
					uni.showToast({
						title: '下载完成',
						icon: 'success',
						duration: 1500
					});
				},
				fail: (err) => {
					console.error('文件保存失败:', err);
					// 保存失败时，直接使用临时路径（至少这次可以打开）
					this.localFilePath = tempFilePath;
					this.isDownloaded = true;
					this.isDownloading = false;
					this.downloadProgress = 100;
					
					// 移除下载状态
					this.removeFromDownloadQueue();
					
					uni.showToast({
						title: '下载完成',
						icon: 'success',
						duration: 1500
					});
				}
			});
		}
	}
}
</script>

<style lang="css" scoped>
.file-preview-container {
	width: 100%;
	height: 100vh;
	background-color: #F7F7F7;
}

/* 头部容器样式 */
.header-container {
	background-color: #FFFFFF;
	border-bottom: 1px solid #E5E5E5;
}

/* 状态栏占位样式 */
.status-bar-placeholder {
	background-color: #FFFFFF;
	width: 100%;
}

/* 导航栏样式 */
.nav-bar {
	height: 44px;
	background-color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 16px;
}

.nav-left, .nav-right {
	width: 44px;
	height: 44px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-icon, .more-icon {
	font-size: 24px;
	font-weight: 600;
	color: #000000;
}

.nav-center {
	flex: 1;
	text-align: center;
}

.nav-title {
	font-size: 18px;
	font-weight: 600;
	color: #000000;
}

/* 文件内容区域 */
.file-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px 20px;
}

.file-icon-container {
	margin-bottom: 30px;
}

.file-icon {
	width: 120px;
	height: 120px;
}

.file-name-container {
	margin-bottom: 60px;
	text-align: center;
	padding: 0 20px;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.file-name {
	font-size: 18px;
	font-weight: 500;
	color: #000000;
	line-height: 1.4;
	word-break: break-all;
	margin-bottom: 8px;
}

/* 下载状态区域 */
.download-status {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.download-text {
	font-size: 16px;
	color: #888888;
	margin-bottom: 20px;
}

/* 进度圆形指示器 */
.progress-container {
	position: relative;
}

.progress-circle {
	width: 80px;
	height: 80px;
	border-radius: 50%;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

.progress-bar {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background: conic-gradient(#07C160 0% 0%, #E5E5E5 0% 100%);
}

.pause-button, .play-button {
	width: 60px;
	height: 60px;
	background-color: #FFFFFF;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	z-index: 1;
}

.pause-icon {
	width: 3px;
	height: 16px;
	background-color: #07C160;
	margin: 0 2px;
}

.play-icon {
	width: 0;
	height: 0;
	border-left: 12px solid #07C160;
	border-top: 8px solid transparent;
	border-bottom: 8px solid transparent;
	margin-left: 3px;
}

/* 重试按钮 */
.retry-button {
	background-color: #07C160;
	border-radius: 6px;
	padding: 12px 24px;
	margin-top: 20px;
}

.retry-text {
	color: #FFFFFF;
	font-size: 16px;
	font-weight: 500;
}

/* 下载完成状态 */
.download-complete {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.open-button {
	background-color: #07C160;
	border-radius: 6px;
	padding: 12px 32px;
}

.open-text {
	color: #FFFFFF;
	font-size: 16px;
	font-weight: 500;
}

/* 更多选项弹窗 */
.options-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-end;
	z-index: 1000;
}

.options-container {
	width: 100%;
	background-color: #FFFFFF;
	border-radius: 12px 12px 0 0;
	padding: 8px 0 20px 0;
}

.option-item {
	height: 56px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-bottom: 1px solid #F0F0F0;
}

.option-item.cancel {
	border-top: 8px solid #F0F0F0;
	border-bottom: none;
}

.option-text {
	font-size: 18px;
	color: #000000;
}

.option-item.cancel .option-text {
	color: #888888;
}
</style> 