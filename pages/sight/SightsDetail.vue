<template>
    <view class="article-detail-wrap">
        <CustomHeader backgroundColor="#fff" @height-update="updateHeaderHeight">
            <template #title>
                <view class = "user-title" style="width: 100%; text-align: left; padding-left: 12px; display: flex; align-items: center; gap: 12px;" v-if="shouldShowHeaderUserInfo">
                    <image :src="sight.userInfo?.avatar" style="width: 30px; height: 30px; border-radius: 8px; font-size: 16px;" />
                    <view style="display: flex; flex-direction: column; gap: 4px;">
                        <view style="display: flex; align-items: center; gap: 8px;">
                            <text style="font-size: 18px; color: #000">{{ sight.userInfo?.displayName || '加载中...' }}</text>
                        </view>
                    </view>
                </view>
                <view v-else style="width: 100%; text-align: left; padding-left: 12px;">
                    <text style="font-size: 18px; color: #000"></text>
                </view>
            </template>
            <template v-slot:right>
                <view class="header-right-wrapper">
                    <view class="right-info-focus header-follow-btn" v-if="shouldShowHeaderUserInfo" @click="toggleFollow">
                        {{ isFollowing ? '已关注' : '+ 关注' }}
                    </view>
                    <uni-icons color="#333" type="more-filled" size="20" @click="handleMoreOp" />
                </view>
            </template>
        </CustomHeader>

        <view class="user-info row-flex">
            <view class="user-info-left row-flex">
                <image class="user-avatar" :src="sight.userInfo?.avatar" mode="" />
                <view class="user-info-des col-flex">
                    <text class="user-name">{{ sight.userInfo?.displayName || '用户' }}</text>
                </view>
            </view>
            <view class="right-info-focus" @click="toggleFollow">
                {{ isFollowing ? '已关注' : '+ 关注' }}
            </view>
        </view>
        <view class="main-content">
            <view class="article-content">
                <text class="infocontent" selectable>
                    {{ sight.content }}
                </text>
                <!-- 图片展示 -->
                <view v-if="sight.url && sight.url.length > 0" class="swiper-container">
                <swiper :indicator-dots="sight.url.length > 1" :autoplay="false" class="image-swiper">
                    <swiper-item v-for="(img, idx) in sight.url" :key="idx">
                        <image :src="img" class="swiper-image" mode="aspectFill" @click.stop="previewImage(idx, sight.url)" />
                    </swiper-item>
                </swiper>
                </view>
            </view>

            <view class="other-info">
                <text>{{ formatPostTime(sight.createTime) }}</text>
                <!-- <text>IP属地: {{ displayIpAddress }}</text> -->
            </view>
        </view>

        <!-- 评论统计和筛选区域 -->
        <view class="comment-header-container">
            <view class="comment-header" :class="{ 'sticky': isSticky }" :style="stickyStyle">
                <view class="replay-top row-flex">
                    <view class="r-count">
                        {{ sight.comments.total|| 0 }}条回复
                    </view>
                </view>
            </view>
            <!-- 占位元素，当评论头部固定时显示 -->
            <view v-if="isSticky" class="comment-header-placeholder"></view>
        </view>

        <view class="replay-block">
            <view class="replay-content" v-for="comment in nestedComments" :key="comment.id">
                <view class="r-pic">
                    <image class="r-avatar" :src="comment.sendUser.avatar || '/static/logo.png'" />
                </view>
                <view class="right-block">
                    <view class="r-user">
                        <view class="r-username-row">
                            <text class="r-username">{{ comment.sendUser.displayName }}</text>
                            <text v-if="comment.sendUser.userId === sight.userInfo.userId" class="author-label">作者</text>
                        </view>
                        <text class="r-cont">{{ comment.content }}</text>
                    </view>
                    <view class="r-hot row-flex">
                        <text>{{ comment.createTime }}</text>
                    </view>
                    <!-- 楼中楼回复 -->
                    <view class="r-data" v-if="comment.replies && comment.replies.length">
                        <view v-for="reply in comment.replies" :key="reply.id" class="reply-content">
                            <text class="name">{{ reply.sendUser.displayName }}</text>
                            <text v-if="reply.sendUser.userId === sight.userInfo.userId" class="author-label">作者</text>
                            <text v-if="reply.toUser">回复</text>
                            <text class="name" v-if="reply.toUser">{{ reply.toUser.displayName }}</text>
                            <text>：{{ reply.content }}</text>
                        </view>
                    </view>
                </view>
            </view>
            <!-- 加载状态提示 -->
            <view v-if="isLoading" class="loading-status">
                <text>加载中...</text>
            </view>
            <view v-if="!hasMore && !isLoading && sight.comments.total > 0" class="loading-status">
                <text>没有更多评论了</text>
            </view>
            <view v-if="!sight.comments.total && !isLoading" class="empty-status">
                <text>还没有评论，快来抢沙发吧！</text>
            </view>
        </view>
        <view class="op-block row-flex">
            <view class="">
                <input 
                    class="uni-input" 
                    type="text" 
                    v-model="commentContent" 
                    placeholder='说说你的看法' 
                    placeholder-class="input-placeholder"
                    :adjust-position="false"
                    :cursor-spacing="20"
                    @click="handleComment(sight)"
                    readonly
                />
            </view>
            <view class="row-flex">
                <!-- <view class="" @click="showCommentInput2()">
                    <uni-icons :color="isCommented ? '#386BF6' : '#333'" type="chat" size="24" />
                    <text>{{ formatNumber(sight.comments.total || 0) }}</text>
                </view> -->
                <view class="" @click="handleComment(sight)">
                    <uni-icons type="chat" size="24" :color="isCommented ? '#386BF6' : '#333'" />
                    <text>{{ formatNumber(sight.comments.total || 0) }}</text>
                </view>
                <view class="" @click="handleLike(sight)">
                    <uni-icons :type="isUserLiked(sight) ? 'heart-filled' : 'heart'" size="24" :color="isUserLiked(sight) ? '#E75D5A' : '#999'" />
                    <text>{{ formatNumber( sight.likeUser.length || 0 ) }}</text>
                </view>
                <!-- <view class="" @click="handleLike()">
                    <uni-icons :color="sight.hasLike ? '#FF0000' : '#333'" :type="sight.hasLike ? 'hand-up-filled' : 'hand-up'" size="24" />
                    <text>{{ formatNumber( sight.likeUser.length || 0 ) }}</text>
                </view> -->
                <view class="" @click="handleFavorite">
                    <uni-icons :color="isFavorited ? '#386BF6' : '#333'" type="star" size="24" />
                    <text>{{ formatNumber(sight.favoriteCount || 0) }}</text>
                </view>
            </view>
        </view>
        <!-- 弹框 -->
        <uni-popup ref="morePopup" borderRadius='20px 20px 0 0 ' background-color="#fff" @change="handlePopupChange" z-index="1000">
            <view class="top-line"></view>
            <view class="pop-op">
                <!-- 发帖者本人显示删除按钮 -->
                <view v-if="sight.userInfo?.userId === userInfo.userId" class="col-flex" @click="handleDelete">
                    <image src="../../assets/images/bar/delete.png" mode=""></image>
                    <text>删除</text>
                </view>
                <!-- 非发帖者显示其他按钮 -->
                <template v-else>
                    <view class="col-flex" @click="handleBlack">
                        <image src="../../assets/images/bar/lahei.png" mode=""></image>
                        <text>拉黑</text>
                    </view>
                    <view class="col-flex" @click="handleReport">
                        <image src="../../assets/images/bar/jubao.png" mode=""></image>
                        <text>举报</text>
                    </view>
                    <view class="col-flex" @click="handleDisinterest">
                        <image src="../../assets/images/bar/buganxingqu.png" mode=""></image>
                        <text>不感兴趣</text>
                    </view>
                </template>
            </view>
        </uni-popup>
        <!-- 跟帖 -->
        <view class="comment-popup" v-if="showComment2" :class="{'comment-popup-show': showComment2}">
            <view class="comment-mask" @click="handleCloseComment2"></view>
            <div class="comment-contentblock" :style="{ bottom: keyboardHeight + 'px' }">
                <view class="comment-content">
                    <input 
                        v-model="commentContent" 
                        class="comment-input" 
                        :placeholder="'请输入跟帖内容'" 
                        :adjust-position="false"
                        :cursor-spacing="20"
                        @focus="onInputFocus"
                        @blur="onInputBlur"
                        focus 
                    />
                    <button class="comment-submit" @click="submitComment">发送</button>
                </view>
            </div>
        </view>

        <!-- 添加评论弹窗 -->
        <view class="comment-popup" v-if="showComment" :class="{'comment-popup-show': showComment}">
            <view class="comment-mask" @click="handleCloseComment"></view>
            <div class="comment-contentblock" :style="{ bottom: keyboardHeight + 'px' }">
                <view class="comment-content">
                    <input 
                        v-model="commentContent" 
                        class="comment-input" 
                        :placeholder="toUserData?'回复:'+toUserData?.displayName:'请输入评论内容'" 
                        :adjust-position="false"
                        :cursor-spacing="20"
                        @focus="onInputFocus"
                        @blur="onInputBlur"
                        focus 
                    />
                    <button class="comment-submit" @click="submitComment">发送</button>
                </view>
            </div>
        </view>

        <!-- 删除确认弹窗 -->
        <uni-popup ref="deletePopup" type="dialog">
            <uni-popup-dialog
                type="warn"
                title="删除评论"
                content="确定要删除这条评论吗？"
                :before-close="true"
                @confirm="confirmDelete"
                @close="cancelDelete"
            ></uni-popup-dialog>
        </uni-popup>
    </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import CollapseText from '@/components/CollapseText'
import appServerApi from '@/api/appServerApi'
import { getItem, setItem } from '../util/storageHelper'
import nVideo from '../bar/nVideo.vue'

export default {
    components: {
        CustomHeader,
        CollapseText,
        nVideo
    },
    data() {
        return {
            activeItem: 0,
            article: {
                url: [],
            },
            userInfo: {},
            comments: [],
            isFollowing: false,
            defaultAvatars: [
                '/static/image/me/tx1.png',
                '/static/image/me/tx2.png',
                '/static/image/me/tx3.png',
                '/static/image/me/tx4.png',
                '/static/image/me/tx5.png',
            ],
            defaultContents: [
                '今天天气真好，阳光明媚，适合出去走走。',
                '分享一个有趣的发现，希望大家喜欢。',
                '生活中处处都有美，关键是要用心去发现。',
                '每一天都是新的开始，保持积极乐观的心态。',
                '最近学到了很多新东西，感觉收获满满。',
                '与其担心未来，不如好好把握现在。',
                '世界那么大，总有值得期待的事情。',
                '平凡的日子里也能找到快乐。',
                '保持热爱，奔赴山海。',
                '愿每个人都能遇见更好的自己。',
            ],
            commentText: '',
            showComment: false,
            showComment2: false,
            isLiked: false,
            isFavorited: false,
            isCommented: false,
            pageNo: 1,
            pageSize: 10,
            total: 0,
            hasMore: true, // 是否有更多数据,
            id: '',
            toUserData: null,
            tofloorData: null, //当前评论的动态
            keyboardHeight: 0,
            isPlaying: {},
            isFullscreen: false,
            videoRatios: {},
            videoObserver: null,
            currentPlayingVideoId: null,
            likeThrottleTimers: {}, // 添加节流计时器对象
            localLikeStatus: {}, // 添加本地点赞状态存储
            initialReplyCount: 2, // 初始显示的回复数量
            expandedComments: {}, // 记录已展开的评论
            isSticky: false,
            headerHeight: 0, // 添加变量存储 CustomHeader 的高度
            scrollTop: 0,
            scrollTimer: null,
            isLoading: false, // 是否正在加载数据
            shouldShowHeaderUserInfo: false, // 是否在顶部导航栏显示用户信息
            userInfoRect: null, // 用于存储用户信息区域的位置信息

            likesMap: new Map(), // 用于缓存点赞状态
            likeLoading: false, // 防止重复点击
            currentMoment: null, // 当前评论的动态
            commentLoading: false, // 防止重复提交评论
            commentContent: '', // 评论内容
            toUser: null,
            sight: {} // 用于存放传递过来的对象
        }
    },
    computed: {
        stickyStyle() {
            if (this.isSticky) {
                return {
                    top: this.headerHeight + 'px'
                }
            }
            return {}
        },
        displayIpAddress() {
            const ip = this.article.ipAddress;
            if (!ip || ip === 'null·null' || ip === 'null' || ip === 'undefined' || ip === undefined) {
                return '未知';
            }
            return ip;
        },
        nestedComments() {
            if (!this.sight.comments || !this.sight.comments.result) return [];
            const flat = this.sight.comments.result;
            // 一级评论
            const roots = flat.filter(c => !c.toUser);
            // 回复
            const replies = flat.filter(c => c.toUser);
            // 按一级评论分组
            roots.forEach(root => {
                root.replies = replies.filter(r => r.toUser && r.toUser.userId === root.sendUser.userId);
            });
            return roots;
        }
    },
    methods: {
        handleMoreOp() {
            this.$refs.morePopup.open('bottom')
        },
        previewImage(index, images) {
            uni.previewImage({
                current: index, // 当前显示图片索引
                urls: images, // 需要预览的图片http链接列表
            })
        },
        hanleClickItem(item) {
            this.activeItem = item.value;
            if (item.value === '3') { // 楼主筛选
                this.fetchAuthorComments();
            } else if (item.value === '1') { // 最新筛选
                this.fetchLatestComments();
            } else if (item.value === '2') { // 热门筛选
                this.fetchHotComments();
            } else {
                this.pageNo = 1;
                this.fetchComments(this.id);
            }
        },
        fetchCheckUserFollow() {
            // 检查用户是否已登录
            if (!this.userInfo || !this.userInfo.userId) {
                this.isFollowing = false;
                return;
            }
            
            // 检查是否是自己的帖子
            if (this.article.userInfo?.userId === this.userInfo.userId) {
                this.isFollowing = false;
                return;
            }
            
            appServerApi.getCheckUserFollow(this.article.userInfo?.userId)
                .then((response) => {
                    console.log('关注状态检查结果:', response);
                    // 更新关注状态
                    this.isFollowing = response.data || false;
                })
                .catch((error) => {
                    console.error('检查关注状态失败:', error);
                    this.isFollowing = false;
                });
        },
        fetchAuthorComments() {
            this.pageNo = 1;
            this.comments = [];
            //一次性获得更多数据
            const largePageSize = 100;
            //显示加载
            this.isLoading = true;
            uni.showLoading({
                title: '加载中...'
            });

            appServerApi
                .getAllFollowUps(1, largePageSize, this.id)
                .then((response) => {
                    // 过滤出楼主的评论
                    const authorComments = response.data.result.filter(
                        comment => comment.simpleUserInfo?.userId === this.article.userInfo?.userId
                    );

                    this.comments = authorComments;
                    this.total = authorComments.length;
                    this.hasMore = false; // 楼主评论一次性加载完毕
                    
                })
                .catch((error) => {
                    console.error('Error fetching author comments:', error);
                    this.comments = [];
                })
                .finally(() => {
                    this.isLoading = false;
                    uni.hideLoading();
                });
        },
        fetchLatestComments() {
            // 重置页码，确保从第一页开始获取所有数据
            this.pageNo = 1;
            this.comments = [];
            
            // 使用较大的pageSize一次性获取更多数据
            const largePageSize = 100;
            
            // 显示加载提示
            this.isLoading = true;
            uni.showLoading({
                title: '加载中...'
            });

            appServerApi
                .getAllFollowUps(1, largePageSize, this.id)
                .then((response) => {
                    // 按创建时间排序
                    const sortedComments = response.data.result.sort((a, b) => {
                        const timeA = new Date(a.createTime).getTime();
                        const timeB = new Date(b.createTime).getTime();
                        return timeB - timeA; // 降序排序，最新的在前
                    });
                    
                    this.comments = sortedComments;
                    
                    this.total = response.data.total;
                    this.hasMore = this.total > this.comments.length;
                })
                .catch((error) => {
                    console.error('Error fetching latest comments:', error);
                    this.comments = [];
                })
                .finally(() => {
                    this.isLoading = false;
                    uni.hideLoading();
                });
        },
        fetchHotComments() {
            this.pageNo = 1;
            this.comments = [];
            
            // 使用较大的pageSize一次性获取更多数据
            const largePageSize = 100;

            // 显示加载提示
            this.isLoading = true;
            uni.showLoading({
                title: '加载中...'
            });
            
            appServerApi
                .getAllFollowUps(1, largePageSize, this.id)
                .then((response) => {
                    // 按点赞数和评论数排序
                    const sortedComments = response.data.result.sort((a, b) => {
                        // 首先比较点赞数
                        const likeDiff = (b.likeNum || 0) - (a.likeNum || 0);
                        if (likeDiff !== 0) {
                            return likeDiff;
                        }
                        // 如果点赞数相同，则比较评论数
                        return (b.commentNum || 0) - (a.commentNum || 0);
                    });
                    
                    this.comments = sortedComments;
                    
                    
                    this.total = response.data.total;
                    this.hasMore = this.total > this.comments.length;
                })
                .catch((error) => {
                    console.error('Error fetching hot comments:', error);
                    this.comments = [];
                })
                .finally(() => {
                    this.isLoading = false;
                    uni.hideLoading();
                });
        },
        toggleFollow() {
            // Check if user is logged in first
            if (!this.userInfo || !this.userInfo.userId) {
                uni.showToast({
                    title: '请先登录',
                    icon: 'none'
                });
                return;
            }

            // Don't allow following yourself
            if (this.article.userInfo?.userId === this.userInfo.userId) {
                uni.showToast({
                    title: '不能关注自己',
                    icon: 'none'
                });
                return;
            }

            // Optimistically update UI first
            this.isFollowing = !this.isFollowing;

            // Call the API
            appServerApi
                .toggleUserFollow(this.article.userInfo?.userId)
                .then((response) => {
                    console.log('Follow status updated:', response);
                    // Store the follow status in local storage for future reference
                    const followedUsers = uni.getStorageSync('followedUsers') || {};
                    if (this.isFollowing) {
                        followedUsers[this.article.userInfo?.userId] = true;
                    } else {
                        delete followedUsers[this.article.userInfo?.userId];
                    }
                    uni.setStorageSync('followedUsers', followedUsers);
                    
                    uni.showToast({
                        title: this.isFollowing ? '关注成功' : '已取消关注',
                        icon: 'success'
                    });
                    
                    // 更新关注状态
                    this.fetchCheckUserFollow();
                })
                .catch((error) => {
                    console.error('Error updating follow status:', error);
                    // Revert UI if the API call fails
                    this.isFollowing = !this.isFollowing;
                    uni.showToast({
                        title: '操作失败',
                        icon: 'none'
                    });
                });
        },
        fetchArticleDetail(topicId) {
            appServerApi
                .getPostsDetail(topicId)
                .then((response) => {
                    console.log('Article detail:', response);
                    // 获取本地存储的点赞状态
                    const localLikeStatus = uni.getStorageSync('localLikeStatus') || {};
                    const localLikeState = localLikeStatus[topicId];
                    const serverLikeState = response.data.hasLike || false;
                    
                    // 如果本地状态与服务器状态不一致，以服务器状态为准
                    if (localLikeState !== undefined && localLikeState !== serverLikeState) {
                        delete localLikeStatus[topicId];
                        uni.setStorageSync('localLikeStatus', localLikeStatus);
                    }
                    
                    this.article = {
                        ...response.data,
                        hasLike: serverLikeState,
                        likeNum: parseInt(response.data.likeNum || 0),
                        ipAddress: response.data.ipAddress || '未知'
                    };
                    
                    // 如果有bbsId但没有barName，调用接口获取吧名
                    if (this.article.bbsId && !this.article.barName) {
                        this.fetchBarName(this.article.bbsId);
                    }
                    
                    // 获取帖子详情后，检查关注状态
                    this.fetchCheckUserFollow();
                })
                .catch((error) => {
                    console.error('Error fetching article detail:', error);
                });
        },
        
        // 添加获取吧名的方法
        fetchBarName(bbsId) {
            appServerApi
                .getBbsDetail(bbsId)
                .then((response) => {
                    if (response && response.data) {
                        this.$set(this.article, 'barName', response.data.name);
                    }
                })
                .catch((error) => {
                    console.error('获取吧名失败:', error);
                });
        },
        fetchUserInfo() {
            appServerApi
                .getUserInfo()
                .then((response) => {
                    this.userInfo = {
                        ...response,
                        avatar: response.avatar || this.getRandomAvatar(),
                        city: response.city || this.getRandomLocation(),
                    }

                    // 获取用户信息后，检查关注状态
                    this.fetchCheckUserFollow();
                })
                .catch((error) => {
                    console.error('Error fetching user info:', error)
                })
        },
        fetchComments(articleId) {
            // getCommentsByPage
            appServerApi
                .getAllFollowUps(this.pageNo, this.pageSize, articleId)
                .then((response) => {
                    // this.comments = response.data.result || [] // 确保 comments 是数组
                    this.total = response.data.total
                    // 如果是第一页，直接替换数据
                    if (this.pageNo == 1) {
                        this.comments = response.data.result || [] // 确保 comments 是数组
                    } else {
                        // 否则追加数据
                        this.comments = [
                            ...this.comments,
                            ...response.data.result,
                        ]
                    }
                    if (this.total == this.comments.length) {
                        this.hasMore = false
                        console.log('已经到底了')
                    } else {
                        this.hasMore = true
                    }
                    console.log('评论列表', response)
                })
                .catch((error) => {
                    console.error('Error fetching comments:', error)
                    this.comments = [] // 确保 comments 是数组
                })
        },
        getRandomAvatar() {
            const index = Math.floor(Math.random() * this.defaultAvatars.length)
            return this.defaultAvatars[index]
        },
         getRandomLocation() {
            const province =
                this.provinces[
                    Math.floor(Math.random() * this.provinces.length)
                ]
            const city =
                province.cities[
                    Math.floor(Math.random() * province.cities.length)
                ]
            return `${province.name} ${city}`
        },
        getRandomContent() {
            const index = Math.floor(
                Math.random() * this.defaultContents.length
            )
            return this.defaultContents[index]
        },
        // 评论相关
        showCommentInput(row) {
            console.log('row', row)
            this.tofloorData = row
            // 如果没有传入 row,说明是直接跟贴
            if (!row) {
                this.showComment2 = true
                // 监听键盘高度变化
                uni.onKeyboardHeightChange(this.onKeyboardHeightChange)
                return
            }
            // 设置被回复用户
            this.toUserData = row.simpleUserInfo
            this.showComment = true
            // 监听键盘高度变化
            uni.onKeyboardHeightChange(this.onKeyboardHeightChange)
        },

        // 直接对帖子进行评论
        showCommentInput2() {
            this.tofloorData = null;
            this.toUserData = null;
            
            // 优先监听键盘高度变化
            uni.onKeyboardHeightChange(this.onKeyboardHeightChange);
            
            // 直接设置状态，不使用DOM操作
            this.showComment = true;
            
            // uni-app环境下不需要手动操作DOM，直接使用focus属性
            // 延迟确保键盘能正常弹出
            setTimeout(() => {
                // 如有需要，可在此处添加其他操作
            }, 50);
        },
        // 切换回复的展开/收起状态
        toggleReplies(comment) {
            if (!comment.floorId) return;
            
            // 如果当前是展开状态，且有更多回复，则加载更多
            if (this.expandedComments[comment.floorId]) {
                // 如果还有更多回复，加载更多
                if (comment.commentNum - comment.comments.result.length > 0) {
                    this.loadMoreReplies(comment);
                } else {
                    // 如果没有更多回复，则收起
                    this.$set(this.expandedComments, comment.floorId, false);
                }
            } else {
                // 如果未展开，则展开显示初始回复
                this.$set(this.expandedComments, comment.floorId, true);
            }
        },
        loadMoreReplies(comment) {
            if (!comment.floorId) return;
            
            // 获取当前显示的回复数量
            const currentCount = this.expandedComments[comment.floorId] ? 
                comment.comments.result.length : 
                this.initialReplyCount;
            
            // 计算新的显示数量
            const newCount = currentCount + 5;
            
            // 调用API获取更多回复
            appServerApi
                .getCommentsByPage(comment.comments.pageNo + 1, comment.comments.pageSize, comment.floorId)
                .then((response) => {
                    // 合并新的回复到现有回复列表中
                    const newComments = [...comment.comments.result, ...response.data.result];
                    this.$set(comment.comments, 'result', newComments);
                    this.$set(comment.comments, 'pageNo', comment.comments.pageNo + 1);
                    
                    // 更新展开状态
                    this.$set(this.expandedComments, comment.floorId, true);
                    
                    // 如果还有更多回复，保持展开状态
                    if (newComments.length < comment.commentNum) {
                        this.$set(this.expandedComments, comment.floorId, true);
                    }
                })
                .catch((error) => {
                    console.error('Error loading more replies:', error);
                    uni.showToast({
                        title: '加载更多回复失败',
                        icon: 'none'
                    });
                });
        },
        SeeMore(data, floorId) {
            console.log('data', data)
            var obj = data
            obj.pageNo++
            // return
            // getCommentsByPage
            appServerApi
                .getCommentsByPage(obj.pageNo, obj.pageSize, floorId)
                .then((response) => {
                    // this.$set(obj, 'result', [])
                    console.log('评论列表', response)
                    const newComments = [...response.data.result, ...obj.result]
                    console.log('newComments', newComments)
                    this.$set(obj, 'result', newComments)
                })
                .catch((error) => {
                    console.error('Error fetching comments:', error)
                })
        },
        // 回复评论中的回复
        showCommentInput3(row, row2) {
            this.tofloorData = row
            // 设置被回复用户
            this.toUserData = row2.sendUser
            this.showComment = true
            // 监听键盘高度变化
            uni.onKeyboardHeightChange(this.onKeyboardHeightChange)
        },
        async submitComment() {
            if (!this.commentContent.trim() || !this.currentMoment || this.commentLoading)
                return
            this.commentLoading = true

            try {
                var commentData = {
                    momentId: parseInt(this.currentMoment.id),
                    content: this.commentContent.trim(),
                }
                if (!this.toUser) {
                    commentData = {
                        momentId: parseInt(this.currentMoment.id),
                        content: this.commentContent.trim(),
                    }
                } else {
                    commentData = {
                        momentId: parseInt(this.currentMoment.id),
                        content: this.commentContent.trim(),
                        toUserId: this.toUser.userId,
                    }
                }
                const response = await appServerApi.publishMomentComment(commentData)

                // 使用 Vue.set 确保响应式更新
                if (!this.currentMoment.comments) {
                    this.$set(this.currentMoment, 'comments', { result: [] })
                }

                // 创建新评论对象
                const newComment = {
                    id: response?.data?.id || Date.now(),
                    content: this.commentContent.trim(),
                    createTime: new Date().toISOString(),
                    sendUser: {
                        userId: this.userInfo.userId,
                        displayName: this.userInfo.displayName,
                        avatar: this.userInfo.avatar,
                    },
                    toUser: this.toUser,
                    momentId: this.currentMoment.id,
                }
                
                // 将新评论添加到数组末尾
                const newComments = [...this.currentMoment.comments.result, newComment]
                this.$set(this.currentMoment.comments, 'result', newComments)

                // 关键：同步 sight.comments
                if (this.sight && this.sight.id === this.currentMoment.id) {
                    if (!this.sight.comments) {
                        this.$set(this.sight, 'comments', { result: [] })
                    }
                    this.$set(this.sight.comments, 'result', newComments)
                }

                // 更新缓存
                this.commentsMap.set(this.currentMoment.id, [...newComments])
                
                // 获取最新的动态详情
                this.refreshCurrentMoment(this.currentMoment.id)

                uni.$emit('momentCommented', momentId, newComment)
            } catch (error) {
                console.error('评论失败:', error)
                uni.showToast({
                    title: '评论失败',
                    icon: 'none',
                })
            } finally {
                this.commentLoading = false
                this.currentMoment = null
                this.hideCommentInput()
            }
        },

        async refreshCurrentMoment(momentId) {
            try {
                // 延迟一小段时间，确保服务器已处理完评论请求
                setTimeout(async () => {
                    // 获取最新的动态列表数据
                    const response = await appServerApi.getMomentList({
                        pageNo: 1,
                        pageSize: 5, // 请求较少的数据以提高效率
                        momentId: momentId // 如果API支持按ID查询
                    })
                    
                    if (response?.data?.result && response.data.result.length > 0) {
                        // 查找相应的动态
                        const updatedMoment = response.data.result.find(m => m.id === momentId)
                        if (updatedMoment) {
                            // 查找当前动态在列表中的索引
                            const momentIndex = this.momentsList.findIndex(m => m.id === momentId)
                            if (momentIndex !== -1) {
                                // 更新动态数据，保留UI状态
                                this.$set(this.momentsList, momentIndex, {
                                    ...updatedMoment,
                                    // 保留可能的UI状态
                                    isExpanded: this.momentsList[momentIndex].isExpanded
                                })
                                
                                // 更新缓存
                                setItem('moments_cache', this.momentsList)
                                console.log('已刷新动态数据')
                            }
                        }
                    }
                }, 500)
            } catch (error) {
                console.error('刷新动态数据失败:', error)
            }
        },
        submitComment2() {
            if (!this.commentText.trim()) {
                uni.showToast({
                    title: '请输入跟贴内容',
                    icon: 'none',
                })
                return
            }
            var toUserId = ''
            if (this.toUserData) {
                toUserId = this.toUserData.userId
            }
            // {"topicId":"1","content":"沙发","url":["url","url2"]}
            appServerApi
                .publishFollowUp({
                    topicId: this.article.topicId,
                    content: this.commentText.trim(),
                    url: [],
                })
                .then((res) => {
                    uni.showToast({
                        title: '跟贴成功',
                        icon: 'success',
                    })
                    this.article.commentCount++
                    this.isCommented = true
                    
                    // 清空评论内容并关闭评论框
                    this.commentText = '';
                    this.handleCloseComment2();
                    
                    // 重新获取评论列表
                    this.fetchComments(this.article.topicId)
                })
                .catch((err) => {
                    console.error('跟贴失败:', err)
                    uni.showToast({
                        title: '跟贴失败',
                        icon: 'none',
                    })
                })
        },
        
        /**
         * 处理点赞/取消点赞
         * @param {Object} sight 动态对象
         */
         async handleLike(sight) {
            if (this.likeLoading) return
            this.likeLoading = true

            try {
                const isLiked = this.isUserLiked(sight)
                const response = await appServerApi.toggleMomentLike(sight.id)
                console.log('response', response)
                console.log('sight   ', sight)
                // 使用 Vue.set 确保响应式更新
                if (!sight.likeUser) {
                    this.$set(sight, 'likeUser', [])
                }

                if (isLiked) {
                    // 使用数组方法确保响应式
                    const newLikeUsers = sight.likeUser.filter(
                        (user) => user.userId !== this.userInfo.userId
                    )
                    this.$set(sight, 'likeUser', newLikeUsers)

                    console.log('newLikeUsers----1111', newLikeUsers)
                } else {
                    // 创建新数组以触发响应式更新
                    const newLikeUsers = [
                        {
                            userId: this.userInfo.userId,
                            displayName: this.userInfo.displayName,
                            avatar: this.userInfo.avatar,
                        },
                        ...sight.likeUser,
                    ]
                    this.$set(sight, 'likeUser', newLikeUsers)

                    console.log('newLikeUsers----2222', newLikeUsers)
                }

                // 更新缓存
                this.likesMap.set(sight.id, [...sight.likeUser])

                console.log('this.likesMap----', this.likesMap)
            } catch (error) {
                console.error('点赞操作失败:', error)
                uni.showToast({
                    title: '操作失败',
                    icon: 'none',
                })
            } finally {
                this.likeLoading = false
            }
        },
        // 判断是否点赞
        isUserLiked(sight) {
            if (!sight || !sight.likeUser || !Array.isArray(sight.likeUser) || !this.userInfo) return false;
            return sight.likeUser.some(sight => sight.userId === this.userInfo.userId);
        },

        /**
        * 处理评论
        * @param {Object} moment 动态对象
        */
        async handleComment(moment) {
            this.currentMoment = moment
            this.showComment = true
            this.commentContent = ''
            this.toUser = null
        },

        // 隐藏评论输入框
        hideCommentInput() {
            this.showComment = false
            this.commentContent = ''
            this.currentMoment = null
        },

        /**
         * 处理评论
         * @param {Object} moment 动态对象
         */
         async handleComment(moment) {
            this.currentMoment = moment
            this.showComment = true
            this.commentContent = ''
            this.toUser = null
        },
        async handleComment2(moment, comment) {
            this.toUser = comment.sendUser
            this.currentMoment = moment
            this.showComment = true
            this.commentContent = ''
        },
        
        // 跟帖点赞
        handleLike2(row) {
            const isLiked = !row.isLiked
            var obj = row
            appServerApi
                .likeFloor(obj.floorId)
                .then((res) => {
                    obj.isLiked = isLiked
                    obj.likeNum += isLiked ? 1 : -1
                })
                .catch((err) => {
                    console.error('点赞失败:', err)
                    uni.showToast({
                        title: '操作失败',
                        icon: 'none',
                    })
                })
        },
        // 收藏
        handleFavorite() {
            appServerApi
                .likeTopic(this.article.id)
                .then((res) => {
                    this.isFavorited = !this.isFavorited
                    const currentFavoriteCount = this.article.favoriteCount || 0
                    const favoriteCount = this.isFavorited ?  currentFavoriteCount+ 1 : currentFavoriteCount - 1
                    this.article = {...this.article, favoriteCount: favoriteCount}
                    uni.showToast({
                        title: this.isFavorited ? '收藏成功' : '取消收藏',
                        icon: 'success',
                    })
                })
                .catch((err) => {
                    console.error('收藏失败:', err)
                    uni.showToast({
                        title: '操作失败',
                        icon: 'none',
                    })
                })
        },
        handleBlack() {
            uni.showToast({
                title: '已拉黑，您将不再收到该用户动态',
                icon: 'none',
            })
            setTimeout(() => {
                uni.navigateBack()
            }, 1000)
        },
        handleReport() {
            uni.navigateTo({
                url: `/pages/complaint/ComplaintPage`,
            })
        },
        handleDisinterest() {
            uni.showToast({
                title: '操作成功，将减少此类内容推荐',
                icon: 'none',
            })
            setTimeout(() => {
                uni.navigateBack()
            }, 1000)
        },
        onKeyboardHeightChange(e) {
            if (e && e.detail) {
                this.keyboardHeight = e.detail.height || 0;
            } else {
                this.keyboardHeight = 0;
            }
        },
        onInputFocus(e) {
            const that = this;
            uni.onKeyboardHeightChange(function(res) {
                that.keyboardHeight = res.height;
            });
        },
        
        onInputBlur() {
            // 延迟处理，确保在键盘收起后再判断是否需要关闭评论框
            setTimeout(() => {
                if (!this.commentText.trim()) {
                    this.showComment = false;
                    this.showComment2 = false;
                    this.keyboardHeight = 0;
                }
            }, 200);
        },
        // 判断是否为视频URL
        isVideo(url) {
            if (!url) return false;
            const videoExtensions = ['.mp4', '.mov', '.m4v', '.3gp', '.avi', '.m3u8'];
            return videoExtensions.some(ext => url.toLowerCase().includes(ext));
        },

        // 处理视频播放
        onVideoPlay(event, videoId) {
            // 暂停其他视频
            Object.keys(this.isPlaying).forEach(id => {
                if (id !== videoId && this.isPlaying[id]) {
                    const videoContext = uni.createVideoContext('video-' + id);
                    if (videoContext) {
                        videoContext.pause();
                    }
                }
            });
            
            this.$set(this.isPlaying, videoId, true);
            this.currentPlayingVideoId = videoId;
        },

        // 处理视频暂停
        onVideoPause(event, videoId) {
            this.$set(this.isPlaying, videoId, false);
            if (this.currentPlayingVideoId === videoId) {
                this.currentPlayingVideoId = null;
            }
        },

        // 处理视频播放结束
        onVideoEnded(event, videoId) {
            this.$set(this.isPlaying, videoId, false);
            if (this.currentPlayingVideoId === videoId) {
                this.currentPlayingVideoId = null;
            }
        },

        // 处理全屏变化
        onFullscreenChange(e) {
            this.isFullscreen = e.detail.fullScreen;
            const videoId = this.currentPlayingVideoId;
            
            if (!e.detail.fullScreen && videoId) {
                const videoContext = uni.createVideoContext(videoId);
                if (videoContext) {
                    videoContext.pause();
                }
                this.$set(this.isPlaying, videoId, false);
                this.currentPlayingVideoId = null;
            }
        },

        // 视频加载完成处理
        onVideoLoaded(event, videoId) {
            const video = event.target;
            if (video) {
                const ratio = video.videoWidth / video.videoHeight;
                this.$set(this.videoRatios, videoId, ratio);
                this.$forceUpdate();
            }
        },

        // 获取视频样式
        getVideoStyle(videoId) {
            const ratio = this.videoRatios[videoId];
            if (!ratio) return {};
            
            if (ratio < 1) {
                // 竖屏视频
                return {
                    width: '56.25vw',
                    height: `${56.25 / ratio}vw`,
                    margin: '0 auto'
                };
            } else {
                // 横屏或方形视频
                return {
                    width: '100%',
                    height: `${100 / ratio}%`
                };
            }
        },

        // 获取视频容器类名
        getVideoClass(momentId) {
            const ratio = this.videoRatios[momentId];
            if (!ratio) return '';
            
            if (ratio > 1.2) return 'landscape';
            if (ratio < 0.8) return 'portrait';
            return 'square';
        },

        getVideoObjectFit(momentId) {
            const ratio = this.videoRatios[momentId];
            if (!ratio) return 'contain';
            
            if (ratio > 1.2) return 'cover';
            if (ratio < 0.8) return 'contain';
            return 'cover';
        },

        // 视频点击处理
        handleVideoClick(videoId) {
            const videoContext = uni.createVideoContext(videoId);
            if (this.isPlaying[videoId]) {
                videoContext.pause();
            } else {
                videoContext.play();
            }
        },

        // 添加滚动到评论区域的方法
        scrollToComments() {
            // 使用 nextTick 确保 DOM 已更新
            this.$nextTick(() => {
                const query = uni.createSelectorQuery().in(this);
                query.select('.comment-header').boundingClientRect(data => {
                    if (data) {
                        // 计算需要滚动的位置，考虑评论头部的固定高度
                        const scrollTop = data.top - this.headerHeight - 10;
                        
                        // 使用平滑滚动效果，提高体验
                        uni.pageScrollTo({
                            scrollTop: scrollTop,
                            duration: 0 // 使用300ms的滚动动画，而不是立即滚动
                        });
                    }
                }).exec();
            });
        },
        updateHeaderHeight(height) {
            this.headerHeight = height;
        },
        handleCloseComment2() {
            if (!this.commentText.trim()) {
                this.showComment2 = false;
                this.commentText = '';
                this.tofloorData = null;
                this.toUserData = null;
                this.onInputBlur();
            }
        },
        handleCloseComment() {
            if (!this.commentText.trim()) {
                this.showComment = false;
                this.commentText = '';
                this.tofloorData = null;
                this.toUserData = null;
                this.onInputBlur();
            }
        },
        handlePopupChange(e) {
            // 处理弹出框变化逻辑
        },
        // 长按评论处理
        handleDeleteComment(comment) {
            // 检查是否是当前用户的评论
            if (comment.simpleUserInfo?.userId === this.userInfo.userId) {
                this.currentCommentToDelete = comment;
                this.$refs.deletePopup.open();
            }
        },
        // 确认删除
        confirmDelete() {
            if (!this.currentCommentToDelete) return;
            
            // 根据评论类型选择删除方法
            const deletePromise = this.currentCommentToDelete.floorId ? 
                appServerApi.deleteFloor(this.currentCommentToDelete.floorId) :
                appServerApi.deleteComment(this.currentCommentToDelete.id);
            
            deletePromise
                .then(() => {
                    // 从评论列表中移除被删除的评论
                    this.comments = this.comments.filter(
                        comment => comment.floorId !== this.currentCommentToDelete.floorId
                    );
                    // 更新评论总数
                    this.total--;
                    
                    uni.showToast({
                        title: '删除成功',
                        icon: 'success'
                    });
                })
                .catch(err => {
                    console.error('删除失败:', err);
                    uni.showToast({
                        title: '删除失败',
                        icon: 'none'
                    });
                })
                .finally(() => {
                    this.currentCommentToDelete = null;
                    this.$refs.deletePopup.close();
                });
        },
        
        // 取消删除
        cancelDelete() {
            this.currentCommentToDelete = null;
            this.$refs.deletePopup.close();
        },
        handleDelete() {
            uni.showModal({
                title: '删除帖子',
                content: '确定要删除这条帖子吗？',
                success: (res) => {
                    if (res.confirm) {
                        appServerApi
                            .deletePost(this.article.topicId)
                            .then(() => {
                                uni.showToast({
                                    title: '删除成功',
                                    icon: 'success'
                                });
                                // 发送删除成功事件
                                uni.$emit('postDeleted', this.article.topicId);
                                // 延迟返回上一页，让用户看到成功提示
                                setTimeout(() => {
                                    uni.navigateBack();
                                }, 1500);
                            })
                            .catch(err => {
                                console.error('删除帖子失败:', err);
                                uni.showToast({
                                    title: '删除失败',
                                    icon: 'none'
                                });
                            });
                    }
                }
            });
        },
        handleBack() {
            uni.navigateBack();
        },
        /**
         * Check if the current user is following the author of the article
         */
        checkFollowStatus() {
            if (!this.article.userInfo?.userId || !this.userInfo.userId) {
                return;
            }
            
            // First check local storage for quick response
            const followedUsers = uni.getStorageSync('followedUsers') || {};
            if (followedUsers[this.article.userInfo?.userId] !== undefined) {
                this.isFollowing = followedUsers[this.article.userInfo?.userId];
                return;
            }
            
            // If not in local storage, can make an API call here to get the status
            // For now we'll use local storage only
        },
        // 添加检测用户信息区域可见性的方法
        checkUserInfoVisibility() {
            // 每次检查时重新获取用户信息区域的位置信息
            const query = uni.createSelectorQuery().in(this);
            query.select('.user-info').boundingClientRect(data => {
                if (data) {
                    this.userInfoRect = data;
                    
                    // 修正判断逻辑：只有当用户信息区域顶部超出屏幕（负值）时才显示导航栏信息
                    // 并且考虑导航栏的高度，确保用户信息区域完全不可见
                    this.shouldShowHeaderUserInfo = data.top < 0 && Math.abs(data.top) > data.height * 0.5;
                }
            }).exec();
        },
        
        // 获取用户信息区域的位置信息
        getUserInfoRect() {
            const query = uni.createSelectorQuery().in(this);
            query.select('.user-info').boundingClientRect(data => {
                if (data) {
                    this.userInfoRect = data;
                    // 初始化时默认为不显示导航栏用户信息
                    this.shouldShowHeaderUserInfo = false;
                }
            }).exec();
        },
        // 格式化数字，大于10000显示为x万
        formatNumber(number) {
            if (number >= 10000) {
                return (number / 10000).toFixed(1) + '万';
            }
            return number.toString();
        },
        // 新增跳转到吧主页的方法
        goToBarHome() {
            if (this.article.bbsId) {
                uni.navigateTo({
                    url: `/pages/bar/BarHome?id=${this.article.bbsId}`
                });
            } else {
                uni.showToast({
                    title: '未找到对应的吧',
                    icon: 'none'
                });
            }
        },
        async getUserInfo() {
            try {
                const response = await appServerApi.getUserInfo()
                if (response) {
                    this.userInfo = response
                    console.log('this.userInfo', this.userInfo)
                }
            } catch (error) {
                console.error('获取用户信息失败:', error)
            }
        },
        formatPostTime(dateString) {
            if (!dateString) return '';
            // 兼容CST格式
            let date = new Date(dateString.replace(/CST/, 'GMT+0800'));
            if (isNaN(date.getTime())) {
                // 兜底处理
                date = new Date(dateString);
            }
            const now = new Date();
            const diff = (now - date) / 1000; // 秒

            if (diff < 60) return '刚刚';
            if (diff < 3600) return Math.floor(diff / 60) + '分钟前';
            if (diff < 86400) return Math.floor(diff / 3600) + '小时前';
            if (diff < 604800) return Math.floor(diff / 86400) + '天前';

            // 超过7天
            const y = date.getFullYear();
            const m = String(date.getMonth() + 1).padStart(2, '0');
            const d = String(date.getDate()).padStart(2, '0');
            return `${y}-${m}-${d}`;
        },
    },
    onLoad(options) {

        if (options.sight) {
            this.sight = JSON.parse(decodeURIComponent(options.sight));
        }
        console.log('sight', this.sight)

        this.getUserInfo();

        // const articleId = options.topicId
        // this.id = options.topicId
        // if (articleId) {
        //     this.fetchArticleDetail(articleId)
        //     this.fetchComments(articleId)
        // }
        // console.log(options)
        
        // Initialize local point status
        this.localLikeStatus = uni.getStorageSync('localLikeStatus') || {};
        
        // If there is a showComment parameter, scroll to the comment area
        if(options.showComment === 'true') {
            setTimeout(() => {
                this.scrollToComments();
            }, 300);
        }

        // Create observer
        this.$nextTick(() => {
            const observer = uni.createIntersectionObserver(this, {
                thresholds: [0, 0.1, 0.5, 1]
            });
            
            observer.relativeToViewport({
                top: 0
            }).observe('.comment-header-container', (res) => {
                const rect = res.boundingClientRect;
                const headerOffset = this.headerHeight;
                
                const shouldSticky = rect.top <= headerOffset && 
                                   this.scrollTop > headerOffset;
                
                this.$nextTick(() => {
                    this.isSticky = shouldSticky;
                });
            });
            
            // 监听用户信息区域的可见性变化
            const userInfoObserver = uni.createIntersectionObserver(this);
            userInfoObserver.relativeToViewport({
                top: -this.headerHeight
            }).observe('.user-info', (res) => {
                // 获取用户信息区域的高度和位置信息
                const rect = res.boundingClientRect;
                // 计算用户信息区域有多少比例不在可视区域内
                const topOutOfView = rect.top < 0 ? Math.abs(rect.top) : 0;
                const percentHidden = topOutOfView / rect.height;
                
                // 只有当用户信息区域超过一半被隐藏时才显示顶部导航栏的用户信息
                this.shouldShowHeaderUserInfo = percentHidden > 0.5;
            });
        });

        // 在页面加载后获取用户信息区域的位置
        setTimeout(() => {
            this.getUserInfoRect();
        }, 500); // 延迟获取，确保DOM已经渲染
    },
    onShow() {
        this.fetchUserInfo(this.article.authorId);
        this.fetchCheckUserFollow();
        // Check if we're following the author after user info is loaded
        this.$nextTick(() => {
            if (this.article.userInfo?.userId) {
                this.checkFollowStatus();
            }
        });
    },
    onReachBottom() {
        if (this.hasMore) {
            // this.status = 'loading'
            this.pageNo++
            this.fetchComments(this.id)
            // this.fetchPosts(this.id, 'push')
        }
    },
    onUnload() {
        // 移除键盘监听
        uni.offKeyboardHeightChange();
    },
    beforeDestroy() {
        if (this.videoObserver) {
            this.videoObserver.disconnect();
        }
        if (this.scrollTimer) {
            clearTimeout(this.scrollTimer);
        }
    },
    onHide() {
        // 页面隐藏时暂停所有视频
        Object.keys(this.isPlaying).forEach(videoId => {
            if (this.isPlaying[videoId]) {
                const videoContext = uni.createVideoContext(videoId);
                if (videoContext) {
                    videoContext.pause();
                }
                this.$set(this.isPlaying, videoId, false);
            }
        });
        this.currentPlayingVideoId = null;
    },
    // 优化页面滚动监听
    onPageScroll(e) {
        // 保存滚动位置
        this.scrollTop = e.scrollTop;
        
        // 检查用户信息区域的可见性
        this.checkUserInfoVisibility();
        
        // 检查评论标题是否需要固定
        if (!this.scrollTimer) {
            this.scrollTimer = setTimeout(() => {
                const query = uni.createSelectorQuery().in(this);
                query.select('.comment-header-container').boundingClientRect(data => {
                    if (data) {
                        const shouldSticky = data.top <= this.headerHeight && 
                                        this.scrollTop > this.headerHeight;
                        
                        if (this.isSticky !== shouldSticky) {
                            this.isSticky = shouldSticky;
                        }
                    }
                }).exec();
                this.scrollTimer = null;
            }, 50); // 添加50ms的节流，避免频繁触发
        }
    },
}
</script>

<style scoped lang="scss">
.article-detail-wrap {
    min-height: 100vh;
    background: #fff;
    position: relative;
    padding-bottom: 60px; /* 为底部操作栏留出空间 */
    display: flex;
    flex-direction: column;
}

.post-image-container {
    border-radius: 15px;
    overflow: hidden;
}
.post-image-group {
    display: flex;
    .post-image2:nth-child(1) {
        margin-right: 2.5px;
    }
    .post-image2:nth-child(2) {
        margin-left: 2.5px;
    }
    .post-image3 {
        height: 200px;
    }
    .post-image3:nth-child(1) {
        margin-right: 5px;
    }
    .post-image3:nth-child(3) {
        margin-left: 5px;
    }
}
.post-image-group2 {
    display: flex;
    flex-wrap: wrap;
    .post-image4 {
        // flex-grow: 1;
        width: 30%;
        height: 100px;
        margin: 2.5px;
    }
}
.post-image {
    width: 100%;
    margin-top: 10px;
    border-radius: 15px;
    height: 210px;
}
.main-content {
    padding: 0 16px 15px;
    margin-bottom: 15px;
    border-bottom: 1px solid #efefef;
    flex-shrink: 0; /* 防止内容区域被压缩 */

    .article-content {
        font-size: 14px;
        line-height: 20px;

        // image {
        //     width: 100%;
        //     height: auto;
        // }
    }
}

.other-info {
    font-size: 11px;
    color: #999;
    padding: 4px 0px; /* 减小上下内边距 */
    display: flex;
    flex-wrap: wrap;
    gap: 8px; /* 减小元素间距 */
}

.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .tag-item {
        padding: 0 5px;
        height: 24px;
        font-size: 10px;
        display: flex;
        align-items: center;
        background: #f5f5f5;
        color: #999;

        image {
            width: 17px;
            height: 17px;
            border-radius: 50%;
        }
    }
}

.comment-header-container {
    position: relative;
    z-index: 100;
}

.comment-header {
    background: #fff;
    padding: 0 16px;
    z-index: 100;
    position: relative;
    flex-shrink: 0;
    width: 100%;
    height: 56px;
    transform-origin: top;
    will-change: transform, position;
    
    &.sticky {
        position: fixed;
        left: 0;
        right: 0;
        top: var(--header-height);
        border-bottom: 1px solid #efefef;
        background: #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .replay-top {
        height: 100%;
        font-size: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #fff;
    }

    .r-count {
        height: 36px;
        line-height: 36px;
    }

    .filter-block {
        margin-bottom: 0;
        display: flex;
        align-items: center;
        width: 190px;
        border-radius: 100px;
        padding: 2px;
        background: #f5f5f5;
    }

    .filter-item {
        flex: 1;
        height: 32px;
        line-height: 32px;
        text-align: center;
        font-size: 12px;

        &.active {
            background-color: #fff;
            border-radius: 16px;
        }
    }
}

.comment-header-placeholder {
    height: 56px;
    visibility: hidden;
}

.replay-block {
    padding: 0 16px;
    padding-bottom: 100px;
    margin-top: 0;
    position: relative;
    z-index: 1;
    flex: 1;
    background: #fff;
    overflow-y: auto;

    image {
        width: 36px;
        height: 36px;
        border-radius: 8px;
    }

    .replay-content {
        display: flex;
        align-items: flex-start;
        gap: 13px;
        font-size: 14px;
        padding-bottom: 10px;
        .right-block {
            width: 100%;
        }

        .r-pic {
            width: 36px;
            height: 36px;
        }

        .r-user {
            .r-username-row {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 5px;
            }
            .r-username {
                margin-bottom: 0;
                color: #386bf6;
                font-size: 14px;
            }
            .author-label {
                background-color: #386bf6;
                color: #fff;
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 4px;
                line-height: 1;
                display: inline-flex;
                align-items: center;
            }
        }

        .r-cont {
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            cursor: pointer;
        }

        .r-hot {
            // background: #386bf6;
            width: 100%;
            display: flex;
            justify-content: space-between;
            margin: 17px 0;

            > text {
                color: #999999;
            }

            .hot-right {
                gap: 23px;
            }

            image {
                width: 16px;
                height: 16px;
                margin-right: 10px;
            }
        }

        .r-data {
            background: #f5f5f5;
            padding: 14px;
            font-size: 14px;
            font-weight: 400;
            line-height: 18px;
            border-radius: 10px;
            
            > view {
                margin-bottom: 8px; /* 增加回复之间的间距 */
                
                &:last-child {
                    margin-bottom: 0;
                }
            }
            
            .reply-content {
                word-break: break-all;
                line-height: 1.4; /* 根据实际情况调整行高 */
            }

            .name {
                color: #386bf6;
                font-size: 12px;
                margin-right: 4px; /* 添加右侧间距 */
            }

            .author-label {
                background-color: #386bf6;
                color: #fff;
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 4px;
                margin-right: 4px; /* 添加右侧间距 */
                display: inline-block; /* 确保标签正确显示 */
            }

            .content {
                display: inline; /* 确保内容行内显示 */
                word-break: break-all; /* 允许内容换行 */
            }

            /* 调整其他文本元素的间距 */
            .reply-content text {
                display: inline;
                margin-right: 4px;
            }
            .reply-content text:last-child {
                margin-right: 0;
            }
            
            .more-op {
                color: #386bf6;
                margin-top: 8px; /* 增加"展开更多"按钮的顶部间距 */
                text-align: center; /* 文字居中 */
                position: relative; /* 为分割线定位 */
                padding-top: 8px; /* 为分割线留出空间 */
                
                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background-color: rgba(0, 0, 0, 0.1); /* 分割线颜色 */
                }
            }
        }
    }

    /* 加载状态提示 */
    .loading-status {
        text-align: center;
        padding: 15px 0;
        color: #999;
        font-size: 12px;
    }
    
    .no-more-status {
        text-align: center;
        padding: 15px 0;
        color: #999;
        font-size: 12px;
        margin-bottom: 50px;
    }
    
    .empty-status {
        text-align: center;
        padding: 40px 0;
        color: #999;
        font-size: 14px;
    }
}

.user-info {
    padding: 16px 16px 0;

    margin-bottom: 23px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    gap: 15px;

    .user-avatar {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        overflow: hidden;
    }

    .user-info-left {
        gap: 15px;
    }

    .user-info-des {
        gap: 10px;
    }

    .user-name {
        // margin: 0 20px 0 6px;
        font-size: 16px;
    }

    .right-info-focus {
        width: 52px;
        height: 22px;
        line-height: 22px;
        text-align: center;
        border-radius: 11px;
        border: 1px solid #c0c0c1;
        color: #c0c0c1;
    }
}

.op-block {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 16px;
    font-size: 12px;
    color: #4b4b4b;
    background: #fff;
    box-shadow: 0 4px 17px 0 #00000017;
    z-index: 998; /* 确保在评论框下方 */


    > view {
        display: flex;
        align-items: center;
    }

    .row-flex {
        gap: 12px;
        
        view {
            display: flex;
            align-items: center;
            height: 30px;
            min-width: 48px;
            justify-content: center;
            
            .uni-icons {
                display: flex;
                align-items: center;
                margin-right: 3px;
            }
            
            text {
                line-height: 1;
                font-size: 12px;
                white-space: nowrap;
            }
        }
    }

    input {
        background: #f5f5f5;
        font-size: 12px;
        width: 145px;
        height: 30px;
        border-radius: 16px;
        padding: 0 12px;
    }
    
    input::placeholder {
        text-align: left;
    }
}

.pop-op {
    display: flex;
    gap: 38px;
    padding: 23px 14px;
    font-size: 12px;

    .col-flex {
        gap: 8px;
        text-align: center;
    }

    image {
        width: 48px;
        height: 48px;
    }
}

.top-line {
    width: 38px;
    height: 4px;
    margin: 13px auto 0;
    border-radius: 10px;
    background: #e7e7e7;
}

.comment-popup {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    /* 使用uni-app支持的方式设置动画 */
    transform: translateY(100%); /* 初始状态为隐藏 */
    transition-property: transform, opacity;
    transition-duration: 0.25s;
    transition-timing-function: ease-out;
    opacity: 0;
    
    .comment-mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.4);
        z-index: -1;
    }
    
    .comment-contentblock {
        position: relative;
        background: #fff;
        padding: 10px 16px;
        display: flex;
        align-items: center;
        gap: 12px;
        box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
        
        .comment-content {
            display: flex;
            align-items: center;
            width: 100%;
            
            .comment-input {
                flex: 1;
                height: 36px;
                background-color: #f5f5f5;
                border-radius: 18px;
                padding: 0 16px;
                font-size: 14px;
                margin-right: 12px;
            }
            
            .comment-submit {
                width: 60px;
                height: 36px;
                background-color: #386bf6;
                color: #fff;
                border-radius: 18px;
                font-size: 14px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0;
                padding: 0;
            }
        }
    }
}

/* 显示状态的样式 */
.comment-popup-show {
    transform: translateY(0);
    opacity: 1;
}

.uni-input {
    height: 36px;
    background-color: #f5f5f5;
    border-radius: 36px;
    padding: 0 16px;
    font-size: 14px;
    border: 1px solid #e5e5e5;
    width: 120px;
}

.input-placeholder {
    color: #999;
    font-size: 14px;
}

/* 添加视频相关样式 */
.video-wrapper {
    width: 100%;
    height: 200px; /* 添加固定高度 */
    //margin: 12rpx 0;
    //background: #f8f8f8;
    //border-radius: 8rpx;
    //overflow: hidden;
    //position: relative;
    //display: flex;
    //justify-content: center;
    //align-items: center;
}

.post-video {
    display: block;
    width: 100%;
    background: transparent;
    border-radius: 8rpx;
    
    /* 移除全屏按钮 */
    &::-webkit-media-controls-fullscreen-button {
        display: none;
    }
}

/* 竖屏视频容器样式 */
.video-wrapper.portrait {
    padding: 0;
    display: flex;
    justify-content: center;
    
    .post-video {
        width: 56.25vw; /* 适当调整宽度 */
        max-height: 80vh;
        margin: 0 auto;
    }
}

/* 横屏视频容器样式 */
.video-wrapper.landscape {
    width: 100%;
    
    .post-video {
        width: 100%;
        max-height: 56.25vw; /* 保持16:9的比例 */
    }
}

/* 方形视频容器样式 */
.video-wrapper.square {
    width: 100%;
    
    .post-video {
        width: 100%;
        height: 100vw; /* 保持1:1的比例 */
        max-height: 750rpx; /* 设置最大高度 */
    }
}

.uni-popup {
    z-index: 1000; /* 确保弹出菜单在最上层 */
}

.reply-user-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    
    .author-label {
        background-color: #386bf6;
        color: #fff;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 4px;
        line-height: 1;
        display: inline-flex;
        align-items: center;
    }
}

// 为顶部导航栏的用户信息添加过渡效果
.user-title {
    transition: all 0.3s ease;
    opacity: 1;
    transform: translateY(0);
}

// 确保其他元素有相同的过渡效果
.right-info-focus {
    transition: all 0.3s ease;
}

.header-right-wrapper {
    display: flex;
    align-items: center;
    height: 100%;
    padding-right: 8px;
}

.header-follow-btn {
    width: 52px;
    height: 22px;
    line-height: 22px;
    text-align: center;
    border-radius: 11px;
    border: 1px solid #c0c0c1;
    color: #c0c0c1;
    margin-right: 12px;
}

/* 添加吧来源按钮样式 */
.bar-source {
    margin: 8px 0 4px 0;
    display: inline-flex;
    align-items: center;
    background-color: #f5f5f5;
    padding: 4px 4px;
    border-radius: 6px;
    color: 	#696969;
    font-size: 11px;
    cursor: pointer;
    
    uni-icons {
        margin-right: 3px;
    }
    
    &:active {
        opacity: 0.8;
    }
}
.swiper-container {
    width: 100%;
    height: auto;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
}
.image-swiper {
    width: 100%;
    aspect-ratio: 3 / 4;
    height: auto;
}
.swiper-image {
    width: 100%;
    aspect-ratio: 3 / 4;
    height: auto;
    object-fit: cover;
    border-radius: 0;
}
</style>