<template>
    <view class="sight-page-root">
        <!-- 可滚动内容区域 -->
        <scroll-view scroll-y="true" class="moments-scroll">
            <view class="moments-wrap">
                <view class="waterfall-container">
                    <view class="waterfall-column">
                        <view v-for="(sight, index) in waterfallLists.left" :key="'l'+index" class="card">
                            <view class="content-wrapper">
                                <!-- 图片或视频区域 -->
                                <view v-if="sight.url && sight.url.length">
                                    <view v-if="isVideo(sight.url[0])" 
                                        class="video-wrapper"
                                        :class="getVideoClass(sight.url[0])">
                                        <nVideo 
                                            :src="sight.url[0]"
                                            :poster="sight.url[0] + '?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'"
                                            :object-fit="getVideoObjectFit(sight.id)"
                                            :style="getVideoStyle(sight.url[0])"
                                        ></nVideo>
                                    </view>
                                    <view v-else class="image-grid single-image">
                                        <image 
                                            :src="sight.url[0]" 
                                            class="grid-image" 
                                            mode="widthFix"
                                            @click="goToSightsDetail(sight)" 
                                        />
                                    </view>
                                </view>
                                <!-- 文本内容和用户信息/喜欢信息的结构重构 -->
                                <view class="card-main-content">
                                    <!-- 二级容器1：文本内容 -->
                                    <view class="text-content card-text-content">
                                        <view class="text-wrapper" :class="{ 'text-collapsed': !expandedTextMap[sight.id] }">
                                            <text class="text">{{ sight.content }}</text>
                                        </view>
                                        <view class="expand-btn-wrapper" v-if="shouldShowExpandButton(sight)">
                                            <text class="expand" @click.stop="toggleExpandText(sight.id, $event)">
                                                {{ expandedTextMap[sight.id] ? '收起' : '全文' }}
                                            </text>
                                        </view>
                                    </view>
                                    <!-- 二级容器2：用户信息和喜欢信息 -->
                                    <view class="card-info-row">
                                        <!-- 三级容器1：用户信息 -->
                                        <view class="user-meta-left card-user-meta-left">
                                            <view class="avatar-container" @click="goToUserDetail(sight.userInfo.userId)">
                                                <image class="avatar" :src="sight.userInfo?.avatar || defaultAvatar" mode="aspectFill" />
                                            </view>
                                            <text class="displayName" @click="goToUserMoments(sight.userInfo?.userId)">
                                                {{ sight.userInfo?.displayName || '' }}
                                            </text>
                                        </view>
                                        <!-- 三级容器2：喜欢信息 -->
                                        <view class="like-section card-like-section">
                                            <view class="action-item" @click="handleLike(sight)">
                                                <uni-icons :type="isUserLiked(sight) ? 'heart-filled' : 'heart'" size="20" :color="isUserLiked(sight) ? '#E75D5A' : '#999'" />
                                            </view>
                                            <text class="like-count">
                                                {{ sight.likeUser ? sight.likeUser.length : 0 }}
                                            </text>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="waterfall-column">
                        <view v-for="(sight, index) in waterfallLists.right" :key="'r'+index" class="card">
                            <view class="content-wrapper">
                                <!-- 图片或视频区域 -->
                                <view v-if="sight.url && sight.url.length">
                                    <view v-if="isVideo(sight.url[0])" 
                                        class="video-wrapper"
                                        :class="getVideoClass(sight.url[0])">
                                        <nVideo 
                                            :src="sight.url[0]"
                                            :poster="sight.url[0] + '?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto'"
                                            :object-fit="getVideoObjectFit(sight.id)"
                                            :style="getVideoStyle(sight.url[0])"
                                        ></nVideo>
                                    </view>
                                    <view v-else class="image-grid single-image">
                                        <image 
                                            :src="sight.url[0]" 
                                            class="grid-image" 
                                            mode="widthFix"
                                            @click="goToSightsDetail(sight)" 
                                        />
                                    </view>
                                </view>
                                <!-- 文本内容和用户信息/喜欢信息的结构重构 -->
                                <view class="card-main-content">
                                    <!-- 二级容器1：文本内容 -->
                                    <view class="text-content card-text-content">
                                        <view class="text-wrapper" :class="{ 'text-collapsed': !expandedTextMap[sight.id] }">
                                            <text class="text">{{ sight.content }}</text>
                                        </view>
                                        <view class="expand-btn-wrapper" v-if="shouldShowExpandButton(sight)">
                                            <text class="expand" @click.stop="toggleExpandText(sight.id, $event)">
                                                {{ expandedTextMap[sight.id] ? '收起' : '全文' }}
                                            </text>
                                        </view>
                                    </view>
                                    <!-- 二级容器2：用户信息和喜欢信息 -->
                                    <view class="card-info-row">
                                        <!-- 三级容器1：用户信息 -->
                                        <view class="user-meta-left card-user-meta-left">
                                            <view class="avatar-container" @click="goToUserDetail(sight.userInfo.userId)">
                                                <image class="avatar" :src="sight.userInfo?.avatar || defaultAvatar" mode="aspectFill" />
                                            </view>
                                            <text class="displayName" @click="goToUserMoments(sight.userInfo?.userId)">
                                                {{ sight.userInfo?.displayName || '' }}
                                            </text>
                                        </view>
                                        <!-- 三级容器2：喜欢信息 -->
                                        <view class="like-section card-like-section">
                                            <view class="action-item" @click="handleLike(sight)">
                                                <uni-icons :type="isUserLiked(sight) ? 'heart-filled' : 'heart'" size="20" :color="isUserLiked(sight) ? '#E75D5A' : '#999'" />
                                            </view>
                                            <text class="like-count">
                                                {{ sight.likeUser ? sight.likeUser.length : 0 }}
                                            </text>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <!-- 加载更多提示 -->
                <view class="loading-status">
                    <view v-if="loading" class="loading">加载中...</view>
                    <view v-else-if="!hasMore && momentsList.length > 0" class="no-more">没有更多了</view>
                    <view v-else-if="!hasMore && momentsList.length === 0" class="empty">暂无数据</view>
                </view>
            </view>
        </scroll-view>
    </view>
</template>

<script>

import appServerApi from '@/api/appServerApi'
import { getItem, setItem } from '@/pages/util/storageHelper'
import nVideo from '../bar/nVideo.vue'

export default {
    components: {
        nVideo
    },
    onReachBottomDistance: 50,
    data() {
        return {
            userInfo: {},
            toUser: null,
            momentsList: [],
            pageNo: 1,
            hasMore: true, // 是否还有更多数据
            isLoadingMore: false, // 是否正在加载更多
            isRefreshing: false,
            pageSize: 20,
            loading: false,
            showComment: false, // 是否显示评论输入框
            commentContent: '', // 评论内容
            currentMoment: null, // 当前评论的动态
            likeLoading: false, // 防止重复点击
            commentLoading: false, // 防止重复提交评论
            likesMap: new Map(), // 用于缓存点赞状态
            commentsMap: new Map(), // 用于缓存评论列表
            expandedTextMap: {}, // 跟踪每条动态文本是否展开
            defaultAvatar: `/static/image/me/tx${
                Math.floor(Math.random() * 5) + 1
            }.png`,
            isExpanded: false,
            momentNotices: [],
            currentPlayingVideoId: null,
            isPlaying: {},
            isFullscreen: false, // 添加全屏状态跟踪
            videoRatios: {}, // 存储视频宽高比
            videoObserver: null,
            headerTitle: '',
            headerColor: 'transparent',
            loadTriggered: false, // 添加标记，防止重复触发
            contentHeight: 0, // 记录内容高度
            viewportHeight: 0, // 记录视口高度
            _reachBottomTimer: null,
            leftList: [],
            rightList: [],
        }
    },
    computed: {
        waterfallLists() {
            // 只分列有图片/视频的朋友圈
            const filtered = this.momentsList.filter(sight => {
                // 只要有图片且第一个不是视频
                if (!Array.isArray(sight.url) || !sight.url.length) return false;
                // 判断第一个url是否为视频
                return !this.isVideo(sight.url[0]);
            });
            const left = [];
            const right = [];
            let leftHeight = 0;
            let rightHeight = 0;
            filtered.forEach(item => {
                let estHeight = 300;
                if (item.content) estHeight += item.content.length * 2;
                estHeight += 60;
                if (leftHeight <= rightHeight) {
                    left.push(item);
                    leftHeight += estHeight;
                } else {
                    right.push(item);
                    rightHeight += estHeight;
                }
            });
            // 平衡两列数量，避免最后几条全在一边
            while (Math.abs(left.length - right.length) > 1) {
                if (left.length > right.length) {
                    right.push(left.pop());
                } else {
                    left.push(right.pop());
                }
            }
            return { left, right };
        }
    },

    async onHide(){
      this.isExpanded = false
      // 页面隐藏时暂停所有视频
      Object.keys(this.isPlaying).forEach(momentId => {
          if (this.isPlaying[momentId]) {
              const videoContext = uni.createVideoContext('video-' + momentId)
              if (videoContext) {
                  videoContext.pause()
              }
              this.$set(this.isPlaying, momentId, false)
          }
      })
      this.currentPlayingVideoId = null
    },

    async onLoad() {
      console.log('Moments onLoad')
      // 立即从缓存加载数据，减少白屏时间
      this.loadFromCache()
      // 获取通知数据
      this.momentNotices = getItem('setMomentNotices') || []
      // 获取用户信息
      this.getUserInfo()
      
      // 初始数据加载完成后立即预加载下一页
      this.$nextTick(() => {
          if (this.hasMore && !this.loading) {
              this.preloadNextPage();
          }
      });
    },

    onShow() {
        console.log('Moments onShow')
        // 获取通知数据
        this.momentNotices = getItem('setMomentNotices') || []
        
        // 判断是否从发布页面返回
        const pages = getCurrentPages()
        const prevPage = pages.length > 1 ? pages[pages.length - 2] : null
        const isFromCreateMoment = prevPage && prevPage.route === 'pages/moments/CreateMoments'

        // 1. 始终先从缓存加载数据，确保界面有内容显示
        if (!this.momentsList || this.momentsList.length === 0) {
            this.loadFromCache()
        }
        
        // 2. 获取用户信息
        this.getUserInfo()
        
        // 3. 初始化文本折叠状态
        this.initExpandedTextMap()
        
        // 4. 后台更新数据
        if (isFromCreateMoment) {
            // 从发布页返回，使用无闪烁更新
            this.backgroundRefreshAfterPublish()
        } else {
            // 普通进入，静默更新
            this.silentRefresh()
        }
        
        // 5. 确保预加载机制启动
        this.$nextTick(() => {
            if (this.hasMore && !this.loading) {
                this.preloadNextPage();
            }
        });
    },

    mounted() {
        this.setupVideoObserver();
        
        // 添加滚动监听
        this.setupScrollListener();
        
        // 创建触底检测定时器
        this.setupScrollDetection();
    },

    methods: {
        // 初始化文本折叠状态
        initExpandedTextMap() {
            // 为所有动态初始化展开状态
            if (this.momentsList && this.momentsList.length > 0) {
                this.momentsList.forEach(moment => {
                    // 只有当状态未设置时才初始化
                    if (!this.expandedTextMap.hasOwnProperty(moment.id)) {
                        this.$set(this.expandedTextMap, moment.id, false);
                    }
                });
            }
        },
        
        // 从缓存加载数据
        loadFromCache() {
            try {
                const cachedMoments = getItem('moments_cache')
                if (cachedMoments && Array.isArray(cachedMoments) && cachedMoments.length > 0) {
                    this.momentsList = cachedMoments
                    console.log('从缓存加载动态数据:', cachedMoments.length, '条')
                    // 在加载完缓存数据后初始化折叠状态
                    this.$nextTick(() => {
                        this.initExpandedTextMap();
                        
                        // 缓存加载完成后立即预加载下一页
                        if (this.hasMore && !this.loading) {
                            this.preloadNextPage();
                        }
                    });
                    return true;
                }
                return false;
            } catch (error) {
                console.error('加载缓存数据失败:', error)
                return false;
            }
        },

        // 添加无闪烁后台更新方法
        async backgroundRefreshAfterPublish() {
            try {
                // 请求第一页新数据
                const response = await appServerApi.getSightList({
                    pageNo: 1,
                    pageSize: this.pageSize,
                });
                
                if (response?.data) {
                    const newList = response.data.result || [];
                    
                    if (newList.length > 0) {
                        // 创建现有动态的Map用于状态保持
                        const existingStates = new Map(
                            this.momentsList.map(moment => [
                                moment.id, 
                                { isExpanded: this.expandedTextMap[moment.id] || false }
                            ])
                        );
                        
                        // 保留原来的滚动位置
                        const scrollTop = this.getScrollTop();
                        
                        // 更新列表时保持原有状态
                        this.momentsList = newList.map(moment => {
                            const state = existingStates.get(moment.id);
                            if (state) {
                                // 保持原有UI状态
                                this.$set(this.expandedTextMap, moment.id, state.isExpanded);
                            }
                            return moment;
                        });
                        
                        // 更新缓存
                        setItem('moments_cache', this.momentsList);
                        
                        // 重置分页到第2页，因为第1页已经加载
                        this.pageNo = 2;
                        this.hasMore = newList.length >= this.pageSize;
                        
                        // 恢复滚动位置
                        this.$nextTick(() => {
                            this.setScrollTop(scrollTop);
                            this.initExpandedTextMap();
                        });
                    }
                }
            } catch (error) {
                console.error('后台刷新数据失败:', error);
                // 失败时静默处理，不影响用户体验
            }
        },
        
        // 获取当前滚动位置
        getScrollTop() {
            let scrollTop = 0;
            try {
                // 尝试获取页面滚动位置
                const query = uni.createSelectorQuery();
                query.selectViewport().scrollOffset(res => {
                    scrollTop = res.scrollTop;
                }).exec();
                
                return scrollTop;
            } catch (e) {
                console.error('获取滚动位置失败', e);
                return 0;
            }
        },
        
        // 设置滚动位置
        setScrollTop(scrollTop) {
            try {
                if (scrollTop > 0) {
                    uni.pageScrollTo({
                        scrollTop: scrollTop,
                        duration: 0  // 瞬间滚动，无动画
                    });
                }
            } catch (e) {
                console.error('设置滚动位置失败', e);
            }
        },

        // 静默更新数据
        async silentRefresh() {
            try {
                const response = await appServerApi.getSightList({
                    pageNo: 1,
                    pageSize: this.pageSize,
                })
                
                if (response?.data) {
                    const newList = response.data.result || []
                    
                    if (newList.length > 0) {
                        let needUpdate = false
                        
                        // 保存当前滚动位置
                        const scrollTop = this.getScrollTop();
                        
                        // 创建现有动态的Map，用于快速查找
                        const existingMomentsMap = new Map(
                            this.momentsList.map(moment => [moment.id, moment])
                        )
                        
                        // 创建UI状态Map
                        const uiStateMap = new Map(
                            this.momentsList.map(moment => [
                                moment.id, 
                                { isExpanded: this.expandedTextMap[moment.id] || false }
                            ])
                        );
                        
                        // 比对每条动态
                        newList.forEach(newMoment => {
                            const existingMoment = existingMomentsMap.get(newMoment.id)
                            
                            if (!existingMoment) {
                                // 新动态，需要更新
                                needUpdate = true
                            } else {
                                // 比对点赞和评论数据
                                const likesChanged = JSON.stringify(newMoment.likeUser) !== JSON.stringify(existingMoment.likeUser)
                                const commentsChanged = JSON.stringify(newMoment.comments) !== JSON.stringify(existingMoment.comments)
                                
                                if (likesChanged || commentsChanged) {
                                    // 更新现有动态的点赞和评论数据
                                    existingMoment.likeUser = newMoment.likeUser
                                    existingMoment.comments = newMoment.comments
                                    needUpdate = true
                                }
                            }
                        })
                        
                        if (needUpdate || newList.length !== this.momentsList.length) {
                            // 合并新旧数据，保留UI状态
                            const mergedList = newList.map(newMoment => {
                                const existingMoment = existingMomentsMap.get(newMoment.id)
                                const state = uiStateMap.get(newMoment.id);
                                
                                if (state) {
                                    // 保持原有UI状态
                                    this.$set(this.expandedTextMap, newMoment.id, state.isExpanded);
                                }
                                
                                return existingMoment || newMoment
                            })
                            
                            // 更新列表和缓存
                            this.momentsList = mergedList
                            setItem('moments_cache', this.momentsList)
                            
                            // 恢复滚动位置
                            this.$nextTick(() => {
                                this.setScrollTop(scrollTop);
                                this.initExpandedTextMap();
                            });
                            
                            // 重置分页到第2页，因为第1页已经加载
                            this.pageNo = 2;
                            this.hasMore = newList.length >= this.pageSize;
                            
                            // 静默刷新完成后，立即预加载下一页
                            if (this.hasMore) {
                                setTimeout(() => {
                                    console.log('静默刷新完成，预加载下一页');
                                    this.preloadNextPage();
                                }, 300);
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('静默更新失败:', error)
                // 失败时静默处理，不影响用户体验
            }
        },

        // 下拉刷新
        async onRefresh() {
            try {
                await this.silentRefresh()
            } catch (error) {
                console.error('刷新失败:', error)
            }
        },

        // 获取动态列表
        async getList(isLoadMore = false) {
            // 首次加载时如果已有缓存数据，先显示缓存内容
            if (!isLoadMore && !this.loading && this.momentsList.length === 0) {
                const hasCachedData = this.loadFromCache();
                if (hasCachedData) {
                    // 有缓存数据，在后台刷新
                    setTimeout(() => {
                        this.silentRefresh();
                    }, 100);
                    return;
                }
            }
            
            if (this.loading) {
                return;
            }
            
            // 加载更多时显示加载状态，首次加载可能从缓存已加载
            this.loading = true;
            
            try {
                // 如果不是加载更多，则为初始加载，页码应该是1
                let currentPageNo = isLoadMore ? this.pageNo : 1;
                
                const response = await appServerApi.getSightList({
                    pageNo: currentPageNo,
                    pageSize: this.pageSize,
                })

                if (response && response.data) {
                    const newList = response.data.result || []
                    
                    if (newList.length > 0) {
                        if (isLoadMore) {
                            // 加载更多时追加到后面
                            this.momentsList = [...this.momentsList, ...newList]
                        } else {
                            // 刷新时替换整个列表
                            this.momentsList = newList
                        }
                        
                        // 更新缓存
                        setItem('moments_cache', this.momentsList)
                        
                        this.hasMore = newList.length >= this.pageSize
                        
                        // 只有在以下情况更新页码：
                        // 1. 初始加载时，设为2（因为已加载第1页）
                        // 2. 加载更多时且有更多数据，页码增加1
                        if (!isLoadMore) {
                            this.pageNo = 2;
                            
                            // 非加载更多（首次加载或刷新）完成后，立即预加载下一页
                            this.$nextTick(() => {
                                if (this.hasMore) {
                                    console.log('首次加载完成，立即预加载下一页');
                                    this.preloadNextPage();
                                }
                            });
                        } else if (this.hasMore) {
                            this.pageNo++;
                        }
                        
                        // 在获取新数据后初始化文本折叠状态
                        this.$nextTick(() => {
                            this.initExpandedTextMap();
                        });
                    } else if (!isLoadMore) {
                        // 当没有获取到数据且不是加载更多时，清空列表
                        this.momentsList = [];
                        setItem('moments_cache', []);
                    } else {
                        this.hasMore = false;
                    }
                } else {
                    if (isLoadMore) {
                        this.hasMore = false;
                    }
                }
            } catch (error) {
                console.error('获取动态列表失败:', error)
                if (isLoadMore) {
                    this.hasMore = false
                }
                
                // 加载失败时，如果是首次加载，尝试使用缓存
                if (!isLoadMore && (!this.momentsList || this.momentsList.length === 0)) {
                    this.loadFromCache();
                }
            } finally {
                this.loading = false;
            }
        },

        async getUserInfo() {
            try {
                const response = await appServerApi.getUserInfo()
                if (response) {
                    this.userInfo = response
                    console.log('this.userInfo', this.userInfo)
                }
            } catch (error) {
                console.error('获取用户信息失败:', error)
            }
        },
        formatTimeAgo(dateString) {
            const now = new Date()
            const date = new Date(dateString)
            const diff = now - date

            const seconds = Math.floor(diff / 1000)
            const minutes = Math.floor(seconds / 60)
            const hours = Math.floor(minutes / 60)
            const days = Math.floor(hours / 24)

            // 更细致的时间显示逻辑
            if (seconds < 30) {
                return '刚刚'
            } else if (seconds < 60) {
                return '1分钟内'
            } else if (minutes < 60) {
                return `${minutes}分钟前`
            } else if (hours <= 22) {
                // 22小时内显示具体小时数
                return `${hours}小时前`
            } else if (hours < 24) {
                return '今天'
            } else if (days === 1) {
                return '昨天'
            } else if (days === 2) {
                return '前天'
            } else if (days < 30) {
                return `${days}天前`
            } else {
                return dateString
            }
        },

        // 跳转到创建动态页面
        goToCreateMoment(e) {
            if (e) {
                e.stopPropagation();
            }
            uni.navigateTo({
                url: '/pages/moments/CreateMoments',
                fail: (err) => {
                    console.error('跳转失败:', err)
                    uni.showToast({
                        title: '跳转失败',
                        icon: 'none',
                    })
                },
            })
        },

        /**
         * 处理评论
         * @param {Object} moment 动态对象
         */
        async handleComment(moment) {
            this.currentMoment = moment
            this.showComment = true
            this.commentContent = ''
            this.toUser = null
        },
        async handleComment2(moment, comment) {
            console.log('handleComment2', moment)
            // 判断是否是自己的评论
            this.toUser = comment.sendUser
            // {"momentId":37,"toUserId":"","content":"内容"}
            this.currentMoment = moment
            this.showComment = true
            this.commentContent = ''
        },

        /**
         * 预览图片
         * @param {Array} urls 图片URL数组
         * @param {number} current 当前图片索引
         */
        previewImage(urls, current) {
            uni.previewImage({
                urls: urls, // 需要预览的图片链接列表
                current: current, // 当前显示图片的索引
                indicator: 'number', // 显示页码
                loop: true, // 是否循环预览
                success: () => {
                    console.log('图片预览成功')
                },
                fail: (err) => {
                    console.error('图片预览失败:', err)
                },
            })
        },

        /**
         * 处理点赞/取消点赞
         * @param {Object} moment 动态对象
         */
        async handleLike(moment) {
            if (this.likeLoading) return
            this.likeLoading = true

            try {
                const isLiked = this.isUserLiked(moment)
                const response = await appServerApi.toggleMomentLike(moment.id)
                console.log('response', response)
                console.log('moment   ', moment)
                // 使用 Vue.set 确保响应式更新
                if (!moment.likeUser) {
                    this.$set(moment, 'likeUser', [])
                }

                if (isLiked) {
                    // 使用数组方法确保响应式
                    const newLikeUsers = moment.likeUser.filter(
                        (user) => user.userId !== this.userInfo.userId
                    )
                    this.$set(moment, 'likeUser', newLikeUsers)

                    console.log('newLikeUsers----1111', newLikeUsers)
                } else {
                    // 创建新数组以触发响应式更新
                    const newLikeUsers = [
                        {
                            userId: this.userInfo.userId,
                            displayName: this.userInfo.displayName,
                            avatar: this.userInfo.avatar,
                        },
                        ...moment.likeUser,
                    ]
                    this.$set(moment, 'likeUser', newLikeUsers)

                    console.log('newLikeUsers----2222', newLikeUsers)
                }

                // 更新缓存
                this.likesMap.set(moment.id, [...moment.likeUser])

                console.log('this.likesMap----', this.likesMap)
            } catch (error) {
                console.error('点赞操作失败:', error)
                uni.showToast({
                    title: '操作失败',
                    icon: 'none',
                })
            } finally {
                this.likeLoading = false
            }
        },

        // 隐藏评论输入框
        hideCommentInput() {
            this.showComment = false
            this.commentContent = ''
            this.currentMoment = null
        },

        // 提交评论
        async submitComment() {
            if (!this.commentContent.trim() || !this.currentMoment || this.commentLoading)
                return
            this.commentLoading = true

            try {
                var commentData = {
                    momentId: parseInt(this.currentMoment.id),
                    content: this.commentContent.trim(),
                }
                if (!this.toUser) {
                    commentData = {
                        momentId: parseInt(this.currentMoment.id),
                        content: this.commentContent.trim(),
                    }
                } else {
                    commentData = {
                        momentId: parseInt(this.currentMoment.id),
                        content: this.commentContent.trim(),
                        toUserId: this.toUser.userId,
                    }
                }
                const response = await appServerApi.publishMomentComment(commentData)

                // 使用 Vue.set 确保响应式更新
                if (!this.currentMoment.comments) {
                    this.$set(this.currentMoment, 'comments', { result: [] })
                }

                // 创建新评论对象
                const newComment = {
                    id: response?.data?.id || Date.now(),
                    content: this.commentContent.trim(),
                    createTime: new Date().toISOString(),
                    sendUser: {
                        userId: this.userInfo.userId,
                        displayName: this.userInfo.displayName,
                        avatar: this.userInfo.avatar,
                    },
                    toUser: this.toUser,
                    momentId: this.currentMoment.id,
                }
                
                // 将新评论添加到数组末尾
                const newComments = [...this.currentMoment.comments.result, newComment]
                
                this.$set(this.currentMoment.comments, 'result', newComments)

                // 更新缓存
                this.commentsMap.set(this.currentMoment.id, [...newComments])
                
                // 获取最新的动态详情
                this.refreshCurrentMoment(this.currentMoment.id)
            } catch (error) {
                console.error('评论失败:', error)
                uni.showToast({
                    title: '评论失败',
                    icon: 'none',
                })
            } finally {
                this.commentLoading = false
                this.currentMoment = null
                this.hideCommentInput()
            }
        },
        
        // 刷新当前动态数据
        async refreshCurrentMoment(momentId) {
            try {
                // 延迟一小段时间，确保服务器已处理完评论请求
                setTimeout(async () => {
                    // 获取最新的动态列表数据
                    const response = await appServerApi.getSightList({
                        pageNo: 1,
                        pageSize: 5, // 请求较少的数据以提高效率
                        momentId: momentId // 如果API支持按ID查询
                    })
                    
                    if (response?.data?.result && response.data.result.length > 0) {
                        // 查找相应的动态
                        const updatedMoment = response.data.result.find(m => m.id === momentId)
                        if (updatedMoment) {
                            // 查找当前动态在列表中的索引
                            const momentIndex = this.momentsList.findIndex(m => m.id === momentId)
                            if (momentIndex !== -1) {
                                // 更新动态数据，保留UI状态
                                this.$set(this.momentsList, momentIndex, {
                                    ...updatedMoment,
                                    // 保留可能的UI状态
                                    isExpanded: this.momentsList[momentIndex].isExpanded
                                })
                                
                                // 更新缓存
                                setItem('moments_cache', this.momentsList)
                                console.log('已刷新动态数据')
                            }
                        }
                    }
                }, 500)
            } catch (error) {
                console.error('刷新动态数据失败:', error)
            }
        },

        /**
         * 判断当前用户是否点赞了该动态
         * @param {Object} moment 动态对象
         * @returns {boolean} 是否已点赞
         */
        isUserLiked(moment) {
            // 优先从缓存中获取点赞状态
            const cachedLikes = this.likesMap.get(moment.id)
            const likeUsers = cachedLikes || moment.likeUser
            if (!likeUsers || !this.userInfo) return false
            return likeUsers.some(
                (user) => user.userId === this.userInfo.userId
            )
        },

        // 跳转到用户详情页面
        goToUserDetail(userId) {
            if (!userId) return
            uni.navigateTo({
                url: `/pages/contact/UserDetailPage?userId=${userId}`,
            })
        },

        // 跳转到用户朋友圈页面
        goToUserMoments(userId) {
            if (!userId) return
            uni.navigateTo({
                url: `/pages/moments/SelfMoments?userId=${userId}`,
            })
        },

        goToSightDetail(sight) {
            if (!sight) return;
            console.log('sight', sight)
            const sightStr = encodeURIComponent(JSON.stringify(sight));
            uni.navigateTo({
                url: `/pages/sight/SightDetail?sight=${sightStr}`
            });
        },
        goToSightsDetail(sight) {
            if (!sight) return;
            console.log('sight', sight)
            const sightStr = encodeURIComponent(JSON.stringify(sight));
            uni.navigateTo({
                url: `/pages/sight/SightsDetail?sight=${sightStr}`
            });
        },

        toggleExpand() {
            this.isExpanded = !this.isExpanded
        },

        goToChangeCover() {
            uni.navigateTo({
                url: '/pages/moments/ChangeCover'
            })
        },

        goToNotices() {
            console.log('goToNotices')

            uni.navigateTo({
                url: '/pages/moments/MomentNotices',
                success: () => {
                  uni.removeTabBarBadge({
                    index: 3
                  })
                  // 清除通知和角标
                  setItem('setMomentNotices', [])
                }
            })
        },

        // 判断是否为视频URL
        isVideo(url) {
            if (!url) return false;
            // 检查文件扩展名
            const videoExtensions = ['.mp4', '.mov', '.m4v', '.3gp', '.avi', '.m3u8'];
            return videoExtensions.some(ext => url.toLowerCase().includes(ext));
        },

        // 处理视频播放
        onVideoPlay(event, momentId) {
            // 暂停其他视频
            Object.keys(this.isPlaying).forEach(id => {
                if (id !== momentId.toString() && this.isPlaying[id]) {
                    const videoContext = uni.createVideoContext('video-' + id);
                    if (videoContext) {
                        videoContext.pause();
                    }
                }
            });
            
            this.$set(this.isPlaying, momentId, true);
            this.currentPlayingVideoId = momentId;
        },

        // 处理视频暂停
        onVideoPause(event, momentId) {
            this.$set(this.isPlaying, momentId, false)
            if (this.currentPlayingVideoId === momentId) {
                this.currentPlayingVideoId = null
            }
        },

        // 处理视频播放结束
        onVideoEnded(event, momentId) {
            this.$set(this.isPlaying, momentId, false)
            if (this.currentPlayingVideoId === momentId) {
                this.currentPlayingVideoId = null
            }
        },
        // 修改全屏变化处理
        onFullscreenChange(e) {
            this.isFullscreen = e.detail.fullScreen;
            const momentId = this.currentPlayingVideoId;
            
            if (!e.detail.fullScreen && momentId) {
                // 退出全屏时暂停视频
                const videoContext = uni.createVideoContext('video-' + momentId);
                if (videoContext) {
                    videoContext.pause();
                }
                this.$set(this.isPlaying, momentId, false);
                this.currentPlayingVideoId = null;
            }
        },

        // 监听页面滚动
        onPageScroll(e) {
            console.log('页面滚动事件，scrollTop:', e.scrollTop);
            
            // 触发自定义滚动事件
            uni.$emit('page-scroll', e);
            
            // 只在非全屏状态下暂停视频
            if (!this.isFullscreen) {
                Object.keys(this.isPlaying).forEach(momentId => {
                    if (this.isPlaying[momentId]) {
                        const videoContext = uni.createVideoContext('video-' + momentId)
                        if (videoContext) {
                            videoContext.pause()
                        }
                        this.$set(this.isPlaying, momentId, false)
                    }
                })
                this.currentPlayingVideoId = null
            }
            
            if( e.scrollTop > 280 ) {
                this.headerTitle = '发现'
                this.headerColor = '#ffffff'
            } else {
                this.headerTitle = ''
                this.headerColor = 'transparent'
            }
        },
        
        // 修改加载更多方法
        async loadMore() {
            if (!this.hasMore || this.loading) {
                return;
            }
            
            this.loading = true;
            
            try {
                // 调用API获取数据，确保传递正确的页码参数
                const response = await appServerApi.getSightList({
                    pageNo: this.pageNo,
                    pageSize: this.pageSize,
                });
                
                if (response && response.data) {
                    const newList = response.data.result || [];
                    
                    if (newList.length > 0) {
                        // 将新数据添加到列表末尾
                        this.momentsList = [...this.momentsList, ...newList];
                        
                        // 更新缓存
                        setItem('moments_cache', this.momentsList);
                        
                        // 判断是否还有更多数据
                        this.hasMore = newList.length >= this.pageSize;
                        
                        // 只有在有更多数据的情况下才增加页码
                        if (this.hasMore) {
                            this.pageNo++;
                            
                            // 预加载下一页数据
                            this.preloadNextPage();
                        }
                        
                        // 初始化新加载数据的文本折叠状态
                        this.$nextTick(() => {
                            this.initExpandedTextMap();
                        });
                    } else {
                        this.hasMore = false;
                    }
                } else {
                    this.hasMore = false;
                }
            } catch (error) {
                console.error('加载更多数据出错:', error);
                this.hasMore = false;
            } finally {
                this.loading = false;
            }
        },

        // 添加预加载下一页的方法
        async preloadNextPage() {
            // 确保不干扰当前加载状态
            if (this.loading || !this.hasMore) {
                return;
            }
            
            try {
                console.log('预加载下一页数据，页码:', this.pageNo);
                
                // 检查是否已有预加载数据
                const existingPreload = getItem('moments_preload_cache');
                if (existingPreload && Array.isArray(existingPreload) && existingPreload.length > 0) {
                    console.log('已有预加载数据，跳过重复预加载');
                    return;
                }
                
                // 静默加载下一页数据
                const response = await appServerApi.getSightList({
                    pageNo: this.pageNo,
                    pageSize: this.pageSize,
                });
                
                if (response && response.data) {
                    const nextPageData = response.data.result || [];
                    
                    // 将预加载的数据存储在缓存中
                    if (nextPageData.length > 0) {
                        setItem('moments_preload_cache', nextPageData);
                        console.log('成功预加载下一页数据:', nextPageData.length, '条');
                        
                        // 预判断是否还有更多数据
                        const hasMoreNext = nextPageData.length >= this.pageSize;
                        
                        // 如果这是最后一页，就不再尝试预加载
                        if (!hasMoreNext) {
                            console.log('预加载显示没有更多数据了');
                        }
                    } else {
                        console.log('预加载显示没有更多数据');
                        // 清除预加载缓存，避免混淆
                        setItem('moments_preload_cache', []);
                    }
                }
            } catch (error) {
                console.error('预加载下一页数据出错:', error);
                // 预加载失败不影响主体功能，静默处理
            }
        },
        
        // 修改onReachBottom方法
        onReachBottom() {
            // 防抖处理，避免多次触发
            if (this._reachBottomTimer) {
                clearTimeout(this._reachBottomTimer);
            }
            
            this._reachBottomTimer = setTimeout(() => {
                if (this.hasMore && !this.loading) {
                    // 优先使用预加载数据
                    const preloadedData = getItem('moments_preload_cache');
                    
                    if (preloadedData && Array.isArray(preloadedData) && preloadedData.length > 0) {
                        this.usePreloadedData();
                    } else {
                        // 如果没有预加载数据，展示loading状态并加载
                        this.loading = true;
                        this.loadMore();
                    }
                }
            }, 100); // 减少延迟以提高响应速度
        },

        // 添加使用预加载数据的方法
        usePreloadedData() {
            const preloadedData = getItem('moments_preload_cache');
            if (!preloadedData || !Array.isArray(preloadedData) || preloadedData.length === 0) {
                // 没有预加载数据，回退到正常加载
                this.loadMore();
                return;
            }
            
            console.log('使用预加载的数据:', preloadedData.length, '条');
            this.loading = true;
            
            try {
                // 将预加载的数据添加到列表
                this.momentsList = [...this.momentsList, ...preloadedData];
                
                // 更新缓存
                setItem('moments_cache', this.momentsList);
                
                // 判断是否还有更多数据
                this.hasMore = preloadedData.length >= this.pageSize;
                
                // 增加页码
                this.pageNo++;
                
                // 初始化文本折叠状态
                this.$nextTick(() => {
                    this.initExpandedTextMap();
                    
                    // 清除预加载缓存
                    setItem('moments_preload_cache', []);
                    
                    // 立即开始下一页预加载
                    if (this.hasMore) {
                        this.preloadNextPage();
                    }
                });
            } catch (error) {
                console.error('使用预加载数据失败:', error);
            } finally {
                this.loading = false;
            }
        },

        // 添加视频加载完成的处理方法
        onVideoLoaded(event, momentId) {
			
            const video = event.target;
            if (video) {
                const ratio = video.videoWidth / video.videoHeight;
                this.$set(this.videoRatios, momentId, ratio);
                // 强制更新视图
                this.$forceUpdate();
            }
        },

        // 添加获取视频样式的方法
        getVideoStyle(url) {
			const urlArr = url.split('?videoRatio=')
            const ratio = urlArr.length > 1 ? urlArr[1] : null;
            if (!ratio) return {};
            
            if (ratio < 1) {
                // 竖屏视频
                return {
                    width: '56.25vw', // 根据需要调整，这里设置为屏幕宽度的56.25%
                    height: `${56.25 / ratio}vw`,
                    margin: '0 auto'
                };
            } else {
                // 横屏或方形视频
                return {
                    width: `${40 * ratio}vw`,
                    height: `40vw`,
                };
            }
        },

        // 添加获取视频容器类名的方法
        getVideoClass(url) {
			const urlArr = url.split('?videoRatio=')
            const ratio = urlArr.length > 1 ? urlArr[1] : null
            if (!ratio) return '';
            
            if (ratio > 1.2) return 'landscape';
            if (ratio < 0.8) return 'portrait';
            return 'square';
        },

        // 添加视频点击处理方法
        handleVideoClick(momentId) {
            const videoContext = uni.createVideoContext('video-' + momentId);
            if (this.isPlaying[momentId]) {
                videoContext.pause();
            } else {
                videoContext.play();
            }
        },

        setupVideoObserver() {
            // 创建 IntersectionObserver 实例
            this.videoObserver = uni.createIntersectionObserver(this);
            
            // 观察所有视频元素
            this.videoObserver.relativeTo('.container').observe('.moment-video', (res) => {
                const videoId = res.target.id.replace('video-', '');
                
                // 只有当视频完全进入视图时才自动播放
                if (res.intersectionRatio === 1) { // 修改这里，要求完全可见
                    // 自动播放视频
                    const videoContext = uni.createVideoContext(res.target.id);
                    if (videoContext && !this.isPlaying[videoId]) {
                        videoContext.play();
                    }
                } else {
                    // 视频不完全在可视区域时暂停
                    const videoContext = uni.createVideoContext(res.target.id);
                    if (videoContext && this.isPlaying[videoId]) {
                        videoContext.pause();
                    }
                }
            });
        },

        // 处理评论长按
        handleCommentLongpress(moment, comment) {
            // 检查是否是自己的评论
            const isMyComment = comment.sendUser?.userId === this.userInfo?.userId;
            const isMyMoment = moment.userInfo?.userId === this.userInfo?.userId;
            
            // 准备菜单项
            const menuItems = ['复制'];
            if (isMyComment || isMyMoment) {
                menuItems.push('删除');
            }
            
            // 显示操作菜单
            uni.showActionSheet({
                itemList: menuItems,
                success: async (res) => {
                    if (res.tapIndex === 0) {
                        // 复制评论内容
                        this.copyCommentContent(comment.content);
                    } else if (res.tapIndex === 1 && (isMyComment || isMyMoment)) {
                        // 删除评论
                        this.deleteComment(moment, comment);
                    }
                }
            });
        },
        
        // 复制评论内容
        copyCommentContent(content) {
            uni.setClipboardData({
                data: content,
                success: () => {
                    uni.showToast({
                        title: '内容已复制',
                        icon: 'success'
                    });
                }
            });
        },
        
        // 删除评论
        async deleteComment(moment, comment) {
            try {
                // 检查评论ID是否有效
                if (!comment.id) {
                    console.error('评论ID无效');
                    uni.showToast({
                        title: '操作失败',
                        icon: 'none'
                    });
                    return;
                }
                
                // 显示加载提示
                uni.showLoading({
                    title: '删除中...'
                });
                
                // 调用删除评论API
                const result = await appServerApi.deleteComment(comment.id);
                
                // 处理可能的"已删除"情况
                if (result?.data?.code === 500 && result?.data?.msg === "已删除") {
                    uni.hideLoading();
                    uni.showToast({
                        title: '评论已被删除',
                        icon: 'none'
                    });
                    
                    // 仍然从UI中移除该评论
                    this.removeCommentFromUI(moment, comment);
                    
                    // 刷新动态数据
                    this.refreshCurrentMoment(moment.id);
                    return;
                }
                
                // 从UI中移除该评论
                this.removeCommentFromUI(moment, comment);
                
                // 隐藏加载提示
                uni.hideLoading();
                
                // 显示删除成功提示
                uni.showToast({
                    title: '删除成功',
                    icon: 'success'
                });
                
                // 刷新动态数据
                this.refreshCurrentMoment(moment.id);
                
                // 更新缓存
                this.updateMomentsCache();
            } catch (error) {
                console.error('删除评论失败:', error);
                uni.hideLoading();
                uni.showToast({
                    title: '删除失败',
                    icon: 'none'
                });
                
                // 如果失败是因为评论已被删除，仍然尝试刷新
                if (error && error.message && error.message.includes('已删除')) {
                    this.refreshCurrentMoment(moment.id);
                }
            }
        },
        
        // 从UI中移除评论
        removeCommentFromUI(moment, comment) {
            if (moment.comments && moment.comments.result) {
                const index = moment.comments.result.findIndex(c => c.id === comment.id);
                if (index !== -1) {
                    moment.comments.result.splice(index, 1);
                }
            }
        },
        
        // 更新动态缓存
        updateMomentsCache() {
            try {
                // 更新缓存中的朋友圈数据
                setItem('moments_cache', this.momentsList);
                console.log('朋友圈缓存已更新');
            } catch (error) {
                console.error('更新缓存失败:', error);
            }
        },

        // 判断是否应该显示"全文"按钮
        shouldShowExpandButton(moment) {
            if (!moment.content) return false;
            
            // 优化判断逻辑:通过内容长度和可能的换行符判断
            const textLength = moment.content.length;
            const newlineCount = (moment.content.match(/\n/g) || []).length;
            
            // 当文本超过70个字符或有多个换行符时显示展开按钮
            return textLength > 70 || newlineCount > 2;
        },

        // 切换文本展开/折叠状态
        toggleExpandText(momentId, event) {
            // 阻止事件冒泡,防止触发其他点击事件
            if (event) {
                event.stopPropagation();
            }
            
            // 使用Vue的响应式方法更新状态
            this.$set(this.expandedTextMap, momentId, !this.expandedTextMap[momentId]);
        },

        // 添加获取视频对象适配方式的方法
        getVideoObjectFit(momentId) {
            const url = this.momentsList.find(m => m.id === momentId)?.url?.[0];
            if (!url) return 'contain';
            
            const urlArr = url.split('?videoRatio=');
            const ratio = urlArr.length > 1 ? urlArr[1] : null;
            
            if (!ratio) return 'contain';
            
            if (ratio > 1.2) return 'cover';
            if (ratio < 0.8) return 'contain';
            return 'cover';
        },

        // 添加页面滚动监听
        setupScrollListener() {
            // 延迟一会，确保页面加载完毕
            setTimeout(() => {
                // 创建节点查询对象
                this.scrollQuery = uni.createSelectorQuery().in(this);
                
                // 获取视口高度
                this.viewportHeight = uni.getSystemInfoSync().windowHeight;
                
                // 添加滚动事件的辅助功能
                this.addScrollHelper();
            }, 1000);
        },
        
        // 添加辅助滚动功能
        addScrollHelper() {
            // 创建一个滚动到底部检测器
            const checkIfReachedBottom = () => {
                uni.createSelectorQuery().selectViewport().scrollOffset().exec(res => {
                    if (!res || !res[0]) return;
                    
                    const { scrollTop, scrollHeight } = res[0];
                    
                    // 提前触发加载 - 当滚动到页面70%时就开始加载下一页
                    // 这样用户到达底部时数据已经加载好了
                    if (scrollTop + this.viewportHeight + 300 >= scrollHeight * 0.7) {
                        if (this.hasMore && !this.loading && !this.loadTriggered) {
                            this.loadTriggered = true;
                            
                            // 使用预加载数据或加载新数据
                            setTimeout(() => {
                                // 检查是否已经有预加载数据
                                const preloadedData = getItem('moments_preload_cache');
                                if (preloadedData && Array.isArray(preloadedData) && preloadedData.length > 0) {
                                    this.usePreloadedData();
                                } else {
                                    this.loadMore();
                                }
                                
                                // 1秒后重置触发标记
                                setTimeout(() => {
                                    this.loadTriggered = false;
                                }, 1000);
                            }, 100);
                        }
                    }
                });
            };
            
            // 监听页面滚动事件
            uni.$on('page-scroll', (e) => {
                // 优化性能 - 只在滚动距离大于一定值时检查
                if (this._scrollTimer) clearTimeout(this._scrollTimer);
                this._scrollTimer = setTimeout(() => {
                    checkIfReachedBottom();
                }, 100); // 减少延迟提高响应速度
            });
            
            // 定期检查，作为后备方案，更频繁检查
            this.bottomCheckInterval = setInterval(checkIfReachedBottom, 1000);
        },

        // 设置滚动检测定时器
        setupScrollDetection() {
            // 创建定时器，定期检查是否接近底部
            this.scrollDetectionTimer = setInterval(() => {
                const query = uni.createSelectorQuery().in(this);
                query.selectViewport()
                    .scrollOffset()
                    .exec((res) => {
                        if (!res || !res[0]) return;
                        
                        const { scrollTop, scrollHeight } = res[0];
                        const windowHeight = uni.getSystemInfoSync().windowHeight;
                        
                        // 判断是否接近底部（距离底部200px）
                        const isNearBottom = scrollTop + windowHeight + 200 >= scrollHeight;
                        
                        if (isNearBottom && this.hasMore && !this.loading) {
                            this.loadMore();
                        }
                    });
            }, 1000); // 每秒检查一次
        },
    },
    beforeDestroy() {
        // 清理视频观察器
        if (this.videoObserver) {
            this.videoObserver.disconnect();
        }
        
        // 清理所有定时器
        if (this.scrollDetectionTimer) {
            clearInterval(this.scrollDetectionTimer);
        }
        
        if (this.bottomCheckInterval) {
            clearInterval(this.bottomCheckInterval);
        }
        
        if (this._scrollTimer) {
            clearTimeout(this._scrollTimer);
        }
        
        if (this._reachBottomTimer) {
            clearTimeout(this._reachBottomTimer);
        }
        
        // 清理事件监听
        uni.$off('page-scroll');
    },
}
</script>

<style lang="scss" scoped>
.sight-page-root {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 88rpx; // 你实际的导航栏高度
  z-index: 100;
  background: #f8f8f8; // 或你的导航栏背景色
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
}

.nav-bar-inner {
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
}

.nav-tabs {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  gap: 32rpx;
}

.tab {
  font-size: 32rpx;
  color: #888;
  padding: 0 8rpx;
}
.tab.active {
  color: #386bf6;
  font-weight: bold;
  border-bottom: 4rpx solid #386bf6;
}

.nav-actions {
  display: flex;
  gap: 16rpx;
}

.moments-scroll {
  height: calc(100vh - 88rpx - 100rpx); // 88rpx为导航栏高度，100rpx为底部TabBar高度，可根据实际调整
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  background: #fff;
}

.moments-wrap {
    min-height: 100vh;
    background-color: #ffffff;
}

.waterfall-container {
  display: flex;
  flex-direction: row;
//   justify-content: center;
  align-items: flex-start;
  gap: 0px;
}
.waterfall-column {
  width: 50%;
  display: flex;
  flex-direction: column;
  gap: 0px;
}
.card {
  width: 100%;
  margin: 3px;
  border-radius: 8px;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
  background: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0 3px 3px 3px;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
  background-color: #fff;
  margin-bottom: 0rpx;
  padding: 3rpx;
  /* border-bottom: 3rpx solid #f0f0f0; 添加浅灰色分割线 */
}

.image-grid.single-image .grid-image,
.video-wrapper,
.video-wrapper.portrait,
.video-wrapper.landscape,
.video-wrapper.square {
  width: 100% !important;
  max-width: 100% !important;
  height: auto;
  border-radius: 6px;
  margin: 0 0 0px 0;
  display: block;
  object-fit: cover;
}

.text-content {
  width: 100%;
  margin-bottom: 0px;
  margin-top: 0;
  overflow: hidden;
  box-sizing: border-box;
}

.text-wrapper {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
  white-space: normal;
}

.text-wrapper.text-collapsed {
  max-height: 64rpx;
}

.user-info-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 0;
  margin-top: auto;
  box-sizing: border-box;
}

.user-meta-left {
  display: flex;
  align-items: center;
}

.displayName {
  font-size: 25rpx;
  color: #000000;
  line-height: 1.2;
  margin-left: 8rpx;
}

.like-section {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.like-count {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}

.avatar-container {
  margin-right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  flex-shrink: 0;
}

.avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
}

.content-wrapper {
  flex: 1;
  width: 100%;
  padding-top: 0 !important;
  margin-top: 0 !important;
  background: #ffffff;
  .text-content {
    margin-bottom: 12rpx;
  }
}

.text-content {
  width: 100%;
  margin-bottom: 12rpx;
  margin-top: 6rpx;
  position: relative;
  /* 确保内容可换行 */
}

.text-wrapper {
  overflow: visible;
  word-break: break-all;
  white-space: normal;
}

.text-wrapper.text-collapsed {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4; /* 限制显示4行 */
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 168rpx; /* 大约4行文本的高度(每行大约42rpx) */
}

.text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  word-break: break-all;
  white-space: normal;
}

.expand-btn-wrapper {
  text-align: right;
  margin-top: 4rpx;
}

.expand {
  font-size: 28rpx;
  color: #576b95;
  display: inline-block;
  padding: 4rpx 8rpx;
  cursor: pointer;
}

.expand:active {
  opacity: 0.8;
}

/* 图片网格基础样式 */
.image-grid {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 0;
  margin-bottom: 10rpx;
}

/* 基础图片样式 */
.grid-image {
  width: calc((100% - 16rpx) / 3);
  height: 220rpx;
  margin-right: 8rpx;
  margin-bottom: 8rpx;
  border-radius: 4rpx;
  object-fit: cover;
  
  &:nth-child(3n) {
    margin-right: 0;
  }
  
  &:nth-last-child(-n+3):nth-child(3n+1),
  &:nth-last-child(-n+3):nth-child(3n+1) ~ .grid-image {
    margin-bottom: 0;
  }
}

/* 单图样式 */
.image-grid.single-image {
  display: flex;
  justify-content: flex-start;
  .grid-image {
    width: 100%;
    max-width: 100%;
    height: auto;
    object-fit: unset;
    margin: 0;
    border-radius: 8rpx;
    background: #f8f8f8;
    display: block;
  }
  width: 100%;
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* 两张图片样式 */
.image-grid.double-images {
  .grid-image {
    width: calc((100% - 8rpx) / 3);
    height: 220rpx;
    margin-bottom: 0;
    
    &:nth-child(2) {
      margin-right: 0;
    }
  }
}

/* 评论弹窗 */
.comment-popup {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
}

.comment-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.comment-content {
  position: relative;
  background: #fff;
  padding: 20rpx;
display: flex;
  gap: 20rpx;
  align-items: center;
}

.comment-input {
  flex: 1;
  height: 72rpx;
  border: 1px solid #e4e4e4;
  border-radius: 36rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
}

.comment-submit {
  width: 120rpx;
  height: 72rpx;
  background: #386bf6;
  color: #fff;
  border-radius: 36rpx;
  font-size: 28rpx;
  line-height: 72rpx;
  text-align: center;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
  color: #999;
  width: 100%;
  margin-top: 10px;
}

.address {
  display: flex;
  align-items: center;
  gap: 3px;
  color: #576b95;
  width: 70%;
  /*white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis; */
}

.address-inner {
  width: 88%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 添加加载提示样式 */
.loading {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 24rpx;
}

.loading-status {
  padding: 20rpx 0;
  text-align: center;
color: #999;
  font-size: 24rpx;
}

.loading,
.no-more,
.empty {
  padding: 20rpx;
}

.load-more {
  color: #386bf6;
  font-size: 28rpx;
  background-color: rgba(56, 107, 246, 0.08);
  border-radius: 8rpx;
  margin: 0 20rpx;
  text-align: center;
  line-height: 80rpx;
  height: 80rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.load-more:active {
  background-color: rgba(56, 107, 246, 0.15);
}

/* 调整容器样式以支持滚动 */
.container {
  height: 100vh;
}

/* 调整卡片样式，移除最后一个卡片的底部边距 */
.card:last-child {
  margin-bottom: 0;
}

/* 确保每行最多三张图片 */
.image-grid > .grid-image:nth-last-child(-n + 3) {
  margin-bottom: 0;
}

/* 移除之前的计算宽度样式 */
.grid-image {
  margin-right: 6rpx;
  margin-bottom: 6rpx;
}

/* 每行最后一个元素移除右边距 */
.grid-image:nth-child(3n) {
  margin-right: 0;
}

/* 最后一行元素移除底部边距 */
.grid-image:nth-last-child(-n + 3) {
  margin-bottom: 0;
}

.header-bar {
  position: absolute;
  top: 44px;
  /* 状态栏高度 */
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
}

.title {
  font-size: 17px;
  color: #fff;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.camera-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.camera-icon image {
  width: 20px;
  height: 20px;
}

.camera-icon:active {
  opacity: 0.8;
}

.location-wrap {
  display: flex;
  align-items: center;
}

.location-text {
  color: #576b95;
  font-size: 24rpx;
  width: 70%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.interaction-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 4rpx 0;
  padding-left: 96rpx;
  margin-top: -8rpx;

  .right-actions {
    display: flex;
    flex-direction: row;
    align-items: center;

    .action-item {
      padding: 0 8rpx;
      display: flex;
      align-items: center;
    }

    .action-item + .action-item {
      margin-left: 16rpx;
    }
  }
}

.comments-section {
  background-color: #ffffff;
  padding: 6rpx; //评论区域的内边距和上边距
  margin-top: 6rpx;
  width: calc(100% - 96rpx);
  border-radius: 8rpx;
  margin-left: 96rpx;

  .comment-item {
    margin-bottom: 12rpx;
    display: flex;
    white-space: nowrap;

    .comment-user {
      font-size: 26rpx;
      color: #2d68e7;
      font-weight: 500;
      flex-shrink: 0;
      
      text {
        display: inline-block;
        
        &:active {
          opacity: 0.8;
          text-decoration: underline;
        }
      }
    }

    .comment-content2 {
      font-size: 26rpx;
      color: #333;
      word-break: break-all;
    }
  }
}

/* 评论输入框样式 */
.comment-input-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.comment-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
}

.comment-input-box {
  position: relative;
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background-color: #f8f8f8;
  border-top: 1px solid #eee;
}

.comment-input {
  flex: 1;
  height: 72rpx;
  padding: 0 24rpx;
  background-color: #fff;
  border-radius: 36rpx;
  font-size: 28rpx;
}

.send-btn {
  margin-left: 24rpx;
  padding: 0 32rpx;
  height: 72rpx;
  line-height: 72rpx;
  font-size: 28rpx;
color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn.active {
  color: #386bf6;
  cursor: pointer;
}

/* 点赞列表样式 */
.likes-section {
  padding: 4rpx 0; //点赞区域的上下内边距
}
.likes-content {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding-left: 96rpx;
}

.like-users {
  color: #576b95;
  font-size: 26rpx;
  line-height: 1.4;
}

.user-info-container {
  .avatar-container {
    cursor: pointer;

    &:active {
      opacity: 0.8;
    }
  }

  .displayName {
    cursor: pointer;

    &:active {
      opacity: 0.8;
    }
  }
}

/* 确保没有任何加载动画显示 */
.uni-refresh {
  display: none !important;
}

.uni-refresh-cover {
  display: none !important;
}

.change-cover-btn {
  position: absolute;
  right: 30rpx;
  bottom: 30rpx;
  z-index: 3;
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 30rpx;
  
  text {
    color: #fff;
    font-size: 28rpx;
  }
}

.new-notices {
  position: relative;
  z-index: 100;
  display: flex;
  align-items: center;
  width: 50%;
  margin: 0 auto;
  margin-top: 100rpx;
  padding: 12rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  
  .notice-avatar {
    width: 64rpx;
    height: 64rpx;
    border-radius: 8rpx;
    margin-right: 20rpx;
    border: 2rpx solid rgba(255, 255, 255, 0.2);
  }
  
  .notice-text {
    flex: 1;
    color: #fff;
    font-size: 28rpx;
    font-weight: 500;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .uni-icons {
    margin-left: 12rpx;
    color: rgba(255, 255, 255, 0.8) !important;
  }

  &:active {
    opacity: 0.9;
    transform: scale(0.98);
  }
}

.nav-title {
  color: #000;
  font-size: 32rpx;
  font-weight: 500;
  background: transparent;
}

/* 视频相关样式 */
.video-wrapper {
  width: 100%;
  margin: 12rpx 0;
  background: #f8f8f8;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.moment-video {
  display: block;
  width: 100%;
  background: transparent;
  border-radius: 8rpx;
  
  /* 移除全屏按钮 */
  &::-webkit-media-controls-fullscreen-button {
    display: none;
  }
}

/* 竖屏视频容器样式 */
.video-wrapper.portrait {
  padding: 0;
  display: flex;
  background: transparent;
  width: 50vw;
  max-width: 50vw;
  margin: 0 auto;
}
.video-wrapper.portrait .moment-video {
  width: 100%;
  max-width: 100%;
  max-height: 80vh;
  margin: 0 auto;
}

/* 横屏视频容器样式 */
.video-wrapper.landscape {
  width: 50vw;
  max-width: 50vw;
  background: transparent;
  margin: 0 auto;
}
.video-wrapper.landscape .moment-video {
  width: 100%;
  max-width: 100%;
  max-height: 56.25vw;
}

/* 方形视频容器样式 */
.video-wrapper.square {
  width: 50vw;
  max-width: 50vw;
  background: transparent;
  margin: 0 auto;
}
.video-wrapper.square .moment-video {
  width: 100%;
  max-width: 100%;
  height: 50vw;
  max-height: 750rpx;
}

/* 确保评论弹窗在最上层 */
.comment-popup {
  z-index: 999;
}

/* 添加白色分隔条 */
.white-divider {
  height: 60rpx;
  background-color: #ffffff;
  margin: 20rpx 0;
}

.image-grid.single-image + .text-content {
  width: 50vw;
  max-width: 50vw;
  margin: 0;
  margin-top: 8rpx;
}

.card-main-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.card-text-content {
  padding-left: 16rpx;
  padding-right: 16rpx;
  box-sizing: border-box;
}
.card-info-row {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-top: 8rpx;
  box-sizing: border-box;
}
.card-user-meta-left {
  display: flex;
  align-items: center;
  margin-left: 16rpx;
}
.card-like-section {
  display: flex;
  align-items: center;
  margin-right: 16rpx;
}
</style>