<template>
  <view class="web-container">
    <CustomHeader>
      <template v-slot:title>
        <text class="web-title">{{ title }}</text>
      </template>
    </CustomHeader>
    
    <web-view 
      :webview-styles="webviewStyles"
      :src="url" 
      @message="handleMessage"
      @loaded="handleLoad"
      @error="handleError"
    ></web-view>
    
    <view class="loading-wrap" v-if="isLoading">
      <view class="loading-spinner"></view>
    </view>
  </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'

export default {
  components: {
    CustomHeader
  },
  data() {
    return {
      url: '',
      title: '网页',
      isLoading: true,
      webviewStyles: {
        progress: {
          color: '#386BF6'
        }
      }
    }
  },
  onLoad(options) {
    if (options.url) {
      this.url = decodeURIComponent(options.url)
      // 尝试从 URL 中提取标题
      try {
        const urlObj = new URL(this.url)
        this.title = urlObj.hostname
      } catch (error) {
        console.error('解析 URL 失败:', error)
      }
    }
  },
  methods: {
    handleMessage(event) {
      console.log('Received message:', event)
    },
    handleLoad() {
      this.isLoading = false
    },
    handleError() {
      this.isLoading = false
      uni.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.web-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
}

.web-title {
  font-size: 16px;
  color: #333;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.loading-wrap {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  z-index: 999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #386BF6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 