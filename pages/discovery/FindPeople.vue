<template>
  <view class="page">
    <!-- 顶部导航 -->
    <view class="header">
      <text class="tab active">找人</text>
      <text class="tab">找群</text>
      <text class="title">附近</text>
      <text class="filter" @click="openFilter">筛选</text>
    </view>

    <!-- 用户列表 -->
    <view class="user-list">
      <view class="user-item" v-for="(user, index) in users" :key="index">
        <image class="avatar" :src="user.avatar" mode="aspectFill" />
        <view class="user-info">
          <view class="user-name">
            <text>{{ user.name }}</text>
            <text class="vip-tag">总经理</text>
          </view>
          <text class="company">{{ user.company }}</text>
          <text class="status">熟：{{ user.status }}</text>
        </view>
      </view>
    </view>

    <!-- 筛选弹出框 -->
    <view class="filter-modal" v-if="showFilter">
      <view class="filter-options">
        <text class="filter-item" @click="filterBy('female')">只看女生</text>
        <text class="filter-item" @click="filterBy('male')">只看男生</text>
        <text class="filter-item" @click="filterBy('all')">查看全部</text>
        <text class="filter-item" @click="clearLocation">清除位置并退出</text>
        <text class="cancel" @click="closeFilter">取消</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showFilter: false,
      users: [
        {
          avatar: "/static/avatar.jpg",
          name: "李碧宝",
          company: "浙江盛字科技有限公司",
          status: "这个朋友",
        },
        {
          avatar: "/static/avatar.jpg",
          name: "李碧宝",
          company: "浙江盛字科技有限公司",
          status: "这个朋友",
        },
        // 可继续添加更多用户数据
      ],
    };
  },
  methods: {
    openFilter() {
      this.showFilter = true;
    },
    closeFilter() {
      this.showFilter = false;
    },
    filterBy(type) {
      this.showFilter = false;
      // 在这里添加筛选逻辑，例如根据性别筛选用户
      console.log(`筛选条件: ${type}`);
    },
    clearLocation() {
      this.showFilter = false;
      console.log("清除位置并退出");
      // 在这里实现清除位置逻辑
    },
  },
};
</script>

<style scoped>
.page {
  background-color: #f5f5f5;
  height: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #ddd;
  background-color: #fff;
}

.tab {
  font-size: 28rpx;
  color: #888;
}

.tab.active {
  color: #000;
  font-weight: bold;
}

.title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.filter {
  font-size: 28rpx;
  color: #888;
}

.user-list {
  padding: 20rpx;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.user-name text {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.vip-tag {
  font-size: 24rpx;
  color: #fff;
  background-color: #74c0fc;
  border-radius: 5rpx;
  padding: 2rpx 10rpx;
  margin-left: 10rpx;
}

.company {
  font-size: 26rpx;
  color: #888;
}

.status {
  font-size: 26rpx;
  color: #aaa;
}

.filter-modal {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 10rpx 10rpx 0 0;
  box-shadow: 0 -2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.filter-options {
  padding: 20rpx;
}

.filter-item {
  font-size: 30rpx;
  color: #333;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-item:last-child {
  border-bottom: none;
}

.cancel {
  font-size: 30rpx;
  color: #ff4d4f;
  text-align: center;
  padding: 20rpx 0;
}
</style>