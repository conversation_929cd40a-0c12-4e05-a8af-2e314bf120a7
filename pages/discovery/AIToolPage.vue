<template>
  <view class="container">
    <CustomHeader>
      <template v-slot:title>
        <text>新年AI工具</text>
      </template>
    </CustomHeader>

    <view class="tools-list">
      <view class="tool-item" v-for="(tool, index) in tools" :key="index" @click="goToTool(tool)">
        <view class="tool-icon">
          <uni-icons :type="tool.icon" size="30" color="#386bf6"></uni-icons>
        </view>
        <view class="tool-info">
          <text class="tool-name">{{ tool.name }}</text>
          <text class="tool-desc">{{ tool.description }}</text>
        </view>
        <uni-icons type="right" size="16" color="#999"></uni-icons>
      </view>
    </view>
  </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'

export default {
  components: {
    CustomHeader
  },
  data() {
    return {
      tools: [
        {
          id: 'chat',
          name: 'AI聊天',
          description: '智能助手，为您解答各种问题',
          icon: 'chat',
          path: '/pages/discovery/AIChat'
        },
        {
          id: 'couplets',
          name: 'AI春联',
          description: '生成新年春联，喜庆祥和',
          icon: 'compose',
          path: '/pages/discovery/AICouplets'
        },
        {
          id: 'blessing',
          name: 'AI祝福语',
          description: '生成温馨祝福，传递美好心意',
          icon: 'heart',
          path: '/pages/discovery/AIBlessing'
        },
        {
          id: 'joke',
          name: 'AI笑话',
          description: '智能生成笑话，轻松解压',
          icon: 'calendar',
          path: '/pages/discovery/AIJoke'
        },
        {
          id: 'redPacket',
          name: 'AI红包',
          description: '智能生成红包祝福语，喜气洋洋',
          icon: 'gift',
          path: '/pages/discovery/AIRedPacket'
        }
      ]
    }
  },
  methods: {
    goToTool(tool) {
      uni.navigateTo({
        url: tool.path
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: #f7f7f7;
}

.tools-list {
  padding: 20rpx;
}

.tool-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  
  &:active {
    opacity: 0.7;
  }
}

.tool-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(56, 107, 246, 0.1);
  border-radius: 16rpx;
  margin-right: 20rpx;
}

.tool-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.tool-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.tool-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.2;
}
</style> 