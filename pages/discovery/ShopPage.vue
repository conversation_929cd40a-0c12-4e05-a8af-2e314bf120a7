<template>
  <view class="container">
    <view class="status_bar" :style="{ height: statusBarHeight + 'px' }">
      <text class="debug-text">{{statusBarHeight}}px</text>
    </view>
    <view class="shop-container">
      <view class="web-view-container">
        <web-view 
          :webview-styles="webviewStyles"
          :src="url" 
          @message="handleMessage"
          @onPostMessage="handlePostMessage"
          @onLoad="handleLoad"
          @onError="handleError"
        ></web-view>
      </view>
      <view class="loading-wrap" v-if="isLoading">
        <view class="loading-spinner"></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 0,
      isLoading: true,
      url: 'http://*************:8383/',
      webviewStyles: {
        progress: {
          color: '#386BF6'
        }
      }
    }
  },
  created() {
    // 在组件创建时就获取状态栏高度
    const sysInfo = uni.getSystemInfoSync()
    this.statusBarHeight = sysInfo.statusBarHeight
    console.log('状态栏高度:', this.statusBarHeight)
  },
  onLoad() {
    // 隐藏默认导航栏
    uni.hideNavigationBarLoading()
    
    // 设置状态栏样式
    uni.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#ffffff'
    })
  },
  methods: {
    handleLoad() {
      this.isLoading = false
    },
    handleError() {
      this.isLoading = false
      uni.showToast({
        title: '加载失败',
        icon: 'none'
      })
    },
    // 处理 H5 发送的消息
    handleMessage(event) {
      console.log('Received message from H5:', event.detail)
      
      // 根据消息类型处理不同的业务逻辑
      const { type, data } = event.detail
      
      switch(type) {
        case 'login':
          // 处理登录请求
          this.handleLogin(data)
          break
          
        case 'share':
          // 处理分享请求
          this.handleShare(data)
          break
          
        case 'close':
          // 处理关闭请求
          uni.navigateBack()
          break
          
        default:
          console.log('Unknown message type:', type)
      }
    },
    
    // 处理 PostMessage
    handlePostMessage(event) {
      console.log('Received post message:', event.detail)
    },
    
    // 处理登录请求
    handleLogin(data) {
      const token = uni.getStorageSync('token')
      if (token) {
        // 向 H5 发送 token
        const webview = this.$mp.page.$getAppWebview()
        webview.evalJS(`
          window.postMessage({
            type: 'login',
            data: {
              token: '${token}'
            }
          }, '*')
        `)
      } else {
        // 跳转到登录页面
        uni.navigateTo({
          url: '/pages/login/LoginPage'
        })
      }
    },
    
    // 处理分享请求
    handleShare(data) {
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.status_bar {
  width: 100%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: sticky;
  top: 0;
  z-index: 100;
}

.debug-text {
  font-size: 12px;
  color: #999;
}

.shop-container {
  position: relative;
  flex: 1;
  width: 100%;
  height: calc(100vh - var(--status-bar-height, 0px));
  background-color: #ffffff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.web-view-container {
  flex: 1;
  width: 100%;
  height: 100%;
  position: relative;
}

.loading-wrap {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  z-index: 999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #386BF6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

web-view {
  width: 100%;
  height: 100%;
}
</style>
