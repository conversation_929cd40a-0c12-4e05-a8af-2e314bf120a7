<template>
  <view class="status_bar">
    <!-- 这里是状态栏 -->
  </view>
  <view class="shop-container">
    <web-view 
      :webview-styles="webviewStyles"
      :src="url" 
      @message="handleMessage"
      @onPostMessage="handlePostMessage"
    ></web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      url: 'http://*************:8484/',
      webviewStyles: {
        progress: {
          color: '#386BF6' // 设置进度条颜色，使用项目主色调
        }
      }
    }
  },
  onLoad() {
    // 隐藏默认导航栏
    uni.hideNavigationBarLoading()
    
    // 设置状态栏样式
    uni.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#ffffff'
    })
  },
  methods: {
    // 处理 H5 发送的消息
    handleMessage(event) {
      console.log('Received message from H5:', event.detail)
      
      // 根据消息类型处理不同的业务逻辑
      const { type, data } = event.detail
      
      switch(type) {
        case 'login':
          // 处理登录请求
          this.handleLogin(data)
          break
          
        case 'share':
          // 处理分享请求
          this.handleShare(data)
          break
          
        case 'close':
          // 处理关闭请求
          uni.navigateBack()
          break
          
        default:
          console.log('Unknown message type:', type)
      }
    },
    
    // 处理 PostMessage
    handlePostMessage(event) {
      console.log('Received post message:', event.detail)
    },
    
    // 处理登录请求
    handleLogin(data) {
      const token = uni.getStorageSync('token')
      if (token) {
        // 向 H5 发送 token
        const webview = this.$mp.page.$getAppWebview()
        webview.evalJS(`
          window.postMessage({
            type: 'login',
            data: {
              token: '${token}'
            }
          }, '*')
        `)
      } else {
        // 跳转到登录页面
        uni.navigateTo({
          url: '/pages/login/LoginPage'
        })
      }
    },
    
    // 处理分享请求
    handleShare(data) {
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.shop-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
}
.status_bar {
  height: var(--status-bar-height);
}
</style>
