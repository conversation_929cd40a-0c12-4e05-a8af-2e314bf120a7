<template>
  <view class="container">
    <view class="group-list">
      <view class="group-item" v-for="(group, index) in groups" :key="index">
        <image class="group-icon" src="/static/group-icon.png" mode="aspectFill"></image>
        <view class="group-info">
          <text class="group-name">{{ group.name }}</text>
          <text class="group-description">{{ group.description }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      groups: [
        { name: "面料布艺交流群 Te", description: "RA REE" },
        { name: "面料布艺交流群 “工艺", description: "Ley nee ex" },
        { name: "面料布艺交流群 Te", description: "Ley pee wx" },
        { name: "面料布艺交流群 Te", description: "Ley HM ex" }
      ]
    };
  }
};
</script>

<style scoped>
.container {
  padding: 20px;
  background-color: #f5f5f5;
}

.group-list {
  display: flex;
  flex-direction: column;
}

.group-item {
  display: flex;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.group-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
}

.group-info {
  display: flex;
  flex-direction: column;
}

.group-name {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.group-description {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}
</style>