<template>
  <scroll-view class="discovery-container" scroll-y>
    <view class="conversation-rq">
      <div class="item" @click="showMoments">
        <image src="/static/image/icon/dt.png"/>
        <text>发现</text>
        <text v-if="momentNotices.length > 0" class="notice-badge">{{ momentNotices.length }}</text>
        <i class="icon-ion-ios-arrow-right"></i>
      </div>
    </view>
    <view class="conversation-rq">
      <div class="item" @click="handleScan">
        <image src="/static/image/icon/yqhy.png"/>
        <text>扫一扫</text>
        <i class="icon-ion-ios-arrow-right"></i>
      </div>
    </view>
    <!-- 附近的人功能暂时隐藏
    <view class="conversation-rq">
      <div class="item" @click="toNearbyPeople">
        <image src="/static/image/icon/fjr.png"/>
        <text>附近的人</text>
        <i class="icon-ion-ios-arrow-right"></i>
      </div>
    </view>
    -->
    <template v-if="featureConfig.shop">
      <view class="conversation-rq">
        <div class="item" @click="showShop">
          <image src="/static/image/icon/sp.png"/>
          <text>商城</text>
          <i class="icon-ion-ios-arrow-right"></i>
        </div>
        <view class="rq-hine"></view>
        <div class="item" @click="showCommunity">
          <image src="/static/image/icon/sq.png"/>
          <text>社区</text>
          <i class="icon-ion-ios-arrow-right"></i>
        </div>
      </view>
    </template>
    <template v-if="false">
      <view class="conversation-rq">
        <div v-if="false" class="item" @click="showNews">
          <image src="/static/image/icon/ic_channel_1.png"/>
          <text>羽克</text>
          <i class="icon-ion-ios-arrow-right"></i>
        </div>
        <view class="rq-hine"></view>
        <div v-if="false" class="item" @click="showAI">
          <image src="/static/image/icon/grxxyqx.png"/>
          <text>新年AI工具</text>
          <i class="icon-ion-ios-arrow-right"></i>
        </div>
      </view>
    </template>
  </scroll-view>
</template>

<script>
import store from "../../store";
import Conversation from "../../wfc/model/conversation";
import ConversationType from "../../wfc/model/conversationType";
import {getItem, setItem} from '../util/storageHelper';
import appServerApi from "@/api/appServerApi";
import topMessage from '@/common/topMessageView'

export default {
  name: "DiscoveryPage",
  data() {
    return {
      user: store.state.contact.selfUserInfo,
      featureConfig: {
        shop: false,
        news: true,
      },
      momentNotices: []
    }
  },
  onReady() {
    //动态修改状态栏的颜色
    uni.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#F5F5F5',
    })
  },
  onLoad() {

  },
  onShow() {
    // 从 store 获取通知数据
    this.momentNotices = getItem('setMomentNotices') || []
    this.checkMomentNotice()
  },
  methods: {
    checkMomentNotice() {
      // 先获取缓存的通知数据
      const cachedNotices = getItem('setMomentNotices') || []
      if (cachedNotices.length > 0) {
        // 更新角标
        uni.setTabBarBadge({
          index: 3,
          text: '' + cachedNotices.length
        })
      } else {
        // 更新角标
        uni.removeTabBarBadge({
          index: 3
        })
      }
      appServerApi.getMomentNotice().then(result => {
        // console.log('动态提醒 result', result)
        if (result && result.data && Array.isArray(result.data) && result.data.length > 0) {
          // 合并新旧数据，去重
          const newNotices = result.data
          const mergedNotices = this.mergeNotices(cachedNotices, newNotices)

          // 将合并后的数据存储到 store 中
          setItem('setMomentNotices', mergedNotices)

          // 更新角标
          uni.setTabBarBadge({
            index: 3,
            text: '' + mergedNotices.length
          })
        }
      }).catch(error => {
        console.error('获取动态提醒失败:', error)
      })
    },

    // 添加合并通知的方法
    mergeNotices(oldNotices, newNotices) {
      // 创建一个Map用于去重，key为通知的唯一标识
      const noticeMap = new Map()

      // 先添加旧的通知
      oldNotices.forEach(notice => {
        // 使用 momentId + type + fromUserId 作为唯一标识
        const key = `${notice.momentId}-${notice.type}-${notice.fromUserId}`
        noticeMap.set(key, notice)
      })

      // 添加新的通知，如果有相同的会覆盖旧的
      newNotices.forEach(notice => {
        const key = `${notice.momentId}-${notice.type}-${notice.fromUserId}`
        noticeMap.set(key, notice)
      })

      // 将Map转换回数组，并按时间倒序排序（最新的在前面）
      return Array.from(noticeMap.values()).sort((a, b) => {
        return new Date(b.createTime) - new Date(a.createTime)
      })
    },

    async handleScan() {
      console.log('handleScan')
      /*#ifdef APP-PLUS*/
      if (plus.os.name !== 'iOS') {
          var isPermission = plus.navigator.checkPermission(
              'android.permission.CAMERA'
          )
          if (isPermission != 'authorized') {
              topMessage.createTopMessage(
                  '相机、相册权限使用说明',
                  '用于扫描二维码、条形码等，以便快速访问相关内容或服务。我们不会将相机用于其他用途。'
              )
          }
          let res = await topMessage.requestPermissions(
              'CAMERA',
              '相机权限未获得，此权限用于扫码功能，请前往设置中打开'
          )
          setTimeout(() => {
              topMessage.hideTopMessage()
          }, 300)
          if (!res.granted[0]) {
              // 无权限
              return
          }
      }
      /*#endif*/

      uni.scanCode({
        success: (res) => {
          console.log('扫码结果：', res)
          const scanResult = res.result

          // 处理 URL 类型
          if (scanResult.startsWith('http://') || scanResult.startsWith('https://')) {
            // 新增：处理贴吧URL格式
            if (scanResult.includes('bar.html?BarID=')) {
                const barIdPart = scanResult.split('BarID=')[1];
                const barId = barIdPart.split('&')[0]; // 处理可能的额外URL参数
                
                console.log('扫描到贴吧链接, 贴吧ID:', barId);
                uni.navigateTo({
                    url: `/pages/bar/BarHome?id=${barId}`,
                    fail: (err) => {
                        console.error('跳转到贴吧主页失败:', err);
                        uni.showToast({
                            title: '跳转贴吧失败',
                            icon: 'none'
                        });
                    }
                });
                return; // 处理完毕
            }

            // 处理新的群组URL格式
            if (scanResult.includes('?GroupID=')) {
              const groupIdPart = scanResult.split('GroupID=')[1];
              // 处理可能的额外URL参数
              const groupId = groupIdPart.split('&')[0];
              
              console.log('扫描到群组链接, 群ID:', groupId);
              // 跳转到群组详情页面
              uni.navigateTo({
                url: `/pages/group/GroupDetailPage?groupId=${groupId}`,
                fail: (err) => {
                  console.error('跳转失败:', err);
                  uni.showToast({
                    title: '跳转失败',
                    icon: 'none'
                  });
                }
              });
              return;
            }

            // 处理新的用户URL格式
            if (scanResult.includes('?UserID=')) {
              const userIdPart = scanResult.split('UserID=')[1]
              // 处理可能的额外URL参数
              const userId = userIdPart.split('&')[0]
              
              console.log('扫描到用户链接, 用户ID:', userId)
              // 跳转到用户详情页面
              uni.navigateTo({
                url: `/pages/contact/UserDetailPage?userId=${userId}`,
                fail: (err) => {
                  console.error('跳转失败:', err)
                  uni.showToast({
                    title: '跳转失败',
                    icon: 'none'
                  })
                }
              })
              return
            }

            // 其他网址直接跳转到WebPage
            uni.navigateTo({
              url: `/pages/misc/WebPage?url=${encodeURIComponent(scanResult)}`,
              fail: (err) => {
                console.error('跳转失败:', err)
                uni.showToast({
                  title: '跳转失败',
                  icon: 'none'
                })
              }
            })
            return
          }

          // 处理地球号
          if (scanResult.startsWith('EARTHID:')) {
            const userId = scanResult.substring(8) // 截取 EARTHID: 后面的内容
            uni.navigateTo({
              url: `/pages/contact/UserDetailPage?userId=${userId}`,
              fail: (err) => {
                console.error('跳转失败:', err)
                uni.showToast({
                  title: '跳转失败',
                  icon: 'none'
                })
              }
            })
            return
          }

          // 处理其他字符串
          uni.navigateTo({
            url: `/pages/misc/StringPage?content=${encodeURIComponent(scanResult)}`,
            fail: (err) => {
              console.error('跳转失败:', err)
              uni.showToast({
                title: '跳转失败',
                icon: 'none'
              })
            }
          })
        },
        fail: (err) => {
          console.error('扫码失败:', err)
        }
      })
    },
    showMoments() {
      // 检查是否已有缓存数据
      const cachedMoments = getItem('moments_cache');
      
      // 如果没有缓存，提前预加载数据
      if (!cachedMoments || !Array.isArray(cachedMoments) || cachedMoments.length === 0) {
        // 静默加载第一页数据
        appServerApi.getMomentList({
          pageNo: 1,
          pageSize: 20,
        }).then(response => {
          if (response?.data?.result) {
            // 保存到缓存
            setItem('moments_cache', response.data.result)
          }
        }).catch(error => {
          console.error('预加载动态数据失败:', error)
        });
      }
      
      uni.navigateTo({
        url: '/pages/moments/Moments',
        fail: (e) => {
          console.log(e)
        }
      });
    },
    showRobotConversation() {
      let conversation = new Conversation(ConversationType.Single, "FireRobot", 0);
      store.setCurrentConversation(conversation);
      this.$go2ConversationPage();
    },
    showChannelList() {
      uni.navigateTo({
        url: '/pages/contact/ChannelListPage',
        fail: (e) => {
          console.log(e)
        }
      });
    },
    showShop() {
      uni.navigateTo({
        url: '/pages/discovery/ShopPage',
        fail: (e) => {
          console.log(e)
        }
      });
    },
    showCommunity() {
      uni.navigateTo({
        url: '/pages/discovery/CommunityPage',
        fail: (e) => {
          console.log(e)
        }
      });
    },
    showAI() {
      uni.navigateTo({
        url: '/pages/discovery/AIToolPage',
        fail: (e) => {
          console.log(e)
        }
      });
    },
    showNews() {
      uni.navigateTo({
        url: '/pages/discovery/NewsPage',
        fail: (e) => {
          console.log(e)
        }
      });
    },
    showVideo() {
      uni.navigateTo({
        url: '/pages/discovery/VideoPage',
        fail: (e) => {
          console.log(e)
        }
      });
    },
    showGame() {
      uni.navigateTo({
        url: '/pages/discovery/GamePage',
        fail: (e) => {
          console.log(e)
        }
      });
    },
    toNearbyPeople() {
      uni.navigateTo({
        url: '/pages/nearby/NearbyPerple'
      })
    },
    toNearbyGroup() {
      uni.navigateTo({
        url: '/pages/nearby/NearbyGroup'
      })
    },
  }
}
</script>

<style scoped>
.discovery-container {
  width: 100%;
  height: var(--page-full-height-without-header-and-tabbar);
  background: #F5F5F5;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.hidden {
  display: none;
}

.item {
  padding: 10px 10px;
  margin: 0px 0;
  display: flex;
  align-items: center;
  position: relative;
}

.item image {
  max-width: 38px;
  max-height: 38px;
  margin-right: 16px;
  border-radius: 8px;
}

.item text {
  flex: 1;
}

.item i {
  color: #000000;
}

.item:active {
  background: #d6d6d6;
}

.conversation-rq:last-child {
  margin-bottom: 20px;
}

.notice-badge {
  background-color: #ff4d4f;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 12px;
  position: absolute;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
}
</style>