<template>
  <view class="container">
    <CustomHeader>
      <template v-slot:title>
        <text>第三方信息共享清单</text>
      </template>
    </CustomHeader>

    <view class="content">
      <view class="info-section">
        <text class="section-title">信息共享方</text>
        <view class="share-item" v-for="(item, index) in shareList" :key="index">
          <text class="company-name">{{ item.name }}</text>
          <text class="share-purpose">用途：{{ item.purpose }}</text>
          <text class="share-content">共享内容：{{ item.content }}</text>
        </view>
      </view>

      <view class="notice-section">
        <text class="notice-title">重要说明</text>
        <text class="notice-content">
          1. 我们仅会在必要的范围内共享您的信息
          2. 所有第三方都需要遵守我们的隐私保护要求
          3. 您可以随时查看或撤销授权
        </text>
      </view>
    </view>
  </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'


export default {
  components: {
    CustomHeader
  },
  data() {
    return {
      shareList: [
        {
          name: '支付服务商',
          purpose: '处理支付业务',
          content: '订单信息、支付金额'
        },
        {
          name: '云服务提供商',
          purpose: '提供云存储服务',
          content: '用户上传的图片和文件'
        }
        // {
        //   name: '广告合作伙伴',
        //   purpose: '提供广告服务',
        //   content: '设备信息、广告标识符'
        // }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.content {
  padding: 12px;
}

.info-section {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 10px;
  background: #fff;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    display: block;
  }
}

.share-item {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;

  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }

  .company-name {
    font-size: 15px;
    color: #333;
    margin-bottom: 8px;
    display: block;
  }

  .share-purpose,
  .share-content {
    font-size: 14px;
    color: #999;
    line-height: 1.5;
    margin-bottom: 5px;
    display: block;
  }
}

.notice-section {
  padding: 15px;
  border-radius: 10px;
  background: #fff;

  .notice-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    display: block;
  }

  .notice-content {
    font-size: 14px;
    color: #999;
    line-height: 1.8;
    white-space: pre-line;
    display: block;
  }
}
</style> 