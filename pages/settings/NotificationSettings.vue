<template>
  <view class="container">
    <CustomHeader>
      <template v-slot:title>
        <text>新消息通知</text>
      </template>
    </CustomHeader>

    <view class="content">
      <view class="section">
        <view class="item">
          <text class="label">接收新消息通知</text>
          <switch :checked="settings.receiveNotifications" @change="handleMainSwitch" color="#386BF6" />
        </view>
        <view class="item" v-if="settings.receiveNotifications">
          <text class="label">请求通知权限</text>
          <button class="permission-btn" @click="requestNotificationPermission">{{ hasPermission ? '已授权' : '请求授权' }}</button>
        </view>
      </view>

      <view class="section" v-if="settings.receiveNotifications">
        <view class="item">
          <view class="label-container">
            <text class="label">声音</text>
            <text class="permission-status" v-if="!soundPermissionStatus">权限未授权</text>
          </view>
          <switch :checked="settings.sound && soundPermissionStatus" @change="handleSoundSwitch" color="#386BF6" :disabled="!soundPermissionStatus" />
        </view>
        <view class="item">
          <view class="label-container">
            <text class="label">振动</text>
            <text class="permission-status" v-if="!vibratePermissionStatus">权限未授权</text>
          </view>
          <switch :checked="settings.vibrate && vibratePermissionStatus" @change="handleVibrateSwitch" color="#386BF6" :disabled="!vibratePermissionStatus" />
        </view>
        <view class="item">
          <text class="label">桌面图标角标</text>
          <switch :checked="settings.badge" @change="handleBadgeSwitch" color="#386BF6" />
        </view>
        <view class="item bordernone">
          <text class="label">横幅通知</text>
          <switch :checked="settings.banner" @change="handleBannerSwitch" color="#386BF6" />
        </view>
      </view>

      <view class="section" v-if="settings.receiveNotifications">
        <view class="item bordernone">
          <text class="label">系统通知</text>
          <switch :checked="settings.systemNotice" @change="handleSystemNoticeSwitch" color="#386BF6" />
        </view>
      </view>

      <view class="tip-section" v-if="settings.receiveNotifications">
        <text class="tip-text">提示：如果无法收到通知，请检查系统设置中的通知权限是否已开启</text>
        <text class="tip-text" v-if="!soundPermissionStatus || !vibratePermissionStatus">
          部分功能需要额外的系统权限，可能需要在系统设置中手动开启
        </text>
      </view>
    </view>
  </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'

const STORAGE_KEY = 'notification_settings'
const PERMISSION_KEY = 'notification_permission'

export default {
  components: {
    CustomHeader
  },

  data() {
    return {
      settings: {
        receiveNotifications: true,
        sound: true,
        vibrate: true,
        banner: true,
        badge: true,
        systemNotice: true
      },
      hasPermission: false,
      soundPermissionStatus: true,  // 音频播放权限状态
      vibratePermissionStatus: true  // 振动权限状态
    }
  },

  created() {
    // 从缓存加载设置
    const savedSettings = uni.getStorageSync(STORAGE_KEY)
    if (savedSettings) {
      this.settings = JSON.parse(savedSettings)
    }
    
    // 检查通知权限状态
    this.checkNotificationPermission()
    
    // 检查声音和振动权限
    this.checkSoundAndVibratePermissions()
  },

  methods: {
    // 保存设置到缓存
    saveSettings() {
      uni.setStorageSync(STORAGE_KEY, JSON.stringify(this.settings))
      
      // 如果用户已授权且开启了通知，则初始化推送服务
      if (this.hasPermission && this.settings.receiveNotifications) {
        this.initPushService()
      }
    },
    
    // 检查通知权限
    checkNotificationPermission() {
      // 从缓存中获取权限状态
      const permissionStatus = uni.getStorageSync(PERMISSION_KEY)
      this.hasPermission = permissionStatus === 'granted'
      
      // 在支持的平台上，检查实际权限状态
      // #ifdef APP-PLUS
      if (plus.os.name.toLowerCase() === 'android') {
        try {
          const main = plus.android.runtimeMainActivity()
          const NotificationManagerCompat = plus.android.importClass('androidx.core.app.NotificationManagerCompat')
          const notificationManager = NotificationManagerCompat.from(main)
          this.hasPermission = notificationManager.areNotificationsEnabled()
          
          // 更新缓存中的权限状态
          uni.setStorageSync(PERMISSION_KEY, this.hasPermission ? 'granted' : 'denied')
        } catch (e) {
          console.error('检查通知权限失败:', e)
        }
      }
      // #endif
    },
    
    // 检查声音和振动权限
    checkSoundAndVibratePermissions() {
      // #ifdef APP-PLUS
      try {
        // 检查振动权限（Android）
        if (plus.os.name.toLowerCase() === 'android') {
          const vibratePermission = plus.navigator.checkPermission('android.permission.VIBRATE')
          this.vibratePermissionStatus = vibratePermission === 'authorized'
        } else {
          // iOS 默认支持振动
          this.vibratePermissionStatus = true
        }
        
        // 音频播放通常不需要特殊权限，但我们可以通过尝试创建音频上下文来检测
        this.soundPermissionStatus = true
        
      } catch (e) {
        console.error('检查声音和振动权限失败:', e)
        this.soundPermissionStatus = true
        this.vibratePermissionStatus = true
      }
      // #endif
      
      // #ifndef APP-PLUS
      // 在非APP环境下，默认认为有权限
      this.soundPermissionStatus = true
      this.vibratePermissionStatus = true
      // #endif
    },
    
    // 请求通知权限
    requestNotificationPermission() {
      // #ifdef APP-PLUS
      if (plus.os.name.toLowerCase() === 'android') {
        try {
          const main = plus.android.runtimeMainActivity()
          const NotificationManagerCompat = plus.android.importClass('androidx.core.app.NotificationManagerCompat')
          const notificationManager = NotificationManagerCompat.from(main)
          const enabled = notificationManager.areNotificationsEnabled()
          
          if (!enabled) {
            // 如果没有权限，显示权限说明
            uni.showModal({
              title: '通知权限未开启',
              content: '通知权限用于向您推送消息、显示桌面图标角标、声音和振动提醒等，请前往设置中开启',
              success: (res) => {
                if (res.confirm) {
                  // 跳转到应用通知设置页面
                  const Intent = plus.android.importClass('android.content.Intent')
                  const Settings = plus.android.importClass('android.provider.Settings')
                  const Uri = plus.android.importClass('android.net.Uri')
                  
                  const intent = new Intent()
                  intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS)
                  intent.putExtra(Settings.EXTRA_APP_PACKAGE, main.getPackageName())
                  main.startActivity(intent)
                }
              }
            })
          } else {
            this.hasPermission = true
            uni.setStorageSync(PERMISSION_KEY, 'granted')
            uni.showToast({
              title: '通知权限已开启',
              icon: 'success'
            })
          }
        } catch (e) {
          console.error('请求通知权限失败:', e)
        }
      } else if (plus.os.name.toLowerCase() === 'ios') {
        // iOS上请求通知权限
        uni.requestSubscribeMessage({
          tmplIds: [''], // iOS上需要填写模板ID
          success: (res) => {
            console.log('iOS通知权限请求成功', res)
            this.hasPermission = true
            uni.setStorageSync(PERMISSION_KEY, 'granted')
            uni.showToast({
              title: '通知权限已开启',
              icon: 'success'
            })
          },
          fail: (err) => {
            console.error('iOS通知权限请求失败', err)
            uni.showModal({
              title: '通知权限未开启',
              content: '请前往系统设置中开启通知权限',
              showCancel: false
            })
          }
        })
      }
      // #endif
    },
    
    // 初始化推送服务
    initPushService() {
      // 这里可以根据实际使用的推送SDK进行初始化
      // 例如个推SDK的初始化等
      console.log('初始化推送服务，应用通知设置：', this.settings)
      
      // 通知应用其他部分通知设置已更改
      uni.$emit('notification_settings_changed', this.settings)
    },

    // 开关事件处理
    handleMainSwitch(e) {
      this.settings.receiveNotifications = e.detail.value
      this.saveSettings()
    },

    handleSoundSwitch(e) {
      if (!this.soundPermissionStatus) {
        uni.showModal({
          title: '音频权限未授权',
          content: '需要音频播放权限才能启用声音通知',
          showCancel: false
        })
        return
      }
      
      this.settings.sound = e.detail.value
      this.saveSettings()
    },

    handleVibrateSwitch(e) {
      if (!this.vibratePermissionStatus) {
        uni.showModal({
          title: '振动权限未授权',
          content: '需要振动权限才能启用振动通知，请在系统设置中授权',
          showCancel: false
        })
        return
      }
      
      this.settings.vibrate = e.detail.value
      this.saveSettings()
    },

    handleBadgeSwitch(e) {
      this.settings.badge = e.detail.value
      this.saveSettings()
      
      // #ifdef APP-PLUS
      if (!this.settings.badge) {
        // 清除角标
        plus.runtime.setBadgeNumber(0)
      }
      // #endif
    },

    handleBannerSwitch(e) {
      this.settings.banner = e.detail.value
      this.saveSettings()
    },

    handleSystemNoticeSwitch(e) {
      this.settings.systemNotice = e.detail.value
      this.saveSettings()
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.content {
  padding: 12px;
}

.section {
  margin-bottom: 12px;
  border-radius: 10px;
  background: #fff;
  overflow: hidden;
}

.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border-bottom: 1px solid #eee;
  background: #fff;

  .label {
    font-size: 16px;
    color: #333;
  }
}

.label-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .label {
    font-size: 16px;
    color: #333;
    margin-bottom: 2px;
  }
  
  .permission-status {
    font-size: 12px;
    color: #ff6b6b;
  }
}

.bordernone {
  border: none;
}

.permission-btn {
  font-size: 14px;
  padding: 4px 12px;
  background-color: #386BF6;
  color: #fff;
  border-radius: 4px;
  line-height: 1.5;
  height: auto;
}

.tip-section {
  padding: 15px;
  margin-top: 10px;
}

.tip-text {
  font-size: 14px;
  color: #999;
  line-height: 1.5;
  display: block;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}
</style> 