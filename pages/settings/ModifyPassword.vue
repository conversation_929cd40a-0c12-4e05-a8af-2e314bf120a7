<template>
	<view class="container">
		<view class="content">
			<view class="form-box">
				<view class="form-item">
					<text class="label">手机号</text>
					<text class="value">{{ mobile }}</text>
				</view>
				<view class="form-item">
					<text class="label">新密码</text>
					<input class="input" v-model="password" type="password" placeholder="请输入新密码" />
				</view>
				<view class="form-item">
					<text class="label">确认密码</text>
					<input class="input" v-model="confirmPassword" type="password" placeholder="请再次输入新密码" />
				</view>
				<view class="form-item code-item">
					<text class="label">验证码</text>
					<input class="input" v-model="code" type="text" placeholder="请输入验证码" />
					<button class="get-code-btn" :disabled="codeBtnDisabled" @click="getCode">{{ codeBtnText }}</button>
				</view>
			</view>
			<button class="submit-btn" @click="submit">确认修改</button>
		</view>
	</view>
</template>

<script>
import appServerApi from '../../api/appServerApi'
export default {
  data() {
    return {
      mobile: '',
      password: '',
      confirmPassword: '',
      code: '',
      codeBtnText: '获取验证码',
      codeBtnDisabled: false,
      timer: null
    }
  },
  onLoad(options) {
    this.mobile = options.mobile || ''
  },
  methods: {
    getCode() {
      if (this.codeBtnDisabled) return
      if (!this.mobile) {
        uni.showToast({ title: '手机号为空', icon: 'none' })
        return
      }
      // 调用发送验证码接口
      appServerApi.sendCode(this.mobile)
        .then(() => {
          uni.showToast({ title: '验证码已发送', icon: 'none' })
          this.codeBtnDisabled = true
          let count = 60
          this.codeBtnText = `${count}s后重试`
          this.timer = setInterval(() => {
            count--
            if (count <= 0) {
              this.codeBtnDisabled = false
              this.codeBtnText = '获取验证码'
              clearInterval(this.timer)
            } else {
              this.codeBtnText = `${count}s后重试`
            }
          }, 1000)
        })
        .catch(err => {
          uni.showToast({ title: err.message || '发送失败', icon: 'none' })
        })
    },
    submit() {
		if (!this.password || !this.confirmPassword || !this.code) {
			uni.showToast({ title: '请填写完整信息', icon: 'none' })
			return
		}
		if (this.password !== this.confirmPassword) {
			uni.showToast({ title: '两次输入的密码不一致', icon: 'none' })
			return
		}
      // 调用后端接口进行密码修改
    //   uni.showToast({ title: '密码修改逻辑待接入', icon: 'none' })
	// 调用后端接口进行密码修改
		appServerApi.resetPassword(this.mobile, this.code, this.password)
			.then(res => {
			if (res && res.code === 200) {
				uni.showToast({ title: '密码修改成功', icon: 'success' })
				setTimeout(() => {
				uni.navigateBack()
				}, 1500)
			} else {
				uni.showToast({ title: res.msg || '修改失败', icon: 'none' })
			}
			})
			.catch(err => {
			uni.showToast({ title: err.message || '请求失败', icon: 'none' })
			})
		
    }
  }
}
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8f8f8;
}
.header {
  display: flex;
  align-items: center;
  height: 56px;
  background: #fff;
  border-bottom: 1px solid #eee;
  position: relative;
}
.back-btn {
  width: 44px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  color: #222;
  cursor: pointer;
}
.title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #222;
}
.content {
  flex: 1;
  padding: 32px 20px 0 20px;
  display: flex;
  flex-direction: column;
}
.form-box {
  background: #fff;
  border-radius: 10px;
  padding: 35px 10px 8px 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  margin-bottom: 32px;
}
.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
}
.label {
  width: 80px;
  color: #333;
  font-size: 16px;
}
.value {
  flex: 1;
  color: #222;
  font-size: 16px;
}
.input {
  flex: 1;
  height: 36px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 0 10px;
  font-size: 14px;
  background: #fafbfc;
}
.code-item {
  position: relative;
}
.get-code-btn {
  flex: 1;
  margin-left: 8px;
  height: 32px;
  padding: 0 12px;
  background: #e6f0ff;
  color: #1677ff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  text-align: center;
  transition: background 0.2s;
}
.get-code-btn:disabled {
  background: #f0f0f0;
  color: #aaa;
  cursor: not-allowed;
}
.submit-btn {
  width: 100%;
  height: 44px;
  background: linear-gradient(90deg, #1677ff 0%, #3b8cff 100%);
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 24px;
  transition: background 0.2s;
}
.submit-btn:active {
  background: #145fd7;
}
</style>
