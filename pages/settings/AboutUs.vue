<template>
  <view class="container">
    <CustomHeader>
      <template v-slot:title>
        <text>关于我们</text>
      </template>
    </CustomHeader>
    
    <view class="content">
      <view class="logo-section">
        <image src="/static/logo.png" mode="aspectFit" class="logo"></image>
        <text class="app-name">地球岛</text>
        <text class="version">版本 {{version}}</text>
      </view>
      
      <view class="info-section">
        <text class="title">地球岛简介</text>
        <text class="paragraph">
          地球岛是一个致力于连接全球华人的社交平台。我们希望通过科技的力量，让世界各地的华人能够更便捷地交流、分享和互动。
        </text>
        <text class="paragraph">
          在这里，你可以：<br>
          • 分享生活点滴<br>
          • 结识志同道合的朋友<br>
          • 了解世界各地的文化<br>
          • 获取有价值的信息<br>
        </text>
        <text class="paragraph">
          我们始终秉持"让世界更紧密相连"的理念，不断优化产品体验，为用户提供安全、便捷、有趣的社交平台。
        </text>
      </view>
    </view>
  </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'

export default {
  components: {
    CustomHeader
  },
  data() {
    return {
      version: '1.0.0'
    }
  },
  onLoad() {
    // 获取真实版本号
    plus.runtime.getProperty(plus.runtime.appid, (inf) => {
      this.version = inf.version;
    });
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #fff;
}

.content {
  padding: 20px;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 0;
  
  .logo {
    width: 80px;
    height: 80px;
    margin-bottom: 15px;
  }
  
  .app-name {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 8px;
  }
  
  .version {
    font-size: 14px;
    color: #999;
  }
}

.info-section {
  padding: 20px 0;
  
  .title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    display: block;
  }
  
  .paragraph {
    font-size: 15px;
    color: #333;
    line-height: 1.6;
    margin-bottom: 15px;
    display: block;
  }
}
</style> 