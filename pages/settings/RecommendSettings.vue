<template>
    <view class="container">
        <CustomHeader>
            <template v-slot:title>
                <text>推荐设置</text>
            </template>
        </CustomHeader>

        <view class="content">
            <view class="section">
                <view class="item bordernone">
                    <text class="label">个性化内容推荐</text>
                    <switch :checked="settings.personalizedRecommend" @change="handlePersonalizedRecommend" color="#386BF6" />
                </view>
            </view>

            <!-- 暂时隐藏其他选项 -->
            <!-- 
            <view class="section">
                <view class="item">
                    <text class="label">热门内容</text>
                    <switch :checked="settings.hotContent" @change="handleHotContent" color="#386BF6" />
                </view>
                <view class="item">
                    <text class="label">相似用户推荐</text>
                    <switch :checked="settings.similarUsers" @change="handleSimilarUsers" color="#386BF6" />
                </view>
                <view class="item bordernone">
                    <text class="label">地域相关</text>
                    <switch :checked="settings.locationBased" @change="handleLocationBased" color="#386BF6" />
                </view>
            </view>

            <view class="section">
                <view class="section-title">推荐频率</view>
                <radio-group @change="handleFrequencyChange">
                    <view class="item">
                        <view class="radio-content">
                            <radio value="high" :checked="settings.frequency === 'high'" color="#386BF6"/>
                            <view class="radio-info">
                                <text class="radio-label">高频推荐</text>
                                <text class="radio-desc">每天多次推荐新内容</text>
                            </view>
                        </view>
                    </view>
                    <view class="item">
                        <view class="radio-content">
                            <radio value="medium" :checked="settings.frequency === 'medium'" color="#386BF6"/>
                            <view class="radio-info">
                                <text class="radio-label">中等频率</text>
                                <text class="radio-desc">每天推荐几次新内容</text>
                            </view>
                        </view>
                    </view>
                    <view class="item bordernone">
                        <view class="radio-content">
                            <radio value="low" :checked="settings.frequency === 'low'" color="#386BF6"/>
                            <view class="radio-info">
                                <text class="radio-label">低频推荐</text>
                                <text class="radio-desc">每天推荐少量精选内容</text>
                            </view>
                        </view>
                    </view>
                </radio-group>
            </view>

            <view class="section">
                <view class="item">
                    <text class="label">新用户推荐</text>
                    <switch :checked="settings.newUsers" @change="handleNewUsers" color="#386BF6" />
                </view>
                <view class="item bordernone">
                    <text class="label">趋势话题</text>
                    <switch :checked="settings.trendingTopics" @change="handleTrendingTopics" color="#386BF6" />
                </view>
            </view>

            <view class="reset-section">
                <button class="reset-button" @click="resetRecommendSettings">重置推荐设置</button>
            </view>
            -->

            <view class="tip-section">
                <text class="tip-text">提示：个性化推荐基于您的兴趣和行为，关闭后将显示通用内容推荐</text>
            </view>
        </view>
    </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'

const STORAGE_KEY = 'recommend_settings'

export default {
    components: {
        CustomHeader
    },

    data() {
        return {
            settings: {
                personalizedRecommend: true,
                smartPush: true,
                hotContent: true,
                similarUsers: false,
                locationBased: true,
                frequency: 'medium',
                newUsers: false,
                trendingTopics: true
            }
        }
    },

    created() {
        // 从缓存加载设置
        this.loadSettings()
    },

    methods: {
        // 保存设置到缓存
        saveSettings() {
            uni.setStorageSync(STORAGE_KEY, JSON.stringify(this.settings))
            
            uni.showToast({
                title: '设置已保存',
                icon: 'success',
                duration: 1500
            })
            
            // 通知应用其他部分推荐设置已更改
            uni.$emit('recommend_settings_changed', this.settings)
        },

        // 从缓存加载设置
        loadSettings() {
            try {
                const savedSettings = uni.getStorageSync(STORAGE_KEY)
                if (savedSettings) {
                    this.settings = { ...this.settings, ...JSON.parse(savedSettings) }
                }
            } catch (error) {
                console.error('加载推荐设置失败:', error)
            }
        },

        // 重置推荐设置
        resetRecommendSettings() {
            uni.showModal({
                title: '重置确认',
                content: '确定要重置所有推荐设置为默认值吗？',
                success: (res) => {
                    if (res.confirm) {
                        // 重置为默认值
                        this.settings = {
                            personalizedRecommend: true,
                            smartPush: true,
                            hotContent: true,
                            similarUsers: false,
                            locationBased: true,
                            frequency: 'medium',
                            newUsers: false,
                            trendingTopics: true
                        }
                        
                        // 清除本地存储
                        uni.removeStorageSync(STORAGE_KEY)
                        
                        uni.showToast({
                            title: '重置成功',
                            icon: 'success'
                        })
                    }
                }
            })
        },

        // 开关事件处理
        handlePersonalizedRecommend(e) {
            const newValue = e.detail.value
            
            if (newValue) {
                // 打开个性化推荐时的提示
                uni.showModal({
                    title: '提示',
                    content: '是否确认打开个性化推荐，打开个性化推荐系统将会根据您的访问习惯，以及行为【点赞、转发、收藏等行为】去为您推荐对应的内容。',
                    cancelText: '取消',
                    confirmText: '确认',
                    success: (res) => {
                        if (res.confirm) {
                            this.settings.personalizedRecommend = true
                            this.saveSettings()
                        } else {
                            // 用户取消，保持原状态
                            this.settings.personalizedRecommend = false
                        }
                    }
                })
            } else {
                // 关闭个性化推荐时的提示
                uni.showModal({
                    title: '提示',
                    content: '是否确认关闭个性化推荐，关闭个性化推荐系统将会不会给您推荐任何相关信息，而是默认展示',
                    cancelText: '取消',
                    confirmText: '确认',
                    success: (res) => {
                        if (res.confirm) {
                            this.settings.personalizedRecommend = false
                            this.saveSettings()
                        } else {
                            // 用户取消，保持原状态
                            this.settings.personalizedRecommend = true
                        }
                    }
                })
            }
        },

        handleSmartPush(e) {
            this.settings.smartPush = e.detail.value
            this.saveSettings()
        },

        handleHotContent(e) {
            this.settings.hotContent = e.detail.value
            this.saveSettings()
        },

        handleSimilarUsers(e) {
            this.settings.similarUsers = e.detail.value
            this.saveSettings()
        },

        handleLocationBased(e) {
            this.settings.locationBased = e.detail.value
            this.saveSettings()
        },

        handleNewUsers(e) {
            this.settings.newUsers = e.detail.value
            this.saveSettings()
        },

        handleTrendingTopics(e) {
            this.settings.trendingTopics = e.detail.value
            this.saveSettings()
        },

        handleFrequencyChange(e) {
            this.settings.frequency = e.detail.value
            this.saveSettings()
        }
    }
}
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
    background-color: #f8f8f8;
}

.content {
    padding: 12px;
}

.section {
    margin-bottom: 12px;
    border-radius: 10px;
    background: #fff;
    overflow: hidden;
}

.section-title {
    color: #666;
    font-size: 14px;
    padding: 12px 15px 8px;
    background: #f8f8f8;
    border-bottom: 1px solid #eee;
}

.item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border-bottom: 1px solid #eee;
    background: #fff;

    .label {
        font-size: 16px;
        color: #333;
    }
}

.bordernone {
    border: none;
}

.radio-content {
    display: flex;
    align-items: center;
    width: 100%;
}

.radio-info {
    display: flex;
    flex-direction: column;
    margin-left: 10px;
    flex: 1;
}

.radio-label {
    font-size: 16px;
    color: #333;
    margin-bottom: 4px;
}

.radio-desc {
    font-size: 13px;
    color: #999;
}

.reset-section {
    margin-top: 20px;
    text-align: center;
    padding-bottom: 10px;
}

.reset-button {
    width: 90%;
    height: 44px;
    line-height: 44px;
    background: #fff;
    color: #ff6b6b;
    border: 1px solid #ff6b6b;
    border-radius: 10px;
    font-size: 16px;

    &:active {
        opacity: 0.8;
    }
}

.tip-section {
    padding: 15px;
    margin-top: 10px;
}

.tip-text {
    font-size: 14px;
    color: #999;
    line-height: 1.5;
}

button::after {
    border: none;
}
</style> 