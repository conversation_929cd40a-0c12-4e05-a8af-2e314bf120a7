<template>
    <view class="container">
        <!-- 银行卡信息 -->
        <view class="card-section" @click="showPaymentOptions">
            <div class="left-col">
                <image class="avatar" :src="paymentStyle.icon" />
                <div style="margin-left: 16rpx;">
                    <text class="bank-card">{{paymentStyle.name}}</text>
                    <text class="hint">立即到账</text>
                </div>
            </div>
            <div><i class="icon-ion-ios-arrow-right"></i></div>
        </view>

        <view class="money-box">
            <view class="text_colour_3 money-box-title">充值金额</view>
            <view class="flex_r_c text_50 text_weight_bold money-box-row">
                <view class="money-box-row-">￥</view>
                <view class="flex1 flex_r fa_c money">
                    {{keyboard.valueToLocaleString}}
                    <view class="cursor"></view>
                    <!-- <view class="text_26 color__ money-text">
                        {{keyboard.valueText}}
                    </view> -->
                </view>
            </view>
            <view class="line">
            </view>
            <!-- <view class="text_28 money-box-text">
                <text style="color: #686B8A">添加转账说明</text>
            </view> -->
        </view>

        <view class="keyboard">
            <pan-keyboard ref="panKeyboardRef" :maxValue="1000000" :isCheck='checkValue' @onSubmit='onSubmit' @onChange="onChange" @onError="onErrorx"></pan-keyboard>
            <view class="placeholder" :style="placeholderStyle"></view>
        </view>
        <payment-selector v-model:visible="paymentVisible" @close="handlePaymentClose" @confirm="handlePaymentConfirm" />
    </view>
</template>

<script>
// import CustomHeader from '@/components/custom-header'
import PaymentSelector from '@/components/payment/PaymentSelector2'
import appServerApi from '@/api/appServerApi'
import util from '@/utils/util'
export default {
    components: {
        PaymentSelector,
    },
    data() {
        return {
            inputAmount: '',
            currentBalance: ***********.88,
            isPressing: false,
            isShake: false,
            isMaxValueShake: false,
            pageData: {},
            keyboard: {},
            payFlag: false,
            bankCard: {},
            selectedPayment: null,
            paymentVisible: false,
            paymentStyle: {
                id: 'wechat',
                name: '微信支付',
                icon: '/static/image/icon/wechat.png',
            },
        }
    },
    onLoad() {
        // 用这个方式设置初始值；不设置默认为空
        //setTimeout(() => {
        //    this.$refs.panKeyboardRef.setKeyboard(100)
        //}, 100)
    },
    computed: {
        checkValue() {
            let valueNumber = this.keyboard.valueNumber

            // 可以在实时这里校验输入的值
            // if (valueNumber < 100) return false;
            // if (valueNumber % 10) return false;
            return true
        },
        placeholderStyle() {
            const style = {}
            style.height = `${this.height}rpx`
            // #ifndef MP-WEIXIN
            const { safeAreaInsets } = uni.getSystemInfoSync()
            style.paddingBottom = `${safeAreaInsets.bottom}px`
            // #endif
            return style
        },
    },
    methods: {
        handlePaymentClose() {
            console.log('支付弹窗关闭')
        },
        async handlePaymentConfirm({ method }) {
            this.paymentStyle = method
            this.paymentVisible = false
            try {
                console.log('支付确认', method)
                switch (method) {
                    case 'wechat':
                        break
                    case 'alipay':
                        break
                    default:
                        break
                }
            } catch (error) {
                this.$message.error('支付失败')
            }
        },
        // 金额完成/打开验证码
        async onSubmit(e) {
            this.keyboard = e
            console.log(e)
            var obj = {
                amount: e.valueNumber,
                payType: '',
            }
            switch (this.paymentStyle.id) {
                case 'wechat':
                    obj.payType = 2
                    break
                case 'alipay':
                    obj.payType = 1
                    break
                default:
                    break
            }
            const response = await appServerApi.walletCharge(obj)
            console.log(response)
            switch (this.paymentStyle.id) {
                case 'wechat':
                    util.wxpay(
                        response,
                        function (res) {
                            console.log('成功', res)
                            uni.showToast({
                                icon: 'success',
                                title: '充值成功',
                                duration: 1000,
                            })
                            uni.navigateBack()
                        },
                        function (res) {
                            console.log('失败', res)
                            uni.showToast({
                                title: '支付失败',
                                icon: 'none',
                            })
                        }
                    )
                    break
                case 'alipay':
                    util.alipay(
                        response,
                        function (res) {
                            console.log('成功', res)
                            uni.showToast({
                                title: '支付成功',
                                icon: 'none',
                            })
                            uni.showToast({
                                icon: 'success',
                                title: '成功',
                                duration: 1000,
                            })
                            uni.navigateBack()
                        },
                        function (res) {
                            console.log('失败', res)
                            uni.showToast({
                                title: '支付失败',
                                icon: 'none',
                            })
                        }
                    )
                    break
                default:
                    break
            }
        },
        // 金额输入中
        onChange(e) {
            this.keyboard = e
        },
        onErrorx() {
            console.log('超过限制')
        },
        handleInput(num) {
            if (this.inputAmount.split('.')[1]?.length >= 2) return
            this.inputAmount += num.toString()
        },
        handleFullWithdraw() {
            this.inputAmount = this.currentBalance.toFixed(2)
        },
        handlePress() {
            this.isPressing = true
        },
        clearPress() {
            this.isPressing = false
        },
        handleConfirm() {
            // 提现逻辑
        },
        showPaymentOptions() {
            this.paymentVisible = true
        },
        selectPayment(payment) {
            this.selectedPayment = payment
        },
    },
}
</script>

<style lang="scss" scoped>
.container {
    background: #f5f6fa;
    height: 100vh;
}

.status-bar {
    height: 88rpx;
    padding: 0 32rpx;
    font-size: 30rpx;
    color: #333;
    display: flex;
    align-items: center;
}

.nav-bar {
    height: 112rpx;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

    .icon-font {
        font-size: 48rpx;
        color: #333;
    }

    .title {
        font-size: 36rpx;
        font-weight: 600;
        color: #1a1a1a;
    }
}

.card-section {
    background: #ffffff;
    margin: 24rpx 32rpx;
    padding: 40rpx;
    border-radius: 24rpx;
    display: flex;
    justify-content: space-between;
    .left-col {
        display: flex;
    }
    image {
        width: 30px;
        height: 30px;
    }

    .bank-card {
        font-size: 34rpx;
        color: #333;
        display: block;
        margin-bottom: 16rpx;
    }

    .hint {
        font-size: 28rpx;
        color: #999;
    }
}

.amount-section {
    background: #ffffff;
    margin: 0 32rpx;
    padding: 48rpx 40rpx;
    border-radius: 24rpx;

    .input-row {
        display: flex;
        align-items: center;
        border-bottom: 2rpx solid #eee;
        padding-bottom: 32rpx;
        margin-bottom: 32rpx;

        .currency {
            font-size: 72rpx;
            color: #333;
            margin-right: 24rpx;
            font-weight: 500;
        }

        .amount-input {
            flex: 1;
            font-size: 72rpx;
            height: 100rpx;
            color: #333;
            font-weight: 600;

            &::placeholder {
                color: #cccccc;
            }
        }
    }

    .balance-row {
        display: flex;
        justify-content: space-between;
        font-size: 28rpx;
        color: #666;

        .full-btn {
            color: #007aff;
            font-size: 28rpx;
        }
    }
}
.keyboard {
    // 原有样式保持不变
    .key-btn {
        // 新增状态样式
        &:active {
            background: #f0f0f0 !important;
        }

        &.gray {
            background: #f8f8f8;
            font-size: 44rpx;
        }
    }
}

// 新增删除键动画
@keyframes deleting {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
    }
}

.gray:active {
    animation: deleting 0.3s ease;
}

// 优化输入框样式
.amount-input {
    caret-color: #007aff; // 光标颜色
    // 禁用默认样式
    -webkit-appearance: none;
    -moz-appearance: textfield;
}
.placeholder {
    width: 100%;
    height: 20rpx;
    /* #ifdef MP-WEIXIN*/
    padding-bottom: 0;
    padding-bottom: constant(safe-area-inset-bottom); //适配底部
    padding-bottom: env(safe-area-inset-bottom);
    /*#endif*/
}
.top {
    width: 100%;
    height: 260rpx;
    background-color: #ededed;

    .user {
        width: calc(100% - 120rpx);
        height: 100rpx;
        margin-bottom: 20rpx;

        .user-r {
            .user-r-title {
            }

            .user-r-text {
            }
        }

        .user-img {
            width: 100rpx;
            height: 100rpx;
            border-radius: 10rpx;
            overflow: hidden;
        }
    }
}

.money-box {
    position: relative;
    top: -30rpx;
    box-sizing: border-box;
    padding: 50rpx 55rpx;
    width: 100%;
    border-radius: 30rpx 30rpx 0 0;
    background-color: #fff;

    .line {
        width: calc(100% - 40rpx);
        margin-top: 40rpx;
        border-bottom: 1px solid #dcdcdc;
    }

    .money-box-title {
        width: 100%;
        height: 60rpx;
        margin-bottom: 10rpx;
    }

    .money-box-text {
        position: relative;
        margin-top: 30rpx;
        font-size: 30rpx;
        color: #616161;

        .money-box-text_ {
            position: absolute;
            left: 0;
        }
    }

    .money-box-row {
        width: 100%;
        height: 100rpx;
        font-size: 70rpx;

        .money {
            position: relative;
            font-size: 80rpx;

            .money-text {
                position: absolute;
                bottom: -30rpx;
                left: 0;
            }
        }

        .money-box-row- {
            margin-top: 4rpx;
        }

        .money-box-row-icon {
        }
    }
}

.keyboard {
    position: fixed;
    box-sizing: border-box;
    width: 100%;
    bottom: 0;
    padding: 20rpx;
    background-color: #f7f7f7;
}

.cursor {
    position: relative;
    top: 10rpx;
    display: inline-block;
    width: 2px;
    height: 100rpx;
    margin-left: 10rpx;
    animation-name: cursor;
    animation-duration: 0.8s;
    animation-iteration-count: infinite;
    z-index: 1;
    background-color: #178e57;
}

@keyframes cursor {
    0% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

.shake {
    animation: shake 800ms ease-in-out;
}

@keyframes shake {
    /* 水平抖动，核心代码 */
    10%,
    90% {
        transform: translate3d(-1px, 0, 0);
    }

    20%,
    80% {
        transform: translate3d(+2px, 0, 0);
    }

    30%,
    70% {
        transform: translate3d(-4px, 0, 0);
    }

    40%,
    60% {
        transform: translate3d(+4px, 0, 0);
    }

    50% {
        transform: translate3d(-4px, 0, 0);
    }
}
// ---------------以下代码是作者的公共css（部分）, 可通过npm i pan_css下载完整版 在app.vue中引入；这里就简化使用直接复制进来----------------
.wh_100 {
    width: 100%;
    height: 100%;
}

.z_index2 {
    position: relative;
    z-index: 2;
}

.text_colour_red {
    color: #ff0000;
}

.text_colour_white {
    color: #fff;
}

.text_colour_0 {
    color: #000;
}

.text_colour_1 {
    color: #111;
}

.text_colour_2 {
    color: #222;
}

.text_colour_3 {
    color: #333;
}

.text_colour_4 {
    color: #444;
}

.text_colour_5 {
    color: #555;
}

.text_colour_6 {
    color: #666;
}

.text_colour_7 {
    color: #777;
}

.text_colour_8 {
    color: #888;
}

.text_colour_9 {
    color: #999;
}

.flex_r {
    display: flex;
    flex-direction: row;
}

.flex_c {
    display: flex;
    flex-direction: column;
}

.fj_b {
    justify-content: space-between;
}

.fj_a {
    justify-content: space-around;
}

.fj_c {
    justify-content: center;
}

.fa_c {
    align-items: center;
}

.flex_c_c {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.flex_r_c {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.flex1 {
    flex: 1;
}

.text_16 {
    font-size: 16rpx;
}

.text_17 {
    font-size: 17rpx;
}

.text_18 {
    font-size: 18rpx;
}

.text_19 {
    font-size: 19rpx;
}

.text_20 {
    font-size: 20rpx;
}

.text_21 {
    font-size: 21rpx;
}

.text_22 {
    font-size: 22rpx;
}

.text_23 {
    font-size: 23rpx;
}

.text_24 {
    font-size: 24rpx;
}

.text_25 {
    font-size: 25rpx;
}

.text_26 {
    font-size: 26rpx;
}

.text_27 {
    font-size: 27rpx;
}

.text_28 {
    font-size: 28rpx;
}

.text_29 {
    font-size: 29rpx;
}

.text_30 {
    font-size: 30rpx;
}

.text_31 {
    font-size: 31rpx;
}

.text_32 {
    font-size: 32rpx;
}

.text_33 {
    font-size: 33rpx;
}

.text_34 {
    font-size: 34rpx;
}

.text_35 {
    font-size: 35rpx;
}

.text_36 {
    font-size: 36rpx;
}

.text_37 {
    font-size: 37rpx;
}

.text_38 {
    font-size: 38rpx;
}

.text_39 {
    font-size: 39rpx;
}

.text_40 {
    font-size: 40rpx;
}

.text_41 {
    font-size: 41rpx;
}

.text_42 {
    font-size: 42rpx;
}

.text_43 {
    font-size: 43rpx;
}

.text_44 {
    font-size: 44rpx;
}

.text_45 {
    font-size: 45rpx;
}

.text_46 {
    font-size: 46rpx;
}

.text_weight_bold {
    font-weight: bold;
}
</style>