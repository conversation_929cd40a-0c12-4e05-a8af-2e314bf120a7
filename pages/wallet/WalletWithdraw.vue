<template>
  <view class="container">
    <!-- 导航栏 -->
    <CustomHeader title="提现" @back="goBack">
      <template v-slot:right>
        <text style="font-size: 18px; color: #333" @tap="showMoreOptions">...</text>
      </template>
    </CustomHeader>

    <!-- 更多选项弹出菜单 -->
    <view class="more-options-popup" v-if="moreOptionsVisible" @tap.stop="hideMoreOptions">
      <view class="popup-content" @tap.stop>
        <view class="popup-item" @tap="navigateToAccountSettings">
          <text class="popup-item-text">账号设置</text>
        </view>
        <view class="popup-divider"></view>
        <view class="popup-item" @tap="navigateToWithdrawRecords">
          <text class="popup-item-text">提现明细</text>
        </view>
      </view>
    </view>

    <!-- 提现方式选择 -->
    <view class="withdraw-options">
      <view class="withdraw-option" @click="selectWithdrawMethod('alipay')">
        <view class="option-content">
          <text class="option-text">提现到支付宝</text>
          <view class="radio-button" :class="{'radio-selected': withdrawMethod === 'alipay'}">
            <view v-if="withdrawMethod === 'alipay'" class="radio-inner"></view>
          </view>
        </view>
      </view>
      <view class="withdraw-option" @click="selectWithdrawMethod('wechat')">
        <view class="option-content">
          <text class="option-text">提现到微信</text>
          <view class="radio-button" :class="{'radio-selected': withdrawMethod === 'wechat'}">
            <view v-if="withdrawMethod === 'wechat'" class="radio-inner"></view>
          </view>
        </view>
      </view>
      <!-- 暂时隐藏银行卡选项，因为需要人工手动打款 -->
      <!--
      <view class="withdraw-option" @click="selectWithdrawMethod('bank')">
        <view class="option-content">
          <text class="option-text">提现到银行卡</text>
          <view class="radio-button" :class="{'radio-selected': withdrawMethod === 'bank'}">
            <view v-if="withdrawMethod === 'bank'" class="radio-inner"></view>
          </view>
        </view>
      </view>
      -->
    </view>
    <!-- 银行卡信息，仅在选择银行卡时显示 -->
    <view v-if="withdrawMethod === 'bank'" class="card-section" @click="showPaymentOptions">
      <view class="bank-info">
        <text class="bank-name">{{
            selectedCard ? `${selectedCard.name} (${selectedCard.lastFour})` : '请选择银行卡'
          }}
        </text>
        <text class="instant-text">立即到账</text>
      </view>
      <view class="arrow-right">
        <text class="icon-ion-ios-arrow-right"></text>
      </view>
    </view>

    <view class="money-box">
      <view class="money-box-title-container">
        <text class="text_colour_3 money-box-title">提现金额</text>
        <text class="money-box-subtitle">（最小提现单位为"分"）</text>
      </view>
      <view class="flex_r_c text_50 text_weight_bold money-box-row">
        <view class="money-box-row-">￥</view>
        <view class="flex1 flex_r fa_c money">
          {{ keyboard.valueToLocaleString }}
          <view class="cursor"></view>
        </view>
      </view>
      <view class="line">
      </view>
      <view class="balance-info">
        <text class="balance-text">当前余额¥{{ balance }}，</text>
        <text class="withdraw-all" @tap="withdrawAll">全部提现</text>
      </view>
    </view>

    <!-- 键盘 -->
    <view class="keyboard">
      <pan-keyboard ref="panKeyboardRef" :maxValue="balance" :isCheck='checkValue' @onSubmit='onSubmit'
                    @onChange="onChange" @onError="onErrorx" :toFixed="2"></pan-keyboard>
      <view class="placeholder" :style="placeholderStyle"></view>
    </view>
    <payment-selector v-model:visible="paymentVisible" @close="handlePaymentClose" @confirm="handlePaymentConfirm"/>

    <!-- 添加密码输入弹窗 -->
    <withdraw-password-dialog
        v-model:visible="passwordDialogVisible"
        :amount="keyboard.valueNumber"
        :serviceFee="calculateServiceFee"
        :feeRate="0.6"
        @confirm="handlePasswordConfirm"
    />

    <!-- 添加支付宝绑定弹窗 -->
    <alipay-bind-dialog
        ref="alipayBindDialog"
        @success="handleAlipayBindSuccess"
        @cancel="handleAlipayBindCancel"
    />
  </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import PaymentSelector from '@/components/payment/PaymentSelector'
import WithdrawPasswordDialog from '@/components/payment/WithdrawPasswordDialog'
import AlipayBindDialog from '@/components/payment/AlipayBindDialog'
import appServerApi from '@/api/appServerApi'
import wxApi from '@/api/wxApi'
import permission from "../../common/permission";

export default {
  name: 'WalletWithdraw',
  components: {
    CustomHeader,
    PaymentSelector,
    WithdrawPasswordDialog,
    AlipayBindDialog
  },
  data() {
    return {
      inputAmount: '',
      balance: 0,
      openid: '',
      alipayAccount: '',
      realname: '',
      isPressing: false,
      isShake: false,
      isMaxValueShake: false,
      pageData: {},
      keyboard: {},
      payFlag: false,
      bankCard: {},
      selectedCard: null,
      paymentVisible: false,
      passwordDialogVisible: false,
      withdrawMethod: 'alipay',
      isKeyboardInitialized: false,
      isPullMoney: false,
      alipayBindDialogVisible: false,
      isAlipayBound: false,
      moreOptionsVisible: false,
    }
  },
  async onLoad() {
    if (!this.isKeyboardInitialized) {
      await this.getBalance()
    }
    // 只在第一次进入页面时设置键盘默认值
    this.$nextTick(() => {
      if (this.balance > 0 && this.$refs.panKeyboardRef && !this.isKeyboardInitialized) {
        // 将余额保留两位小数后设置到键盘
        const balanceWithTwoDecimals = Math.floor(this.balance * 100) / 100
        this.$refs.panKeyboardRef.setKeyboard(balanceWithTwoDecimals)
        this.isKeyboardInitialized = true // 设置标记，表示已初始化
      }
    })
  },

  async onShow() {
    if (this.isPullMoney) {
      console.log('onLoad 提现成功，刷新余额 ')
      // 刷新用户余额数据，延迟10秒执行以便后台有时间更新数据
      await new Promise(resolve => setTimeout(resolve, 10000))
      await this.getBalance()
      this.isPullMoney = false
    }
  },
  onReady() {
    // 页面渲染完成
  },
  onHide() {
    // 页面隐藏
    // uni.hideLoading()
  },
  computed: {
    checkValue() {
      let valueNumber = this.keyboard.valueNumber
      return true
    },
    placeholderStyle() {
      const style = {}
      style.height = `${this.height}rpx`
      // #ifndef MP-WEIXIN
      const {safeAreaInsets} = uni.getSystemInfoSync()
      style.paddingBottom = `${safeAreaInsets.bottom}px`
      // #endif
      return style
    },
    calculateServiceFee() {
      if (!this.keyboard.valueNumber) return '0.00'
      return (this.keyboard.valueNumber * 0.006).toFixed(2) // 0.6% 的手续费
    }
  },
  methods: {
    async getBalance(isRefresh = true) {
      const response = await appServerApi.mywallet()
      if (isRefresh) {
        this.balance = response.data.data.balance
      }
      console.log('mywallet 余额 response', response.data.data)
      this.openid = response.data.data.openId
      this.alipayAccount = response.data.data.alipayAccount
      this.realname = response.data.data.realname
    },
    goBack() {
      uni.navigateBack()
    },

    // 单独封装微信绑定方法
    async bindWechatAccount(openid) {
      try {
        const response = await appServerApi.bindWx({openId: openid})
        console.log('获取提现参数bindWx ', response)
        return response || {data: {}}
      } catch (error) {
        console.error('微信绑定失败:', error)
        throw new Error('微信账户绑定失败: ' + (error.message || error))
      }
    },

    // 单独封装提现申请方法
    async requestWithdraw(params) {
      try {
        const response = await appServerApi.withDraw(params)
        console.log('获取提现参数 withDraw response:', response)
        // 即使返回data为null也继续执行，但需要进行安全检查
        return response
      } catch (error) {
        console.error('提现申请失败:', error)
        throw new Error('提现申请失败: ' + (error.message || error))
      }
    },
    // 转账成功处理
    async handleTransferSuccess() {
      try {
        const result = this.balance - this.keyboard.valueNumber
        this.balance = Number(result.toFixed(2))  // 保留2位小数并转成数字
        console.log('提现成功后余额已更新:', this.balance)
        this.$emit('transfer-success', result)
      } catch (error) {
        console.error('更新余额失败:', error)
        // 即使更新余额失败，也不影响提现成功的结果展示
      }
    },

    handlePaymentClose() {
      console.log('银行卡选择弹窗关闭')
    },

    handlePaymentConfirm({card}) {
      this.selectedCard = card
      this.paymentVisible = false
    },

    // 选择提现方式
    selectWithdrawMethod(method) {
      this.withdrawMethod = method
    },

    hasAlipayAccount() {
      return !!(this.alipayAccount && this.alipayAccount.length > 0
          && this.alipayAccount !== 'undefined' && this.alipayAccount !== 'null'
          && this.realname && this.realname.length > 0
          && this.realname !== 'undefined' && this.realname !== 'null');
    },

    hasWxAccount() {
      return !!(this.openid && this.alipayAccount.length > 0
          && this.openid !== 'undefined' && this.openid !== 'null');
    },
    // 错误统一处理
    handleTransferError(error) {
      const message = {
        'NO_PERMISSION': '请检查微信支付权限',
        'BALANCE_INSUFFICIENT': '商户账户余额不足'
      }[error.code] || error.message

      uni.showToast({
        title: message,
        icon: 'none',
        duration: 3000
      })

      Sentry.captureException(error) // 错误监控上报
    },
    /**
     * 封装微信商户转账方法
     * @param {Object} transferData - 转账参数对象
     * @returns {Promise<WechatTransferResponse>} 返回转账结果Promise
     */
    callWechatTransfer: function (transferData) {
      return new Promise((resolve, reject) => {
        // 记录转账请求开始
        console.log('发起微信商户转账:', JSON.stringify(transferData, null, 2))

        // 检查参数是否有效
        if (!transferData) {
          console.error('转账参数无效')
          return reject(new Error('转账参数无效'))
        }

        uni.requestMerchantTransfer({
          mchId: "1638474608",
          appId: "wxebe732d88afb8986",
          package: transferData,
          success: (res) => {
            console.log('发起微信商户提现res:', res)
            // 成功日志分级处理
            if (res.errMsg === 'ok') {
              console.info('微信提现成功:', res)
              resolve({
                success: true,
                transactionId: res.transactionId,
                timeStamp: res.timeStamp,
                rawData: res
              })
            } else {
              console.warn('微信提现返回异常:', res)
              resolve({
                success: false,
                code: 'WECHAT_TRANSFER_PENDING',
                message: '转账处理中，请稍后查询结果'
              })
            }
          },
          fail: (err) => {
            // 错误分类处理
            const errorMap = {
              'requestMerchantTransfer:fail no permission': '无支付权限',
              'requestMerchantTransfer:fail balance insufficient': '余额不足'
            }

            const error = new Error(errorMap[err.errMsg] || '微信转账失败')
            error.code = err.errMsg.replace('requestMerchantTransfer:fail ', '')

            console.error('微信转账失败:', error.message, err)
            reject(err)
          }
        })
      })
    },
    // 处理微信提现
    async handleWithdrawWechat() {
      try {
        if (!this.keyboard.valueNumber || this.keyboard.valueNumber <= 0) {
          console.log('请输入有效金额 ', this.keyboard.valueNumber)
          uni.showToast({
            title: '请输入有效金额',
            icon: 'none'
          })
          return
        }
        if (this.keyboard.valueNumber < 0.1 || this.keyboard.valueNumber > 200) {
          console.log('请输入有效金额 ', this.keyboard.valueNumber)
          uni.showToast({
            title: '提现金额需要在0.1和200之间',
            icon: 'none'
          })
          return
        }
        console.log('提现金额 ', this.keyboard.valueNumber)
        const money = this.keyboard.valueNumber.toString()
        // 检查当前运行环境
        const platform = uni.getSystemInfoSync().platform
        const appPlatform = process.env.VUE_APP_PLATFORM
        console.log('当前环境：', platform, appPlatform, this.openid)
        // 显示加载提示
        uni.showLoading({
          title: '获取授权中...'
        })
        if (this.hasWxAccount() === false) {
          await uni.login({
            provider: 'weixin',
            onlyAuthorize: true,      // 仅请求授权，不弹出多余权限
            success: async (res) => {
              console.log('login 成功', res, res.code);
              const code = res.code
              this.openid = await wxApi.getWxOpenId(code)
              console.log('获取openid ', this.openid)
              await this.bindWechatAccount(this.openid)
            },
            fail: function (err) {
              console.error('login 失败', err);
            }
          });
        }
        uni.hideLoading();
        // 显示加载提示
        uni.showLoading({
          title: '请求提现数据中...'
        })
        const withdrawParams = {
          channel: "2",
          amount: money
        }
        const transferData = await this.requestWithdraw(withdrawParams)
        uni.hideLoading();
        console.log('获取提现参数 transferData ', transferData)
        uni.showLoading({
          title: '微信提现申请中...'
        })
        // 调用微信提现
        const transferResult = await this.callWechatTransfer(transferData)
        console.log('获取提现参数 withDraw ', transferResult)
        uni.hideLoading();
        uni.showLoading({
          title: '微信提现完成结算中...'
        })
        await this.handleTransferSuccess()
        // 隐藏加载提示
        uni.hideLoading()
      } catch (error) {
        console.error('调用提现接口出错', error)
        uni.hideLoading()
        uni.showToast({
          title: error.errMsg,
          icon: 'none'
        })
      }
    },


    // 金额完成/打开验证码
    async onSubmit(e) {
      this.keyboard = e
      console.log('输入金额', this.keyboard.valueNumber)
      if (!this.keyboard.valueNumber || this.keyboard.valueNumber <= 0) {
        uni.showToast({
          title: '请输入有效金额',
          icon: 'none'
        })
        return
      }
      // 根据不同的提现方式调用不同的处理逻辑
      if (this.withdrawMethod === 'wechat') {
        // 调用微信提现逻辑
        this.handleWithdrawWechat()
      } else if (this.withdrawMethod === 'alipay') {
        // 调用支付宝提现逻辑
        await this.getBalance()
        if (this.hasAlipayAccount()) {
          this.handleAlipayBindSuccess()
        } else {
          uni.showToast({
            title: '请先在右上角进行账号设置',
            icon: 'none'
          })
          return
        }
      } else if (this.withdrawMethod === 'bank') {
        // 检查是否选择了银行卡
        if (!this.selectedCard) {
          uni.showToast({
            title: '请选择银行卡',
            icon: 'none'
          })
          return
        }
        // 显示密码输入弹窗，进行银行卡提现
        this.passwordDialogVisible = true
      }
      this.isPullMoney = true
    },
    // 金额输入中
    onChange(e) {
      this.keyboard = e
    },
    onErrorx(errorType) {
      if (errorType === 'maxValue') {
        uni.showToast({
          title: '输入金额不能超过可用余额',
          icon: 'none'
        })
      } else {
        console.log('超过限制')
      }
    },
    handleInput(num) {
      if (this.inputAmount.split('.')[1]?.length >= 2) return
      this.inputAmount += num.toString()
    },
    handleFullWithdraw() {
      this.inputAmount = this.currentBalance.toFixed(2)
    },
    withdrawAll() {
      // 将余额保留两位小数后设置到键盘
      const balanceWithTwoDecimals = Math.floor(this.balance * 100) / 100
      this.$refs.panKeyboardRef.setKeyboard(balanceWithTwoDecimals)
    },
    handlePress() {
      this.isPressing = true
    },
    clearPress() {
      this.isPressing = false
    },
    handleConfirm() {
      // 提现逻辑
    },
    showPaymentOptions() {
      this.paymentVisible = true
    },
    // 添加密码确认处理方法
    async handlePasswordConfirm(password) {
      try {
        const obj = {
          amount: this.keyboard.valueNumber,
          payType: 3, // 银行卡提现
          password: password,
          bankCardId: this.selectedCard.id
        }
        const response = await appServerApi.walletCharge(obj)
        uni.showToast({
          icon: 'success',
          title: '提现申请已提交',
          duration: 1000,
        })
        uni.navigateBack()
      } catch (error) {
        console.error('提现失败:', error)
        uni.showToast({
          title: '提现失败',
          icon: 'none'
        })
      } finally {
        this.passwordDialogVisible = false
      }
    },
    // 处理支付宝提现
    async handleWithdrawAlipay() {
      try {
        if (!this.keyboard.valueNumber || this.keyboard.valueNumber <= 0) {
          uni.showToast({
            title: '请输入有效金额',
            icon: 'none'
          })
          return
        }
        if (this.keyboard.valueNumber < 0.1 || this.keyboard.valueNumber > 200) {
          uni.showToast({
            title: '提现金额需要在0.1和200之间',
            icon: 'none'
          })
          return
        }

        const money = this.keyboard.valueNumber.toString()
        uni.showLoading({
          title: '支付宝提现申请中...'
        })

        const withdrawParams = {
          channel: "1",
          amount: money
        }
        await this.requestWithdraw(withdrawParams)
        uni.hideLoading();
        uni.showLoading({
          title: '支付宝提现处理中...'
        })
        await this.handleTransferSuccess()
        // 隐藏加载提示
        uni.hideLoading()
        uni.showModal({
          content: "支付宝提现申请成功,请在右上角查看提现结果",
          confirmText: "确认"
        })
        this.isPullMoney = true
      } catch (error) {
        console.error('支付宝提现失败:', error)
        uni.hideLoading()
        uni.showToast({
          title: error.message || '提现失败',
          icon: 'none'
        })
      }
    },

    // 处理支付宝绑定成功
    handleAlipayBindSuccess() {
      console.log('支付宝绑定成功，开始处理提现')
      this.isAlipayBound = true
      this.handleWithdrawAlipay()
    },

    // 处理支付宝绑定取消
    handleAlipayBindCancel() {
      console.log('用户取消支付宝绑定')
    },

    showMoreOptions() {
      this.moreOptionsVisible = true
    },

    hideMoreOptions() {
      this.moreOptionsVisible = false
    },

    navigateToAccountSettings() {
      // 实现跳转到账号设置页面的逻辑
      console.log('跳转到账号设置页面')
      uni.navigateTo({
        url: '/pages/wallet/AccountSettings'
      })
      this.hideMoreOptions()
    },

    navigateToWithdrawRecords() {
      // 实现跳转到提现明细页面的逻辑
      console.log('跳转到提现明细页面')
      uni.navigateTo({
        url: '/pages/wallet/WithdrawRecords'
      })
      this.hideMoreOptions()
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  background: #f5f6fa;
  height: 100vh;
}

.status-bar {
  height: 88rpx;
  padding: 0 32rpx;
  font-size: 30rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.nav-bar {
  height: 88rpx;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .left, .right {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }

  .icon-font {
    font-size: 40rpx;
    color: #333;
  }
}

.withdraw-options {
  margin: 32rpx;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

  .withdraw-option {
    padding: 32rpx;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .option-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .option-text {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
      }

      .radio-button {
        width: 36rpx;
        height: 36rpx;
        border: 2rpx solid #ccc;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        &.radio-selected {
          border-color: #178e57;
        }

        .radio-inner {
          width: 24rpx;
          height: 24rpx;
          border-radius: 50%;
          background-color: #178e57;
        }
      }
    }
  }
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin: 32rpx;
  margin-top: 60rpx;
  display: block;
}

.card-section {
  background: #F5F5F5;
  margin: 24rpx 32rpx;
  padding: 32rpx;
  border-radius: 12rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .bank-info {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
  }

  .bank-name {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
  }

  .instant-text {
    font-size: 24rpx;
    color: #999;
  }

  .arrow-right {
    color: #999;
    font-size: 24rpx;
  }
}

.amount-section {
  background: #ffffff;
  margin: 0 32rpx;
  padding: 48rpx 40rpx;
  border-radius: 24rpx;

  .input-row {
    display: flex;
    align-items: center;
    border-bottom: 2rpx solid #eee;
    padding-bottom: 32rpx;
    margin-bottom: 32rpx;

    .currency {
      font-size: 72rpx;
      color: #333;
      margin-right: 24rpx;
      font-weight: 500;
    }

    .amount-input {
      flex: 1;
      font-size: 72rpx;
      height: 100rpx;
      color: #333;
      font-weight: 600;

      &::placeholder {
        color: #cccccc;
      }
    }
  }

  .balance-row {
    display: flex;
    justify-content: space-between;
    font-size: 28rpx;
    color: #666;

    .full-btn {
      color: #007aff;
      font-size: 28rpx;
    }
  }
}

.keyboard {
  // 原有样式保持不变
  .key-btn {
    // 新增状态样式
    &:active {
      background: #f0f0f0 !important;
    }

    &.gray {
      background: #f8f8f8;
      font-size: 44rpx;
    }
  }
}

// 新增删除键动画
@keyframes deleting {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}

.gray:active {
  animation: deleting 0.3s ease;
}

// 优化输入框样式
.amount-input {
  caret-color: #007aff; // 光标颜色
  // 禁用默认样式
  -webkit-appearance: none;
  -moz-appearance: textfield;
}

.placeholder {
  width: 100%;
  height: 20rpx;
  /* #ifdef MP-WEIXIN*/
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom); //适配底部
  padding-bottom: env(safe-area-inset-bottom);
  /*#endif*/
}

.top {
  width: 100%;
  height: 260rpx;
  background-color: #ededed;

  .user {
    width: calc(100% - 120rpx);
    height: 100rpx;
    margin-bottom: 20rpx;

    .user-r {
      .user-r-title {
      }

      .user-r-text {
      }
    }

    .user-img {
      width: 100rpx;
      height: 100rpx;
      border-radius: 10rpx;
      overflow: hidden;
    }
  }
}

.money-box {
  position: relative;
  top: 0;
  margin-top: 32rpx;
  box-sizing: border-box;
  padding: 50rpx 55rpx;
  width: 100%;
  border-radius: 30rpx 30rpx 0 0;
  background-color: #fff;

  .line {
    width: calc(100% - 40rpx);
    margin-top: 40rpx;
    border-bottom: 1px solid #dcdcdc;
  }

  .money-box-title-container {
    width: 100%;
    height: 60rpx;
    margin-bottom: 10rpx;
  }

  .money-box-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
  }

  .money-box-subtitle {
    font-size: 24rpx;
    color: #999;
  }

  .money-box-text {
    position: relative;
    margin-top: 30rpx;
    font-size: 30rpx;
    color: #616161;

    .money-box-text_ {
      position: absolute;
      left: 0;
    }
  }

  .money-box-row {
    width: 100%;
    height: 100rpx;
    font-size: 70rpx;

    .money {
      position: relative;
      font-size: 80rpx;

      .money-text {
        position: absolute;
        bottom: -30rpx;
        left: 0;
      }
    }

    .money-box-row- {
      margin-top: 4rpx;
    }

    .money-box-row-icon {
    }
  }
}

.keyboard {
  position: fixed;
  box-sizing: border-box;
  width: 100%;
  bottom: 0;
  padding: 20rpx;
  background-color: #f7f7f7;
}
.keyboard:before {
  content:'' !important;
}
.cursor {
  position: relative;
  top: 10rpx;
  display: inline-block;
  width: 2px;
  height: 100rpx;
  margin-left: 10rpx;
  animation-name: cursor;
  animation-duration: 0.8s;
  animation-iteration-count: infinite;
  z-index: 1;
  background-color: #178e57;
}

@keyframes cursor {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.shake {
  animation: shake 800ms ease-in-out;
}

@keyframes shake {
  /* 水平抖动，核心代码 */
  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(+2px, 0, 0);
  }

  30%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(+4px, 0, 0);
  }

  50% {
    transform: translate3d(-4px, 0, 0);
  }
}

// ---------------以下代码是作者的公共css（部分）, 可通过npm i pan_css下载完整版 在app.vue中引入；这里就简化使用直接复制进来----------------
.wh_100 {
  width: 100%;
  height: 100%;
}

.z_index2 {
  position: relative;
  z-index: 2;
}

.text_colour_red {
  color: #ff0000;
}

.text_colour_white {
  color: #fff;
}

.text_colour_0 {
  color: #000;
}

.text_colour_1 {
  color: #111;
}

.text_colour_2 {
  color: #222;
}

.text_colour_3 {
  color: #333;
}

.text_colour_4 {
  color: #444;
}

.text_colour_5 {
  color: #555;
}

.text_colour_6 {
  color: #666;
}

.text_colour_7 {
  color: #777;
}

.text_colour_8 {
  color: #888;
}

.text_colour_9 {
  color: #999;
}

.flex_r {
  display: flex;
  flex-direction: row;
}

.flex_c {
  display: flex;
  flex-direction: column;
}

.fj_b {
  justify-content: space-between;
}

.fj_a {
  justify-content: space-around;
}

.fj_c {
  justify-content: center;
}

.fa_c {
  align-items: center;
}

.flex_c_c {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex_r_c {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.flex1 {
  flex: 1;
}

.text_16 {
  font-size: 16rpx;
}

.text_17 {
  font-size: 17rpx;
}

.text_18 {
  font-size: 18rpx;
}

.text_19 {
  font-size: 19rpx;
}

.text_20 {
  font-size: 20rpx;
}

.text_21 {
  font-size: 21rpx;
}

.text_22 {
  font-size: 22rpx;
}

.text_23 {
  font-size: 23rpx;
}

.text_24 {
  font-size: 24rpx;
}

.text_25 {
  font-size: 25rpx;
}

.text_26 {
  font-size: 26rpx;
}

.text_27 {
  font-size: 27rpx;
}

.text_28 {
  font-size: 28rpx;
}

.text_29 {
  font-size: 29rpx;
}

.text_30 {
  font-size: 30rpx;
}

.text_31 {
  font-size: 31rpx;
}

.text_32 {
  font-size: 32rpx;
}

.text_33 {
  font-size: 33rpx;
}

.text_34 {
  font-size: 34rpx;
}

.text_35 {
  font-size: 35rpx;
}

.text_36 {
  font-size: 36rpx;
}

.text_37 {
  font-size: 37rpx;
}

.text_38 {
  font-size: 38rpx;
}

.text_39 {
  font-size: 39rpx;
}

.text_40 {
  font-size: 40rpx;
}

.text_41 {
  font-size: 41rpx;
}

.text_42 {
  font-size: 42rpx;
}

.text_43 {
  font-size: 43rpx;
}

.text_44 {
  font-size: 44rpx;
}

.text_45 {
  font-size: 45rpx;
}

.text_46 {
  font-size: 46rpx;
}

.text_weight_bold {
  font-weight: bold;
}

.balance-info {
  margin-top: 20rpx;
  font-size: 26rpx;

  .balance-text {
    color: #999;
  }

  .withdraw-all {
    color: #007AFF;
  }
}

.more-options-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;

  .popup-content {
    position: absolute;
    top: 88rpx;
    right: 32rpx;
    background-color: #fff;
    border-radius: 12rpx;
    width: 240rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

    .popup-item {
      padding: 24rpx 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        background-color: #f5f5f5;
      }

      .popup-item-text {
        font-size: 28rpx;
        color: #333;
      }
    }

    .popup-divider {
      height: 1rpx;
      background-color: #f0f0f0;
    }
  }
}
</style>