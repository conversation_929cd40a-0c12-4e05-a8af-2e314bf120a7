<template>
    <view class="container">
        <!-- 使用uni-app内置页面滚动，用scroll-view会出问题 -->
        <view class="content-area">
            <view class="month-section" v-for="(month, index) in records" :key="index">
                <view class="month-header">{{ index }} <i class="icon-arrow-down"></i></view>
                <view class="transaction-list">
                    <view class="transaction-item" v-for="(transaction, tIndex) in month" :key="tIndex">
                        <view class="transaction-info">
                            <image class="img" src="../../static/image/icon/balance.png"></image>
                            <view class="info-text">
                                <view class="transaction-type">{{ transaction.description }}</view>
                                <view class="transaction-date">{{ transaction.createTime }}</view>
                            </view>
                        </view>
                        <view class="transaction-amount" :class="{ 'negative': transaction.transactionType == 'in' }">
                            {{ transaction.transactionType=='in'?'+':'-' }}
                            {{ transaction.amount }}
                        </view>
                    </view>
                </view>
            </view>
            <view v-if="loading" class="loading">加载中...</view>
            <view v-if="!hasMore && !loading" class="no-more">没有更多数据了</view>
            <!-- 底部安全区域 -->
            <view style="height: 40px;"></view>
        </view>
    </view>
</template>

<script>
import appServerApi from '@/api/appServerApi'
export default {
    data() {
        return {
            records: {},
            pageNo: 1,
            pageSize: 10, // 增加页面大小，减少分页请求次数
            loading: false,
            hasMore: true,
            isFirstLoad: true,
            firstLoad: true,
            lastLoadTime: 0 // 防抖使用
        }
    },
    // 在页面初始化时执行，只执行一次
    onLoad() {
        this.resetData()
        this.getList()
    },
    // 在页面显示时执行
    onShow() {
        // 只在第一次显示时重置数据
        if (this.firstLoad) {
            this.firstLoad = false
        }
    },
    methods: {
        // 重置数据
        resetData() {
            this.records = {}
            this.pageNo = 1
            this.hasMore = true
            this.loading = false
        },
        // 获取数据
        async getList() {
            // 防止重复加载或没有更多数据时加载
            if (this.loading || !this.hasMore) return

            // 防抖：限制请求频率
            const now = Date.now()
            if (now - this.lastLoadTime < 500) return
            this.lastLoadTime = now

            this.loading = true
            try {
                const params = {
                    pageNo: this.pageNo,
                    pageSize: this.pageSize
                }
                console.log('请求参数:', params)

                const response = await appServerApi.walletRecordpage(params)
                console.log('钱包记录响应:', response)

                const currentPageData = response.data.data.result || []
                const total = response.data.data.total || 0

                // 判断是否还有更多数据
                this.hasMore = this.pageNo * this.pageSize < total

                if (currentPageData.length === 0) {
                    this.hasMore = false
                    this.loading = false
                    return
                }

                // 按日期分组
                const grouped = this.groupDataByDate(currentPageData)

                // 合并数据（处理旧数据，避免全量替换）
                this.mergeRecords(grouped)

                console.log('数据加载完成，当前页：', this.pageNo, '剩余数据：', this.hasMore)
            } catch (error) {
                console.error('获取钱包记录失败:', error)
                this.hasMore = false
                uni.showToast({
                    title: '数据加载失败',
                    icon: 'none'
                })
            } finally {
                this.loading = false
            }
        },

        // 按日期分组数据
        groupDataByDate(data) {
            return data.reduce((acc, item) => {
                const date = item.createTime.split(' ')[0]
                if (!acc[date]) {
                    acc[date] = []
                }
                acc[date].push(item)
                return acc
            }, {})
        },

        // 合并记录，保持响应式
        mergeRecords(newGroupedData) {
            // 使用不可变方式合并数据
            const merged = {...this.records}

            Object.keys(newGroupedData).forEach(date => {
                if (merged[date]) {
                    // 检查并去重（假设每条记录有唯一标识如id）
                    const existingIds = new Set(merged[date].map(item => item.id || item.createTime))
                    const newItems = newGroupedData[date].filter(item =>
                        !existingIds.has(item.id || item.createTime)
                    )

                    if (newItems.length > 0) {
                        merged[date] = [...merged[date], ...newItems]
                    }
                } else {
                    merged[date] = [...newGroupedData[date]]
                }
            })

            // 整体替换以触发响应式更新
            this.records = merged
        },

        // 加载更多数据
        loadMore() {
            if (!this.loading && this.hasMore) {
                this.pageNo++
                this.getList()
            }
        }
    },
    // 页面滚动监听
    onPageScroll(e) {
        this.showBackTop = e.scrollTop > 200
    },
    // 触底加载更多
    onReachBottom() {
        console.log('触底加载更多')
        this.loadMore()
    },
    // 下拉刷新
    onPullDownRefresh() {
        console.log('下拉刷新')
        this.resetData()
        this.getList().then(() => {
            uni.stopPullDownRefresh()
        })
    }
}
</script>

<style lang="scss" scoped>
.container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
    box-sizing: border-box;
    position: relative;
}

.content-area {
    width: 100%;
}

.month-section {
    margin-bottom: 20px;
}

.month-header {
    font-size: 15px;
    margin-bottom: 10px;
    background-color: #f5f5f5;
    padding: 5px 0;
    font-weight: 500;
    color: #333;
}

.transaction-list {
    background-color: #fff;
    border-radius: 8px;
    padding: 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 10px;
    border-bottom: 1px solid #f0f0f0;
    box-sizing: border-box;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-info {
    flex: 1;
    display: flex;
    align-items: center;

    .img {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        flex-shrink: 0;
    }

    .info-text {
        margin-left: 10px;
        flex: 1;
    }
}

.transaction-type {
    font-size: 16px;
    color: #333;
    font-weight: 500;
    line-height: 1.3;
}

.transaction-date {
    font-size: 12px;
    color: #999;
    margin-top: 3px;
}

.transaction-amount {
    font-size: 16px;
    font-weight: bold;
    color: #4caf50;
}

.transaction-amount.negative {
    color: #f44336;
}

.loading,
.no-more {
    text-align: center;
    padding: 15px 0;
    color: #999;
    font-size: 14px;
}
</style>