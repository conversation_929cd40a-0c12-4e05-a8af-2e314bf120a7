<template>
  <view class="container">
    <!-- 导航栏 -->
    <CustomHeader title="账号设置" @back="goBack"></CustomHeader>

    <view class="account-section">
      <view class="section-title">支付宝账号信息</view>
      
      <view class="input-group">
        <view class="input-item">
          <text class="input-label">支付宝账号</text>
          <input 
            class="input-field" 
            type="text" 
            v-model="alipayAccount" 
            placeholder="请输入支付宝账号" 
          />
        </view>
        
        <view class="input-item">
          <text class="input-label">真实姓名</text>
          <input 
            class="input-field" 
            type="text" 
            v-model="realname" 
            placeholder="请输入支付宝实名认证的姓名" 
          />
        </view>
      </view>
      
      <view class="tips">
        <text class="tips-text">* 为了确保提现成功，请填写与支付宝实名认证一致的信息</text>
      </view>

      <button class="submit-btn" @tap="saveSettings">保存</button>
    </view>
  </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import appServerApi from '@/api/appServerApi'

export default {
  name: 'AccountSettings',
  components: {
    CustomHeader
  },
  data() {
    return {
      alipayAccount: '',
      realname: '',
      loading: false
    }
  },
  async onLoad() {
    await this.getAccountInfo()
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    /**
     * 获取账户信息
     * 从服务器获取已保存的支付宝账号和姓名信息
     */
    async getAccountInfo() {
      try {
        uni.showLoading({
          title: '加载中...'
        })
        
        const response = await appServerApi.mywallet()
        if (response && response.data && response.data.data) {
          const data = response.data.data
          this.alipayAccount = data.alipayAccount || ''
          this.realname = data.realname || ''
          
          console.log('获取账户信息成功:', data)
        }
      } catch (error) {
        console.error('获取账户信息失败:', error)
        uni.showToast({
          title: '获取账户信息失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },
    
    /**
     * 保存账户设置
     * 将支付宝账号和姓名信息保存到服务器
     */
    async saveSettings() {
      // 输入验证
      if (!this.alipayAccount) {
        uni.showToast({
          title: '请输入支付宝账号',
          icon: 'none'
        })
        return
      }
      
      if (!this.realname) {
        uni.showToast({
          title: '请输入真实姓名',
          icon: 'none'
        })
        return
      }
      
      try {
        this.loading = true
        uni.showLoading({
          title: '保存中...'
        })
        
        // 调用绑定支付宝账号API
        const response = await appServerApi.bindAlipay({
          alipayAccount: this.alipayAccount,
          realname: this.realname
        })
        
        console.log('保存账户信息成功:', response)
        
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
        
        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } catch (error) {
        console.error('保存账户信息失败:', error)
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
        uni.hideLoading()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background: #f5f6fa;
  min-height: 100vh;
}

.account-section {
  margin: 32rpx;
  padding: 40rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 40rpx;
}

.input-group {
  margin-bottom: 40rpx;
}

.input-item {
  margin-bottom: 32rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.input-field {
  width: 100%;
  height: 88rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #333;
  box-sizing: border-box;
}

.tips {
  margin-bottom: 60rpx;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #178e57;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:active {
    opacity: 0.8;
  }
}
</style> 