<template>
    <view class="container">
        <view class="title">钱包密码设置</view>

        <view class="form-group">
            <input class="input-item" type="password" inputmode="numeric" v-model="password" placeholder="输入支付密码" placeholder-class="placeholder" maxlength="6" @input="handleInput" />
            <input class="input-item" type="password" inputmode="numeric" v-model="confirmPassword" placeholder="确认支付密码" placeholder-class="placeholder" maxlength="6" @input="handleInput" />
        </view>

        <view class="tips">
            <text class="tips-text">密码为6位数字</text>
        </view>
        <!--   -->
        <button class="submit-btn" :disabled="!isValid" :class="{'submit-btn-active': isValid}" @click="handleSubmit">
            确认
        </button>

        <!-- 协议勾选 -->
        <view class="agreement">
            <checkbox-group @change="handleAgreementChange">
                <label class="agreement-label">
                    <checkbox :checked="isAgreed" :value="'1'" color="#386BF6" class="round-checkbox" />
                    <text class="agreement-text">钱包服务协议</text>
                </label>
            </checkbox-group>
        </view>
    </view>
</template>

<script>
import appServerApi from '@/api/appServerApi'
export default {
    data() {
        return {
            password: '',
            confirmPassword: '',
            isAgreed: false,
        }
    },

    computed: {
        isValid() {
            // 验证密码是否为6位数字
            const passwordRegex = /^\d{6}$/
            return (
                this.password &&
                this.confirmPassword &&
                this.password === this.confirmPassword &&
                passwordRegex.test(this.password) &&
                this.isAgreed
            )
        },
    },

    methods: {
        handleInput(e) {
            // 限制只能输入数字
            const value = e.target.value.replace(/\D/g, '')
            if (e.target === this.password) {
                this.password = value
            } else {
                this.confirmPassword = value
            }
        },

        handleAgreementChange(e) {
            this.isAgreed = e.detail.value.length > 0
        },

        async handleSubmit() {
            uni.navigateTo({
                url: '/pages/wallet/WalletHome',
            })
            if (!this.isValid) return

            if (this.password !== this.confirmPassword) {
                uni.showToast({
                    title: '两次输入的密码不一致',
                    icon: 'none',
                })
                return
            }

            if (!this.isAgreed) {
                uni.showToast({
                    title: '请先同意钱包服务协议',
                    icon: 'none',
                })
                return
            }

            // uni.showLoading({
            //     title: '设置中',
            // })
            // TODO: 调用开通钱包API
            var obj = {
                pwd: this.password,
            }
            const res = await appServerApi.setWalletpwd(obj)
            console.log(res)
            if (res) {
                // uni.hideLoading()
                // uni.showToast({
                //     title: '设置成功',
                //     icon: 'success',
                // })
                // uni.navigateBack()
            }
            // setTimeout(() => {

            //     setTimeout(() => {
            //         uni.navigateBack()
            //     }, 1500)
            // }, 1000)
        },
    },
}
</script>

<style lang="scss" scoped>
.container {
    padding: 20px;
}

.title {
    font-size: 24px;
    font-weight: 500;
    color: #333;
    margin-bottom: 40px;
    text-align: center;
}

.form-group {
    margin-bottom: 12px;
}

.input-item {
    height: 56px;
    font-size: 16px;
    color: #333;
    border-bottom: 1px solid #eeeeee;
    margin-bottom: 20px;

    &:last-child {
        margin-bottom: 0;
    }
}

.placeholder {
    color: #999;
    font-size: 16px;
}

.tips {
    padding: 0 4px;
    margin-bottom: 32px;
}

.tips-text {
    font-size: 14px;
    color: #999;
}

.submit-btn {
    width: 100%;
    height: 48px;
    line-height: 48px;
    text-align: center;
    background-color: #386bf6;
    color: #fff;
    font-size: 16px;
    border-radius: 10px;
    margin-bottom: 20px;

    &:disabled {
        background-color: #cccccc;
    }

    &::after {
        border: none;
    }
}

.agreement {
    display: flex;
    padding-left: 4px;
}

.agreement-label {
    display: flex;
    align-items: center;
    line-height: 1;
}

.agreement-text {
    font-size: 14px;
    color: #386bf6;
}

:deep(.round-checkbox) {
    .wx-checkbox-input,
    .uni-checkbox-input {
        border-radius: 100%;
        width: 16px;
        height: 16px;
        transform: translateY(-1px);
        margin-right: 8px;

        &.uni-checkbox-input-checked,
        &.wx-checkbox-input-checked {
            background-color: #386bf6;
            border-color: #386bf6;
        }
    }
}
</style>