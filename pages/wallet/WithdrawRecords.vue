<template>
  <view class="container">
    <!-- 导航栏 -->
    <CustomHeader title="提现明细" @back="goBack"></CustomHeader>

    <!-- 列表内容 -->
    <view class="records-content">
      <view v-if="isLoading" class="loading-container">
        <uni-load-more status="loading"/>
      </view>

      <template v-else>
        <!-- 暂无数据提示 -->
        <view v-if="records.length === 0" class="empty-container">
          <image class="empty-image" src="/static/images/empty-data.png" mode="aspectFit"></image>
          <text class="empty-text">暂无提现记录</text>
        </view>

        <!-- 记录列表 -->
        <scroll-view
            v-else
            class="records-list"
            scroll-y
            @scrolltolower="loadMoreRecords"
            :refresher-enabled="true"
            :refresher-triggered="refreshing"
            @refresherrefresh="onRefresh"
        >
          <view class="record-item" v-for="(item, index) in records" :key="index">
            <view class="record-info">
              <view class="record-title-row">
                <text class="record-title">{{ getWithdrawTitle(item.channel) }}</text>
                <text class="record-amount" :class="{'amount-negative': true}">{{ item.amount }}</text>
              </view>
              <view class="record-details-row">
                <text class="record-time">{{ formatDate(item.createTime) }}</text>
                <text class="record-status" :class="getStatusClass(item.state)">{{getPayText(item.payType)}}{{ getStatusText(item.state) }}</text>
              </view>
              <view v-if="item.state===-1" class="record-details-row">
                <text class="record-status" :class="getStatusClass(item.state)">{{item.remark}}</text>
              </view>
            </view>
          </view>

          <!-- 加载更多 -->
          <uni-load-more
              v-if="records.length > 0"
              :status="loadMoreStatus"
              :content-text="loadMoreContentText"
          />
        </scroll-view>
      </template>
    </view>
  </view>
</template>

<script>
import CustomHeader from '@/components/custom-header'
import appServerApi from '@/api/appServerApi'

export default {
  name: 'WithdrawRecords',
  components: {
    CustomHeader
  },
  data() {
    return {
      isLoading: true, // 页面加载状态
      refreshing: false, // 下拉刷新状态
      records: [], // 提现记录数据
      pageNo: 1, // 当前页码
      pageSize: 20, // 每页记录数
      hasMore: true, // 是否有更多数据
      loadMoreStatus: 'more', // 加载更多状态
      loadMoreContentText: {
        contentdown: '上拉加载更多',
        contentrefresh: '加载中...',
        contentnomore: '没有更多数据了'
      }
    }
  },
  onLoad() {
    this.fetchRecords()
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },

    /**
     * 获取提现记录
     * 从服务器获取提现记录数据
     */
    async fetchRecords() {
      try {
        this.isLoading = true

        const response = await appServerApi.withdrawRecords({
          pageNo: this.pageNo,
          pageSize: this.pageSize
        })

        if (response && response.data && response.data.data) {
          const data = response.data.data

          if (this.pageNo === 1) {
            this.records = data.result || []
          } else {
            this.records = [...this.records, ...(data.result || [])]
          }

          this.hasMore = data.hasMore || false
          this.loadMoreStatus = this.hasMore ? 'more' : 'noMore'
        }

        console.log('获取提现记录成功:', this.records)
      } catch (error) {
        console.error('获取提现记录失败:', error)
        uni.showToast({
          title: '获取记录失败',
          icon: 'none'
        })
      } finally {
        this.isLoading = false
        this.refreshing = false
      }
    },

    /**
     * 加载更多记录
     * 当滚动到底部时触发加载下一页数据
     */
    loadMoreRecords() {
      if (!this.hasMore || this.isLoading) return

      this.pageNo++
      this.loadMoreStatus = 'loading'
      this.fetchRecords()
    },

    /**
     * 下拉刷新处理
     * 重置页码并重新加载数据
     */
    onRefresh() {
      this.refreshing = true
      this.pageNo = 1
      this.hasMore = true
      this.fetchRecords()
    },

    /**
     * 格式化日期显示
     * @param {string|number} timestamp - 时间戳或日期字符串
     * @returns {string} 格式化后的日期字符串
     */
    formatDate(timestamp) {
      if (!timestamp) return ''

      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hour = date.getHours().toString().padStart(2, '0')
      const minute = date.getMinutes().toString().padStart(2, '0')

      return `${year}-${month}-${day} ${hour}:${minute}`
    },

    /**
     * 获取提现渠道名称
     * @param {string|number} channel - 提现渠道代码
     * @returns {string} 提现渠道名称
     */
    getWithdrawTitle(channel) {
      const channelMap = {
        '1': '提现到支付宝',
        '2': '提现到微信',
        '3': '提现到银行卡'
      }

      return channelMap[channel] || '提现'
    },

    /**
     * 获取提现状态文本
     * @param {string|number} status - 提现状态代码
     * @returns {string} 提现状态文本
     */
    getStatusText(status) {
      const statusMap = {
        '0': '处理中',
        '1': '提现成功',
        '-1': '提现失败'
      }

      return statusMap[status] || '提现失败'
    },

        /**
     * 获取提现状态文本
     * @param {string|number} payType - 提现状态代码
     * @returns {string} 提现状态文本
     */
     getPayText(payType) {
      const payMap = {
        '1': '支付宝',
        '2': '微信',
        '3': '银行卡'
      }

      return payMap[payType] || '未知类型'
    },

    /**
     * 获取状态对应的CSS类名
     * @param {string|number} status - 提现状态代码
     * @returns {string} CSS类名
     */
    getStatusClass(status) {
      const classMap = {
        '1': '',
        '-1': 'status-failed'
      }

      return classMap[status] || ''
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background: #f5f6fa;
  min-height: 100vh;
}

.records-content {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.loading-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 200rpx;
}

.empty-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 200rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.records-list {
  flex: 1;
  height: calc(100vh - 88rpx);
}

.record-item {
  background: #ffffff;
  margin: 20rpx 32rpx;
  padding: 32rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.record-info {
  display: flex;
  flex-direction: column;
}

.record-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.record-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.record-amount {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;

  &.amount-negative {
    color: #ff6b6b;
  }

  &.amount-positive {
    color: #178e57;
  }
}

.record-details-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-time {
  font-size: 26rpx;
  color: #999;
}

.record-status {
  font-size: 26rpx;
  color: #178e57;

  &.status-pending {
    color: #ffa000;
  }

  &.status-failed {
    color: #ff6b6b;
  }
}
</style> 