<template>
    <div class="pick-contact-container">
        <section class="contact-list-container" v-if="users">
            <div class="input-container">
                <input type="text" :placeholder="$t('common.search')" v-model="filterQuery">
                <i class="icon-ion-ios-search"></i>
            </div>
            <div class="friend-list-container">
                <UserListView v-if="!isMultiSelectMode"
                              :enable-pick="true"
                              :users="filterUsers"
                              :click-user-item-func="onClickUser"
                              :show-category-label="showCategoryLabel && !filterQuery"
                              :padding-left="'20px'"
                              enable-category-label-sticky/>
                <CheckableUserListView v-else
                                       :enable-pick="true"
                                       :users="filterUsers"
                                       :show-category-label="showCategoryLabel && !filterQuery"
                                       :padding-left="'20px'"
                                       enable-category-label-sticky/>
            </div>
        </section>
        
        <!-- 多选模式下的已选用户展示和确认按钮 -->
        <section v-if="isMultiSelectMode" class="checked-contact-list-container">
            <div class="content" ref="pickedUserContainer">
                <div class="picked-user" v-for="(user, index) in checkedUsers" :key="index" @click="unpick(user)">
                    <div class="avatar-container">
                        <img class="avatar" :src="user.portrait || '/static/image/icon/150px.png'" alt="">
                    </div>
                </div>
            </div>
            <button class="confirm-btn" 
                    :class="{disabled: checkedUsers.length === 0}" 
                    :disabled="checkedUsers.length === 0" 
                    @click="confirmMultiSelect">
                {{ '完成 (' + checkedUsers.length + ')' }}
            </button>
        </section>
    </div>
</template>

<script>
import store from "@/store";
import UserListView from "../user/UserListView.vue";
import CheckableUserListView from "../user/CheckableUserListView.vue";

export default {
    name: "PickSingleUserPage",
    props: {

    },
    data() {
        return {
            sharedPickState: store.state.pick,
            filterQuery: '',
            scrollLeft: 0,
            users: [],
            showCategoryLabel: true,
            isMultiSelectMode: false,
        }
    },

    onLoad(option) {
        console.log('PickSingleUserPage onLoad')
        // #ifdef APP-NVUE
        const eventChannel = this.$scope.eventChannel; // 兼容APP-NVUE
        // #endif
        // #ifndef APP-NVUE
        const eventChannel = this.getOpenerEventChannel();
        // #endif
        // 监听openerUsers事件，获取上一页面通过eventChannel传送到当前页面的数据
        eventChannel.on('pickOptions', (options) => {
            console.log('pickOptions', options)
            this.users = options.users;
            this.showCategoryLabel = options.showCategoryLabel;
            this.confirmTitle = options.confirmTitle;
        })
    },

    onNavigationBarButtonTap(e) {
        console.log('onNavigationBarButtonTap', e)
        if (e.index === 0) {
            this.toggleMultiSelect();
        }
    },

    onUnload() {
        // 清理多选状态
        this.sharedPickState.users.length = 0;
    },

    methods: {
        toggleMultiSelect() {
            this.isMultiSelectMode = !this.isMultiSelectMode;
            if (!this.isMultiSelectMode) {
                // 切换到单选模式时清空已选用户
                this.sharedPickState.users.length = 0;
            }
            // 更新导航栏按钮文字
            this.updateNavigationBarButton();
        },

        updateNavigationBarButton() {
            const buttonText = this.isMultiSelectMode ? '单选' : '多选';
            // #ifdef APP-PLUS
            const currentWebview = this.$scope.$getAppWebview();
            currentWebview.setTitleNViewButtonText(0, buttonText);
            // #endif
        },

        onClickUser(user) {
            console.log('onClick user', user)
            // #ifdef APP-NVUE
            const eventChannel = this.$scope.eventChannel; // 兼容APP-NVUE
            // #endif
            // #ifndef APP-NVUE
            const eventChannel = this.getOpenerEventChannel();
            // #endif
            eventChannel.emit('pickedUser', user);
            uni.navigateBack();
        },

        unpick(user) {
            store.pickOrUnpickUser(user);
        },

        confirmMultiSelect() {
            const pickedUsers = [...this.sharedPickState.users];
            this.sharedPickState.users.length = 0;

            // #ifdef APP-NVUE
            const eventChannel = this.$scope.eventChannel; // 兼容APP-NVUE
            // #endif
            // #ifndef APP-NVUE
            const eventChannel = this.getOpenerEventChannel();
            // #endif
            eventChannel.emit('pickedUsers', pickedUsers);
            uni.navigateBack();
        },
    },

    computed: {
        filterUsers() {
            console.log('filterUsers', this.filterQuery)
            if (this.filterQuery) {
                return store.filterUsers(this.users, this.filterQuery);
            } else {
                return this.users;
            }
        },

        checkedUsers() {
            return this.sharedPickState.users;
        }
    },

    components: {
        UserListView,
        CheckableUserListView
    },
}
</script>

<style lang="css" scoped>
.pick-contact-container {
    display: flex;
    flex-direction: column;
    position: relative;
    height: 100vh;
    width: 100%;
    overflow: hidden;
}

.contact-list-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    background-color: #f7f7f7;
    overflow: auto;
    padding-bottom: 60px; /* 为多选按钮留出空间 */
}

.contact-list-container .input-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
}

.input-container input {
    height: 35px;
    flex: 1;
    border-radius: 3px;
    border: 1px solid #ededed;
    background-color: white;
    margin: 0 15px;
    padding-left: 20px;
    text-align: left;
    outline: none;
}

/*.input-container input:active {*/
/*    border: 1px solid #4168e0;*/
/*}*/

/*.input-container input:focus {*/
/*    border: 1px solid #4168e0;*/
/*}*/

.input-container i {
    position: absolute;
    left: 20px;
}

.contact-list-container .friend-list-container {
    height: 100%;
    overflow: auto;
}

/* 多选模式样式 */
.checked-contact-list-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background-color: white;
    border-top: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    padding: 0 15px;
    z-index: 1000;
}

.checked-contact-list-container .content {
    flex: 1;
    display: flex;
    align-items: center;
    overflow-x: auto;
    padding-right: 10px;
}

.picked-user {
    margin-right: 8px;
    flex-shrink: 0;
}

.picked-user .avatar-container {
    position: relative;
}

.picked-user .avatar {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    border: 2px solid #3B62E0;
}

.confirm-btn {
    padding: 8px 16px;
    background-color: #3B62E0;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    min-width: 80px;
}

.confirm-btn.disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.confirm-btn:not(.disabled):hover {
    background-color: #2952d3;
}
</style>
