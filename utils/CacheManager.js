// cacheManager.js
class CacheManager {
    static #instance = null;
    
    constructor() {
        if (CacheManager.#instance) {
            return CacheManager.#instance;
        }
        
        this._memoryCache = new Map();
        this._maxMemoryCacheSize = 5000;
        this._maxStorageSize = 100 * 1024 * 1024;
        
        // 定义特殊key集合
        this._specialKeys = new Set(['userId', 'im_token', 'token', 
            'privacy_agreement', 'youth_mode_status', 'gift_list']);
        
        // 冻结实例，防止被修改
        CacheManager.#instance = Object.freeze(this);
    }

    static getInstance() {
        if (!CacheManager.#instance) {
            CacheManager.#instance = new CacheManager();
        }
        return CacheManager.#instance;
    }

    /**
     * 检查是否是特殊key
     * @param {string} key - 要检查的key
     * @returns {boolean} 是否是特殊key
     * @private
     */
    _isSpecialKey(key) {
        return this._specialKeys.has(key);
    }

    /**
     * 获取特殊key的值
     * @param {string} key - 特殊key
     * @returns {any} 对应的值
     * @private
     */
    _getSpecialKeyValue(key) {
        if (this._memoryCache.has(key)) {
			const value = this._memoryCache.get(key)
            // console.log(`从内存获取${key}:`,value);
            return value;
        }
        const value = uni.getStorageSync(key);
        if (value !== null && value !== undefined) {
            // console.log(`从磁盘获取${key}:`,value);
            this._memoryCache.set(key, value);
            return value;
        }
        return "";
    }

    /**
     * 设置特殊key的值
     * @param {string} key - 特殊key
     * @param {any} value - 要设置的值
     * @private
     */
    _setSpecialKeyValue(key, value) {
        try {
            // 存储到内存缓存
            this._memoryCache.set(key, value);
            // 存储到磁盘
            uni.setStorageSync(key, value);
            console.log(`设置${key}成功`);
        } catch (e) {
            console.error(`设置${key}失败:`, e);
        }
    }

    /**
     * 移除指定缓存
     * @param {string} key - 缓存键
     */
    removeItem(key) {
        try {
            // 处理特殊key
            if (this._isSpecialKey(key)) {
                this._memoryCache.delete(key);
                uni.removeStorageSync(key);
                return
            }

            const userId = this.getUserId();
            const fullKey = `${userId}:${key}`;
            // 第一级：内存缓存
            if (this._memoryCache.has(fullKey)) {
                this._memoryCache.delete(fullKey);
            }
            uni.removeStorageSync(fullKey);
        } catch (e) {
            console.error('移除缓存失败:', e);
            // throw new Error(`REMOVE_CACHE_FAILED: ${e.message}`);
        }
    }

    /**
     * 获取用户ID
     * @returns {string} 用户ID
     */
    getUserId() {
        if (this._memoryCache.has('userId')) {
			const userId = this._memoryCache.get('userId')
            // console.log('从内存获取userId:',userId);
            return userId;
        }
        const userId = uni.getStorageSync('userId');
        if (userId !== null && userId !== undefined) {
            console.log('从磁盘获取userId:',userId);
            this._memoryCache.set('userId', userId);
            return userId;
        }
        return "";
    }

    /**
     * 序列化数据
     * @param {any} data - 要序列化的数据
     * @returns {string} 序列化后的字符串
     */
    serialize(data) {
        try {
            // 处理特殊类型
            if (data === undefined) {
                return JSON.stringify({
                    type: 'undefined',
                    data: null,
                    timestamp: Date.now()
                });
            }

            if (data === null) {
                return JSON.stringify({
                    type: 'null',
                    data: null,
                    timestamp: Date.now()
                });
            }

            if (data instanceof Date) {
                return JSON.stringify({
                    type: 'date',
                    data: data.toISOString(),
                    timestamp: Date.now()
                });
            }

            if (data instanceof RegExp) {
                return JSON.stringify({
                    type: 'regexp',
                    data: {
                        source: data.source,
                        flags: data.flags
                    },
                    timestamp: Date.now()
                });
            }

            if (data instanceof Map) {
                return JSON.stringify({
                    type: 'map',
                    data: Array.from(data.entries()),
                    timestamp: Date.now()
                });
            }

            if (data instanceof Set) {
                return JSON.stringify({
                    type: 'set',
                    data: Array.from(data),
                    timestamp: Date.now()
                });
            }

            if (typeof data === 'bigint') {
                return JSON.stringify({
                    type: 'bigint',
                    data: data.toString(),
                    timestamp: Date.now()
                });
            }

            if (typeof data === 'symbol') {
                return JSON.stringify({
                    type: 'symbol',
                    data: data.description,
                    timestamp: Date.now()
                });
            }

            if (data instanceof Error) {
                return JSON.stringify({
                    type: 'error',
                    data: {
                        name: data.name,
                        message: data.message,
                        stack: data.stack
                    },
                    timestamp: Date.now()
                });
            }

            if (ArrayBuffer.isView(data)) {
                return JSON.stringify({
                    type: 'typedarray',
                    data: {
                        constructor: data.constructor.name,
                        buffer: Array.from(new Uint8Array(data.buffer))
                    },
                    timestamp: Date.now()
                });
            }

            if (data instanceof ArrayBuffer) {
                return JSON.stringify({
                    type: 'arraybuffer',
                    data: Array.from(new Uint8Array(data)),
                    timestamp: Date.now()
                });
            }

            // 处理 Vue3 的响应式对象
            if (data && typeof data === 'object' && '__v_isRef' in data) {
                return JSON.stringify({
                    type: 'ref',
                    data: data.value,
                    timestamp: Date.now()
                });
            }

            // 处理基础类型和普通对象
            return JSON.stringify({
                type: typeof data,
                data: data,
                timestamp: Date.now()
            });
        } catch (e) {
            console.error('序列化失败:', e);
            return null;
        }
    }

    /**
     * 反序列化数据
     * @param {string} data - 要反序列化的字符串
     * @returns {any} 反序列化后的数据
     */
    deserialize(data) {
        try {
            if (!data) return null;
            
            const parsed = JSON.parse(data);
            const { type, data: value } = parsed;

            // 根据类型还原数据
            switch (type) {
                case 'undefined':
                    return undefined;
                case 'null':
                    return null;
                case 'date':
                    return new Date(value);
                case 'regexp':
                    return new RegExp(value.source, value.flags);
                case 'map':
                    return new Map(value);
                case 'set':
                    return new Set(value);
                case 'bigint':
                    return BigInt(value);
                case 'symbol':
                    return Symbol(value);
                case 'error':
                    const error = new Error(value.message);
                    error.name = value.name;
                    error.stack = value.stack;
                    return error;
                case 'typedarray':
                    const TypedArray = globalThis[value.constructor];
                    return new TypedArray(new Uint8Array(value.buffer).buffer);
                case 'arraybuffer':
                    return new Uint8Array(value).buffer;
                case 'ref':
                    return ref(value);
                case 'string':
                case 'number':
                case 'boolean':
                    return value;
                case 'object':
                    // 处理 null
                    if (value === null) return null;
                    // 处理数组和普通对象
                    return value;
                default:
                    return value;
            }
        } catch (e) {
            console.error('反序列化失败:', e);
            return null;
        }
    }

    /**
     * 获取缓存数据
     * @param {string} key - 缓存键
     * @returns {any} 缓存数据
     */
    getItem(key) {
        // try {
            // 处理特殊key
            if (this._isSpecialKey(key)) {
                return this._getSpecialKeyValue(key);
            }

            const userId = this.getUserId();
            const fullKey = `${userId}:${key}`;

            // 第一级：内存缓存
            if (this._memoryCache.has(fullKey)) {
                const cachedValue = this._memoryCache.get(fullKey);
                // console.log('从内存获取缓存:', fullKey, cachedValue);    
                return this.deserialize(cachedValue);
            }

            // 第二级：磁盘缓存
            const diskValue = uni.getStorageSync(fullKey);
            if (diskValue !== null && diskValue !== undefined) {
                this._memoryCache.set(fullKey, diskValue);
                // console.log('从磁盘获取缓存:', fullKey, diskValue);
                return this.deserialize(diskValue);
            }

            return null;
        // } catch (e) {
        //     console.error('获取缓存失败:', e);
        //     return null;
        // }
    }

    /**
     * 设置缓存数据
     * @param {string} key - 缓存键
     * @param {any} value - 缓存值
     */
    setItem(key, value) {
        // try {
            if (this._isSpecialKey(key)) {
                return this._setSpecialKeyValue(key, value);
            }
            const userId = this.getUserId();
            const fullKey = `${userId}:${key}`;
            const serializedValue = this.serialize(value);

            if (!serializedValue) {
                console.error('序列化失败，无法存储数据');
                return;
            }

            // 检查存储大小
            if (serializedValue.length > this._maxStorageSize) {
                console.warn('存储数据超过最大限制，可能导致存储失败');
            }

            // 检查内存缓存大小
            if (this._memoryCache.size >= this._maxMemoryCacheSize) {
                console.warn('内存缓存已满，将清除旧数据');
                this._memoryCache.clear();
            }

            this._memoryCache.set(fullKey, serializedValue);
            uni.setStorageSync(fullKey, serializedValue);
        // } catch (e) {
        //     console.error('设置缓存失败:', e);
        // }
    }

    /**
     * 安全清除缓存
     */
    clear() {
        try {
            const privacyValue = uni.getStorageSync('privacy_agreement');
            this._memoryCache.clear();
            // uni.clearStorageSync();
            if (privacyValue) {
                this.setItem('privacy_agreement', privacyValue);
            }
        } catch (e) {
            console.error('清除缓存失败:', e);
            throw new Error(`CLEAR_CACHE_FAILED: ${e.message}`);
        }
    }

    /**
     * 设置带过期时间的缓存
     * @param {string} key - 缓存键
     * @param {any} value - 缓存值
     * @param {number} ttl - 过期时间（毫秒）
     */
    setWithExpiry(key, value, ttl = 3600000) {
        const item = {
            value: value,
            expiry: Date.now() + ttl
        };
        this.setItem(key, item);
    }

    /**
     * 获取带过期时间的缓存
     * @param {string} key - 缓存键
     * @returns {any} 缓存值
     */
    getWithExpiry(key) {
        const item = this.getItem(key);
        if (!item) return null;

        const now = Date.now();
        return now > item.expiry ? null : item.value;
    }

    /**
     * 清除内存缓存
     */
    clearMemoryCache() {
        this._memoryCache.clear();
    }
}

// 导出冻结的单例实例
export default Object.freeze(CacheManager.getInstance());
