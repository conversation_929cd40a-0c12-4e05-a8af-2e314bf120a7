/*
 * Copyright 2014 The Netty Project
 *
 * The Netty Project licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

package io.netty.handler.codec.mqtt;

import io.moquette.server.netty.NettyMQTTHandler;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.DecoderException;
import io.netty.handler.codec.ReplayingDecoder;
import io.netty.handler.codec.TooLongFrameException;
import io.netty.util.CharsetUtil;
import io.netty.util.internal.ObjectUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 从字节流中解码MQTT消息，遵循MQTT协议规范
 * <a href="https://public.dhe.ibm.com/software/dw/webservices/ws-mqtt/mqtt-v3r1.html">v3.1</a>
 * 或
 * <a href="https://docs.oasis-open.org/mqtt/mqtt/v5.0/mqtt-v5.0.html">v5.0</a>，
 * 具体取决于首次通过通道的CONNECT消息中指定的版本。
 * 
 * MQTT是一种轻量级的发布/订阅消息传输协议，专为受限设备和低带宽、高延迟或不可靠的网络设计。
 * 该解码器负责将二进制数据流解析为MQTT协议消息对象。
 */
/**
 * MQTT消息解码器，负责将二进制数据解析为MQTT协议消息对象
 * 继承自ReplayingDecoder，这是Netty提供的一种特殊解码器，可以处理半包问题
 */
public final class MqttDecoder extends ReplayingDecoder<MqttDecoder.DecoderState> {


    // 静态日志记录器，用于记录该类的日志信息
    private static final Logger LOG = LoggerFactory.getLogger(MqttDecoder.class);
    /**
     * 解码器的状态枚举
     * 我们从READ_FIXED_HEADER开始，然后是
     * READ_VARIABLE_HEADER，最后是READ_PAYLOAD
     * 
     * MQTT消息由三部分组成：
     * 1. 固定头部(Fixed Header) - 所有MQTT控制包都包含
     * 2. 可变头部(Variable Header) - 某些MQTT控制包包含
     * 3. 有效载荷(Payload) - 某些MQTT控制包包含
     */
    enum DecoderState {
        READ_FIXED_HEADER,    // 读取固定头部状态
        READ_VARIABLE_HEADER, // 读取可变头部状态
        READ_PAYLOAD,         // 读取有效载荷状态
        BAD_MESSAGE,          // 错误消息状态
    }

    // 当前解码的MQTT固定头部
    private MqttFixedHeader mqttFixedHeader;
    // 当前解码的可变头部
    private Object variableHeader;
    // 可变部分中剩余的字节数
    private int bytesRemainingInVariablePart;

    // 消息中允许的最大字节数
    private final int maxBytesInMessage;
    // 客户端ID允许的最大长度
    private final int maxClientIdLength;

    /**
     * 默认构造函数，使用默认的最大消息字节数和客户端ID长度
     */
    public MqttDecoder() {
      this(MqttConstant.DEFAULT_MAX_BYTES_IN_MESSAGE, MqttConstant.DEFAULT_MAX_CLIENT_ID_LENGTH);
    }

    /**
     * 构造函数，指定最大消息字节数，使用默认的客户端ID长度
     * 
     * @param maxBytesInMessage 消息中允许的最大字节数
     */
    public MqttDecoder(int maxBytesInMessage) {
        this(maxBytesInMessage, MqttConstant.DEFAULT_MAX_CLIENT_ID_LENGTH);
    }

    /**
     * 构造函数，指定最大消息字节数和客户端ID长度
     * 
     * @param maxBytesInMessage 消息中允许的最大字节数
     * @param maxClientIdLength 客户端ID允许的最大长度
     */
    public MqttDecoder(int maxBytesInMessage, int maxClientIdLength) {
        super(DecoderState.READ_FIXED_HEADER); // 初始状态为读取固定头部
        this.maxBytesInMessage = ObjectUtil.checkPositive(maxBytesInMessage, "maxBytesInMessage");
        this.maxClientIdLength = ObjectUtil.checkPositive(maxClientIdLength, "maxClientIdLength");
    }

    /**
     * 解码方法，根据当前状态解析MQTT消息
     * 
     * @param ctx 通道处理上下文
     * @param buffer 输入的字节缓冲区
     * @param out 解码后的输出列表
     */
    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf buffer, List<Object> out) throws Exception {
        LOG.info("decode: buffer = {}, out = {}", buffer, out);
        switch (state()) {
            case READ_FIXED_HEADER: try {
                // 解码固定头部
                mqttFixedHeader = decodeFixedHeader(ctx, buffer);
                // 获取可变部分的剩余字节数
                bytesRemainingInVariablePart = mqttFixedHeader.remainingLength();
                // 更新解码器状态为读取可变头部
                checkpoint(DecoderState.READ_VARIABLE_HEADER);
                // 继续执行下一个状态的处理（不中断，直接进入下一个case）
            } catch (Exception cause) {
                cause.printStackTrace();
                // 如果解析固定头部出错，添加一个无效消息到输出列表
                out.add(invalidMessage(cause));
                return;
            }

            case READ_VARIABLE_HEADER:  try {
                // 解码可变头部
                final Result<?> decodedVariableHeader = decodeVariableHeader(ctx, buffer, mqttFixedHeader);
                variableHeader = decodedVariableHeader.value;
                // 检查消息大小是否超过限制
                if (bytesRemainingInVariablePart > maxBytesInMessage) {
                    buffer.skipBytes(actualReadableBytes());
                    throw new TooLongFrameException("too large message: " + bytesRemainingInVariablePart + " bytes");
                }
                // 更新剩余字节数
                bytesRemainingInVariablePart -= decodedVariableHeader.numberOfBytesConsumed;
                // 更新解码器状态为读取有效载荷
                checkpoint(DecoderState.READ_PAYLOAD);
                // 继续执行下一个状态的处理
            } catch (Exception cause) {
                cause.printStackTrace();
                // 如果解析可变头部出错，添加一个无效消息到输出列表
                out.add(invalidMessage(cause));
                return;
            }

            case READ_PAYLOAD: try {
                // 解码有效载荷
                final Result<?> decodedPayload =
                        decodePayload(
                                ctx,
                                buffer,
                                mqttFixedHeader.messageType(),
                                bytesRemainingInVariablePart,
                                maxClientIdLength,
                                variableHeader);
                // 更新剩余字节数
                bytesRemainingInVariablePart -= decodedPayload.numberOfBytesConsumed;
                // 确保所有字节都已被消费
                if (bytesRemainingInVariablePart != 0) {
                    throw new DecoderException(
                            "non-zero remaining payload bytes: " +
                                    bytesRemainingInVariablePart + " (" + mqttFixedHeader.messageType() + ')');
                }
                // 重置解码器状态为读取固定头部，准备解析下一条消息
                checkpoint(DecoderState.READ_FIXED_HEADER);
                // 创建完整的MQTT消息对象
                MqttMessage message = MqttMessageFactory.newMessage(
                        mqttFixedHeader, variableHeader, decodedPayload.value);
                // 清除临时变量
                mqttFixedHeader = null;
                variableHeader = null;
                // 将解码后的消息添加到输出列表
                out.add(message);
                break;
            } catch (Exception cause) {
                cause.printStackTrace();
                // 如果解析有效载荷出错，添加一个无效消息到输出列表
                out.add(invalidMessage(cause));
                return;
            }

            case BAD_MESSAGE:
                // 对于错误消息，丢弃所有数据直到连接断开
                buffer.skipBytes(actualReadableBytes());
                break;

            default:
                // 不应该到达这里
                throw new Error();
        }
    }

    /**
     * 创建一个无效的MQTT消息
     * 
     * @param cause 导致消息无效的原因
     * @return 无效的MQTT消息对象
     */
    private MqttMessage invalidMessage(Throwable cause) {
      checkpoint(DecoderState.BAD_MESSAGE);
      return MqttMessageFactory.newInvalidMessage(mqttFixedHeader, variableHeader, cause);
    }

    /**
     * 解码MQTT消息的可变头部（如果有的话）
     * 根据消息类型选择不同的解码方法
     *
     * @param ctx 通道处理上下文
     * @param buffer 输入的字节缓冲区
     * @param mqttFixedHeader 同一消息的固定头部
     * @return 解码后的可变头部及消耗的字节数
     */
    private Result<?> decodeVariableHeader(ChannelHandlerContext ctx, ByteBuf buffer, MqttFixedHeader mqttFixedHeader) {
        switch (mqttFixedHeader.messageType()) {
            case CONNECT:
                // 连接请求消息的可变头部解码
                return decodeConnectionVariableHeader(ctx, buffer);

            case CONNACK:
                // 连接确认消息的可变头部解码
                return decodeConnAckVariableHeader(ctx, buffer);

            case UNSUBSCRIBE:
            case SUBSCRIBE:
            case SUBACK:
            case UNSUBACK:
                // 订阅/取消订阅及其确认消息的可变头部解码
                // 这些消息都包含消息ID和属性
                return decodeMessageIdAndPropertiesVariableHeader(ctx, buffer);

            case PUBACK:
            case PUBREC:
            case PUBCOMP:
            case PUBREL:
                // 发布确认、发布收到、发布完成、发布释放消息的可变头部解码
                return decodePubReplyMessage(buffer);

            case PUBLISH:
                // 发布消息的可变头部解码
                return decodePublishVariableHeader(ctx, buffer, mqttFixedHeader);

            case DISCONNECT:
            case AUTH:
                // 断开连接和认证消息的可变头部解码
                // 包含原因码和属性
                return decodeReasonCodeAndPropertiesVariableHeader(buffer);

            case PINGREQ:
            case PINGRESP:
                // Ping请求和响应消息没有可变头部
                return new Result<Object>(null, 0);
            default:
                // 不应该到达这里
                throw new DecoderException("Unknown message type: " + mqttFixedHeader.messageType());
        }
    }

    /**
     * 解码MQTT消息的固定头部。固定头部由一个字节的标志位和可变字节的剩余长度组成。
     *
     * 固定头部格式：
     * 第1个字节: XXXX YYYY
     *   XXXX - 消息类型（4位）
     *   YYYY - 标志位（4位）：DUP(1位) + QoS(2位) + RETAIN(1位)
     * 第2-5个字节: 剩余长度（可变字节编码）
     *
     * @see
     * <a href="https://docs.oasis-open.org/mqtt/mqtt/v3.1.1/errata01/os/mqtt-v3.1.1-errata01-os-complete.html#_Toc442180841">MQTT v3.1.1规范</a>
     *
     * @param ctx 通道处理上下文
     * @param buffer 输入的字节缓冲区
     * @return 解码后的MQTT固定头部对象
     */
    private static MqttFixedHeader decodeFixedHeader(ChannelHandlerContext ctx, ByteBuf buffer) {
        // 读取第一个字节，包含消息类型和标志位
        short b1 = buffer.readUnsignedByte();

        // 解析消息类型（高4位）
        MqttMessageType messageType = MqttMessageType.valueOf(b1 >> 4);
        // 解析DUP标志（第3位）- 表示是否是重复发送的消息
        boolean dupFlag = (b1 & 0x08) == 0x08;
        // 解析QoS级别（第2-1位）- 服务质量等级
        int qosLevel = (b1 & 0x06) >> 1;
        // 解析RETAIN标志（第0位）- 表示服务器是否应该保留这条消息
        boolean retain = (b1 & 0x01) != 0;

        switch (messageType) {
            case PUBLISH:
                if (qosLevel == 3) {
                    throw new DecoderException("Illegal QOS Level in fixed header of PUBLISH message ("
                        + qosLevel + ')');
                }
                break;

            case PUBREL:
            case SUBSCRIBE:
            case UNSUBSCRIBE:
                if (dupFlag) {
                    throw new DecoderException("Illegal BIT 3 in fixed header of " + messageType
                        + " message, must be 0, found 1");
                }
                if (qosLevel != 1) {
                    throw new DecoderException("Illegal QOS Level in fixed header of " + messageType
                        + " message, must be 1, found " + qosLevel);
                }
                if (retain) {
                    throw new DecoderException("Illegal BIT 0 in fixed header of " + messageType
                        + " message, must be 0, found 1");
                }
                break;

            case AUTH:
            case CONNACK:
            case CONNECT:
            case DISCONNECT:
            case PINGREQ:
            case PINGRESP:
            case PUBACK:
            case PUBCOMP:
            case PUBREC:
            case SUBACK:
            case UNSUBACK:
                if (dupFlag) {
                    throw new DecoderException("Illegal BIT 3 in fixed header of " + messageType
                        + " message, must be 0, found 1");
                }
                if (qosLevel != 0) {
                    throw new DecoderException("Illegal BIT 2 or 1 in fixed header of " + messageType
                        + " message, must be 0, found " + qosLevel);
                }
                if (retain) {
                    throw new DecoderException("Illegal BIT 0 in fixed header of " + messageType
                        + " message, must be 0, found 1");
                }
                break;
            default:
                throw new DecoderException("Unknown message type, do not know how to validate fixed header");
        }

        int remainingLength = 0;
        int multiplier = 1;
        short digit;
        int loops = 0;
        do {
            digit = buffer.readUnsignedByte();
            remainingLength += (digit & 127) * multiplier;
            multiplier *= 128;
            loops++;
        } while ((digit & 128) != 0 && loops < 4);

        // MQTT protocol limits Remaining Length to 4 bytes
        if (loops == 4 && (digit & 128) != 0) {
            throw new DecoderException("remaining length exceeds 4 digits (" + messageType + ')');
        }
        MqttFixedHeader decodedFixedHeader =
            new MqttFixedHeader(messageType, dupFlag, MqttQoS.valueOf(qosLevel), retain, remainingLength);
        return MqttCodecUtil.validateFixedHeader(ctx, MqttCodecUtil.resetUnusedFields(decodedFixedHeader));
    }

    /**
     * 解码MQTT CONNECT消息的可变头部
     * CONNECT消息的可变头部包含协议名称、协议级别、连接标志和保持连接时间
     * 
     * @param ctx 通道处理上下文
     * @param buffer 输入的字节缓冲区
     * @return 解码后的CONNECT消息可变头部及消耗的字节数
     */
    private static Result<MqttConnectVariableHeader> decodeConnectionVariableHeader(
            ChannelHandlerContext ctx,
            ByteBuf buffer) {
        // 解码协议名称字符串（如"MQTT"或"MQIsdp"）
        final Result<String> protoString = decodeString(buffer);
        int numberOfBytesConsumed = protoString.numberOfBytesConsumed;

        // 读取协议级别（如MQTT 3.1.1为0x04，MQTT 5.0为0x05）
        final byte protocolLevel = buffer.readByte();
        numberOfBytesConsumed += 1;

        // 根据协议名称和级别确定MQTT版本
        MqttVersion version = MqttVersion.fromProtocolNameAndLevel(protoString.value, protocolLevel);
        // 在通道上下文中设置MQTT版本，以便后续处理使用
        MqttCodecUtil.setMqttVersion(ctx, version);

        // 读取连接标志字节
        final int b1 = buffer.readUnsignedByte();
        numberOfBytesConsumed += 1;

        // 读取保持连接时间（以秒为单位）
        final int keepAlive = decodeMsbLsb(buffer);
        numberOfBytesConsumed += 2;

        // 解析连接标志位
        // 用户名标志 - 第7位，表示是否包含用户名
        final boolean hasUserName = (b1 & 0x80) == 0x80;
        // 密码标志 - 第6位，表示是否包含密码
        final boolean hasPassword = (b1 & 0x40) == 0x40;
        // 遗嘱保留标志 - 第5位，表示遗嘱消息是否应该被保留
        final boolean willRetain = (b1 & 0x20) == 0x20;
        // 遗嘱QoS - 第4-3位，表示遗嘱消息的服务质量等级
        final int willQos = (b1 & 0x18) >> 3;
        // 遗嘱标志 - 第2位，表示是否有遗嘱消息
        final boolean willFlag = (b1 & 0x04) == 0x04;
        // 清理会话标志 - 第1位，表示是否清除之前的会话状态
        final boolean cleanSession = (b1 & 0x02) == 0x02;
        // 对于MQTT 3.1.1和MQTT 5.0版本，检查保留位（第0位）是否为0
        if (version == MqttVersion.MQTT_3_1_1 || version == MqttVersion.MQTT_5) {
            final boolean zeroReservedFlag = (b1 & 0x01) == 0x0;
            if (!zeroReservedFlag) {
                // MQTT v3.1.1规范要求：服务器必须验证CONNECT控制包中的保留标志
                // 是否设置为零，如果不是零，则断开客户端连接。
                // 参见 https://docs.oasis-open.org/mqtt/mqtt/v3.1.1/os/mqtt-v3.1.1-os.html#_Toc385349230
                throw new DecoderException("non-zero reserved flag");
            }
        }

        // 解码MQTT 5.0属性（如果适用）
        final MqttProperties properties;
        if (version == MqttVersion.MQTT_5) {
            // MQTT 5.0版本包含属性字段
            final Result<MqttProperties> propertiesResult = decodeProperties(buffer);
            properties = propertiesResult.value;
            numberOfBytesConsumed += propertiesResult.numberOfBytesConsumed;
        } else {
            // MQTT 3.1.1及以下版本没有属性字段
            properties = MqttProperties.NO_PROPERTIES;
        }

        // 创建CONNECT消息可变头部对象，包含所有解析出的字段
        final MqttConnectVariableHeader mqttConnectVariableHeader = new MqttConnectVariableHeader(
                version.protocolName(),    // 协议名称
                version.protocolLevel(),   // 协议级别
                hasUserName,               // 是否包含用户名
                hasPassword,               // 是否包含密码
                willRetain,                // 遗嘱消息是否保留
                willQos,                   // 遗嘱消息的QoS级别
                willFlag,                  // 是否有遗嘱消息
                cleanSession,              // 是否清除会话
                keepAlive,                 // 保持连接时间
                properties);               // MQTT 5.0属性
        return new Result<MqttConnectVariableHeader>(mqttConnectVariableHeader, numberOfBytesConsumed);
    }

    private static Result<MqttConnAckVariableHeader> decodeConnAckVariableHeader(
            ChannelHandlerContext ctx,
            ByteBuf buffer) {
        final MqttVersion mqttVersion = MqttCodecUtil.getMqttVersion(ctx);
        final boolean sessionPresent = (buffer.readUnsignedByte() & 0x01) == 0x01;
        byte returnCode = buffer.readByte();
        int numberOfBytesConsumed = 2;

        final MqttProperties properties;
        if (mqttVersion == MqttVersion.MQTT_5) {
            final Result<MqttProperties> propertiesResult = decodeProperties(buffer);
            properties = propertiesResult.value;
            numberOfBytesConsumed += propertiesResult.numberOfBytesConsumed;
        } else {
            properties = MqttProperties.NO_PROPERTIES;
        }

        final MqttConnAckVariableHeader mqttConnAckVariableHeader =
                new MqttConnAckVariableHeader(MqttConnectReturnCode.valueOf(returnCode), sessionPresent, properties);
        return new Result<MqttConnAckVariableHeader>(mqttConnAckVariableHeader, numberOfBytesConsumed);
    }

    private static Result<MqttMessageIdAndPropertiesVariableHeader> decodeMessageIdAndPropertiesVariableHeader(
            ChannelHandlerContext ctx,
            ByteBuf buffer) {
        final MqttVersion mqttVersion = MqttCodecUtil.getMqttVersion(ctx);
        final int packetId = decodeMessageId(buffer);

        final MqttMessageIdAndPropertiesVariableHeader mqttVariableHeader;
        final int mqtt5Consumed;

        if (mqttVersion == MqttVersion.MQTT_5) {
            final Result<MqttProperties> properties = decodeProperties(buffer);
            mqttVariableHeader = new MqttMessageIdAndPropertiesVariableHeader(packetId, properties.value);
            mqtt5Consumed = properties.numberOfBytesConsumed;
        } else {
            mqttVariableHeader = new MqttMessageIdAndPropertiesVariableHeader(packetId,
                    MqttProperties.NO_PROPERTIES);
            mqtt5Consumed = 0;
        }

        return new Result<MqttMessageIdAndPropertiesVariableHeader>(mqttVariableHeader,
                2 + mqtt5Consumed);
    }

    private Result<MqttPubReplyMessageVariableHeader> decodePubReplyMessage(ByteBuf buffer) {
        final int packetId = decodeMessageId(buffer);

        final MqttPubReplyMessageVariableHeader mqttPubAckVariableHeader;
        final int consumed;
        final int packetIdNumberOfBytesConsumed = 2;
        if (bytesRemainingInVariablePart > 3) {
            final byte reasonCode = buffer.readByte();
            final Result<MqttProperties> properties = decodeProperties(buffer);
            mqttPubAckVariableHeader = new MqttPubReplyMessageVariableHeader(packetId,
                    reasonCode,
                    properties.value);
            consumed = packetIdNumberOfBytesConsumed + 1 + properties.numberOfBytesConsumed;
        } else if (bytesRemainingInVariablePart > 2) {
            final byte reasonCode = buffer.readByte();
            mqttPubAckVariableHeader = new MqttPubReplyMessageVariableHeader(packetId,
                    reasonCode,
                    MqttProperties.NO_PROPERTIES);
            consumed = packetIdNumberOfBytesConsumed + 1;
        } else {
            mqttPubAckVariableHeader = new MqttPubReplyMessageVariableHeader(packetId,
                    (byte) 0,
                    MqttProperties.NO_PROPERTIES);
            consumed = packetIdNumberOfBytesConsumed;
        }

        return new Result<MqttPubReplyMessageVariableHeader>(mqttPubAckVariableHeader, consumed);
    }

    private Result<MqttReasonCodeAndPropertiesVariableHeader> decodeReasonCodeAndPropertiesVariableHeader(
            ByteBuf buffer) {
        final byte reasonCode;
        final MqttProperties properties;
        final int consumed;
        if (bytesRemainingInVariablePart > 1) {
            reasonCode = buffer.readByte();
            final Result<MqttProperties> propertiesResult = decodeProperties(buffer);
            properties = propertiesResult.value;
            consumed = 1 + propertiesResult.numberOfBytesConsumed;
        } else if (bytesRemainingInVariablePart > 0) {
            reasonCode = buffer.readByte();
            properties = MqttProperties.NO_PROPERTIES;
            consumed = 1;
        } else {
            reasonCode = 0;
            properties = MqttProperties.NO_PROPERTIES;
            consumed = 0;
        }
        final MqttReasonCodeAndPropertiesVariableHeader mqttReasonAndPropsVariableHeader =
                new MqttReasonCodeAndPropertiesVariableHeader(reasonCode, properties);

        return new Result<MqttReasonCodeAndPropertiesVariableHeader>(
                mqttReasonAndPropsVariableHeader,
                consumed);
    }

    /**
     * 解码MQTT PUBLISH消息的可变头部
     * PUBLISH消息的可变头部包含主题名称、消息ID（仅当QoS > 0时）和属性（MQTT 5.0）
     * 
     * @param ctx 通道处理上下文
     * @param buffer 输入的字节缓冲区
     * @param mqttFixedHeader PUBLISH消息的固定头部
     * @return 解码后的PUBLISH消息可变头部及消耗的字节数
     */
    private Result<MqttPublishVariableHeader> decodePublishVariableHeader(
            ChannelHandlerContext ctx,
            ByteBuf buffer,
            MqttFixedHeader mqttFixedHeader) {
        // 获取MQTT版本
        final MqttVersion mqttVersion = MqttCodecUtil.getMqttVersion(ctx);
        
        // 解码主题名称
        final Result<String> decodedTopic = decodeString(buffer);
        // 验证主题名称是否有效（发布主题不能包含通配符）
        if (!MqttCodecUtil.isValidPublishTopicName(decodedTopic.value)) {
            throw new DecoderException("invalid publish topic name: " + decodedTopic.value + " (contains wildcards)");
        }
        int numberOfBytesConsumed = decodedTopic.numberOfBytesConsumed;

        // 解码消息ID（仅当QoS > 0时）
        int messageId = -1;
        if (mqttFixedHeader.qosLevel().value() > 0) {
            messageId = decodeMessageId(buffer);
            numberOfBytesConsumed += 2;
        }

        // 解码MQTT 5.0属性（如果适用）
        final MqttProperties properties;
        if (mqttVersion == MqttVersion.MQTT_5) {
            final Result<MqttProperties> propertiesResult = decodeProperties(buffer);
            properties = propertiesResult.value;
            numberOfBytesConsumed += propertiesResult.numberOfBytesConsumed;
        } else {
            // MQTT 3.1.1及以下版本没有属性
            properties = MqttProperties.NO_PROPERTIES;
        }

        // 创建PUBLISH消息可变头部对象
        final MqttPublishVariableHeader mqttPublishVariableHeader =
                new MqttPublishVariableHeader(decodedTopic.value, messageId, properties);
        return new Result<MqttPublishVariableHeader>(mqttPublishVariableHeader, numberOfBytesConsumed);
    }

    /**
     * @return messageId with numberOfBytesConsumed is 2
     */
    /**
     * 解码MQTT消息ID
     * 消息ID是一个16位的无符号整数，用于标识QoS > 0的消息
     * 
     * @param buffer 输入的字节缓冲区
     * @return 解码后的消息ID
     * @throws DecoderException 如果消息ID无效
     */
    private static int decodeMessageId(ByteBuf buffer) {
        // 从缓冲区中读取消息ID（MSB和LSB）
        final int messageId = decodeMsbLsb(buffer);
        // 验证消息ID是否有效（必须在1-65535范围内）
        if (!MqttCodecUtil.isValidMessageId(messageId)) {
            throw new DecoderException("invalid messageId: " + messageId);
        }
        return messageId;
    }

    /**
     * Decodes the payload.
     *
     * @param buffer the buffer to decode from
     * @param messageType  type of the message being decoded
     * @param bytesRemainingInVariablePart bytes remaining
     * @param variableHeader variable header of the same message
     * @return the payload
     */
    private static Result<?> decodePayload(
            ChannelHandlerContext ctx,
            ByteBuf buffer,
            MqttMessageType messageType,
            int bytesRemainingInVariablePart,
            int maxClientIdLength,
            Object variableHeader) {
                // 根据MQTT消息类型选择相应的解码方法
        switch (messageType) {
            case CONNECT:
                // 解码CONNECT消息的有效载荷，包含客户端ID、遗嘱信息、用户名密码等
                return decodeConnectionPayload(buffer, maxClientIdLength, (MqttConnectVariableHeader) variableHeader);

            case SUBSCRIBE:
                // 解码SUBSCRIBE消息的有效载荷，包含订阅主题列表和QoS级别
                return decodeSubscribePayload(buffer, bytesRemainingInVariablePart);

            case SUBACK:
                // 解码SUBACK消息的有效载荷，包含订阅确认的返回码列表
                return decodeSubackPayload(buffer, bytesRemainingInVariablePart);

            case UNSUBSCRIBE:
                // 解码UNSUBSCRIBE消息的有效载荷，包含要取消订阅的主题列表
                return decodeUnsubscribePayload(buffer, bytesRemainingInVariablePart);

            case UNSUBACK:
                // 解码UNSUBACK消息的有效载荷，包含取消订阅确认的返回码列表
                return decodeUnsubAckPayload(ctx, buffer, bytesRemainingInVariablePart);

            case PUBLISH:
                // 解码PUBLISH消息的有效载荷，包含实际发布的消息内容
                return decodePublishPayload(buffer, bytesRemainingInVariablePart);

            default:
                // 未知的消息类型，不消耗任何字节，返回空结果
                return new Result<Object>(null, 0);
        }
    }

    /**
     * 解码MQTT CONNECT消息的有效载荷
     * CONNECT消息的有效载荷包含客户端标识符、遗嘱主题、遗嘱消息、用户名和密码等字段
     *
     * @param buffer 输入的字节缓冲区，包含待解码的CONNECT消息有效载荷数据
     * @param maxClientIdLength 客户端ID允许的最大长度，用于验证客户端ID的有效性
     * @param mqttConnectVariableHeader CONNECT消息的可变头部，包含协议版本、连接标志等信息
     * @return 解码后的CONNECT消息有效载荷及消耗的字节数，封装在Result对象中
     * @throws MqttIdentifierRejectedException 当客户端ID无效时抛出此异常
     */
    private static Result<MqttConnectPayload> decodeConnectionPayload(
            ByteBuf buffer,
            int maxClientIdLength,
            MqttConnectVariableHeader mqttConnectVariableHeader) {
        // 解码客户端标识符(ClientId) - MQTT协议中每个客户端的唯一标识
        final Result<String> decodedClientId = decodeString(buffer);
        // 获取解码后的客户端ID字符串值
        final String decodedClientIdValue = decodedClientId.value;
        // 根据协议名称和级别确定MQTT版本（3.1、3.1.1、5.0等）
        final MqttVersion mqttVersion = MqttVersion.fromProtocolNameAndLevel(mqttConnectVariableHeader.name(),
                (byte) mqttConnectVariableHeader.version());
        // 验证客户端ID是否有效（长度、字符集等符合MQTT协议规范）
        if (!MqttCodecUtil.isValidClientId(mqttVersion, maxClientIdLength, decodedClientIdValue)) {
            // 如果客户端ID无效，抛出标识符拒绝异常
            throw new MqttIdentifierRejectedException("invalid clientIdentifier: " + decodedClientIdValue);
        }
        // 初始化已消耗字节数计数器，从客户端ID解码消耗的字节数开始
        int numberOfBytesConsumed = decodedClientId.numberOfBytesConsumed;

        // 用于存储遗嘱主题和遗嘱消息的变量初始化
        Result<String> decodedWillTopic = null;  // 遗嘱主题解码结果
        byte[] decodedWillMessage = null;        // 遗嘱消息字节数组

        // 解码遗嘱属性和遗嘱消息（仅当连接标志中设置了遗嘱标志时）
        final MqttProperties willProperties;
        if (mqttConnectVariableHeader.isWillFlag()) {
            // 检查连接标志中的遗嘱标志位，如果设置则需要解码遗嘱相关信息

            // 对于MQTT 5.0及以上版本，需要解码遗嘱属性
            if (mqttVersion.protocolLevel() <= MqttVersion.MQTT_5.protocolLevel()) {
                // 解码遗嘱属性（MQTT 5.0新增功能，包含各种遗嘱相关的属性）
                final Result<MqttProperties> propertiesResult = decodeProperties(buffer);
                willProperties = propertiesResult.value;
                // 累加遗嘱属性解码消耗的字节数
                numberOfBytesConsumed += propertiesResult.numberOfBytesConsumed;
            } else {
                // 对于MQTT 3.1.1及以下版本，没有遗嘱属性概念
                willProperties = MqttProperties.NO_PROPERTIES;
            }

            // 解码遗嘱主题，限制最大长度为32767字节（MQTT协议规定的字符串最大长度）
            decodedWillTopic = decodeString(buffer, 0, 32767);
            // 累加遗嘱主题解码消耗的字节数
            numberOfBytesConsumed += decodedWillTopic.numberOfBytesConsumed;

            // 解码遗嘱消息内容（二进制数据）
            decodedWillMessage = decodeByteArray(buffer);
            // 累加遗嘱消息解码消耗的字节数（消息长度 + 2字节长度前缀）
            numberOfBytesConsumed += decodedWillMessage.length + 2;
        } else {
            // 如果连接标志中没有设置遗嘱标志，则没有遗嘱属性
            willProperties = MqttProperties.NO_PROPERTIES;
        }
        
        // 解码用户名（仅当连接标志中设置了用户名标志时）
        Result<String> decodedUserName = null;  // 用户名解码结果初始化
        if (mqttConnectVariableHeader.hasUserName()) {
            // 检查连接标志中的用户名标志位，如果设置则解码用户名
            decodedUserName = decodeString(buffer);
            // 累加用户名解码消耗的字节数
            numberOfBytesConsumed += decodedUserName.numberOfBytesConsumed;
        }

        // 解码密码（仅当连接标志中设置了密码标志时）
        byte[] decodedPassword = null;  // 密码字节数组初始化
        if (mqttConnectVariableHeader.hasPassword()) {
            // 检查连接标志中的密码标志位，如果设置则解码密码
            decodedPassword = decodeByteArray(buffer);
            // 累加密码解码消耗的字节数（密码长度 + 2字节长度前缀）
            numberOfBytesConsumed += decodedPassword.length + 2;
        }

        // 解码签名（仅当遗嘱保留标志设置时，用于消息完整性验证）
        byte[] decodedSignature = null;  // 签名字节数组初始化
        if (mqttConnectVariableHeader.isWillRetain()) {
            // 检查遗嘱保留标志，如果设置则解码签名信息
            decodedSignature = decodeByteArray(buffer);
            // 累加签名解码消耗的字节数（签名长度 + 2字节长度前缀）
            numberOfBytesConsumed += decodedSignature.length + 2;
        }

        // 创建CONNECT消息有效载荷对象，将所有解码的字段组装成完整的载荷
        final MqttConnectPayload mqttConnectPayload =
                new MqttConnectPayload(
                        decodedClientId.value,                                  // 客户端标识符（必需字段）
                        willProperties,                                         // 遗嘱属性（MQTT 5.0特性）
                        decodedWillTopic != null ? decodedWillTopic.value : null, // 遗嘱主题（可选，仅当设置遗嘱标志时存在）
                        decodedWillMessage,                                     // 遗嘱消息内容（可选，仅当设置遗嘱标志时存在）
                        decodedUserName != null ? decodedUserName.value : null, // 用户名（可选，仅当设置用户名标志时存在）
                        decodedPassword,                                        // 密码（可选，仅当设置密码标志时存在）
                        decodedSignature                                        // 签名（可选，用于消息完整性验证）
                    );
        // 返回解码结果，包含构建的载荷对象和总共消耗的字节数
        return new Result<MqttConnectPayload>(mqttConnectPayload, numberOfBytesConsumed);
    }

    private static Result<MqttSubscribePayload> decodeSubscribePayload(
            ByteBuf buffer,
            int bytesRemainingInVariablePart) {
        final List<MqttTopicSubscription> subscribeTopics = new ArrayList<MqttTopicSubscription>();
        int numberOfBytesConsumed = 0;
        while (numberOfBytesConsumed < bytesRemainingInVariablePart) {
            final Result<String> decodedTopicName = decodeString(buffer);
            numberOfBytesConsumed += decodedTopicName.numberOfBytesConsumed;
            //See ******* Subscription Options of MQTT 5.0 specification for optionByte details
            final short optionByte = buffer.readUnsignedByte();

            MqttQoS qos = MqttQoS.valueOf(optionByte & 0x03);
            boolean noLocal = ((optionByte & 0x04) >> 2) == 1;
            boolean retainAsPublished = ((optionByte & 0x08) >> 3) == 1;
            MqttSubscriptionOption.RetainedHandlingPolicy retainHandling = MqttSubscriptionOption.RetainedHandlingPolicy.valueOf((optionByte & 0x30) >> 4);

            final MqttSubscriptionOption subscriptionOption = new MqttSubscriptionOption(qos,
                    noLocal,
                    retainAsPublished,
                    retainHandling);

            numberOfBytesConsumed++;
            subscribeTopics.add(new MqttTopicSubscription(decodedTopicName.value, subscriptionOption));
        }
        return new Result<MqttSubscribePayload>(new MqttSubscribePayload(subscribeTopics), numberOfBytesConsumed);
    }

    private static Result<MqttSubAckPayload> decodeSubackPayload(
            ByteBuf buffer,
            int bytesRemainingInVariablePart) {
        final List<Integer> grantedQos = new ArrayList<Integer>(bytesRemainingInVariablePart);
        int numberOfBytesConsumed = 0;
        while (numberOfBytesConsumed < bytesRemainingInVariablePart) {
            int reasonCode = buffer.readUnsignedByte();
            numberOfBytesConsumed++;
            grantedQos.add(reasonCode);
        }
        return new Result<MqttSubAckPayload>(new MqttSubAckPayload(grantedQos), numberOfBytesConsumed);
    }

    private static Result<MqttUnsubAckPayload> decodeUnsubAckPayload(
        ChannelHandlerContext ctx,
        ByteBuf buffer,
        int bytesRemainingInVariablePart) {
        final List<Short> reasonCodes = new ArrayList<Short>(bytesRemainingInVariablePart);
        int numberOfBytesConsumed = 0;
        while (numberOfBytesConsumed < bytesRemainingInVariablePart) {
            short reasonCode = buffer.readUnsignedByte();
            numberOfBytesConsumed++;
            reasonCodes.add(reasonCode);
        }
        return new Result<MqttUnsubAckPayload>(new MqttUnsubAckPayload(reasonCodes), numberOfBytesConsumed);
    }

    private static Result<MqttUnsubscribePayload> decodeUnsubscribePayload(
            ByteBuf buffer,
            int bytesRemainingInVariablePart) {
        final List<String> unsubscribeTopics = new ArrayList<String>();
        int numberOfBytesConsumed = 0;
        while (numberOfBytesConsumed < bytesRemainingInVariablePart) {
            final Result<String> decodedTopicName = decodeString(buffer);
            numberOfBytesConsumed += decodedTopicName.numberOfBytesConsumed;
            unsubscribeTopics.add(decodedTopicName.value);
        }
        return new Result<MqttUnsubscribePayload>(
                new MqttUnsubscribePayload(unsubscribeTopics),
                numberOfBytesConsumed);
    }

    private static Result<ByteBuf> decodePublishPayload(ByteBuf buffer, int bytesRemainingInVariablePart) {
        ByteBuf b = buffer.readRetainedSlice(bytesRemainingInVariablePart);
        return new Result<ByteBuf>(b, bytesRemainingInVariablePart);
    }

    /**
     * 解码MQTT UTF-8编码字符串
     * 
     * @param buffer 输入的字节缓冲区
     * @return 解码后的字符串及消耗的字节数
     */
    private static Result<String> decodeString(ByteBuf buffer) {
        // 使用默认的最小和最大字节限制调用重载方法
        return decodeString(buffer, 0, Integer.MAX_VALUE);
    }

    /**
     * 解码MQTT UTF-8编码字符串，带有长度限制
     * MQTT字符串由两个字节的长度前缀和UTF-8编码的字符串内容组成
     * 
     * @param buffer 输入的字节缓冲区
     * @param minBytes 字符串内容的最小字节数
     * @param maxBytes 字符串内容的最大字节数
     * @return 解码后的字符串及消耗的字节数
     */
    private static Result<String> decodeString(ByteBuf buffer, int minBytes, int maxBytes) {
        // 读取字符串长度（两字节，MSB和LSB）
        int size = decodeMsbLsb(buffer);
        int numberOfBytesConsumed = 2;
        
        // 验证字符串长度是否在允许的范围内
        if (size < minBytes || size > maxBytes) {
            // 如果长度无效，跳过字符串内容
            buffer.skipBytes(size);
            numberOfBytesConsumed += size;
            // 返回空字符串
            return new Result<String>(null, numberOfBytesConsumed);
        }
        
        // 从缓冲区中读取UTF-8编码的字符串
        String s = buffer.toString(buffer.readerIndex(), size, CharsetUtil.UTF_8);
        // 跳过已读取的字符串内容
        buffer.skipBytes(size);
        numberOfBytesConsumed += size;
        
        return new Result<String>(s, numberOfBytesConsumed);
    }

    /**
     * 解码MQTT二进制数据
     * MQTT二进制数据由两个字节的长度前缀和实际的二进制内容组成
     * 
     * @param buffer 输入的字节缓冲区
     * @return 解码后的字节数组，消耗的字节数 = 字节数组长度 + 2（长度前缀）
     */
    private static byte[] decodeByteArray(ByteBuf buffer) {
        // 读取二进制数据长度（两字节，MSB和LSB）
        int size = decodeMsbLsb(buffer);
        // 创建对应大小的字节数组
        byte[] bytes = new byte[size];
        // 从缓冲区中读取二进制数据到字节数组
        buffer.readBytes(bytes);
        return bytes;
    }

    // packing utils to reduce the amount of garbage while decoding ints
    private static long packInts(int a, int b) {
        return (((long) a) << 32) | (b & 0xFFFFFFFFL);
    }

    private static int unpackA(long ints) {
        return (int) (ints >> 32);
    }

    private static int unpackB(long ints) {
        return (int) ints;
    }

    /**
     * 解码MQTT中的MSB/LSB格式整数（两字节无符号整数）
     * MQTT协议中的两字节整数使用大端序（MSB在前，LSB在后）
     * 
     * @param buffer 输入的字节缓冲区
     * @return 解码后的整数值，范围0-65535，如果超出范围则返回-1
     * @note 消耗的字节数固定为2
     */
    private static int decodeMsbLsb(ByteBuf buffer) {
        // 定义有效值范围
        int min = 0;
        int max = 65535;
        // 读取最高有效字节(MSB)
        short msbSize = buffer.readUnsignedByte();
        // 读取最低有效字节(LSB)
        short lsbSize = buffer.readUnsignedByte();
        // 组合MSB和LSB得到完整的两字节整数
        int result = msbSize << 8 | lsbSize;
        // 验证结果是否在有效范围内
        if (result < min || result > max) {
            result = -1;
        }
        return result;
    }

    /**
     * See 1.5.5 Variable Byte Integer section of MQTT 5.0 specification for encoding/decoding rules
     *
     * @param buffer the buffer to decode from
     * @return result pack with a = decoded integer, b = numberOfBytesConsumed. Need to unpack to read them.
     * @throws DecoderException if bad MQTT protocol limits Remaining Length
     */
    private static long decodeVariableByteInteger(ByteBuf buffer) {
        int remainingLength = 0;
        int multiplier = 1;
        short digit;
        int loops = 0;
        do {
            digit = buffer.readUnsignedByte();
            remainingLength += (digit & 127) * multiplier;
            multiplier *= 128;
            loops++;
        } while ((digit & 128) != 0 && loops < 4);

        if (loops == 4 && (digit & 128) != 0) {
            throw new DecoderException("MQTT protocol limits Remaining Length to 4 bytes");
        }
        return packInts(remainingLength, loops);
    }

    private static final class Result<T> {

        private final T value;
        private final int numberOfBytesConsumed;

        Result(T value, int numberOfBytesConsumed) {
            this.value = value;
            this.numberOfBytesConsumed = numberOfBytesConsumed;
        }
    }

    private static Result<MqttProperties> decodeProperties(ByteBuf buffer) {
        final long propertiesLength = decodeVariableByteInteger(buffer);
        int totalPropertiesLength = unpackA(propertiesLength);
        int numberOfBytesConsumed = unpackB(propertiesLength);

        MqttProperties decodedProperties = new MqttProperties();
        while (numberOfBytesConsumed < totalPropertiesLength) {
            long propertyId = decodeVariableByteInteger(buffer);
            final int propertyIdValue = unpackA(propertyId);
            numberOfBytesConsumed += unpackB(propertyId);
            MqttProperties.MqttPropertyType propertyType = MqttProperties.MqttPropertyType.valueOf(propertyIdValue);
            switch (propertyType) {
                case PAYLOAD_FORMAT_INDICATOR:
                case REQUEST_PROBLEM_INFORMATION:
                case REQUEST_RESPONSE_INFORMATION:
                case MAXIMUM_QOS:
                case RETAIN_AVAILABLE:
                case WILDCARD_SUBSCRIPTION_AVAILABLE:
                case SUBSCRIPTION_IDENTIFIER_AVAILABLE:
                case SHARED_SUBSCRIPTION_AVAILABLE:
                    final int b1 = buffer.readUnsignedByte();
                    numberOfBytesConsumed++;
                    decodedProperties.add(new MqttProperties.IntegerProperty(propertyIdValue, b1));
                    break;
                case SERVER_KEEP_ALIVE:
                case RECEIVE_MAXIMUM:
                case TOPIC_ALIAS_MAXIMUM:
                case TOPIC_ALIAS:
                    final int int2BytesResult = decodeMsbLsb(buffer);
                    numberOfBytesConsumed += 2;
                    decodedProperties.add(new MqttProperties.IntegerProperty(propertyIdValue, int2BytesResult));
                    break;
                case PUBLICATION_EXPIRY_INTERVAL:
                case SESSION_EXPIRY_INTERVAL:
                case WILL_DELAY_INTERVAL:
                case MAXIMUM_PACKET_SIZE:
                    final int maxPacketSize = buffer.readInt();
                    numberOfBytesConsumed += 4;
                    decodedProperties.add(new MqttProperties.IntegerProperty(propertyIdValue, maxPacketSize));
                    break;
                case SUBSCRIPTION_IDENTIFIER:
                    long vbIntegerResult = decodeVariableByteInteger(buffer);
                    numberOfBytesConsumed += unpackB(vbIntegerResult);
                    decodedProperties.add(new MqttProperties.IntegerProperty(propertyIdValue, unpackA(vbIntegerResult)));
                    break;
                case CONTENT_TYPE:
                case RESPONSE_TOPIC:
                case ASSIGNED_CLIENT_IDENTIFIER:
                case AUTHENTICATION_METHOD:
                case RESPONSE_INFORMATION:
                case SERVER_REFERENCE:
                case REASON_STRING:
                    final Result<String> stringResult = decodeString(buffer);
                    numberOfBytesConsumed += stringResult.numberOfBytesConsumed;
                    decodedProperties.add(new MqttProperties.StringProperty(propertyIdValue, stringResult.value));
                    break;
                case USER_PROPERTY:
                    final Result<String> keyResult = decodeString(buffer);
                    final Result<String> valueResult = decodeString(buffer);
                    numberOfBytesConsumed += keyResult.numberOfBytesConsumed;
                    numberOfBytesConsumed += valueResult.numberOfBytesConsumed;
                    decodedProperties.add(new MqttProperties.UserProperty(keyResult.value, valueResult.value));
                    break;
                case CORRELATION_DATA:
                case AUTHENTICATION_DATA:
                    final byte[] binaryDataResult = decodeByteArray(buffer);
                    numberOfBytesConsumed += binaryDataResult.length + 2;
                    decodedProperties.add(new MqttProperties.BinaryProperty(propertyIdValue, binaryDataResult));
                    break;
                default:
                    //shouldn't reach here
                    throw new DecoderException("Unknown property type: " + propertyType);
            }
        }

        return new Result<MqttProperties>(decodedProperties, numberOfBytesConsumed);
    }
}
