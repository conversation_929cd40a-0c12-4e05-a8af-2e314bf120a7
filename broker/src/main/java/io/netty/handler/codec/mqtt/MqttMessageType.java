/*
 * Copyright 2014 The Netty Project
 *
 * The Netty Project licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

package io.netty.handler.codec.mqtt;

/**
 * MQTT Message Types.
 */
public enum MqttMessageType {
    /**
     * 客户端请求连接到服务器的消息类型
     */
    CONNECT(1),
    /**
     * 服务器确认客户端连接请求的消息类型
     */
    CONNACK(2),
    /**
     * 发布消息的消息类型，用于传输实际数据
     */
    PUBLISH(3),
    /**
     * 发布确认的消息类型，用于确认QoS 1消息的传递
     */
    PUBACK(4),
    /**
     * 发布收到的消息类型，QoS 2消息流的第一部分
     */
    PUBREC(5),
    /**
     * 发布释放的消息类型，QoS 2消息流的第二部分
     */
    PUBREL(6),
    /**
     * 发布完成的消息类型，QoS 2消息流的第三部分
     */
    PUBCOMP(7),
    /**
     * 客户端订阅请求的消息类型
     */
    SUBSCRIBE(8),
    /**
     * 服务器对订阅请求的确认消息类型
     */
    SUBACK(9),
    /**
     * 客户端取消订阅请求的消息类型
     */
    UNSUBSCRIBE(10),
    /**
     * 服务器对取消订阅请求的确认消息类型
     */
    UNSUBACK(11),
    /**
     * 客户端发送的心跳请求消息类型，用于保持连接活跃
     */
    PINGREQ(12),
    /**
     * 服务器对心跳请求的响应消息类型
     */
    PINGRESP(13),
    /**
     * 客户端请求断开连接的消息类型
     */
    DISCONNECT(14),
    /**
     * 认证交换的消息类型，用于增强安全性（MQTT 5.0新增）
     */
    AUTH(15);

    private static final MqttMessageType[] VALUES;

    static {
        // this prevent values to be assigned with the wrong order
        // and ensure valueOf to work fine
        final MqttMessageType[] values = values();
        VALUES = new MqttMessageType[values.length + 1];
        for (MqttMessageType mqttMessageType : values) {
            final int value = mqttMessageType.value;
            if (VALUES[value] != null) {
                throw new AssertionError("value already in use: " + value);
            }
            VALUES[value] = mqttMessageType;
        }
    }

    private final int value;

    MqttMessageType(int value) {
        this.value = value;
    }

    public int value() {
        return value;
    }

    public static MqttMessageType valueOf(int type) {
        if (type <= 0 || type >= VALUES.length) {
            throw new IllegalArgumentException("unknown message type: " + type);
        }
        return VALUES[type];
    }
}

