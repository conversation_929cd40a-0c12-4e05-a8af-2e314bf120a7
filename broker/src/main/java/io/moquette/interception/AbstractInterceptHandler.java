/*
 * Copyright (c) 2012-2017 The original author or authors
 * ------------------------------------------------------
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * and Apache License v2.0 which accompanies this distribution.
 *
 * The Eclipse Public License is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * The Apache License v2.0 is available at
 * http://www.opensource.org/licenses/apache2.0.php
 *
 * You may elect to redistribute this code under either of these licenses.
 */

package io.moquette.interception;

import io.moquette.interception.messages.*;

/**
 * 抽象拦截器处理基类
 * 提供InterceptHandler接口的默认空实现，子类只需要重写感兴趣的方法
 * 
 * 特点：
 * 1. 实现InterceptHandler接口的所有方法
 * 2. 提供默认的空实现，子类可选择性重写
 * 3. 简化拦截器的开发工作
 * 4. 保持与moquette框架的兼容性
 * 
 * <AUTHOR>
 */
public abstract class AbstractInterceptHandler implements InterceptHandler {

    /**
     * 返回拦截器的唯一标识符
     * 子类必须实现此方法
     */
    @Override
    public abstract String getID();

    /**
     * 返回此拦截器处理的消息类型
     * 默认处理所有类型的消息，子类可以重写以指定特定类型
     */
    @Override
    public Class<?>[] getInterceptedMessageTypes() {
        return ALL_MESSAGE_TYPES;
    }

    /**
     * 客户端连接事件
     * 默认空实现，子类可选择重写
     */
    @Override
    public void onConnect(InterceptConnectMessage msg) {
        // 默认空实现
    }

    /**
     * 客户端断开连接事件
     * 默认空实现，子类可选择重写
     */
    @Override
    public void onDisconnect(InterceptDisconnectMessage msg) {
        // 默认空实现
    }

    /**
     * 客户端连接丢失事件
     * 默认空实现，子类可选择重写
     */
    @Override
    public void onConnectionLost(InterceptConnectionLostMessage msg) {
        // 默认空实现
    }

    /**
     * MQTT发布消息事件
     * 默认空实现，子类可选择重写
     * 这是最常用的拦截点
     */
    @Override
    public void onPublish(InterceptPublishMessage msg) {
        // 默认空实现
    }

    /**
     * 消息确认事件
     * 默认空实现，子类可选择重写
     */
    @Override
    public void onMessageAcknowledged(InterceptAcknowledgedMessage msg) {
        // 默认空实现
    }
} 