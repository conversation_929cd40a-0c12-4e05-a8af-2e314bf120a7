//package io.moquette.server.websocket;
//
//import io.netty.buffer.ByteBuf;
//import io.netty.buffer.Unpooled;
//import io.netty.handler.codec.mqtt.*;
//import com.google.gson.*;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import javax.websocket.*;
//import java.nio.ByteBuffer;
//import java.nio.charset.StandardCharsets;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.Map;
//
///**
// * WebSocket到MQTT的网关
// * 处理H5客户端的WebSocket连接，转换为标准MQTT消息
// */
//@ServerEndpoint("/mqtt")
//public class WebSocketMqttGateway {
//
//    private static final Logger log = LoggerFactory.getLogger(WebSocketMqttGateway.class);
//
//    // 协议常量
//    private static final int PROTOCOL_MAGIC = 0x57494C44; // "WILD"
//    private static final int PROTOCOL_VERSION = 1;
//
//    // 客户端会话管理
//    private static final Map<String, Session> clientSessions = new ConcurrentHashMap<>();
//    private static final Map<Session, String> sessionClients = new ConcurrentHashMap<>();
//
//    @OnOpen
//    public void onOpen(Session session) {
//        log.info("WebSocket连接建立: {}", session.getId());
//    }
//
//    @OnClose
//    public void onClose(Session session) {
//        String clientId = sessionClients.remove(session);
//        if (clientId != null) {
//            clientSessions.remove(clientId);
//            log.info("客户端断开连接: {}", clientId);
//        }
//    }
//
//    @OnError
//    public void onError(Session session, Throwable error) {
//        log.error("WebSocket连接错误: {}", session.getId(), error);
//    }
//
//    @OnMessage
//    public void onBinaryMessage(ByteBuffer buffer, Session session) {
//        try {
//            // 1. 解析自定义协议消息
//            ProtocolMessage protocolMsg = parseProtocolMessage(buffer);
//            log.debug("接收到协议消息: 版本={}, 加密={}, 数据长度={}",
//                protocolMsg.getVersion(), protocolMsg.isEncrypted(), protocolMsg.getData().length);
//
//            // 2. 解析JSON消息内容
//            WildfireMessage wildfireMsg = parseWildfireMessage(protocolMsg.getData());
//            log.debug("解析野火消息: 类型={}, 消息ID={}", wildfireMsg.getType(), wildfireMsg.getMessageId());
//
//            // 3. 转换为MQTT消息并处理
//            handleWildfireMessage(wildfireMsg, session);
//
//        } catch (Exception e) {
//            log.error("处理WebSocket消息失败", e);
//            sendErrorResponse(session, "消息处理失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 解析自定义协议消息
//     */
//    private ProtocolMessage parseProtocolMessage(ByteBuffer buffer) {
//        if (buffer.remaining() < 13) { // 最小头部长度
//            throw new IllegalArgumentException("消息长度不足");
//        }
//
//        // 读取魔数
//        int magic = buffer.getInt();
//        if (magic != PROTOCOL_MAGIC) {
//            throw new IllegalArgumentException("无效的协议魔数: 0x" + Integer.toHexString(magic));
//        }
//
//        // 读取版本
//        int version = buffer.getInt();
//        if (version != PROTOCOL_VERSION) {
//            log.warn("协议版本不匹配: 期望={}, 实际={}", PROTOCOL_VERSION, version);
//        }
//
//        // 读取加密标志
//        boolean encrypted = buffer.get() == 1;
//
//        // 读取数据长度
//        int dataLength = buffer.getInt();
//        if (dataLength <= 0 || dataLength > buffer.remaining()) {
//            throw new IllegalArgumentException("无效的数据长度: " + dataLength);
//        }
//
//        // 读取数据
//        byte[] data = new byte[dataLength];
//        buffer.get(data);
//
//        return new ProtocolMessage(version, encrypted, data);
//    }
//
//    /**
//     * 解析野火消息
//     */
//    private WildfireMessage parseWildfireMessage(byte[] data) {
//        String jsonData = new String(data, StandardCharsets.UTF_8);
//        log.debug("JSON数据: {}", jsonData);
//
//        Gson gson = new Gson();
//        return gson.fromJson(jsonData, WildfireMessage.class);
//    }
//
//    /**
//     * 处理野火消息
//     */
//    private void handleWildfireMessage(WildfireMessage message, Session session) {
//        switch (message.getType()) {
//            case "CONNECT_REQUEST":
//                handleConnectRequest(message, session);
//                break;
//            case "SEND_MESSAGE":
//                handleSendMessage(message, session);
//                break;
//            case "HEARTBEAT":
//                handleHeartbeat(message, session);
//                break;
//            default:
//                log.warn("不支持的消息类型: {}", message.getType());
//                sendErrorResponse(session, "不支持的消息类型: " + message.getType());
//        }
//    }
//
//    /**
//     * 处理连接请求
//     */
//    private void handleConnectRequest(WildfireMessage message, Session session) {
//        try {
//            // 解析连接请求载荷
//            Gson gson = new Gson();
//            JsonObject payload = gson.fromJson(message.getPayload(), JsonObject.class);
//
//            String userId = payload.get("userId").getAsString();
//            String clientId = payload.get("clientId").getAsString();
//            String token = payload.get("token").getAsString();
//            String platform = payload.get("platform").getAsString();
//
//            log.info("处理连接请求: userId={}, clientId={}, platform={}", userId, clientId, platform);
//
//            // 验证用户身份
//            if (validateUser(userId, token)) {
//                // 注册客户端会话
//                clientSessions.put(clientId, session);
//                sessionClients.put(session, clientId);
//
//                // 发送连接响应
//                sendConnectResponse(session, message.getMessageId(), true, "连接成功", clientId);
//
//                log.info("客户端连接成功: userId={}, clientId={}", userId, clientId);
//            } else {
//                sendConnectResponse(session, message.getMessageId(), false, "身份验证失败", null);
//                log.warn("客户端身份验证失败: userId={}", userId);
//            }
//
//        } catch (Exception e) {
//            log.error("处理连接请求失败", e);
//            sendErrorResponse(session, "连接请求处理失败");
//        }
//    }
//
//    /**
//     * 处理发送消息
//     */
//    private void handleSendMessage(WildfireMessage message, Session session) {
//        try {
//            String clientId = sessionClients.get(session);
//            if (clientId == null) {
//                sendErrorResponse(session, "客户端未连接");
//                return;
//            }
//
//            // 解析消息载荷
//            Gson gson = new Gson();
//            JsonObject payload = gson.fromJson(message.getPayload(), JsonObject.class);
//
//            // 这里可以转换为MQTT PUBLISH消息
//            // 或者直接处理业务逻辑
//
//            log.info("处理发送消息: 客户端={}, 消息ID={}", clientId, message.getMessageId());
//
//            // 发送确认响应
//            sendAckResponse(session, message.getMessageId());
//
//        } catch (Exception e) {
//            log.error("处理发送消息失败", e);
//            sendErrorResponse(session, "发送消息失败");
//        }
//    }
//
//    /**
//     * 处理心跳
//     */
//    private void handleHeartbeat(WildfireMessage message, Session session) {
//        String clientId = sessionClients.get(session);
//        log.debug("收到心跳: 客户端={}", clientId);
//
//        // 发送心跳响应
//        sendHeartbeatResponse(session, message.getMessageId());
//    }
//
//    /**
//     * 发送连接响应
//     */
//    private void sendConnectResponse(Session session, String messageId, boolean success,
//                                   String message, String sessionId) {
//        try {
//            WildfireMessage response = new WildfireMessage();
//            response.setType("CONNECT_RESPONSE");
//            response.setMessageId(generateMessageId());
//            response.setTimestamp(System.currentTimeMillis());
//            response.setFromUser("server");
//
//            JsonObject payload = new JsonObject();
//            payload.addProperty("success", success);
//            payload.addProperty("errorMessage", success ? null : message);
//            payload.addProperty("serverTime", System.currentTimeMillis());
//            payload.addProperty("sessionId", sessionId);
//
//            response.setPayload(payload.toString());
//
//            sendResponse(session, response);
//
//        } catch (Exception e) {
//            log.error("发送连接响应失败", e);
//        }
//    }
//
//    /**
//     * 发送确认响应
//     */
//    private void sendAckResponse(Session session, String originalMessageId) {
//        try {
//            WildfireMessage response = new WildfireMessage();
//            response.setType("ACK");
//            response.setMessageId(generateMessageId());
//            response.setTimestamp(System.currentTimeMillis());
//            response.setFromUser("server");
//
//            JsonObject payload = new JsonObject();
//            payload.addProperty("messageId", originalMessageId);
//
//            response.setPayload(payload.toString());
//
//            sendResponse(session, response);
//
//        } catch (Exception e) {
//            log.error("发送确认响应失败", e);
//        }
//    }
//
//    /**
//     * 发送心跳响应
//     */
//    private void sendHeartbeatResponse(Session session, String messageId) {
//        try {
//            WildfireMessage response = new WildfireMessage();
//            response.setType("HEARTBEAT");
//            response.setMessageId(generateMessageId());
//            response.setTimestamp(System.currentTimeMillis());
//            response.setFromUser("server");
//
//            JsonObject payload = new JsonObject();
//            payload.addProperty("pong", System.currentTimeMillis());
//
//            response.setPayload(payload.toString());
//
//            sendResponse(session, response);
//
//        } catch (Exception e) {
//            log.error("发送心跳响应失败", e);
//        }
//    }
//
//    /**
//     * 发送错误响应
//     */
//    private void sendErrorResponse(Session session, String errorMessage) {
//        try {
//            WildfireMessage response = new WildfireMessage();
//            response.setType("ERROR");
//            response.setMessageId(generateMessageId());
//            response.setTimestamp(System.currentTimeMillis());
//            response.setFromUser("server");
//
//            JsonObject payload = new JsonObject();
//            payload.addProperty("errorCode", 500);
//            payload.addProperty("errorMessage", errorMessage);
//
//            response.setPayload(payload.toString());
//
//            sendResponse(session, response);
//
//        } catch (Exception e) {
//            log.error("发送错误响应失败", e);
//        }
//    }
//
//    /**
//     * 发送响应消息
//     */
//    private void sendResponse(Session session, WildfireMessage message) {
//        try {
//            // 序列化为JSON
//            Gson gson = new Gson();
//            String jsonData = gson.toJson(message);
//            byte[] jsonBytes = jsonData.getBytes(StandardCharsets.UTF_8);
//
//            // 构建协议消息
//            ByteBuffer buffer = ByteBuffer.allocate(13 + jsonBytes.length);
//            buffer.putInt(PROTOCOL_MAGIC);      // 魔数
//            buffer.putInt(PROTOCOL_VERSION);    // 版本
//            buffer.put((byte) 0);               // 未加密
//            buffer.putInt(jsonBytes.length);    // 数据长度
//            buffer.put(jsonBytes);              // 数据
//
//            buffer.flip();
//            session.getBasicRemote().sendBinary(buffer);
//
//            log.debug("发送响应: 类型={}, 长度={}", message.getType(), buffer.remaining());
//
//        } catch (Exception e) {
//            log.error("发送响应失败", e);
//        }
//    }
//
//    /**
//     * 验证用户身份
//     */
//    private boolean validateUser(String userId, String token) {
//        // 这里实现具体的用户验证逻辑
//        // 例如：检查token有效性、用户状态等
//        return token != null && !token.isEmpty();
//    }
//
//    /**
//     * 生成消息ID
//     */
//    private String generateMessageId() {
//        return "srv_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 10000);
//    }
//
//    /**
//     * 协议消息数据结构
//     */
//    private static class ProtocolMessage {
//        private final int version;
//        private final boolean encrypted;
//        private final byte[] data;
//
//        public ProtocolMessage(int version, boolean encrypted, byte[] data) {
//            this.version = version;
//            this.encrypted = encrypted;
//            this.data = data;
//        }
//
//        public int getVersion() { return version; }
//        public boolean isEncrypted() { return encrypted; }
//        public byte[] getData() { return data; }
//    }
//
//    /**
//     * 野火消息数据结构
//     */
//    private static class WildfireMessage {
//        private String type;
//        private String messageId;
//        private long timestamp;
//        private String fromUser;
//        private String payload;
//
//        // Getters and Setters
//        public String getType() { return type; }
//        public void setType(String type) { this.type = type; }
//
//        public String getMessageId() { return messageId; }
//        public void setMessageId(String messageId) { this.messageId = messageId; }
//
//        public long getTimestamp() { return timestamp; }
//        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
//
//        public String getFromUser() { return fromUser; }
//        public void setFromUser(String fromUser) { this.fromUser = fromUser; }
//
//        public String getPayload() { return payload; }
//        public void setPayload(String payload) { this.payload = payload; }
//    }
//}
