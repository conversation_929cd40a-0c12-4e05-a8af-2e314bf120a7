/*
 * Copyright (c) 2012-2017 The original author or authors
 * ------------------------------------------------------
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * and Apache License v2.0 which accompanies this distribution.
 *
 * The Eclipse Public License is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * The Apache License v2.0 is available at
 * http://www.opensource.org/licenses/apache2.0.php
 *
 * You may elect to redistribute this code under either of these licenses.
 */

package io.moquette.server.netty; // Netty服务器网络层包

import cn.wildfirechat.proto.WFCMessage;
import io.moquette.BrokerConstants; // 代理服务器常量定义
import io.moquette.imhandler.IMHandler;
import io.moquette.server.ServerAcceptor; // 服务器接收器接口
import io.moquette.server.config.IConfig; // 配置接口
import io.moquette.server.netty.metrics.*; // 网络指标相关类
import io.moquette.server.websocket.*; // WebSocket相关处理器
import io.moquette.spi.impl.ProtocolProcessor; // 协议处理器实现
import io.moquette.spi.security.ISslContextCreator; // SSL上下文创建器接口
import io.netty.bootstrap.ServerBootstrap; // Netty服务器启动器
import io.netty.channel.*; // Netty通道相关类
import io.netty.channel.epoll.EpollEventLoopGroup; // Linux Epoll事件循环组
import io.netty.channel.epoll.EpollServerSocketChannel; // Epoll服务器套接字通道
import io.netty.channel.nio.NioEventLoopGroup; // NIO事件循环组
import io.netty.channel.socket.ServerSocketChannel; // 服务器套接字通道接口
import io.netty.channel.socket.SocketChannel; // 套接字通道
import io.netty.channel.socket.nio.NioServerSocketChannel; // NIO服务器套接字通道
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.codec.LengthFieldPrepender;
import io.netty.handler.codec.http.FullHttpRequest; // 完整HTTP请求
import io.netty.handler.codec.http.HttpObjectAggregator; // HTTP对象聚合器
import io.netty.handler.codec.http.HttpServerCodec; // HTTP服务器编解码器
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler; // WebSocket服务器协议处理器
import io.netty.handler.codec.mqtt.MqttDecoder; // MQTT解码器
import io.netty.handler.codec.mqtt.MqttEncoder; // MQTT编码器
import io.netty.handler.codec.protobuf.ProtobufDecoder;
import io.netty.handler.codec.protobuf.ProtobufEncoder;
import io.netty.handler.ssl.SslHandler; // SSL处理器
import io.netty.handler.timeout.IdleStateHandler; // 空闲状态处理器
import io.netty.util.concurrent.Future; // Netty异步操作Future
import org.slf4j.Logger; // SLF4J日志记录器接口
import org.slf4j.LoggerFactory; // SLF4J日志工厂

import javax.net.ssl.SSLContext; // Java SSL上下文
import javax.net.ssl.SSLEngine; // SSL引擎
import java.io.IOException; // IO异常
import java.util.Optional; // 可选值容器
import java.util.concurrent.TimeUnit; // 时间单位枚举

import static io.moquette.BrokerConstants.DISABLED_PORT_BIND; // 禁用端口绑定常量
import static io.moquette.BrokerConstants.PORT_PROPERTY_NAME; // 端口属性名常量
import static io.netty.channel.ChannelFutureListener.FIRE_EXCEPTION_ON_FAILURE; // 失败时触发异常监听器

/**
 * Netty网络接收器
 * <p>
 * 这个类实现了ServerAcceptor接口，负责初始化和管理基于Netty的网络传输层。
 * 它支持两种主要的传输协议：
 * 1. TCP MQTT - 传统的MQTT over TCP协议
 * 2. WebSocket - 支持WebSocket连接的MQTT协议
 * <p>
 * 主要功能：
 * - 初始化Netty服务器引导程序
 * - 配置网络参数（如SO_BACKLOG、TCP_NODELAY等）
 * - 设置通道处理器管道
 * - 管理事件循环组（Boss和Worker）
 * - 收集网络指标数据
 * - 支持SSL/TLS加密
 */
public class NettyAcceptor implements ServerAcceptor {

    /**
     * 管道初始化器抽象类
     * <p>
     * 用于定义不同协议的通道处理器管道初始化逻辑。
     * 每种传输协议（TCP MQTT、WebSocket等）都需要实现此抽象类
     * 来配置相应的处理器链。
     */
    abstract class PipelineInitializer {
        /**
         * 初始化通道管道
         *
         * @param pipeline 要初始化的通道管道
         * @throws Exception 如果初始化过程中发生异常
         */
        abstract void init(ChannelPipeline pipeline) throws Exception;
    }

    // 日志记录器，用于记录NettyAcceptor的运行日志
    private static final Logger LOG = LoggerFactory.getLogger(NettyAcceptor.class);

    // Boss事件循环组，负责接受新的连接
    EventLoopGroup m_bossGroup;
    // Worker事件循环组，负责处理已建立连接的I/O操作
    EventLoopGroup m_workerGroup;
    // 字节指标收集器，统计网络传输的字节数
    BytesMetricsCollector m_bytesMetricsCollector = new BytesMetricsCollector();
    // 消息指标收集器，统计消息的读写数量
    MessageMetricsCollector m_metricsCollector = new MessageMetricsCollector();
    // 可选的指标处理器，用于额外的指标收集
    private Optional<? extends ChannelInboundHandler> metrics;
    // 可选的错误捕获处理器，用于异常处理和错误报告
    private Optional<? extends ChannelInboundHandler> errorsCather;

    // Netty SO_BACKLOG参数，控制服务器套接字的连接队列大小
    private int nettySoBacklog;
    // Netty SO_REUSEADDR参数，允许重用本地地址
    private boolean nettySoReuseaddr;
    // Netty TCP_NODELAY参数，禁用Nagle算法以减少延迟
    private boolean nettyTcpNodelay;
    // Netty SO_KEEPALIVE参数，启用TCP保活机制
    private boolean nettySoKeepalive;
    // 通道超时时间（秒），用于检测空闲连接
    private int nettyChannelTimeoutSeconds;

    // 服务器套接字通道类，根据配置选择NIO或Epoll实现
    private Class<? extends ServerSocketChannel> channelClass;

    // WebSocket通道引用
    private Channel websocketChannel;
    // WebSocket服务路径
    private String websocketPath;
    // WebSocket服务端口
    private int websocketPort;

    /**
     * 初始化Netty接收器
     * <p>
     * 这是ServerAcceptor接口的实现方法，负责初始化整个Netty网络层。
     * 包括配置网络参数、选择I/O模型、创建事件循环组、初始化传输协议等。
     *
     * @param processor     协议处理器，用于处理MQTT协议逻辑
     * @param props         配置属性，包含各种网络和服务器配置参数
     * @param sslCtxCreator SSL上下文创建器，用于创建SSL/TLS加密连接
     * @throws IOException 如果初始化过程中发生I/O异常
     */
    @Override
    public void initialize(ProtocolProcessor processor, IConfig props, ISslContextCreator sslCtxCreator)
        throws IOException {
        LOG.info("Initializing Netty acceptor..."); // 记录初始化开始日志

        // 从配置中读取Netty网络参数，如果配置不存在则使用默认值
        // SO_BACKLOG：服务器套接字的连接队列大小，默认128
        nettySoBacklog = Integer.parseInt(props.getProperty(BrokerConstants.NETTY_SO_BACKLOG_PROPERTY_NAME, "128"));
        // SO_REUSEADDR：允许重用本地地址，默认true
        nettySoReuseaddr = Boolean
            .parseBoolean(props.getProperty(BrokerConstants.NETTY_SO_REUSEADDR_PROPERTY_NAME, "true"));
        // TCP_NODELAY：禁用Nagle算法以减少延迟，默认true
        nettyTcpNodelay = Boolean
            .parseBoolean(props.getProperty(BrokerConstants.NETTY_TCP_NODELAY_PROPERTY_NAME, "true"));
        // SO_KEEPALIVE：启用TCP保活机制，默认true
        nettySoKeepalive = Boolean
            .parseBoolean(props.getProperty(BrokerConstants.NETTY_SO_KEEPALIVE_PROPERTY_NAME, "true"));
        // 通道超时时间（秒），用于检测空闲连接，默认10秒
        nettyChannelTimeoutSeconds = Integer
            .parseInt(props.getProperty(BrokerConstants.NETTY_CHANNEL_TIMEOUT_SECONDS_PROPERTY_NAME, "10"));

        // 检查是否启用Epoll（Linux高性能I/O模型）
        boolean epoll = Boolean.parseBoolean(props.getProperty(BrokerConstants.NETTY_EPOLL_PROPERTY_NAME, "false"));
        if (epoll) {
            // 使用Epoll模型（仅在Linux系统上可用）
            // 由于目前只支持TCP MQTT，所以bossGroup的线程数配置为1
            LOG.info("Netty is using Epoll"); // 记录使用Epoll的日志
            m_bossGroup = new EpollEventLoopGroup(1); // 创建Epoll Boss事件循环组
            m_workerGroup = new EpollEventLoopGroup(); // 创建Epoll Worker事件循环组
            channelClass = EpollServerSocketChannel.class; // 设置Epoll服务器套接字通道类
        } else {
            // 使用NIO模型（跨平台兼容）
            LOG.info("Netty is using NIO"); // 记录使用NIO的日志
            m_bossGroup = new NioEventLoopGroup(1); // 创建NIO Boss事件循环组
            m_workerGroup = new NioEventLoopGroup(); // 创建NIO Worker事件循环组
            channelClass = NioServerSocketChannel.class; // 设置NIO服务器套接字通道类
        }

        // 创建MQTT协议处理器，用于处理MQTT消息
        final NettyMQTTHandler mqttHandler = new NettyMQTTHandler(processor);

        // 初始化可选的指标处理器为空
        this.metrics = Optional.empty();

        // 初始化可选的错误捕获处理器为空
        this.errorsCather = Optional.empty();

        // 初始化TCP MQTT传输协议
        initializePlainTCPTransport(mqttHandler, props);
        // 初始化WebSocket传输协议
        initializeNettyWebSocketTransport(mqttHandler, props, processor);

    }
    // 将 protobufDecoder 声明为成员变量，方便在ProtocolDetectorHandler中访问
    private ProtobufDecoder protobufDecoder = new ProtobufDecoder(WFCMessage.Message.getDefaultInstance());

    // 提供一个getter方法给ProtocolDetectorHandler使用
    public ProtobufDecoder getProtobufDecoder() {
        return this.protobufDecoder;
    }

    /**
     * 初始化服务器工厂方法
     * <p>
     * 这是一个通用的服务器初始化方法，用于创建和配置Netty服务器。
     * 它设置服务器引导程序、配置网络选项、绑定端口并启动服务器。
     *
     * @param host      服务器绑定的主机地址
     * @param port      服务器绑定的端口号
     * @param protocol  协议名称（用于日志记录）
     * @param pipeliner 管道初始化器，用于配置通道处理器链
     */
    private void initFactory(String host, int port, String protocol, final PipelineInitializer pipeliner) {
        LOG.info("Initializing server. Protocol={}", protocol); // 记录服务器初始化日志
        // 创建Netty服务器引导程序
        ServerBootstrap b = new ServerBootstrap();
        // 配置事件循环组和通道类型
        b.group(m_bossGroup, m_workerGroup).channel(channelClass)
            // 设置子通道处理器
            .childHandler(new ChannelInitializer<SocketChannel>() {

                /**
                 * 初始化新连接的通道
                 *
                 * 当有新的客户端连接建立时，此方法会被调用来初始化通道的处理器管道。
                 *
                 * @param ch 新建立的套接字通道
                 * @throws Exception 如果初始化过程中发生异常
                 */
                @Override
                public void initChannel(SocketChannel ch) throws Exception {
                    // 获取通道的处理器管道
                    ChannelPipeline pipeline = ch.pipeline();
                    try {
                        // 使用传入的管道初始化器配置处理器链
                        pipeliner.init(pipeline);
                    } catch (Throwable th) {
                        // 记录管道创建过程中的严重错误
                        LOG.error("Severe error during pipeline creation", th);
                        throw th; // 重新抛出异常
                    }
                }
            })
            // 设置服务器套接字选项
            .option(ChannelOption.SO_BACKLOG, nettySoBacklog) // 设置连接队列大小
            .option(ChannelOption.SO_REUSEADDR, nettySoReuseaddr) // 设置地址重用
            // 设置子通道选项
            .childOption(ChannelOption.TCP_NODELAY, nettyTcpNodelay) // 设置TCP无延迟
            .childOption(ChannelOption.SO_KEEPALIVE, nettySoKeepalive); // 设置保活机制
        try {
            LOG.info("Binding server. host={}, port={}", host, port); // 记录绑定开始日志
            // 绑定地址和端口，开始接受传入连接
            ChannelFuture f = b.bind(host, port);
            LOG.info("Server has been bound. host={}, port={}", host, port); // 记录绑定成功日志
            // 同步等待绑定完成，并添加失败时触发异常的监听器
            f.sync().addListener(FIRE_EXCEPTION_ON_FAILURE);
        } catch (InterruptedException ex) {
            // 处理中断异常
            LOG.error("An interruptedException was caught while initializing server. Protocol={}", protocol, ex);
        } catch (Exception e) {
            // 处理其他异常，通常是端口被占用或权限不足
            e.printStackTrace(); // 打印异常堆栈
            // 记录详细的错误信息和解决建议
            LOG.error("端口 {} 已经被占用或者无权限使用。如果无权限使用，请以root权限运行；如果被占用，请检查该端口被那个程序占用，找到程序停掉。\n查找端口被那个程序占用的命令是: netstat -tunlp | grep {}", port, port);
            // 向控制台输出错误信息
            System.out.println("端口 " + port + " 已经被占用或者无权限使用。如果无权限使用，请以root权限运行；如果被占用，请检查该端口被那个程序占用，找到程序停掉。\n查找端口被那个程序占用的命令是: netstat -tunlp | grep " + port);
            // 退出程序
            System.exit(-1);
        }
    }


    /**
     * 初始化纯TCP MQTT传输协议
     * <p>
     * 配置和启动标准的MQTT over TCP传输层。这是MQTT协议的标准传输方式，
     * 直接在TCP连接上传输MQTT消息。
     *
     * @param handler MQTT协议处理器，用于处理MQTT消息逻辑
     * @param props   配置属性，包含主机、端口等网络配置
     * @throws IOException 如果初始化过程中发生I/O异常
     */
    private void initializePlainTCPTransport(final NettyMQTTHandler handler,
                                             IConfig props) throws IOException {
        LOG.info("Configuring TCP MQTT transport"); // 记录TCP MQTT传输配置日志
        // 创建空闲超时处理器，用于检测和处理空闲连接
        final MoquetteIdleTimeoutHandler timeoutHandler = new MoquetteIdleTimeoutHandler();
        // 从配置中获取主机地址，默认为"**************"
        String host = props.getProperty(BrokerConstants.HOST_PROPERTY_NAME, "**************");
        // 从配置中获取TCP端口，默认为禁用状态
        String tcpPortProp = props.getProperty(PORT_PROPERTY_NAME, DISABLED_PORT_BIND);
        // 检查TCP端口是否被禁用
        if (DISABLED_PORT_BIND.equals(tcpPortProp)) {
            // 如果端口被设置为禁用，记录日志并返回
            LOG.info("Property {} has been set to {}. TCP MQTT will be disabled", BrokerConstants.PORT_PROPERTY_NAME,
                DISABLED_PORT_BIND);
            return;
        }
        // 解析端口号
        int port = Integer.parseInt(tcpPortProp);
        // 使用工厂方法初始化TCP MQTT服务器
        initFactory(host, port, "TCP MQTT", new PipelineInitializer() {

            /**
             * 初始化TCP MQTT通道管道
             *
             * 配置TCP MQTT连接的处理器链，包括空闲检测、编解码器、
             * 指标收集、消息日志等处理器。
             *
             * @param pipeline 要配置的通道管道
             */
            @Override
            void init(ChannelPipeline pipeline) {
                // 添加空闲状态处理器，检测读空闲（nettyChannelTimeoutSeconds秒无读取则触发）
                pipeline.addFirst("idleStateHandler", new IdleStateHandler(nettyChannelTimeoutSeconds, 0, 0));
                // 在空闲状态处理器后添加空闲事件处理器
                pipeline.addAfter("idleStateHandler", "idleEventHandler", timeoutHandler);
                // 可选：添加日志处理器（当前被注释掉）
                // pipeline.addLast("logger", new LoggingHandler("Netty", LogLevel.ERROR));
                // 如果存在错误捕获处理器，则添加到管道中
                if (errorsCather.isPresent()) {
                    pipeline.addLast("bugsnagCatcher", errorsCather.get());
                }
                // 添加字节指标处理器，统计网络传输的字节数
                pipeline.addFirst("bytemetrics", new BytesMetricsHandler(m_bytesMetricsCollector));
                // 添加MQTT解码器，将字节流解码为MQTT消息对象
                pipeline.addLast("decoder", new MqttDecoder());
                // 添加MQTT编码器，将MQTT消息对象编码为字节流
                pipeline.addLast("encoder", MqttEncoder.INSTANCE);
                // 添加消息指标处理器，统计消息的读写数量
                pipeline.addLast("metrics", new MessageMetricsHandler(m_metricsCollector));
                // 添加MQTT消息日志处理器，记录MQTT消息的详细信息
                pipeline.addLast("messageLogger", new MQTTMessageLogger());
                // 如果存在额外的指标处理器，则添加到管道中
                if (metrics.isPresent()) {
                    pipeline.addLast("wizardMetrics", metrics.get());
                }
                // 添加主要的MQTT协议处理器，处理MQTT业务逻辑
                pipeline.addLast("handler", handler);
                // 添加MQTT到WebSocket桥接处理器 - 实现双向通信
                pipeline.addLast("mqttWebSocketBridge", new MqttToWebSocketBridgeHandler());

            }
        });
    }

    /**
     * 初始化Netty WebSocket传输协议
     * <p>
     * 配置和启动WebSocket传输层，支持基于WebSocket的MQTT通信。
     * WebSocket传输允许Web浏览器和其他支持WebSocket的客户端
     * 通过HTTP升级协议与MQTT代理进行通信。
     * <p>
     * 主要功能：
     * 1. 配置HTTP到WebSocket的协议升级
     * 2. 设置WebSocket消息处理器
     * 3. 集成Protobuf消息编解码
     * 4. 提供连接状态监控和日志记录
     *
     * @param handler   MQTT协议处理器（在WebSocket场景下主要用于兼容性）
     * @param props     配置属性，包含WebSocket端口、路径等配置
     * @param processor
     * @throws IOException 如果初始化过程中发生I/O异常
     */
    private void initializeNettyWebSocketTransport(final NettyMQTTHandler handler, IConfig props, ProtocolProcessor processor) throws IOException {
        // 从配置中获取主机地址，默认为"**************"
        String host = props.getProperty(BrokerConstants.HOST_PROPERTY_NAME, "**************");
        // 从配置中获取WebSocket端口，默认为禁用状态
        String wsPortProp = props.getProperty(BrokerConstants.WS_PORT_PROPERTY_NAME, DISABLED_PORT_BIND);

        // 检查WebSocket端口是否被禁用
        if (DISABLED_PORT_BIND.equals(wsPortProp)) {
            LOG.info("WebSocket端口被设置为禁用状态，跳过Netty WebSocket初始化");
            return;
        }

        // 解析WebSocket端口号
        int port = Integer.parseInt(wsPortProp);
        websocketPort = port; // 保存WebSocket端口号
        // 从配置中获取WebSocket服务路径，默认为"/mqtt"
        websocketPath = props.getProperty(BrokerConstants.WS_PATH_PROPERTY_NAME, "/mqtt");

        LOG.info("初始化Netty WebSocket传输协议，端口: {}, 路径: {}", port, websocketPath);

        // 使用工厂方法初始化WebSocket服务器
        initFactory(host, port, "Netty WebSocket", new PipelineInitializer() {
            /**
             * 初始化WebSocket通道管道
             *
             * 配置WebSocket连接的完整处理器链，包括HTTP协议处理、
             * WebSocket协议升级、消息编解码、业务逻辑处理等。
             *
             * @param pipeline 要配置的通道管道
             * @throws Exception 如果初始化过程中发生异常
             */
            @Override
            void init(ChannelPipeline pipeline) throws Exception {
                // 添加通用处理器（错误捕获、字节指标、额外指标等）
                addCommonHandlers(pipeline);

                // 添加连接调试日志处理器
                pipeline.addLast("connectionLogger", new ChannelInboundHandlerAdapter() {
                    /**
                     * 通道激活事件处理
                     * 当新的WebSocket连接建立时记录日志
                     */
                    @Override
                    public void channelActive(ChannelHandlerContext ctx) throws Exception {
                        LOG.info("🔗 新WebSocket连接: {}", ctx.channel().remoteAddress());
                        super.channelActive(ctx);
                    }

                    /**
                     * 异常捕获事件处理
                     * 当WebSocket连接发生异常时记录错误日志
                     */
                    @Override
                    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
                        LOG.error("❌ WebSocket连接异常: {}", ctx.channel().remoteAddress(), cause);
                        super.exceptionCaught(ctx, cause);
                    }
                });

                // 添加HTTP协议处理器
                // HTTP服务器编解码器，处理HTTP请求和响应的编解码
                pipeline.addLast("httpServerCodec", new HttpServerCodec());
                // HTTP对象聚合器，将HTTP消息片段聚合为完整的HTTP消息（最大64KB）
                pipeline.addLast("httpObjectAggregator", new HttpObjectAggregator(65536));

                // 添加HTTP请求日志处理器（用于调试）
                pipeline.addLast("httpRequestLogger", new ChannelInboundHandlerAdapter() {
                    /**
                     * 通道读取事件处理
                     * 记录接收到的HTTP请求信息
                     */
                    @Override
                    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
                        // 检查消息是否为完整的HTTP请求
                        if (msg instanceof FullHttpRequest) {
                            FullHttpRequest request = (FullHttpRequest) msg;
                            // 记录HTTP请求的方法、URI和来源地址
                            LOG.info("📥 HTTP请求: {} {} from {}",
                                request.method(), request.uri(), ctx.channel().remoteAddress());
                        }
                        super.channelRead(ctx, msg);
                    }
                });

                // 添加WebSocket协议处理器 - 处理WebSocket握手和协议升级
                // 参数说明：websocketPath=WebSocket路径，null=不限制子协议，true=允许扩展，65536=最大帧大小
                pipeline.addLast("websocketHandler", new WebSocketServerProtocolHandler(websocketPath, null, true, 65536));

                // 添加WebSocket握手完成监听器
                pipeline.addLast("websocketHandshakeListener", new ChannelInboundHandlerAdapter() {
                    @Override
                    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
                        if (evt instanceof WebSocketServerProtocolHandler.HandshakeComplete) {
                            WebSocketServerProtocolHandler.HandshakeComplete handshake =
                                (WebSocketServerProtocolHandler.HandshakeComplete) evt;
                            LOG.info("✅ WebSocket握手完成: {} -> {}",
                                ctx.channel().remoteAddress(), handshake.requestUri());
                        }
                        super.userEventTriggered(ctx, evt);
                    }

                    @Override
                    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
                        LOG.error("❌ WebSocket握手异常: {}", ctx.channel().remoteAddress(), cause);
                        ctx.close();
                    }
                });

                // 添加WebSocket帧到ByteBuf转换处理器
                // 这个处理器将WebSocket帧中的数据提取为ByteBuf格式
                pipeline.addLast("wsFrameToByteBuf", new NettyWebSocketMqttGatewayHandler());
                // 添加MQTT解码器，将字节流解码为MQTT消息对象
                pipeline.addLast("decoder", new MqttDecoder());
                // 添加MQTT编码器，将MQTT消息对象编码为字节流
                pipeline.addLast("encoder", MqttEncoder.INSTANCE);
                // 添加消息指标处理器，统计消息的读写数量
                pipeline.addLast("metrics", new MessageMetricsHandler(m_metricsCollector));
                // 添加MQTT消息日志处理器，记录MQTT消息的详细信息
                pipeline.addLast("messageLogger", new MQTTMessageLogger());
                // 添加野火IM协议处理器
                //pipeline.addLast("protobufWebSocketHandler", new ProtobufWebSocketHandler(processor));
                pipeline.addLast("protobufWebSocketHandler", handler);

                LOG.info("✅ Netty WebSocket管道初始化完成，路径: {}", websocketPath);
            }
        });

        LOG.info("Netty WebSocket传输协议已在端口{}上初始化", port);
    }


    /**
     * 添加公共处理器到管道
     * <p>
     * 这是一个辅助方法，用于向通道管道添加所有传输协议共用的处理器。
     * 包括错误捕获处理器、字节指标处理器和额外的指标处理器。
     *
     * @param pipeline 要添加处理器的通道管道
     */
    private void addCommonHandlers(ChannelPipeline pipeline) {
        // 如果存在错误捕获处理器，则添加到管道中
        if (errorsCather.isPresent()) {
            pipeline.addLast("bugsnagCatcher", errorsCather.get());
        }
        // 添加字节指标处理器，统计网络传输的字节数
        pipeline.addLast("bytemetrics", new BytesMetricsHandler(m_bytesMetricsCollector));
        // 如果存在额外的指标处理器，则添加到管道中
        if (metrics.isPresent()) {
            pipeline.addLast("wizardMetrics", metrics.get());
        }
    }

    /**
     * 关闭Netty接收器
     * <p>
     * 优雅地关闭Netty服务器，包括关闭事件循环组、等待连接终止、
     * 收集最终的指标数据等。这个方法确保所有资源被正确释放。
     */
    public void close() {
        LOG.info("Closing Netty acceptor..."); // 记录关闭开始日志

        // 检查事件循环组是否已初始化
        if (m_workerGroup == null || m_bossGroup == null) {
            LOG.error("Netty acceptor is not initialized"); // 记录错误日志
            // 抛出异常，表示在未初始化的接收器上调用了关闭方法
            throw new IllegalStateException("Invoked close on an Acceptor that wasn't initialized");
        }
        // 优雅地关闭Worker事件循环组
        Future<?> workerWaiter = m_workerGroup.shutdownGracefully();
        // 优雅地关闭Boss事件循环组
        Future<?> bossWaiter = m_bossGroup.shutdownGracefully();

        /*
         * 如果被中断，我们不应该抛出IllegalStateException。
         * 如果这样做，代理服务器将无法正确关闭。
         */
        LOG.info("Waiting for worker and boss event loop groups to terminate..."); // 记录等待日志
        try {
            // 等待Worker事件循环组终止，最多等待10秒
            workerWaiter.await(10, TimeUnit.SECONDS);
            // 等待Boss事件循环组终止，最多等待10秒
            bossWaiter.await(10, TimeUnit.SECONDS);
        } catch (InterruptedException iex) {
            // 如果等待过程中被中断，记录警告日志
            LOG.warn("An InterruptedException was caught while waiting for event loops to terminate...");
        }

        // 检查Worker事件循环组是否已完全终止
        if (!m_workerGroup.isTerminated()) {
            LOG.warn("Forcing shutdown of worker event loop..."); // 记录强制关闭警告
            // 强制立即关闭Worker事件循环组
            m_workerGroup.shutdownGracefully(0L, 0L, TimeUnit.MILLISECONDS);
        }

        // 检查Boss事件循环组是否已完全终止
        if (!m_bossGroup.isTerminated()) {
            LOG.warn("Forcing shutdown of boss event loop..."); // 记录强制关闭警告
            // 强制立即关闭Boss事件循环组
            m_bossGroup.shutdownGracefully(0L, 0L, TimeUnit.MILLISECONDS);
        }

        // 收集和记录消息指标
        LOG.info("Collecting message metrics..."); // 记录指标收集开始日志
        MessageMetrics metrics = m_metricsCollector.computeMetrics(); // 计算消息指标
        // 记录消息指标统计结果
        LOG.info("Metrics have been collected. Read messages={}, written messages={}", metrics.messagesRead(),
            metrics.messagesWrote());

        // 收集和记录字节指标
        LOG.info("Collecting bytes metrics..."); // 记录字节指标收集开始日志
        BytesMetrics bytesMetrics = m_bytesMetricsCollector.computeMetrics(); // 计算字节指标
        // 记录字节指标统计结果
        LOG.info("Bytes metrics have been collected. Read bytes={}, written bytes={}", bytesMetrics.readBytes(),
            bytesMetrics.wroteBytes());
    }

    /**
     * 创建SSL处理器
     * <p>
     * 根据提供的SSL上下文创建SSL/TLS处理器，用于加密网络通信。
     * 这个方法配置SSL引擎为服务器模式，并可选择是否需要客户端认证。
     *
     * @param sslContext      SSL上下文，包含证书、密钥等SSL配置信息
     * @param needsClientAuth 是否需要客户端认证（双向SSL）
     * @return 配置好的SSL处理器，可以添加到Netty管道中
     */
    private ChannelHandler createSslHandler(SSLContext sslContext, boolean needsClientAuth) {
        // 从SSL上下文创建SSL引擎
        SSLEngine sslEngine = sslContext.createSSLEngine();
        // 设置为服务器模式（false表示服务器模式，true表示客户端模式）
        sslEngine.setUseClientMode(false);
        // 如果需要客户端认证，则启用客户端认证
        if (needsClientAuth) {
            sslEngine.setNeedClientAuth(true);
        }
        // 创建并返回SSL处理器
        return new SslHandler(sslEngine);
    }

}
