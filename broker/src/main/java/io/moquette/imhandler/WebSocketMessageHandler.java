///*
// * This file is part of the Wildfire Chat package.
// * (c) Heavyrain2012 <<EMAIL>>
// *
// * For the full copyright and license information, please view the LICENSE
// * file that was distributed with this source code.
// */
//
//package io.moquette.imhandler;
//
//import cn.wildfirechat.common.ErrorCode;
//import cn.wildfirechat.common.IMExceptionEvent;
//import cn.wildfirechat.pojos.Conversation;
//import cn.wildfirechat.pojos.MessagePayload;
//import cn.wildfirechat.pojos.SendMessageData;
//import cn.wildfirechat.pojos.SendMessageResult;
//import com.google.gson.Gson;
//import com.google.gson.GsonBuilder;
//import com.google.gson.JsonSyntaxException;
//import com.xiaoleilu.loServer.RestResult;
//import com.xiaoleilu.loServer.action.Action;
//import com.xiaoleilu.loServer.action.channel.ChannelAction;
//import com.xiaoleilu.loServer.action.robot.RobotAction;
//import io.moquette.server.Server;
//import io.moquette.spi.impl.Utils;
//import io.netty.buffer.ByteBuf;
//import io.netty.buffer.Unpooled;
//import io.netty.channel.Channel;
//import io.netty.channel.ChannelHandlerContext;
//import io.netty.channel.SimpleChannelInboundHandler;
//import io.netty.handler.codec.http.FullHttpRequest;
//import io.netty.handler.codec.http.HttpHeaders;
//import io.netty.handler.codec.http.HttpRequest;
//import io.netty.handler.codec.http.QueryStringDecoder;
//import io.netty.handler.codec.http.websocketx.*;
//import io.netty.util.internal.StringUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import win.liyufan.im.IMTopic;
//import win.liyufan.im.Utility;
//
//import java.nio.charset.StandardCharsets;
//import java.util.List;
//import java.util.Map;
//import java.util.UUID;
//
//import static cn.wildfirechat.proto.ProtoConstants.ConversationType.ConversationType_Channel;
//import static cn.wildfirechat.proto.ProtoConstants.ConversationType.ConversationType_Private;
//
///**
// * 简化的WebSocket消息处理器
// * 负责WebSocket连接的建立和消息处理（无认证版本）
// */
//public class WebSocketMessageHandler extends SimpleChannelInboundHandler<Object> {
//    private static final Logger LOG = LoggerFactory.getLogger(WebSocketMessageHandler.class);
//    private static final Gson GSON = new Gson();
//    protected static final Gson gson = new GsonBuilder().disableHtmlEscaping().create();
//    private final WebSocketMessageAdapter webSocketMessageAdapter;
//    private final Server server;
//    private WebSocketServerHandshaker handshaker;
//
//    public WebSocketMessageHandler(WebSocketMessageAdapter adapter, Server server) {
//        this.webSocketMessageAdapter = adapter;
//        this.server = server;
//    }
//
//    @Override
//    protected void channelRead0(ChannelHandlerContext ctx, Object msg) throws Exception {
//        if (msg instanceof FullHttpRequest) {
//            // 处理WebSocket握手请求
//            handleHttpRequest(ctx, (FullHttpRequest) msg);
//        } else if (msg instanceof WebSocketFrame) {
//            // 处理WebSocket消息帧
//            handleWebSocketFrame(ctx, (WebSocketFrame) msg);
//        }
//    }
//
//    /**
//     * 处理HTTP请求（WebSocket握手）
//     */
//    private void handleHttpRequest(ChannelHandlerContext ctx, FullHttpRequest req) {
//        // 检查是否为WebSocket升级请求
//        if (!req.decoderResult().isSuccess() || !"websocket".equals(req.headers().get("Upgrade"))) {
//            LOG.warn("Invalid WebSocket upgrade request");
//            ctx.close();
//            return;
//        }
//
//        try {
//            // 从URL参数中获取客户端ID（可选）
//            QueryStringDecoder decoder = new QueryStringDecoder(req.uri());
//            Map<String, List<String>> params = decoder.parameters();
//
//            String clientId = getParameterValue(params, "clientId");
//            if (StringUtil.isNullOrEmpty(clientId)) {
//                clientId = "ws_" + UUID.randomUUID().toString();
//            }
//
//            // 执行WebSocket握手
//            WebSocketServerHandshakerFactory wsFactory = new WebSocketServerHandshakerFactory(
//                getWebSocketURL(req), null, true, 65536);
//            handshaker = wsFactory.newHandshaker(req);
//
//            if (handshaker == null) {
//                WebSocketServerHandshakerFactory.sendUnsupportedVersionResponse(ctx.channel());
//            } else {
//                handshaker.handshake(ctx.channel(), req);
//
//                // 保存连接信息到Channel属性
//                ctx.channel().attr(WebSocketMessageAdapter.CLIENT_ID_KEY).set(clientId);
//
//                LOG.info("WebSocket connection established, clientId: {}", clientId);
//
//                // 发送连接成功消息
//                sendConnectionSuccessMessage(ctx.channel(), clientId);
//            }
//
//        } catch (Exception e) {
//            LOG.error("Error during WebSocket handshake", e);
//            ctx.close();
//        }
//    }
//
//    /**
//     * 处理WebSocket消息帧
//     */
//    private void handleWebSocketFrame(ChannelHandlerContext ctx, WebSocketFrame frame) {
//        // 处理关闭帧
//        if (frame instanceof CloseWebSocketFrame) {
//            handshaker.close(ctx.channel(), (CloseWebSocketFrame) frame.retain());
//            return;
//        }
//
//        // 处理Ping帧
//        if (frame instanceof PingWebSocketFrame) {
//            ctx.channel().write(new PongWebSocketFrame(frame.content().retain()));
//            return;
//        }
//
//        // 处理文本消息帧
//        if (frame instanceof TextWebSocketFrame) {
//            String message = ((TextWebSocketFrame) frame).text();
//            LOG.info("Received WebSocket message: {}", message);
//
//            try {
//
//                // 简单的消息回显处理
//                webSocketMessageAdapter.handleWebSocketMessage(ctx.channel(), message);
//            } catch (Exception e) {
//                LOG.error("Error handling WebSocket message", e);
//                sendErrorMessage(ctx.channel(), "Internal server error");
//            }
//            return;
//        }
//
//        // 处理二进制消息帧（暂不支持）
//        if (frame instanceof BinaryWebSocketFrame) {
//            LOG.warn("Binary WebSocket frames are not supported");
//            sendErrorMessage(ctx.channel(), "Binary frames not supported");
//        }
//    }
//
//
//    /**
//     * 获取URL参数值
//     */
//    private String getParameterValue(Map<String, List<String>> params, String paramName) {
//        List<String> values = params.get(paramName);
//        return (values != null && !values.isEmpty()) ? values.get(0) : null;
//    }
//
//    /**
//     * 获取WebSocket URL
//     */
//    private String getWebSocketURL(FullHttpRequest req) {
//        String protocol = "ws://";
//        if (req.headers().get("X-Forwarded-Proto") != null) {
//            protocol = req.headers().get("X-Forwarded-Proto").equals("https") ? "wss://" : "ws://";
//        }
//        return protocol + req.headers().get(HttpHeaders.Names.HOST) + req.uri();
//    }
//
//    /**
//     * 发送连接成功消息
//     */
//    private void sendConnectionSuccessMessage(Channel channel, String clientId) {
//        SimpleWebSocketResponse response = new SimpleWebSocketResponse();
//        response.setType("connection");
//        response.setStatus("connected");
//        response.setClientId(clientId);
//        response.setMessage("WebSocket connection established successfully");
//
//        String jsonResponse = GSON.toJson(response);
//        LOG.info("Sending connection success message: {}", jsonResponse);
//        channel.writeAndFlush(new TextWebSocketFrame(jsonResponse));
//    }
//
//    /**
//     * 发送错误消息
//     */
//    private void sendErrorMessage(Channel channel, String errorMessage) {
//        SimpleWebSocketResponse response = new SimpleWebSocketResponse();
//        response.setType("error");
//        response.setStatus("error");
//        response.setMessage(errorMessage);
//
//        String jsonResponse = GSON.toJson(response);
//        channel.writeAndFlush(new TextWebSocketFrame(jsonResponse));
//    }
//
//    @Override
//    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
//        String clientId = ctx.channel().attr(WebSocketMessageAdapter.CLIENT_ID_KEY).get();
//
//        if (clientId != null) {
//            LOG.info("WebSocket connection closed for clientId: {}", clientId);
//        }
//
//        super.channelInactive(ctx);
//    }
//
//    @Override
//    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
//        cause.printStackTrace();
//        LOG.error("WebSocket connection error", cause);
//        ctx.close();
//    }
//
//    /**
//     * 简单的WebSocket响应数据结构
//     */
//    public static class SimpleWebSocketResponse {
//        private String type;
//        private String status;
//        private String clientId;
//        private String message;
//        private Object data;
//
//        public String getType() {
//            return type;
//        }
//
//        public void setType(String type) {
//            this.type = type;
//        }
//
//        public String getStatus() {
//            return status;
//        }
//
//        public void setStatus(String status) {
//            this.status = status;
//        }
//
//        public String getClientId() {
//            return clientId;
//        }
//
//        public void setClientId(String clientId) {
//            this.clientId = clientId;
//        }
//
//        public String getMessage() {
//            return message;
//        }
//
//        public void setMessage(String message) {
//            this.message = message;
//        }
//
//        public Object getData() {
//            return data;
//        }
//
//        public void setData(Object data) {
//            this.data = data;
//        }
//    }
//}
