/*
 * Copyright (c) 2012-2017 The original author or authors
 * ------------------------------------------------------
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * and Apache License v2.0 which accompanies this distribution.
 *
 * The Eclipse Public License is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * The Apache License v2.0 is available at
 * http://www.opensource.org/licenses/apache2.0.php
 *
 * You may elect to redistribute this code under either of these licenses.
 */

package io.moquette.spi;

/**
 * 用于接收输入事件队列中某些事件通知的回调接口。
 * 
 * 这是附加在前端协议解析功能之后的消息处理功能的抽象。
 * 
 * 该接口定义了消息处理的核心抽象，允许系统在协议解析完成后
 * 进行自定义的消息处理逻辑。目前该接口为空，为未来的扩展预留了接口。
 * 
 * 设计目的：
 * 1. 提供消息处理的统一抽象层
 * 2. 支持自定义消息处理逻辑的扩展
 * 3. 实现协议解析与消息处理的解耦
 */
public interface IMessaging {
    // 目前接口为空，为未来的消息处理功能预留扩展点
    // 可以在此添加消息路由、过滤、转换等方法
}
