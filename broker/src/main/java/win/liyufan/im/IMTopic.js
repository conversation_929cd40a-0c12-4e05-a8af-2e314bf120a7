/*
 * This file is part of the Wildfire Chat package.
 * (c) Heavyrain2012 <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/**
 * 即时通讯系统中用于消息路由和功能调用的主题（Topic）常量定义
 * 每个常量对应一个特定的操作类型，通过消息队列进行通信
 */
const IMTopic = {
    /** 发送单聊消息（点对点消息） */
    SendMessageTopic: "MS",
    /** 发送群组消息（多播消息） */
    MultiCastMessageTopic: "MMC",
    /** 撤回单聊消息 */
    RecallMessageTopic: "MR",
    /** 撤回群组消息 */
    RecallMultiCastMessageTopic: "MRMC",
    /** 拉取离线消息（用户上线时获取未读消息） */
    PullMessageTopic: "MP",
    /** 新消息通知（推送通知） */
    NotifyMessageTopic: "MN",
    /** 消息撤回通知 */
    NotifyRecallMessageTopic: "RMN",
    /** 用户离线通知（通知其他用户该用户已下线） */
    NotifyOffline: "ROFL",

    /** 广播消息（发送给全平台用户） */
    BroadcastMessageTopic: "MBC",

    /** 获取用户设置（如免打扰模式等） */
    GetUserSettingTopic: "UG",
    /** 更新用户设置 */
    PutUserSettingTopic: "UP",
    /** 用户设置变更通知 */
    NotifyUserSettingTopic: "UN",

    /** 创建群组 */
    CreateGroupTopic: "GC",
    /** 添加群成员 */
    AddGroupMemberTopic: "GAM",
    /** 踢出群成员 */
    KickoffGroupMemberTopic: "GKM",
    /** 退出群组 */
    QuitGroupTopic: "GQ",
    /** 解散群组 */
    DismissGroupTopic: "GD",
    /** 修改群信息（如群名称、公告） */
    ModifyGroupInfoTopic: "GMI",
    /** 修改群备注名 */
    ModifyGroupAliasTopic: "GMA",
    /** 修改群成员昵称 */
    ModifyGroupMemberAliasTopic: "GMMA",
    /** 修改群成员扩展信息 */
    ModifyGroupMemberExtraTopic: "GMME",
    /** 获取群信息 */
    GetGroupInfoTopic: "GPGI",
    /** 获取群成员列表 */
    GetGroupMemberTopic: "GPGM",
    /** 转移群主权限 */
    TransferGroupTopic: "GTG",
    /** 设置/取消管理员 */
    SetGroupManagerTopic: "GSM",
    /** 获取当前用户加入的群组列表 */
    GetMyGroupsTopic: "GMGS",
    /** 获取与指定用户的共同群组 */
    GetCommonGroupsTopic: "GCGS",

    /** 获取用户信息 */
    GetUserInfoTopic: "UPUI",
    /** 修改当前用户信息（如昵称、头像） */
    ModifyMyInfoTopic: "MMI",
    /** 用户信息变更通知 */
    NotifyUserInfoTopic: "UIN",

    /** 获取七牛云文件上传凭证 */
    GetQiniuUploadTokenTopic: "GQNUT",

    /** 发送好友请求 */
    AddFriendRequestTopic: "FAR",
    /** 处理好友请求（同意/拒绝） */
    HandleFriendRequestTopic: "FHR",
    /** 拉取待处理的好友请求 */
    FriendRequestPullTopic: "FRP",
    /** 新好友请求通知 */
    NotifyFriendRequestTopic: "FRN",
    /** 同步未读好友请求数 */
    RriendRequestUnreadSyncTopic: "FRUS",

    /** 删除好友 */
    DeleteFriendTopic: "FDL",
    /** 拉取好友列表 */
    FriendPullTopic: "FP",
    /** 好友状态变更通知（如上线/下线） */
    NotifyFriendTopic: "FN",
    /** 拉黑用户 */
    BlackListUserTopic: "BLU",
    /** 设置好友备注名 */
    SetFriendAliasTopic: "FALS",

    /** 上传设备推送令牌（用于消息推送） */
    UploadDeviceTokenTopic: "UDT",

    /** 搜索用户（按用户名/ID等） */
    UserSearchTopic: "US",

    /** 加入聊天室 */
    JoinChatroomTopic: "CRJ",
    /** 退出聊天室 */
    QuitChatroomTopic: "CRQ",
    /** 获取聊天室信息 */
    GetChatroomInfoTopic: "CRI",
    /** 获取聊天室成员列表 */
    GetChatroomMemberTopic: "CRMI",

    /** 内部消息路由（服务间通信） */
    RouteTopic: "ROUTE",

    /** 创建频道 */
    CreateChannelTopic: "CHC",
    /** 修改频道信息 */
    ModifyChannelInfoTopic: "CHMI",
    /** 转移频道所有权 */
    TransferChannelInfoTopic: "CHT",
    /** 销毁频道 */
    DestroyChannelInfoTopic: "CHD",
    /** 搜索频道 */
    ChannelSearchTopic: "CHS",
    /** 订阅/取消订阅频道 */
    ChannelListenTopic: "CHL",
    /** 拉取频道列表 */
    ChannelPullTopic: "CHP",
    /** 获取已订阅的频道列表 */
    ListenedChannelListTopic: "CHLL",

    /** 获取用户登录Token */
    GetTokenTopic: "GETTOKEN",
    /** 注销用户账号 */
    DestroyUserTopic: "DESTROYUSER",

    /** 加载远程历史消息（跨服务器同步） */
    LoadRemoteMessagesTopic: "LRM",
    /** 踢出PC端客户端（强制下线） */
    KickoffPCClientTopic: "KPCC",

    /** 清除用户会话（如退出登录） */
    ClearSessionTopic: "CST",

    /** 获取应用Token（第三方接入） */
    GetApplicationTokenRequestTopic: "ATR",
    /** 获取应用配置信息 */
    ApplicationConfigRequestTopic: "ACR",

    //****************************** 用户管理相关主题 ******************************/
    /** 创建用户 (对应 API: Create_User) */
    CreateUserTopic: "UC",
    /** 删除用户 (对应 API: Destroy_User) */
    DeleteUserTopic: "UD",
    /** 批量获取用户信息 (对应 API: User_Batch_Get_Infos) */
    BatchGetUserInfoTopic: "UBGI",
    /** 获取所有用户 (对应 API: User_Get_All) */
    GetAllUsersTopic: "UAG",
    /** 更新用户封禁状态 (对应 API: User_Update_Block_Status) */
    UpdateUserBlockStatusTopic: "UBS",
    /** 获取被封禁用户列表 (对应 API: User_Get_Blocked_List) */
    GetBlockedUsersTopic: "UBL",
    /** 检查用户封禁状态 (对应 API: User_Check_Block_Status) */
    CheckUserBlockStatusTopic: "UBCS",
    /** 获取用户在线状态 (对应 API: User_Get_Online_Status) */
    GetUserOnlineStatusTopic: "UOS",
    /** 获取在线用户数量 (对应 API: User_Online_Count) */
    GetOnlineUserCountTopic: "UOC",
    /** 获取在线用户列表 (对应 API: User_Online_List) */
    GetOnlineUserListTopic: "UOL",
    /** 获取用户会话列表 (对应 API: User_Session_List) */
    GetUserSessionListTopic: "USL",

    //****************************** 群组管理相关主题 ******************************/
    /** 获取群成员信息 (对应 API: Group_Member_Get) */
    GetGroupMemberInfoTopic: "GPGMI",
    /** 设置群成员别名 (对应 API: Group_Set_Member_Alias) */
    SetGroupMemberAliasTopic: "GMMA",
    /** 设置群成员扩展信息 (对应 API: Group_Set_Member_Extra) */
    SetGroupMemberExtraTopic: "GMMEI",
    /** 按类型获取用户加入的群组 (对应 API: Get_User_Groups_By_Type) */
    GetUserGroupsByTypeTopic: "GMGST",

    //****************************** 消息管理相关主题 ******************************/
    /** 发布消息 (对应 API: Msg_Publish) */
    PublishMessageTopic: "MPUB",
    /** 删除消息 (对应 API: Msg_Delete) */
    DeleteMessageTopic: "MD",
    /** 更新消息 (对应 API: Msg_Update) */
    UpdateMessageTopic: "MU",
    /** 获取一条消息 (对应 API: Msg_GetOne) */
    GetMessageOneTopic: "MGO",
    /** 广播消息 (对应 API: Msg_Broadcast) */
    BroadcastMessageTopicNew: "MBR",
    /** 组播消息 (对应 API: Msg_Multicast) */
    MulticastMessageTopic: "MMCST",
    /** 撤回广播消息 (对应 API: Msg_RecallBroadCast) */
    RecallBroadcastMessageTopic: "MRBR",
    /** 撤回组播消息 (对应 API: Msg_RecallMultiCast) */
    RecallMulticastMessageTopic: "MRMCST",
    /** 删除广播消息 (对应 API: Msg_DeleteBroadCast) */
    DeleteBroadcastMessageTopic: "MDBR",
    /** 删除组播消息 (对应 API: Msg_DeleteMultiCast) */
    DeleteMulticastMessageTopic: "MDMCST",
    /** 标记会话已读 (对应 API: Msg_ConvRead) */
    MarkConversationReadTopic: "MCR",
    /** 标记消息已送达 (对应 API: Msg_Delivery) */
    MarkMessageDeliveredTopic: "MDL",
    /** 删除会话 (对应 API: Conversation_Delete) */
    DeleteConversationTopic: "DCV",
    /** 清除用户的消息 (对应 API: Msg_Clear_By_User) */
    ClearMessagesByUserTopic: "CMU",

    //****************************** 敏感词管理相关主题 ******************************/
    /** 添加敏感词 (对应 API: Sensitive_Add) */
    AddSensitiveWordTopic: "SAW",
    /** 删除敏感词 (对应 API: Sensitive_Del) */
    DeleteSensitiveWordTopic: "SDW",
    /** 查询敏感词 (对应 API: Sensitive_Query) */
    QuerySensitiveWordsTopic: "SQW",

    //****************************** 聊天室管理相关主题 ******************************/
    /** 删除聊天室 (对应 API: Chatroom_Destroy) */
    DestroyChatroomTopic: "CRD",
    /** 获取聊天室信息 (对应 API: Chatroom_Info) */
    GetChatroomInfoTopicNew: "CRI",
    /** 获取聊天室成员列表 (对应 API: Chatroom_GetMembers) */
    GetChatroomMembersTopic: "CRML",
    /** 获取用户所在的聊天室 (对应 API: Chatroom_GetUserChatroom) */
    GetUserChatroomsTopic: "CUR",
    /** 设置聊天室黑名单状态 (对应 API: Chatroom_SetBlacklist) */
    SetChatroomBlacklistStatusTopic: "CRBS",
    /** 获取聊天室黑名单列表 (对应 API: Chatroom_GetBlacklist) */
    GetChatroomBlacklistTopic: "CRBL",
    /** 设置聊天室管理员 (对应 API: Chatroom_SetManager) */
    SetChatroomManagerTopic: "CRMGR",
    /** 获取聊天室管理员列表 (对应 API: Chatroom_GetManagerList) */
    GetChatroomManagersTopic: "CRMGRL",
    /** 聊天室全体禁言 (对应 API: Chatroom_MuteAll) */
    MuteAllInChatroomTopic: "CRMUTEALL",

    //****************************** 域名管理相关主题 ******************************/
    /** 创建域名 (对应 API: Create_Domain) */
    CreateDomainTopic: "DOMC",
    /** 获取域名信息 (对应 API: Get_Domain) */
    GetDomainInfoTopic: "DOMG",
    /** 删除域名 (对应 API: Destroy_Domain) */
    DeleteDomainTopic: "DOMD",
    /** 获取域名列表 (对应 API: List_Domain) */
    GetDomainListTopic: "DOML",

    //****************************** 系统设置相关主题 ******************************/
    /** 获取系统设置 (对应 API: Get_System_Setting) */
    GetSystemSettingTopic: "SYSGET",
    /** 更新系统设置 (对应 API: Put_System_Setting) */
    UpdateSystemSettingTopic: "SYSPUT",
    /** 获取客户信息 (对应 API: GET_CUSTOMER) */
    GetCustomerInfoTopic: "CUSTINFO",

    //****************************** 会议管理相关主题 ******************************/
    /** 获取会议列表 (对应 API: Conference_List) */
    GetConferenceListTopic: "CONFLIST",
    /** 检查会议是否存在 (对应 API: Conference_Exist) */
    CheckConferenceExistsTopic: "CONFEXISTS",
    /** 获取会议参与者列表 (对应 API: Conference_List_Participant) */
    GetConferenceParticipantsTopic: "CONFPARTICIPANTS",
    /** 销毁会议 (对应 API: Conference_Destroy) */
    DestroyConferenceTopic: "CONFD",
    /** 会议录制 (对应 API: Conference_Recording) */
    RecordConferenceTopic: "CONFR",
    /** 会议RTP转发 (对应 API: Conference_Rtp_Forward) */
    ForwardConferenceRtpTopic: "CONFFORWARD",
    /** 停止RTP转发 (对应 API: Conference_Stop_Rtp_Forward) */
    StopForwardingConferenceRtpTopic: "CONFFORWARDSTOP",
    /** 获取RTP转发列表 (对应 API: Conference_List_Rtp_Forward) */
    GetForwardedConferenceRtpListTopic: "CONFFORWARDLIST",

    //****************************** 频道管理相关主题 ******************************/
    /** 获取频道资料 (对应 API: Channel_Get_Profile) */
    GetChannelProfileTopic: "CHGP",
    /** 更新频道资料 (对应 API: Channel_Update_Profile) */
    UpdateChannelProfileTopic: "CHUP",
    /** 发送频道消息 (对应 API: Channel_Message_Send) */
    SendChannelMessageTopic: "CHMSG",
    /** 获取频道订阅者列表 (对应 API: Channel_Subscriber_List) */
    GetChannelSubscribersTopic: "CHSUBS",
    /** 检查用户是否订阅该频道 (对应 API: Channel_Is_Subscriber) */
    CheckIfUserSubscribedToChannelTopic: "CHISSUB",
    /** 应用获取用户信息 (对应 API: Channel_Application_Get_UserInfo) */
    GetUserInfoForChannelApplicationTopic: "CHAPPUSER",

    //****************************** 朋友圈管理相关主题 ******************************/
    /** 管理员发布朋友圈 (对应 API: Admin_Moments_Post_Feed) */
    AdminPostMomentFeedTopic: "ADMPF",
    /** 机器人发布朋友圈 (对应 API: Robot_Moments_Post_Feed) */
    RobotPostMomentFeedTopic: "ROBPFC",
    /** 机器人拉取朋友圈动态 (对应 API: Robot_Moments_Pull_Feeds) */
    RobotPullMomentFeedsTopic: "ROBPF",
    /** 机器人更新朋友圈动态 (对应 API: Robot_Moments_Update_Feed) */
    RobotUpdateMomentFeedTopic: "ROBUF",
    /** 机器人发表评论 (对应 API: Robot_Moments_Post_Comment) */
    RobotPostCommentOnMomentTopic: "ROBCMT",
    /** 机器人拉取评论 (对应 API: Robot_Moments_Pull_Comment) */
    RobotPullCommentsOnMomentTopic: "ROBCMTP",
    /** 机器人获取单条朋友圈动态 (对应 API: Robot_Moments_Fetch_Feed) */
    RobotFetchSingleMomentFeedTopic: "ROBFF",
    /** 机器人获取朋友圈资料 (对应 API: Robot_Moments_Fetch_Profiles) */
    RobotFetchProfilesFromMomentsTopic: "ROBPROF",
    /** 机器人撤回评论 (对应 API: Robot_Moments_Recall_Comment) */
    RobotRecallCommentOnMomentTopic: "ROBRCLCMT",
    /** 机器人撤回朋友圈动态 (对应 API: Robot_Moments_Recall_Feed) */
    RobotRecallMomentFeedTopic: "ROBRCLFEED",
    /** 机器人更新朋友圈资料列表值 (对应 API: Robot_Moments_Update_Profiles_List_Value) */
    RobotUpdateProfilesListValueOnMomentTopic: "ROBUPDV",
    /** 机器人更新朋友圈资料值 (对应 API: Robot_Moments_Update_Profiles_Value) */
    RobotUpdateProfileValueOnMomentTopic: "ROBUPV"
};

// 导出模块 (支持 CommonJS 和 ES6 模块)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = IMTopic;
}

// 支持 ES6 模块导出
if (typeof window !== 'undefined') {
    window.IMTopic = IMTopic;
}
