@echo off
chcp 65001 > nul
echo ================================
echo 编译WebSocket服务器
echo ================================

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请安装JDK 8或更高版本
    pause
    exit /b 1
)

javac -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Java编译器，请安装JDK（不是JRE）
    pause
    exit /b 1
)

echo.
echo 1. 创建输出目录...
if not exist "broker\target\classes" mkdir "broker\target\classes"
if not exist "common\target\classes" mkdir "common\target\classes"

echo.
echo 2. 编译common模块...
cd common\src\main\java
for /r %%f in (*.java) do (
    echo 编译: %%f
    javac -d "..\..\..\..\target\classes" -encoding UTF-8 "%%f"
    if %errorlevel% neq 0 (
        echo 编译失败: %%f
        cd ..\..\..\..
        pause
        exit /b 1
    )
)
cd ..\..\..\..

echo.
echo 3. 编译broker模块...
cd broker\src\main\java
for /r %%f in (*.java) do (
    echo 编译: %%f
    javac -cp "..\..\..\..\common\target\classes" -d "..\..\..\..\target\classes" -encoding UTF-8 "%%f"
    if %errorlevel% neq 0 (
        echo 编译可能失败: %%f （某些依赖可能缺失，但WebSocket相关代码应该已编译）
    )
)
cd ..\..\..\..

echo.
echo 4. 复制资源文件...
if exist "broker\src\main\resources" (
    xcopy "broker\src\main\resources\*" "broker\target\classes\" /s /y
)
if exist "common\src\main\resources" (
    xcopy "common\src\main\resources\*" "common\target\classes\" /s /y
)

echo.
echo 编译完成！
echo 注意：由于缺少某些依赖库，可能会有编译警告或错误
echo 但是WebSocket相关的核心代码应该已经成功编译
echo.
echo 现在可以尝试运行 start_websocket_server.bat 启动服务器
echo.
pause 