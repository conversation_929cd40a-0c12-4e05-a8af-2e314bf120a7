@font-face {
	font-family: 'icomoon';
	src:  url('/static/iconfonts/icomoon/fonts/icomoon.ttf') format('truetype');
	font-weight: normal;
	font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
	/* use !important to prevent issues with browser extensions that change iconfonts */
	font-family: 'icomoon' !important;
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.icon-ion-alert-circled:before {
	content: "\f100";
}

.icon-ion-alert:before {
	content: "\f101";
}

.icon-ion-android-add-circle:before {
	content: "\f359";
}

.icon-ion-android-add:before {
	content: "\f2c7";
}

.icon-ion-android-alarm-clock:before {
	content: "\f35a";
}

.icon-ion-android-alert:before {
	content: "\f35b";
}

.icon-ion-android-apps:before {
	content: "\f35c";
}

.icon-ion-android-archive:before {
	content: "\f2c9";
}

.icon-ion-android-arrow-back:before {
	content: "\f2ca";
}

.icon-ion-android-arrow-down:before {
	content: "\f35d";
}

.icon-ion-android-arrow-dropdown-circle:before {
	content: "\f35e";
}

.icon-ion-android-arrow-dropdown:before {
	content: "\f35f";
}

.icon-ion-android-arrow-dropleft-circle:before {
	content: "\f360";
}

.icon-ion-android-arrow-dropleft:before {
	content: "\f361";
}

.icon-ion-android-arrow-dropright-circle:before {
	content: "\f362";
}

.icon-ion-android-arrow-dropright:before {
	content: "\f363";
}

.icon-ion-android-arrow-dropup-circle:before {
	content: "\f364";
}

.icon-ion-android-arrow-dropup:before {
	content: "\f365";
}

.icon-ion-android-arrow-forward:before {
	content: "\f30f";
}

.icon-ion-android-arrow-up:before {
	content: "\f366";
}

.icon-ion-android-attach:before {
	content: "\f367";
}

.icon-ion-android-bar:before {
	content: "\f368";
}

.icon-ion-android-bicycle:before {
	content: "\f369";
}

.icon-ion-android-boat:before {
	content: "\f36a";
}

.icon-ion-android-bookmark:before {
	content: "\f36b";
}

.icon-ion-android-bulb:before {
	content: "\f36c";
}

.icon-ion-android-bus:before {
	content: "\f36d";
}

.icon-ion-android-calendar:before {
	content: "\f2d1";
}

.icon-ion-android-call:before {
	content: "\f2d2";
}

.icon-ion-android-camera:before {
	content: "\f2d3";
}

.icon-ion-android-cancel:before {
	content: "\f36e";
}

.icon-ion-android-car:before {
	content: "\f36f";
}

.icon-ion-android-cart:before {
	content: "\f370";
}

.icon-ion-android-chat:before {
	content: "\f2d4";
}

.icon-ion-android-checkbox-blank:before {
	content: "\f371";
}

.icon-ion-android-checkbox-outline-blank:before {
	content: "\f372";
}

.icon-ion-android-checkbox-outline:before {
	content: "\f373";
}

.icon-ion-android-checkbox:before {
	content: "\f374";
}

.icon-ion-android-checkmark-circle:before {
	content: "\f375";
}

.icon-ion-android-clipboard:before {
	content: "\f376";
}

.icon-ion-android-close:before {
	content: "\f2d7";
}

.icon-ion-android-cloud-circle:before {
	content: "\f377";
}

.icon-ion-android-cloud-done:before {
	content: "\f378";
}

.icon-ion-android-cloud-outline:before {
	content: "\f379";
}

.icon-ion-android-cloud:before {
	content: "\f37a";
}

.icon-ion-android-color-palette:before {
	content: "\f37b";
}

.icon-ion-android-compass:before {
	content: "\f37c";
}

.icon-ion-android-contact:before {
	content: "\f2d8";
}

.icon-ion-android-contacts:before {
	content: "\f2d9";
}

.icon-ion-android-contract:before {
	content: "\f37d";
}

.icon-ion-android-create:before {
	content: "\f37e";
}

.icon-ion-android-delete:before {
	content: "\f37f";
}

.icon-ion-android-desktop:before {
	content: "\f380";
}

.icon-ion-android-document:before {
	content: "\f381";
}

.icon-ion-android-done-all:before {
	content: "\f382";
}

.icon-ion-android-done:before {
	content: "\f383";
}

.icon-ion-android-download:before {
	content: "\f2dd";
}

.icon-ion-android-drafts:before {
	content: "\f384";
}

.icon-ion-android-exit:before {
	content: "\f385";
}

.icon-ion-android-expand:before {
	content: "\f386";
}

.icon-ion-android-favorite-outline:before {
	content: "\f387";
}

.icon-ion-android-favorite:before {
	content: "\f388";
}

.icon-ion-android-film:before {
	content: "\f389";
}

.icon-ion-android-folder-open:before {
	content: "\f38a";
}

.icon-ion-android-folder:before {
	content: "\f2e0";
}

.icon-ion-android-funnel:before {
	content: "\f38b";
}

.icon-ion-android-globe:before {
	content: "\f38c";
}

.icon-ion-android-hand:before {
	content: "\f2e3";
}

.icon-ion-android-hangout:before {
	content: "\f38d";
}

.icon-ion-android-happy:before {
	content: "\f38e";
}

.icon-ion-android-home:before {
	content: "\f38f";
}

.icon-ion-android-image:before {
	content: "\f2e4";
}

.icon-ion-android-laptop:before {
	content: "\f390";
}

.icon-ion-android-list:before {
	content: "\f391";
}

.icon-ion-android-locate:before {
	content: "\f2e9";
}

.icon-ion-android-lock:before {
	content: "\f392";
}

.icon-ion-android-mail:before {
	content: "\f2eb";
}

.icon-ion-android-map:before {
	content: "\f393";
}

.icon-ion-android-menu:before {
	content: "\f394";
}

.icon-ion-android-microphone-off:before {
	content: "\f395";
}

.icon-ion-android-microphone:before {
	content: "\f2ec";
}

.icon-ion-android-more-horizontal:before {
	content: "\f396";
}

.icon-ion-android-more-vertical:before {
	content: "\f397";
}

.icon-ion-android-navigate:before {
	content: "\f398";
}

.icon-ion-android-notifications-none:before {
	content: "\f399";
}

.icon-ion-android-notifications-off:before {
	content: "\f39a";
}

.icon-ion-android-notifications:before {
	content: "\f39b";
}

.icon-ion-android-open:before {
	content: "\f39c";
}

.icon-ion-android-options:before {
	content: "\f39d";
}

.icon-ion-android-people:before {
	content: "\f39e";
}

.icon-ion-android-person-add:before {
	content: "\f39f";
}

.icon-ion-android-person:before {
	content: "\f3a0";
}

.icon-ion-android-phone-landscape:before {
	content: "\f3a1";
}

.icon-ion-android-phone-portrait:before {
	content: "\f3a2";
}

.icon-ion-android-pin:before {
	content: "\f3a3";
}

.icon-ion-android-plane:before {
	content: "\f3a4";
}

.icon-ion-android-playstore:before {
	content: "\f2f0";
}

.icon-ion-android-print:before {
	content: "\f3a5";
}

.icon-ion-android-radio-button-off:before {
	content: "\f3a6";
}

.icon-ion-android-radio-button-on:before {
	content: "\f3a7";
}

.icon-ion-android-refresh:before {
	content: "\f3a8";
}

.icon-ion-android-remove-circle:before {
	content: "\f3a9";
}

.icon-ion-android-remove:before {
	content: "\f2f4";
}

.icon-ion-android-restaurant:before {
	content: "\f3aa";
}

.icon-ion-android-sad:before {
	content: "\f3ab";
}

.icon-ion-android-search:before {
	content: "\f2f5";
}

.icon-ion-android-send:before {
	content: "\f2f6";
}

.icon-ion-android-settings:before {
	content: "\f2f7";
}

.icon-ion-android-share-alt:before {
	content: "\f3ac";
}

.icon-ion-android-share:before {
	content: "\f2f8";
}

.icon-ion-android-star-half:before {
	content: "\f3ad";
}

.icon-ion-android-star-outline:before {
	content: "\f3ae";
}

.icon-ion-android-star:before {
	content: "\f2fc";
}

.icon-ion-android-stopwatch:before {
	content: "\f2fd";
}

.icon-ion-android-subway:before {
	content: "\f3af";
}

.icon-ion-android-sunny:before {
	content: "\f3b0";
}

.icon-ion-android-sync:before {
	content: "\f3b1";
}

.icon-ion-android-textsms:before {
	content: "\f3b2";
}

.icon-ion-android-time:before {
	content: "\f3b3";
}

.icon-ion-android-train:before {
	content: "\f3b4";
}

.icon-ion-android-unlock:before {
	content: "\f3b5";
}

.icon-ion-android-upload:before {
	content: "\f3b6";
}

.icon-ion-android-volume-down:before {
	content: "\f3b7";
}

.icon-ion-android-volume-mute:before {
	content: "\f3b8";
}

.icon-ion-android-volume-off:before {
	content: "\f3b9";
}

.icon-ion-android-volume-up:before {
	content: "\f3ba";
}

.icon-ion-android-walk:before {
	content: "\f3bb";
}

.icon-ion-android-warning:before {
	content: "\f3bc";
}

.icon-ion-android-watch:before {
	content: "\f3bd";
}

.icon-ion-android-wifi:before {
	content: "\f305";
}

.icon-ion-aperture:before {
	content: "\f313";
}

.icon-ion-archive:before {
	content: "\f102";
}

.icon-ion-arrow-down-a:before {
	content: "\f103";
}

.icon-ion-arrow-down-b:before {
	content: "\f104";
}

.icon-ion-arrow-down-c:before {
	content: "\f105";
}

.icon-ion-arrow-expand:before {
	content: "\f25e";
}

.icon-ion-arrow-graph-down-left:before {
	content: "\f25f";
}

.icon-ion-arrow-graph-down-right:before {
	content: "\f260";
}

.icon-ion-arrow-graph-up-left:before {
	content: "\f261";
}

.icon-ion-arrow-graph-up-right:before {
	content: "\f262";
}

.icon-ion-arrow-left-a:before {
	content: "\f106";
}

.icon-ion-arrow-left-b:before {
	content: "\f107";
}

.icon-ion-arrow-left-c:before {
	content: "\f108";
}

.icon-ion-arrow-move:before {
	content: "\f263";
}

.icon-ion-arrow-resize:before {
	content: "\f264";
}

.icon-ion-arrow-return-left:before {
	content: "\f265";
}

.icon-ion-arrow-return-right:before {
	content: "\f266";
}

.icon-ion-arrow-right-a:before {
	content: "\f109";
}

.icon-ion-arrow-right-b:before {
	content: "\f10a";
}

.icon-ion-arrow-right-c:before {
	content: "\f10b";
}

.icon-ion-arrow-shrink:before {
	content: "\f267";
}

.icon-ion-arrow-swap:before {
	content: "\f268";
}

.icon-ion-arrow-up-a:before {
	content: "\f10c";
}

.icon-ion-arrow-up-b:before {
	content: "\f10d";
}

.icon-ion-arrow-up-c:before {
	content: "\f10e";
}

.icon-ion-asterisk:before {
	content: "\f314";
}

.icon-ion-at:before {
	content: "\f10f";
}

.icon-ion-backspace-outline:before {
	content: "\f3be";
}

.icon-ion-backspace:before {
	content: "\f3bf";
}

.icon-ion-bag:before {
	content: "\f110";
}

.icon-ion-battery-charging:before {
	content: "\f111";
}

.icon-ion-battery-empty:before {
	content: "\f112";
}

.icon-ion-battery-full:before {
	content: "\f113";
}

.icon-ion-battery-half:before {
	content: "\f114";
}

.icon-ion-battery-low:before {
	content: "\f115";
}

.icon-ion-beaker:before {
	content: "\f269";
}

.icon-ion-beer:before {
	content: "\f26a";
}

.icon-ion-bluetooth:before {
	content: "\f116";
}

.icon-ion-bonfire:before {
	content: "\f315";
}

.icon-ion-bookmark:before {
	content: "\f26b";
}

.icon-ion-bowtie:before {
	content: "\f3c0";
}

.icon-ion-briefcase:before {
	content: "\f26c";
}

.icon-ion-bug:before {
	content: "\f2be";
}

.icon-ion-calculator:before {
	content: "\f26d";
}

.icon-ion-calendar:before {
	content: "\f117";
}

.icon-ion-camera:before {
	content: "\f118";
}

.icon-ion-card:before {
	content: "\f119";
}

.icon-ion-cash:before {
	content: "\f316";
}

.icon-ion-chatbox-working:before {
	content: "\f11a";
}

.icon-ion-chatbox:before {
	content: "\f11b";
}

.icon-ion-chatboxes:before {
	content: "\f11c";
}

.icon-ion-chatbubble-working:before {
	content: "\f11d";
}

.icon-ion-chatbubble:before {
	content: "\f11e";
}

.icon-ion-chatbubbles:before {
	content: "\f11f";
}

.icon-ion-checkmark-circled:before {
	content: "\f120";
}

.icon-ion-checkmark-round:before {
	content: "\f121";
}

.icon-ion-checkmark:before {
	content: "\f122";
}

.icon-ion-chevron-down:before {
	content: "\f123";
}

.icon-ion-chevron-left:before {
	content: "\f124";
}

.icon-ion-chevron-right:before {
	content: "\f125";
}

.icon-ion-chevron-up:before {
	content: "\f126";
}

.icon-ion-clipboard:before {
	content: "\f127";
}

.icon-ion-clock:before {
	content: "\f26e";
}

.icon-ion-close-circled:before {
	content: "\f128";
}

.icon-ion-close-round:before {
	content: "\f129";
}

.icon-ion-close:before {
	content: "\f12a";
}

.icon-ion-closed-captioning:before {
	content: "\f317";
}

.icon-ion-cloud:before {
	content: "\f12b";
}

.icon-ion-code-download:before {
	content: "\f26f";
}

.icon-ion-code-working:before {
	content: "\f270";
}

.icon-ion-code:before {
	content: "\f271";
}

.icon-ion-coffee:before {
	content: "\f272";
}

.icon-ion-compass:before {
	content: "\f273";
}

.icon-ion-compose:before {
	content: "\f12c";
}

.icon-ion-connection-bars:before {
	content: "\f274";
}

.icon-ion-contrast:before {
	content: "\f275";
}

.icon-ion-crop:before {
	content: "\f3c1";
}

.icon-ion-cube:before {
	content: "\f318";
}

.icon-ion-disc:before {
	content: "\f12d";
}

.icon-ion-document-text:before {
	content: "\f12e";
}

.icon-ion-document:before {
	content: "\f12f";
}

.icon-ion-drag:before {
	content: "\f130";
}

.icon-ion-earth:before {
	content: "\f276";
}

.icon-ion-easel:before {
	content: "\f3c2";
}

.icon-ion-edit:before {
	content: "\f2bf";
}

.icon-ion-egg:before {
	content: "\f277";
}

.icon-ion-eject:before {
	content: "\f131";
}

.icon-ion-email-unread:before {
	content: "\f3c3";
}

.icon-ion-email:before {
	content: "\f132";
}

.icon-ion-erlenmeyer-flask-bubbles:before {
	content: "\f3c4";
}

.icon-ion-erlenmeyer-flask:before {
	content: "\f3c5";
}

.icon-ion-eye-disabled:before {
	content: "\f306";
}

.icon-ion-eye:before {
	content: "\f133";
}

.icon-ion-female:before {
	content: "\f278";
}

.icon-ion-filing:before {
	content: "\f134";
}

.icon-ion-film-marker:before {
	content: "\f135";
}

.icon-ion-fireball:before {
	content: "\f319";
}

.icon-ion-flag:before {
	content: "\f279";
}

.icon-ion-flame:before {
	content: "\f31a";
}

.icon-ion-flash-off:before {
	content: "\f136";
}

.icon-ion-flash:before {
	content: "\f137";
}

.icon-ion-folder:before {
	content: "\f139";
}

.icon-ion-fork-repo:before {
	content: "\f2c0";
}

.icon-ion-fork:before {
	content: "\f27a";
}

.icon-ion-forward:before {
	content: "\f13a";
}

.icon-ion-funnel:before {
	content: "\f31b";
}

.icon-ion-gear-a:before {
	content: "\f13d";
}

.icon-ion-gear-b:before {
	content: "\f13e";
}

.icon-ion-grid:before {
	content: "\f13f";
}

.icon-ion-hammer:before {
	content: "\f27b";
}

.icon-ion-happy-outline:before {
	content: "\f3c6";
}

.icon-ion-happy:before {
	content: "\f31c";
}

.icon-ion-headphone:before {
	content: "\f140";
}

.icon-ion-heart-broken:before {
	content: "\f31d";
}

.icon-ion-heart:before {
	content: "\f141";
}

.icon-ion-help-buoy:before {
	content: "\f27c";
}

.icon-ion-help-circled:before {
	content: "\f142";
}

.icon-ion-help:before {
	content: "\f143";
}

.icon-ion-home:before {
	content: "\f144";
}

.icon-ion-icecream:before {
	content: "\f27d";
}

.icon-ion-image:before {
	content: "\f147";
}

.icon-ion-images:before {
	content: "\f148";
}

.icon-ion-information-circled:before {
	content: "\f149";
}

.icon-ion-information:before {
	content: "\f14a";
}

.icon-ion-ionic:before {
	content: "\f14b";
}

.icon-ion-ios-alarm-outline:before {
	content: "\f3c7";
}

.icon-ion-ios-alarm:before {
	content: "\f3c8";
}

.icon-ion-ios-albums-outline:before {
	content: "\f3c9";
}

.icon-ion-ios-albums:before {
	content: "\f3ca";
}

.icon-ion-ios-americanfootball-outline:before {
	content: "\f3cb";
}

.icon-ion-ios-americanfootball:before {
	content: "\f3cc";
}

.icon-ion-ios-analytics-outline:before {
	content: "\f3cd";
}

.icon-ion-ios-analytics:before {
	content: "\f3ce";
}

.icon-ion-ios-arrow-back:before {
	content: "\f3cf";
}

.icon-ion-ios-arrow-down:before {
	content: "\f3d0";
}

.icon-ion-ios-arrow-forward:before {
	content: "\f3d1";
}

.icon-ion-ios-arrow-left:before {
	content: "\f3d2";
}

.icon-ion-ios-arrow-right:before {
	content: "\f3d3";
}

.icon-ion-ios-arrow-thin-down:before {
	content: "\f3d4";
}

.icon-ion-ios-arrow-thin-left:before {
	content: "\f3d5";
}

.icon-ion-ios-arrow-thin-right:before {
	content: "\f3d6";
}

.icon-ion-ios-arrow-thin-up:before {
	content: "\f3d7";
}

.icon-ion-ios-arrow-up:before {
	content: "\f3d8";
}

.icon-ion-ios-at-outline:before {
	content: "\f3d9";
}

.icon-ion-ios-at:before {
	content: "\f3da";
}

.icon-ion-ios-barcode-outline:before {
	content: "\f3db";
}

.icon-ion-ios-barcode:before {
	content: "\f3dc";
}

.icon-ion-ios-baseball-outline:before {
	content: "\f3dd";
}

.icon-ion-ios-baseball:before {
	content: "\f3de";
}

.icon-ion-ios-basketball-outline:before {
	content: "\f3df";
}

.icon-ion-ios-basketball:before {
	content: "\f3e0";
}

.icon-ion-ios-bell-outline:before {
	content: "\f3e1";
}

.icon-ion-ios-bell:before {
	content: "\f3e2";
}

.icon-ion-ios-body-outline:before {
	content: "\f3e3";
}

.icon-ion-ios-body:before {
	content: "\f3e4";
}

.icon-ion-ios-bolt-outline:before {
	content: "\f3e5";
}

.icon-ion-ios-bolt:before {
	content: "\f3e6";
}

.icon-ion-ios-book-outline:before {
	content: "\f3e7";
}

.icon-ion-ios-book:before {
	content: "\f3e8";
}

.icon-ion-ios-bookmarks-outline:before {
	content: "\f3e9";
}

.icon-ion-ios-bookmarks:before {
	content: "\f3ea";
}

.icon-ion-ios-box-outline:before {
	content: "\f3eb";
}

.icon-ion-ios-box:before {
	content: "\f3ec";
}

.icon-ion-ios-briefcase-outline:before {
	content: "\f3ed";
}

.icon-ion-ios-briefcase:before {
	content: "\f3ee";
}

.icon-ion-ios-browsers-outline:before {
	content: "\f3ef";
}

.icon-ion-ios-browsers:before {
	content: "\f3f0";
}

.icon-ion-ios-calculator-outline:before {
	content: "\f3f1";
}

.icon-ion-ios-calculator:before {
	content: "\f3f2";
}

.icon-ion-ios-calendar-outline:before {
	content: "\f3f3";
}

.icon-ion-ios-calendar:before {
	content: "\f3f4";
}

.icon-ion-ios-camera-outline:before {
	content: "\f3f5";
}

.icon-ion-ios-camera:before {
	content: "\f3f6";
}

.icon-ion-ios-cart-outline:before {
	content: "\f3f7";
}

.icon-ion-ios-cart:before {
	content: "\f3f8";
}

.icon-ion-ios-chatboxes-outline:before {
	content: "\f3f9";
}

.icon-ion-ios-chatboxes:before {
	content: "\f3fa";
}

.icon-ion-ios-chatbubble-outline:before {
	content: "\f3fb";
}

.icon-ion-ios-chatbubble:before {
	content: "\f3fc";
}

.icon-ion-ios-checkmark-empty:before {
	content: "\f3fd";
}

.icon-ion-ios-checkmark-outline:before {
	content: "\f3fe";
}

.icon-ion-ios-checkmark:before {
	content: "\f3ff";
}

.icon-ion-ios-circle-filled:before {
	content: "\f400";
}

.icon-ion-ios-circle-outline:before {
	content: "\f401";
}

.icon-ion-ios-clock-outline:before {
	content: "\f402";
}

.icon-ion-ios-clock:before {
	content: "\f403";
}

.icon-ion-ios-close-empty:before {
	content: "\f404";
}

.icon-ion-ios-close-outline:before {
	content: "\f405";
}

.icon-ion-ios-close:before {
	content: "\f406";
}

.icon-ion-ios-cloud-download-outline:before {
	content: "\f407";
}

.icon-ion-ios-cloud-download:before {
	content: "\f408";
}

.icon-ion-ios-cloud-outline:before {
	content: "\f409";
}

.icon-ion-ios-cloud-upload-outline:before {
	content: "\f40a";
}

.icon-ion-ios-cloud-upload:before {
	content: "\f40b";
}

.icon-ion-ios-cloud:before {
	content: "\f40c";
}

.icon-ion-ios-cloudy-night-outline:before {
	content: "\f40d";
}

.icon-ion-ios-cloudy-night:before {
	content: "\f40e";
}

.icon-ion-ios-cloudy-outline:before {
	content: "\f40f";
}

.icon-ion-ios-cloudy:before {
	content: "\f410";
}

.icon-ion-ios-cog-outline:before {
	content: "\f411";
}

.icon-ion-ios-cog:before {
	content: "\f412";
}

.icon-ion-ios-color-filter-outline:before {
	content: "\f413";
}

.icon-ion-ios-color-filter:before {
	content: "\f414";
}

.icon-ion-ios-color-wand-outline:before {
	content: "\f415";
}

.icon-ion-ios-color-wand:before {
	content: "\f416";
}

.icon-ion-ios-compose-outline:before {
	content: "\f417";
}

.icon-ion-ios-compose:before {
	content: "\f418";
}

.icon-ion-ios-contact-outline:before {
	content: "\f419";
}

.icon-ion-ios-contact:before {
	content: "\f41a";
}

.icon-ion-ios-copy-outline:before {
	content: "\f41b";
}

.icon-ion-ios-copy:before {
	content: "\f41c";
}

.icon-ion-ios-crop-strong:before {
	content: "\f41d";
}

.icon-ion-ios-crop:before {
	content: "\f41e";
}

.icon-ion-ios-download-outline:before {
	content: "\f41f";
}

.icon-ion-ios-download:before {
	content: "\f420";
}

.icon-ion-ios-drag:before {
	content: "\f421";
}

.icon-ion-ios-email-outline:before {
	content: "\f422";

}

.icon-ion-ios-email:before {
	content: "\f423";
}

.icon-ion-ios-eye-outline:before {
	content: "\f424";
}

.icon-ion-ios-eye:before {
	content: "\f425";
}

.icon-ion-ios-fastforward-outline:before {
	content: "\f426";
}

.icon-ion-ios-fastforward:before {
	content: "\f427";
}

.icon-ion-ios-filing-outline:before {
	content: "\f428";
}

.icon-ion-ios-filing:before {
	content: "\f429";
}

.icon-ion-ios-film-outline:before {
	content: "\f42a";
}

.icon-ion-ios-film:before {
	content: "\f42b";
}

.icon-ion-ios-flag-outline:before {
	content: "\f42c";
}

.icon-ion-ios-flag:before {
	content: "\f42d";
}

.icon-ion-ios-flame-outline:before {
	content: "\f42e";
}

.icon-ion-ios-flame:before {
	content: "\f42f";
}

.icon-ion-ios-flask-outline:before {
	content: "\f430";
}

.icon-ion-ios-flask:before {
	content: "\f431";
}

.icon-ion-ios-flower-outline:before {
	content: "\f432";
}

.icon-ion-ios-flower:before {
	content: "\f433";
}

.icon-ion-ios-folder-outline:before {
	content: "\f434";
}

.icon-ion-ios-folder:before {
	content: "\f435";
}

.icon-ion-ios-football-outline:before {
	content: "\f436";
}

.icon-ion-ios-football:before {
	content: "\f437";
}

.icon-ion-ios-game-controller-a-outline:before {
	content: "\f438";
}

.icon-ion-ios-game-controller-a:before {
	content: "\f439";
}

.icon-ion-ios-game-controller-b-outline:before {
	content: "\f43a";
}

.icon-ion-ios-game-controller-b:before {
	content: "\f43b";
}

.icon-ion-ios-gear-outline:before {
	content: "\f43c";
}

.icon-ion-ios-gear:before {
	content: "\f43d";
}

.icon-ion-ios-glasses-outline:before {
	content: "\f43e";
}

.icon-ion-ios-glasses:before {
	content: "\f43f";
}

.icon-ion-ios-grid-view-outline:before {
	content: "\f440";
}

.icon-ion-ios-grid-view:before {
	content: "\f441";
}

.icon-ion-ios-heart-outline:before {
	content: "\f442";
}

.icon-ion-ios-heart:before {
	content: "\f443";
}

.icon-ion-ios-help-empty:before {
	content: "\f444";
}

.icon-ion-ios-help-outline:before {
	content: "\f445";
}

.icon-ion-ios-help:before {
	content: "\f446";
}

.icon-ion-ios-home-outline:before {
	content: "\f447";
}

.icon-ion-ios-home:before {
	content: "\f448";
}

.icon-ion-ios-infinite-outline:before {
	content: "\f449";
}

.icon-ion-ios-infinite:before {
	content: "\f44a";
}

.icon-ion-ios-information-empty:before {
	content: "\f44b";
}

.icon-ion-ios-information-outline:before {
	content: "\f44c";
}

.icon-ion-ios-information:before {
	content: "\f44d";
}

.icon-ion-ios-ionic-outline:before {
	content: "\f44e";
}

.icon-ion-ios-keypad-outline:before {
	content: "\f44f";
}

.icon-ion-ios-keypad:before {
	content: "\f450";
}

.icon-ion-ios-lightbulb-outline:before {
	content: "\f451";
}

.icon-ion-ios-lightbulb:before {
	content: "\f452";
}

.icon-ion-ios-list-outline:before {
	content: "\f453";
}

.icon-ion-ios-list:before {
	content: "\f454";
}

.icon-ion-ios-location-outline:before {
	content: "\f455";
}

.icon-ion-ios-location:before {
	content: "\f456";
}

.icon-ion-ios-locked-outline:before {
	content: "\f457";
}

.icon-ion-ios-locked:before {
	content: "\f458";
}

.icon-ion-ios-loop-strong:before {
	content: "\f459";
}

.icon-ion-ios-loop:before {
	content: "\f45a";
}

.icon-ion-ios-medical-outline:before {
	content: "\f45b";
}

.icon-ion-ios-medical:before {
	content: "\f45c";
}

.icon-ion-ios-medkit-outline:before {
	content: "\f45d";
}

.icon-ion-ios-medkit:before {
	content: "\f45e";
}

.icon-ion-ios-mic-off:before {
	content: "\f45f";
}

.icon-ion-ios-mic-outline:before {
	content: "\f460";
}

.icon-ion-ios-mic:before {
	content: "\f461";
}

.icon-ion-ios-minus-empty:before {
	content: "\f462";
}

.icon-ion-ios-minus-outline:before {
	content: "\f463";
}

.icon-ion-ios-minus:before {
	content: "\f464";
}

.icon-ion-ios-monitor-outline:before {
	content: "\f465";
}

.icon-ion-ios-monitor:before {
	content: "\f466";
}

.icon-ion-ios-moon-outline:before {
	content: "\f467";
}

.icon-ion-ios-moon:before {
	content: "\f468";
}

.icon-ion-ios-more-outline:before {
	content: "\f469";
}

.icon-ion-ios-more:before {
	content: "\f46a";
}

.icon-ion-ios-musical-note:before {
	content: "\f46b";
}

.icon-ion-ios-musical-notes:before {
	content: "\f46c";
}

.icon-ion-ios-navigate-outline:before {
	content: "\f46d";
}

.icon-ion-ios-navigate:before {
	content: "\f46e";
}

.icon-ion-ios-nutrition-outline:before {
	content: "\f46f";
}

.icon-ion-ios-nutrition:before {
	content: "\f470";
}

.icon-ion-ios-paper-outline:before {
	content: "\f471";
}

.icon-ion-ios-paper:before {
	content: "\f472";
}

.icon-ion-ios-paperplane-outline:before {
	content: "\f473";
}

.icon-ion-ios-paperplane:before {
	content: "\f474";
}

.icon-ion-ios-partlysunny-outline:before {
	content: "\f475";
}

.icon-ion-ios-partlysunny:before {
	content: "\f476";
}

.icon-ion-ios-pause-outline:before {
	content: "\f477";
}

.icon-ion-ios-pause:before {
	content: "\f478";
}

.icon-ion-ios-paw-outline:before {
	content: "\f479";
}

.icon-ion-ios-paw:before {
	content: "\f47a";
}

.icon-ion-ios-people-outline:before {
	content: "\f47b";
}

.icon-ion-ios-people:before {
	content: "\f47c";
}

.icon-ion-ios-person-outline:before {
	content: "\f47d";
}

.icon-ion-ios-person:before {
	content: "\f47e";
}

.icon-ion-ios-personadd-outline:before {
	content: "\f47f";
}

.icon-ion-ios-personadd:before {
	content: "\f480";
}

.icon-ion-ios-photos-outline:before {
	content: "\f481";
}

.icon-ion-ios-photos:before {
	content: "\f482";
}

.icon-ion-ios-pie-outline:before {
	content: "\f483";
}

.icon-ion-ios-pie:before {
	content: "\f484";
}

.icon-ion-ios-pint-outline:before {
	content: "\f485";
}

.icon-ion-ios-pint:before {
	content: "\f486";
}

.icon-ion-ios-play-outline:before {
	content: "\f487";
}

.icon-ion-ios-play:before {
	content: "\f488";
}

.icon-ion-ios-plus-empty:before {
	content: "\f489";
}

.icon-ion-ios-plus-outline:before {
	content: "\f48a";
}

.icon-ion-ios-plus:before {
	content: "\f48b";
}

.icon-ion-ios-pricetag-outline:before {
	content: "\f48c";
}

.icon-ion-ios-pricetag:before {
	content: "\f48d";
}

.icon-ion-ios-pricetags-outline:before {
	content: "\f48e";
}

.icon-ion-ios-pricetags:before {
	content: "\f48f";
}

.icon-ion-ios-printer-outline:before {
	content: "\f490";
}

.icon-ion-ios-printer:before {
	content: "\f491";
}

.icon-ion-ios-pulse-strong:before {
	content: "\f492";
}

.icon-ion-ios-pulse:before {
	content: "\f493";
}

.icon-ion-ios-rainy-outline:before {
	content: "\f494";
}

.icon-ion-ios-rainy:before {
	content: "\f495";
}

.icon-ion-ios-recording-outline:before {
	content: "\f496";
}

.icon-ion-ios-recording:before {
	content: "\f497";
}

.icon-ion-ios-redo-outline:before {
	content: "\f498";
}

.icon-ion-ios-redo:before {
	content: "\f499";
}

.icon-ion-ios-refresh-empty:before {
	content: "\f49a";
}

.icon-ion-ios-refresh-outline:before {
	content: "\f49b";
}

.icon-ion-ios-refresh:before {
	content: "\f49c";
}

.icon-ion-ios-reload:before {
	content: "\f49d";
}

.icon-ion-ios-reverse-camera-outline:before {
	content: "\f49e";
}

.icon-ion-ios-reverse-camera:before {
	content: "\f49f";
}

.icon-ion-ios-rewind-outline:before {
	content: "\f4a0";
}

.icon-ion-ios-rewind:before {
	content: "\f4a1";
}

.icon-ion-ios-rose-outline:before {
	content: "\f4a2";
}

.icon-ion-ios-rose:before {
	content: "\f4a3";
}

.icon-ion-ios-search-strong:before {
	content: "\f4a4";
}

.icon-ion-ios-search:before {
	content: "\f4a5";
}

.icon-ion-ios-settings-strong:before {
	content: "\f4a6";
}

.icon-ion-ios-settings:before {
	content: "\f4a7";
}

.icon-ion-ios-shuffle-strong:before {
	content: "\f4a8";
}

.icon-ion-ios-shuffle:before {
	content: "\f4a9";
}

.icon-ion-ios-skipbackward-outline:before {
	content: "\f4aa";
}

.icon-ion-ios-skipbackward:before {
	content: "\f4ab";
}

.icon-ion-ios-skipforward-outline:before {
	content: "\f4ac";
}

.icon-ion-ios-skipforward:before {
	content: "\f4ad";
}

.icon-ion-ios-snowy:before {
	content: "\f4ae";
}

.icon-ion-ios-speedometer-outline:before {
	content: "\f4af";
}

.icon-ion-ios-speedometer:before {
	content: "\f4b0";
}

.icon-ion-ios-star-half:before {
	content: "\f4b1";
}

.icon-ion-ios-star-outline:before {
	content: "\f4b2";
}

.icon-ion-ios-star:before {
	content: "\f4b3";
}

.icon-ion-ios-stopwatch-outline:before {
	content: "\f4b4";
}

.icon-ion-ios-stopwatch:before {
	content: "\f4b5";
}

.icon-ion-ios-sunny-outline:before {
	content: "\f4b6";
}

.icon-ion-ios-sunny:before {
	content: "\f4b7";
}

.icon-ion-ios-telephone-outline:before {
	content: "\f4b8";
}

.icon-ion-ios-telephone:before {
	content: "\f4b9";
}

.icon-ion-ios-tennisball-outline:before {
	content: "\f4ba";
}

.icon-ion-ios-tennisball:before {
	content: "\f4bb";
}

.icon-ion-ios-thunderstorm-outline:before {
	content: "\f4bc";
}

.icon-ion-ios-thunderstorm:before {
	content: "\f4bd";
}

.icon-ion-ios-time-outline:before {
	content: "\f4be";
}

.icon-ion-ios-time:before {
	content: "\f4bf";
}

.icon-ion-ios-timer-outline:before {
	content: "\f4c0";
}

.icon-ion-ios-timer:before {
	content: "\f4c1";
}

.icon-ion-ios-toggle-outline:before {
	content: "\f4c2";
}

.icon-ion-ios-toggle:before {
	content: "\f4c3";
}

.icon-ion-ios-trash-outline:before {
	content: "\f4c4";
}

.icon-ion-ios-trash:before {
	content: "\f4c5";
}

.icon-ion-ios-undo-outline:before {
	content: "\f4c6";
}

.icon-ion-ios-undo:before {
	content: "\f4c7";
}

.icon-ion-ios-unlocked-outline:before {
	content: "\f4c8";
}

.icon-ion-ios-unlocked:before {
	content: "\f4c9";
}

.icon-ion-ios-upload-outline:before {
	content: "\f4ca";
}

.icon-ion-ios-upload:before {
	content: "\f4cb";
}

.icon-ion-ios-videocam-outline:before {
	content: "\f4cc";
}

.icon-ion-ios-videocam:before {
	content: "\f4cd";
}

.icon-ion-ios-volume-high:before {
	content: "\f4ce";
}

.icon-ion-ios-volume-low:before {
	content: "\f4cf";
}

.icon-ion-ios-wineglass-outline:before {
	content: "\f4d0";
}

.icon-ion-ios-wineglass:before {
	content: "\f4d1";
}

.icon-ion-ios-world-outline:before {
	content: "\f4d2";
}

.icon-ion-ios-world:before {
	content: "\f4d3";
}

.icon-ion-ipad:before {
	content: "\f1f9";
}

.icon-ion-iphone:before {
	content: "\f1fa";
}

.icon-ion-ipod:before {
	content: "\f1fb";
}

.icon-ion-jet:before {
	content: "\f295";
}

.icon-ion-key:before {
	content: "\f296";
}

.icon-ion-knife:before {
	content: "\f297";
}

.icon-ion-laptop:before {
	content: "\f1fc";
}

.icon-ion-leaf:before {
	content: "\f1fd";
}

.icon-ion-levels:before {
	content: "\f298";
}

.icon-ion-lightbulb:before {
	content: "\f299";
}

.icon-ion-link:before {
	content: "\f1fe";
}

.icon-ion-load-a:before {
	content: "\f29a";
}

.icon-ion-load-b:before {
	content: "\f29b";
}

.icon-ion-load-c:before {
	content: "\f29c";
}

.icon-ion-load-d:before {
	content: "\f29d";
}

.icon-ion-location:before {
	content: "\f1ff";
}

.icon-ion-lock-combination:before {
	content: "\f4d4";
}

.icon-ion-locked:before {
	content: "\f200";
}

.icon-ion-log-in:before {
	content: "\f29e";
}

.icon-ion-log-out:before {
	content: "\f29f";
}

.icon-ion-loop:before {
	content: "\f201";
}

.icon-ion-magnet:before {
	content: "\f2a0";
}

.icon-ion-male:before {
	content: "\f2a1";
}

.icon-ion-man:before {
	content: "\f202";
}

.icon-ion-map:before {
	content: "\f203";
}

.icon-ion-medkit:before {
	content: "\f2a2";
}

.icon-ion-merge:before {
	content: "\f33f";
}

.icon-ion-mic-a:before {
	content: "\f204";
}

.icon-ion-mic-b:before {
	content: "\f205";
}

.icon-ion-mic-c:before {
	content: "\f206";
}

.icon-ion-minus-circled:before {
	content: "\f207";
}

.icon-ion-minus-round:before {
	content: "\f208";
}

.icon-ion-minus:before {
	content: "\f209";
}

.icon-ion-model-s:before {
	content: "\f2c1";
}

.icon-ion-monitor:before {
	content: "\f20a";
}

.icon-ion-more:before {
	content: "\f20b";
}

.icon-ion-mouse:before {
	content: "\f340";
}

.icon-ion-music-note:before {
	content: "\f20c";
}

.icon-ion-navicon-round:before {
	content: "\f20d";
}

.icon-ion-navicon:before {
	content: "\f20e";
}

.icon-ion-navigate:before {
	content: "\f2a3";
}

.icon-ion-network:before {
	content: "\f341";
}

.icon-ion-no-smoking:before {
	content: "\f2c2";
}

.icon-ion-nuclear:before {
	content: "\f2a4";
}

.icon-ion-outlet:before {
	content: "\f342";
}

.icon-ion-paintbrush:before {
	content: "\f4d5";
}

.icon-ion-paintbucket:before {
	content: "\f4d6";
}

.icon-ion-paper-airplane:before {
	content: "\f2c3";
}

.icon-ion-paperclip:before {
	content: "\f20f";
}

.icon-ion-pause:before {
	content: "\f210";
	color: white;
}

.icon-ion-person-add:before {
	content: "\f211";
}

.icon-ion-person-stalker:before {
	content: "\f212";
}

.icon-ion-person:before {
	content: "\f213";
}

.icon-ion-pie-graph:before {
	content: "\f2a5";
}

.icon-ion-pin:before {
	content: "\f2a6";
}

.icon-ion-pinpoint:before {
	content: "\f2a7";
}

.icon-ion-pizza:before {
	content: "\f2a8";
}

.icon-ion-plane:before {
	content: "\f214";
}

.icon-ion-planet:before {
	content: "\f343";
}

.icon-ion-play:before {
	content: "\f215";
}

.icon-ion-playstation:before {
	content: "\f30a";
}

.icon-ion-plus-circled:before {
	content: "\f216";
}

.icon-ion-plus-round:before {
	content: "\f217";
}

.icon-ion-plus:before {
	content: "\f218";
}

.icon-ion-podium:before {
	content: "\f344";
}

.icon-ion-pound:before {
	content: "\f219";
}

.icon-ion-power:before {
	content: "\f2a9";
}

.icon-ion-pricetag:before {
	content: "\f2aa";
}

.icon-ion-pricetags:before {
	content: "\f2ab";
}

.icon-ion-printer:before {
	content: "\f21a";
}

.icon-ion-pull-request:before {
	content: "\f345";
}

.icon-ion-qr-scanner:before {
	content: "\f346";
}

.icon-ion-quote:before {
	content: "\f347";
}

.icon-ion-radio-waves:before {
	content: "\f2ac";
}

.icon-ion-record:before {
	content: "\f21b";
}

.icon-ion-refresh:before {
	content: "\f21c";
}

.icon-ion-reply-all:before {
	content: "\f21d";
}

.icon-ion-reply:before {
	content: "\f21e";
}

.icon-ion-ribbon-a:before {
	content: "\f348";
}

.icon-ion-ribbon-b:before {
	content: "\f349";
}

.icon-ion-sad-outline:before {
	content: "\f4d7";
}

.icon-ion-sad:before {
	content: "\f34a";
}

.icon-ion-scissors:before {
	content: "\f34b";
}

.icon-ion-search:before {
	content: "\f21f";
}

.icon-ion-settings:before {
	content: "\f2ad";
}

.icon-ion-share:before {
	content: "\f220";
}

.icon-ion-shuffle:before {
	content: "\f221";
}

.icon-ion-skip-backward:before {
	content: "\f222";
}

.icon-ion-skip-forward:before {
	content: "\f223";
}

.icon-ion-social-android-outline:before {
	content: "\f224";
}

.icon-ion-social-android:before {
	content: "\f225";
}

.icon-ion-social-angular-outline:before {
	content: "\f4d8";
}

.icon-ion-social-angular:before {
	content: "\f4d9";
}

.icon-ion-social-apple-outline:before {
	content: "\f226";
}

.icon-ion-social-apple:before {
	content: "\f227";
}

.icon-ion-social-bitcoin-outline:before {
	content: "\f2ae";
}

.icon-ion-social-bitcoin:before {
	content: "\f2af";
}

.icon-ion-social-buffer-outline:before {
	content: "\f228";
}

.icon-ion-social-buffer:before {
	content: "\f229";
}

.icon-ion-social-chrome-outline:before {
	content: "\f4da";
}

.icon-ion-social-chrome:before {
	content: "\f4db";
}

.icon-ion-social-codepen-outline:before {
	content: "\f4dc";
}

.icon-ion-social-codepen:before {
	content: "\f4dd";
}

.icon-ion-social-css3-outline:before {
	content: "\f4de";
}

.icon-ion-social-css3:before {
	content: "\f4df";
}

.icon-ion-social-designernews-outline:before {
	content: "\f22a";
}

.icon-ion-social-designernews:before {
	content: "\f22b";
}

.icon-ion-social-dribbble-outline:before {
	content: "\f22c";
}

.icon-ion-social-dribbble:before {
	content: "\f22d";
}

.icon-ion-social-dropbox-outline:before {
	content: "\f22e";
}

.icon-ion-social-dropbox:before {
	content: "\f22f";
}

.icon-ion-social-euro-outline:before {
	content: "\f4e0";
}

.icon-ion-social-euro:before {
	content: "\f4e1";
}

.icon-ion-social-facebook-outline:before {
	content: "\f230";
}

.icon-ion-social-facebook:before {
	content: "\f231";
}

.icon-ion-social-foursquare-outline:before {
	content: "\f34c";
}

.icon-ion-social-foursquare:before {
	content: "\f34d";
}

.icon-ion-social-freebsd-devil:before {
	content: "\f2c4";
}

.icon-ion-social-github-outline:before {
	content: "\f232";
}

.icon-ion-social-github:before {
	content: "\f233";
}

.icon-ion-social-google-outline:before {
	content: "\f34e";
}

.icon-ion-social-google:before {
	content: "\f34f";
}

.icon-ion-social-googleplus-outline:before {
	content: "\f234";
}

.icon-ion-social-googleplus:before {
	content: "\f235";
}

.icon-ion-social-hackernews-outline:before {
	content: "\f236";
}

.icon-ion-social-hackernews:before {
	content: "\f237";
}

.icon-ion-social-html5-outline:before {
	content: "\f4e2";
}

.icon-ion-social-html5:before {
	content: "\f4e3";
}

.icon-ion-social-instagram-outline:before {
	content: "\f350";
}

.icon-ion-social-instagram:before {
	content: "\f351";
}

.icon-ion-social-javascript-outline:before {
	content: "\f4e4";
}

.icon-ion-social-javascript:before {
	content: "\f4e5";
}

.icon-ion-social-linkedin-outline:before {
	content: "\f238";
}

.icon-ion-social-linkedin:before {
	content: "\f239";
}

.icon-ion-social-markdown:before {
	content: "\f4e6";
}

.icon-ion-social-nodejs:before {
	content: "\f4e7";
}

.icon-ion-social-octocat:before {
	content: "\f4e8";
}

.icon-ion-social-pinterest-outline:before {
	content: "\f2b0";
}

.icon-ion-social-pinterest:before {
	content: "\f2b1";
}

.icon-ion-social-python:before {
	content: "\f4e9";
}

.icon-ion-social-reddit-outline:before {
	content: "\f23a";
}

.icon-ion-social-reddit:before {
	content: "\f23b";
}

.icon-ion-social-rss-outline:before {
	content: "\f23c";
}

.icon-ion-social-rss:before {
	content: "\f23d";
}

.icon-ion-social-sass:before {
	content: "\f4ea";
}

.icon-ion-social-skype-outline:before {
	content: "\f23e";
}

.icon-ion-social-skype:before {
	content: "\f23f";
}

.icon-ion-social-snapchat-outline:before {
	content: "\f4eb";
}

.icon-ion-social-snapchat:before {
	content: "\f4ec";
}

.icon-ion-social-tumblr-outline:before {
	content: "\f240";
}

.icon-ion-social-tumblr:before {
	content: "\f241";
}

.icon-ion-social-tux:before {
	content: "\f2c5";
}

.icon-ion-social-twitch-outline:before {
	content: "\f4ed";
}

.icon-ion-social-twitch:before {
	content: "\f4ee";
}

.icon-ion-social-twitter-outline:before {
	content: "\f242";
}

.icon-ion-social-twitter:before {
	content: "\f243";
}

.icon-ion-social-usd-outline:before {
	content: "\f352";
}

.icon-ion-social-usd:before {
	content: "\f353";
}

.icon-ion-social-vimeo-outline:before {
	content: "\f244";
}

.icon-ion-social-vimeo:before {
	content: "\f245";
}

.icon-ion-social-whatsapp-outline:before {
	content: "\f4ef";
}

.icon-ion-social-whatsapp:before {
	content: "\f4f0";
}

.icon-ion-social-windows-outline:before {
	content: "\f246";
}

.icon-ion-social-windows:before {
	content: "\f247";
}

.icon-ion-social-wordpress-outline:before {
	content: "\f248";
}

.icon-ion-social-wordpress:before {
	content: "\f249";
}

.icon-ion-social-yahoo-outline:before {
	content: "\f24a";
}

.icon-ion-social-yahoo:before {
	content: "\f24b";
}

.icon-ion-social-yen-outline:before {
	content: "\f4f1";
}

.icon-ion-social-yen:before {
	content: "\f4f2";
}

.icon-ion-social-youtube-outline:before {
	content: "\f24c";
}

.icon-ion-social-youtube:before {
	content: "\f24d";
}

.icon-ion-soup-can-outline:before {
	content: "\f4f3";
}

.icon-ion-soup-can:before {
	content: "\f4f4";
}

.icon-ion-speakerphone:before {
	content: "\f2b2";
}

.icon-ion-speedometer:before {
	content: "\f2b3";
}

.icon-ion-spoon:before {
	content: "\f2b4";
}

.icon-ion-star:before {
	content: "\f24e";
}

.icon-ion-stats-bars:before {
	content: "\f2b5";
}

.icon-ion-steam:before {
	content: "\f30b";
}

.icon-ion-stop:before {
	content: "\f24f";
}

.icon-ion-thermometer:before {
	content: "\f2b6";
}

.icon-ion-thumbsdown:before {
	content: "\f250";
}

.icon-ion-thumbsup:before {
	content: "\f251";
}

.icon-ion-toggle-filled:before {
	content: "\f354";
}

.icon-ion-toggle:before {
	content: "\f355";
}

.icon-ion-transgender:before {
	content: "\f4f5";
}

.icon-ion-trash-a:before {
	content: "\f252";
}

.icon-ion-trash-b:before {
	content: "\f253";
}

.icon-ion-trophy:before {
	content: "\f356";
}

.icon-ion-tshirt-outline:before {
	content: "\f4f6";
}

.icon-ion-tshirt:before {
	content: "\f4f7";
}

.icon-ion-umbrella:before {
	content: "\f2b7";
}

.icon-ion-university:before {
	content: "\f357";
}

.icon-ion-unlocked:before {
	content: "\f254";
}

.icon-ion-upload:before {
	content: "\f255";
}

.icon-ion-usb:before {
	content: "\f2b8";
}

.icon-ion-videocamera:before {
	content: "\f256";
}

.icon-ion-volume-high:before {
	content: "\f257";
}

.icon-ion-volume-low:before {
	content: "\f258";
}

.icon-ion-volume-medium:before {
	content: "\f259";
}

.icon-ion-volume-mute:before {
	content: "\f25a";
}

.icon-ion-wand:before {
	content: "\f358";
}

.icon-ion-waterdrop:before {
	content: "\f25b";
}

.icon-ion-wifi:before {
	content: "\f25c";
}

.icon-ion-wineglass:before {
	content: "\f2b9";
}

.icon-ion-woman:before {
	content: "\f25d";
}

.icon-ion-wrench:before {
	content: "\f2ba";
}

.icon-ion-xbox:before {
	content: "\f30c";
}
