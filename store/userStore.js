// userStore.js

import cacheManager from "@/utils/CacheManager";
import appServerApi from '@/api/appServerApi'

let userStore = {
    debug: true,
    state: {
        // 用户相关状态
        userInfo: null, // 当前用户信息
        isLoggedIn: false, // 登录状态
        token: '', // 用户token
        userId: '', // 用户ID
    },

    // 用户相关方法
    /**
     * 获取用户信息
     * 先从缓存获取，没有则调用接口并缓存
     */
    async getUserInfo() {
        try {
            // 先从缓存获取
            const cachedUserInfo = cacheManager.getItem('userInfo')
            if (cachedUserInfo && cachedUserInfo.userId) {
                console.log('从缓存获取用户信息成功 ', cachedUserInfo)
                this.state.userInfo = cachedUserInfo
                return cachedUserInfo
            }

            // 缓存没有，调用接口获取
            console.log('缓存中无用户信息，从接口获取')
            const result = await appServerApi.getUserInfo()
            if (result) {
                console.log('从接口获取用户信息成功:', result)
                // 接口获取成功，存入缓存
                cacheManager.setItem('userInfo', result)
                this.state.userInfo = result
                console.log('接口获取用户信息成功，已缓存')
                return result
            }
        } catch (error) {
            console.error('获取用户信息失败:', error)
            // throw error
        }
    },

    /**
     * 注销用户信息
     */
    async cancelAccount() {
        try {
            const result = await appServerApi.cancelAccount()
            console.log('注销用户信息成功 result:', result)
            // 清除用户状态
            this.state.userInfo = null
            this.state.isLoggedIn = false
            this.state.token = ''
            // 清除缓存
            cacheManager.clear()
            return result
        } catch (error) {
            console.error('注销用户信息失败:', error)
            throw error
        }
    },

    /**
     * 完善用户信息
     * @param {Object} userInfo 用户信息对象
     */
    async completeUserInfo(userInfo) {
        try {
            // 更新成功后更新缓存和状态
            const result = await appServerApi.completeUserInfo(userInfo)
            if (result) {
                cacheManager.setItem('userInfo', userInfo)
                this.state.userInfo = userInfo
                return true
            }
            return false
        } catch (error) {
            console.error('更新用户信息失败:', error)
            throw error
        }
    },

    /**
     * 更新用户状态
     * @param {Object} userInfo 用户信息
     */
    updateUserState(userInfo) {
        this.state.userInfo = userInfo
        this.state.isLoggedIn = true
        this.state.userId = userInfo.userId
        cacheManager.setItem('userInfo', userInfo)
    },

    /**
     * 清除用户状态
     */
    clearUserState() {
        this.state.userInfo = null
        this.state.isLoggedIn = false
        this.state.token = ''
        this.state.userId = ''
        cacheManager.clear()
    },

    /**
     * 使用密码登录
     * @param {String} mobile 手机号
     * @param {String} password 密码
     * @returns {Promise} 登录结果Promise
     */
    async loginWithPassword(mobile, password) {
        try {
            const result = await appServerApi.loginWithPassword(mobile, password)
            if (result) {
                // 保存登录信息
                cacheManager.setItem('userId', result.userId)
                cacheManager.setItem('token', result.access_token)
                cacheManager.setItem('im_token', result.im_token)

                // 更新状态
                this.state.isLoggedIn = true
                this.state.userId = result.userId
                this.state.token = result.access_token

                // 获取用户信息
                await this.getUserInfo()
                return result
            }
            throw new Error('登录失败')
        } catch (error) {
            console.error('密码登录失败:', error)
            throw error
        }
    },

    /**
     * 使用验证码登录
     * @param {String} mobile 手机号
     * @param {String} authCode 验证码
     * @returns {Promise} 登录结果Promise
     */
    async loginWithAuthCode(mobile, authCode) {
        try {
            const result = await appServerApi.loginWithAuthCode(mobile, authCode)
            if (result) {
                // 保存登录信息
                cacheManager.setItem('userId', result.userId)
                cacheManager.setItem('token', result.access_token)
                cacheManager.setItem('im_token', result.im_token)

                // 更新状态
                this.state.isLoggedIn = true
                this.state.userId = result.userId
                this.state.token = result.access_token

                // 获取用户信息
                await this.getUserInfo()
                return result
            }
            throw new Error('登录失败')
        } catch (error) {
            console.error('验证码登录失败:', error)
            throw error
        }
    },

    /**
     * 一键登录
     * @param {String} authCode 认证码
     * @returns {Promise} 登录结果Promise
     */
    async loginByaliSDK(authCode) {
        try {
            const result = await appServerApi.loginByaliSDK(authCode)
            if (result) {
                // 保存登录信息
                cacheManager.setItem('userId', result.userId)
                cacheManager.setItem('token', result.access_token)
                cacheManager.setItem('im_token', result.im_token)

                // 更新状态
                this.state.isLoggedIn = true
                this.state.userId = result.userId
                this.state.token = result.access_token

                // 获取用户信息
                await this.getUserInfo()
                return result
            }
            // throw new Error('登录失败')
        } catch (error) {
            console.error('一键登录失败:', error)
            // throw error
        }
    },

    /**
     * 修改密码-旧密码方式
     * @param {String} oldPassword 旧密码
     * @param {String} newPassword 新密码
     * @returns {Promise} 修改结果Promise
     */
    async changePassword(oldPassword, newPassword) {
        try {
            return await appServerApi.changePassword(oldPassword, newPassword)
        } catch (error) {
            console.error('修改密码失败:', error)
            throw error
        }
    },

    /**
     * 请求重置密码验证码
     * @param {String} mobile 手机号
     * @returns {Promise} 发送结果Promise
     */
    async requestResetPasswordAuthCode(mobile) {
        try {
            return await appServerApi.requestResetPasswordAuthCode(mobile)
        } catch (error) {
            console.error('请求重置密码验证码失败:', error)
            throw error
        }
    },

    /**
     * 重置密码
     * @param {String} mobile 手机号
     * @param {String} resetPasswordAuthCode 验证码
     * @param {String} newPassword 新密码
     * @returns {Promise} 重置结果Promise
     */
    async resetPassword(mobile, resetPasswordAuthCode, newPassword) {
        try {
            return await appServerApi.resetPassword(mobile, resetPasswordAuthCode, newPassword)
        } catch (error) {
            console.error('重置密码失败:', error)
            throw error
        }
    },

    /**
     * 获取指定用户的信息
     * @param {String} userId 用户ID
     * @returns {Promise} 用户信息Promise
     */
    async getOtherUserInfo(userId) {
        try {
            return await appServerApi.getOtherUserInfo(userId)
        } catch (error) {
            console.error('获取用户信息失败:', error)
            throw error
        }
    },

    /**
     * 获取用户的获赞/关注/粉丝数统计
     * @param {String} userId 用户ID
     * @returns {Promise} 统计数据Promise
     */
    async getUserStatistics(userId) {
        try {
            return await appServerApi.getUserStatistics(userId)
        } catch (error) {
            console.error('获取用户统计数据失败:', error)
            throw error
        }
    },

    /**
     * 获取用户的吧贴数统计
     * @param {String} userId 用户ID
     * @returns {Promise} 吧贴数Promise
     */
    async getUserPostCount(userId) {
        try {
            return await appServerApi.getUserPostCount(userId)
        } catch (error) {
            console.error('获取用户吧贴数失败:', error)
            throw error
        }
    },

    /**
     * 关注/取关用户
     * @param {String} targetId 对方用户ID
     * @returns {Promise} 操作结果Promise
     */
    async toggleUserFollow(targetId) {
        try {
            return await appServerApi.toggleUserFollow(targetId)
        } catch (error) {
            console.error('关注/取关用户失败:', error)
            throw error
        }
    },

    /**
     * 检查是否关注用户
     * @param {String} targetId 目标用户ID
     * @returns {Promise} 是否关注Promise
     */
    async getCheckUserFollow(targetId) {
        try {
            return await appServerApi.getCheckUserFollow(targetId)
        } catch (error) {
            console.error('检查是否关注用户失败:', error)
            throw error
        }
    },

    /**
     * 设置朋友圈背景图片
     * @param {String} cover 背景图片URL
     * @returns {Promise} 设置结果Promise
     */
    async setMomentCover(cover) {
        try {
            const result = await appServerApi.setMomentCover(cover)
            if (result) {
                // 更新用户状态中的cover
                if (this.state.userInfo) {
                    this.state.userInfo.cover = cover
                    // 更新缓存
                    cacheManager.setItem('userInfo', this.state.userInfo)
                }
                return result
            }
            return false
        } catch (error) {
            console.error('设置朋友圈背景失败:', error)
            throw error
        }
    },

}

export default userStore;
