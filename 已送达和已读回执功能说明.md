已送达和已读回执是两个非常难的功能，难度不在于是实现有多难，而是对性能的影响实在是太大了。业内对于回执有两种做法，一种是对每一条消息的送达和阅读都做一次回执给发送者。这样当在一个1000人的群中，发送者发送一条消息，这条消息分发给其它的999个群成员，群成员每人收到就会单独给发送者发送一条回执，这样就是999条已送达回执发送和接收，同理已读也是一样，需要999条已阅读回执的发送和接收。所以网络交互的次数从 1（消息发送） + 999（消息接收）= 1000次 变成了 1（消息发送） + 999（消息接收） + 999（送达回执发送） + 999（送达回执接收） + 999（已读回执发送） + 999（已读回执接收）=4996次。不但是对网络交互有着很大的影响，对于本地数据也是一样有非常大的压力，需要把所有信息都存储起来，存储数据量也打了好多倍

一个改进的办法就是只记录最后的时间点，对于送达回执，只记录某个用户最后的接收消息的时间；对于已阅读回执，只记录某个用户在某个会话内的最后阅读时间。因为大多数消息并不是实时接收的，都是离线接收的，这样当用户上线后，无论接收到多少条消息，都只会发送一条已接收回执；进入到一个会话中时，无论会话中有多少新消息，也只会发送一个已阅读回执。另外数据存储量也会极大地减少。

这种做法缺点是，只能知道用户最后一次接收的时间点和最后一次阅读的时间点，不能记录每条消息的送达时间和阅读时间。这也是对性能问题的妥协。如果您非常需要这个功能，可以在野火IM服务配置文件中关掉回执功能，自己来实现，实现难度不是问题。

在客户端上的使用方法就是，在一个会话中，已送达和已读的消息状态跟未开启回执的状态是不变的。SDK提供了两个函数getMessageDelivery和getConversationRead，参数都是会话，返回的值是当前会话的每个人最后接收消息的时间还有阅读会话的时间，这样当显示自己发送的消息时，根据发送消息的时间和这两个值进行比较，就可以知道目标用户是否已经收下来了和是否已经阅读。
