/build
*/build/
*/node_modules/
/target/
*/package-lock.json
/parser/target/
/benchmarking/target/
.idea/
*.iml
logs/
broker/target/
client/target/
parser_commons/target/
netty_parser/target/
netty_parser/target/
parser_commons/target/
distribution/target/
broker/moquette.log
broker/runner
/bundle/target/
/bundle/runner/
/osgi_test/target/
/perf/target/
/mapdb_storage/target/
*~
*.log*
.DS_Store
.settings/
.project
.classpath
/*/bin/
*.swp

*.class

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.war
*.ear

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*
broker/*moquette_store.*
embedding_moquette/target/
broker/nb-configuration.xml
broker/nbactions-Server.xml

maven-metadata.xml
/.gradle/
/bin/
broker/config/git.properties
distribution/src/main/resources/git.properties
license/target
server/target
broker/h2db
broker/media
common/target
server/target
sdk/target
moments/target
monitor
wildfire_jmeter
git.properties
wildfirechat.license
broker/logs
