package com.wildfire.websocket.client.service;

import com.wildfire.websocket.client.handler.WebSocketClientHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.WebSocketConnectionManager;

import javax.annotation.PostConstruct;
import java.net.URI;
import java.net.URISyntaxException;

/**
 * WebSocket客户端服务
 * 负责管理WebSocket连接
 */
@Slf4j
@Service
public class WebSocketClientService {

    @Value("${websocket.server.url}")
    private String webSocketUrl;

    private final WebSocketClient webSocketClient;
    private final WebSocketClientHandler webSocketHandler;
    private WebSocketConnectionManager connectionManager;

    @Autowired
    public WebSocketClientService(WebSocketClient webSocketClient, WebSocketClientHandler webSocketHandler) {
        this.webSocketClient = webSocketClient;
        this.webSocketHandler = webSocketHandler;
    }

    /**
     * 初始化WebSocket连接
     */
    @PostConstruct
    public void init() {
        connect();
    }

    /**
     * 连接到WebSocket服务器
     */
    public void connect() {
        try {
            URI uri = new URI(webSocketUrl);
            connectionManager = new WebSocketConnectionManager(
                    webSocketClient,
                    webSocketHandler,
                    uri.toString()
            );
            connectionManager.start();
            log.info("WebSocket客户端已启动，连接到: {}", webSocketUrl);
        } catch (URISyntaxException e) {
            log.error("WebSocket URL格式错误: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("WebSocket连接失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 断开WebSocket连接
     */
    public void disconnect() {
        if (connectionManager != null) {
            connectionManager.stop();
            log.info("WebSocket客户端已断开连接");
        }
    }

    /**
     * 重新连接WebSocket
     */
    public void reconnect() {
        disconnect();
        connect();
    }

    /**
     * 发送文本消息
     * @param message 要发送的文本消息
     * @return 是否发送成功
     */
    public boolean sendMessage(String message) {
        return webSocketHandler.sendMessage(message);
    }
    
    /**
     * 发送二进制消息
     * @param data 要发送的二进制数据
     * @return 是否发送成功
     */
    public boolean sendBinaryMessage(byte[] data) {
        return webSocketHandler.sendBinaryMessage(data);
    }

    /**
     * 定时检查连接状态，如果断开则重连
     * 每30秒执行一次
     */
    @Scheduled(fixedRate = 30000)
    public void checkConnection() {
        if (webSocketHandler.getCurrentSession() == null ||
            !webSocketHandler.getCurrentSession().isOpen()) {
            log.info("检测到WebSocket连接已断开，尝试重新连接...");
            reconnect();
        }
    }
}
