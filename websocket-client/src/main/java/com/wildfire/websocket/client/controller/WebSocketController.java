package com.wildfire.websocket.client.controller;

import com.wildfire.websocket.client.model.BinaryMessage;
import com.wildfire.websocket.client.service.WebSocketClientService;
import com.wildfire.websocket.client.utils.AES;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket控制器
 * 提供REST API来管理WebSocket连接和发送消息
 */
@Slf4j
@RestController
@RequestMapping("/api/websocket")
public class WebSocketController {

    private final WebSocketClientService webSocketClientService;

    @Autowired
    public WebSocketController(WebSocketClientService webSocketClientService) {
        this.webSocketClientService = webSocketClientService;
    }

    /**
     * 发送消息
     * @param message 消息内容
     * @return 发送结果
     */
    @PostMapping("/send-text")
    public ResponseEntity<Map<String, Object>> sendMessage(@RequestBody Map<String, String> message) {
        Map<String, Object> response = new HashMap<>();

        if (!message.containsKey("content")) {
            response.put("success", false);
            response.put("message", "消息内容不能为空");
            return ResponseEntity.badRequest().body(response);
        }

        String content = message.get("content");
        boolean sent = webSocketClientService.sendMessage(content);

        response.put("success", sent);
        if (sent) {
            response.put("message", "消息发送成功");
        } else {
            response.put("message", "消息发送失败，WebSocket可能未连接");
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 重新连接WebSocket
     * @return 重连结果
     */
    @PostMapping("/reconnect")
    public ResponseEntity<Map<String, Object>> reconnect() {
        Map<String, Object> response = new HashMap<>();

        try {
            webSocketClientService.reconnect();
            response.put("success", true);
            response.put("message", "WebSocket重连已触发");
        } catch (Exception e) {
            log.error("WebSocket重连失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "WebSocket重连失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 断开WebSocket连接
     * @return 断开结果
     */
    @PostMapping("/disconnect")
    public ResponseEntity<Map<String, Object>> disconnect() {
        Map<String, Object> response = new HashMap<>();

        try {
            webSocketClientService.disconnect();
            response.put("success", true);
            response.put("message", "WebSocket连接已断开");
        } catch (Exception e) {
            log.error("WebSocket断开失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "WebSocket断开失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 连接WebSocket
     * @return 连接结果
     */
    @PostMapping("/connect")
    public ResponseEntity<Map<String, Object>> connect() {
        Map<String, Object> response = new HashMap<>();

        try {
            webSocketClientService.connect();
            response.put("success", true);
            response.put("message", "WebSocket连接已启动");
        } catch (Exception e) {
            log.error("WebSocket连接失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "WebSocket连接失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }

        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送二进制消息
     * @param message 包含Base64编码的二进制数据
     * @return 发送结果
     */
    @PostMapping("/send")
    public ResponseEntity<Map<String, Object>> sendBinaryMessage(@RequestBody Map<String, String> message) {
        Map<String, Object> response = new HashMap<>();

        if (!message.containsKey("binaryData")) {
            response.put("success", false);
            response.put("message", "二进制数据不能为空");
            return ResponseEntity.badRequest().body(response);
        }

        try {
            String base64Data = message.get("binaryData");
            byte[] binaryData = Base64.getDecoder().decode(base64Data);
            
            boolean sent = webSocketClientService.sendBinaryMessage(binaryData);

            response.put("success", sent);
            if (sent) {
                response.put("message", "二进制消息发送成功");
                response.put("bytesSent", binaryData.length);
            } else {
                response.put("message", "二进制消息发送失败，WebSocket可能未连接");
            }
        } catch (IllegalArgumentException e) {
            log.error("Base64解码失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "Base64解码失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("发送二进制消息失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "发送二进制消息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }

        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送MQTT格式的二进制消息
     * @param message 包含消息类型、QoS、消息ID和有效载荷的请求
     * @return 发送结果
     */
    @PostMapping("/send-structured")
    public ResponseEntity<Map<String, Object>> sendStructuredBinaryMessage(@RequestBody Map<String, Object> message) {
        Map<String, Object> response = new HashMap<>();

        // 验证必要的参数
        if (!message.containsKey("messageType") || !message.containsKey("payload")) {
            response.put("success", false);
            response.put("message", "消息类型和有效载荷不能为空");
            return ResponseEntity.badRequest().body(response);
        }

        try {
            // 解析参数
            byte messageType = ((Number) message.get("messageType")).byteValue();
            boolean dup = message.containsKey("dup") ? (Boolean) message.get("dup") : false;
            byte qosLevel = message.containsKey("qos") ? ((Number) message.get("qos")).byteValue() : 0;
            boolean retain = message.containsKey("retain") ? (Boolean) message.get("retain") : false;
            short messageId = message.containsKey("messageId") ? ((Number) message.get("messageId")).shortValue() : 0;
            
            // 解码Base64有效载荷
            String base64Payload = (String) message.get("payload");
            byte[] payload = Base64.getDecoder().decode(base64Payload);
            
            // 创建并编码MQTT格式的二进制消息
            BinaryMessage binaryMessage = new BinaryMessage(messageType, dup, qosLevel, retain, messageId, payload);
            byte[] encodedMessage = binaryMessage.encode();
            
            // 发送二进制消息
            boolean sent = webSocketClientService.sendBinaryMessage(encodedMessage);

            response.put("success", sent);
            if (sent) {
                response.put("message", "MQTT格式二进制消息发送成功");
                response.put("messageType", messageType);
                response.put("messageId", messageId);
                response.put("bytesSent", encodedMessage.length);
            } else {
                response.put("message", "MQTT格式二进制消息发送失败，WebSocket可能未连接");
            }
        } catch (ClassCastException e) {
            log.error("参数类型错误: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "参数类型错误: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (IllegalArgumentException e) {
            log.error("Base64解码失败或参数无效: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "Base64解码失败或参数无效: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("发送MQTT格式二进制消息失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "发送MQTT格式二进制消息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }

        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送MQTT PUBLISH消息
     * @param message 包含主题名称、QoS、消息ID和有效载荷的请求
     * @return 发送结果
     */
    @PostMapping("/send-publish")
    public ResponseEntity<Map<String, Object>> sendPublishMessage(@RequestBody Map<String, Object> message) {
        Map<String, Object> response = new HashMap<>();

        // 验证必要的参数
        if (!message.containsKey("topic") || !message.containsKey("payload")) {
            response.put("success", false);
            response.put("message", "主题名称和有效载荷不能为空");
            return ResponseEntity.badRequest().body(response);
        }

        try {
            // 解析参数
            String topicName = (String) message.get("topic");
            boolean dup = message.containsKey("dup") ? (Boolean) message.get("dup") : false;
            byte qosLevel = message.containsKey("qos") ? ((Number) message.get("qos")).byteValue() : 0;
            boolean retain = message.containsKey("retain") ? (Boolean) message.get("retain") : false;
            short messageId = message.containsKey("messageId") ? ((Number) message.get("messageId")).shortValue() : 0;
            
            // 解码Base64有效载荷
            String base64Payload = (String) message.get("payload");
            byte[] payload = Base64.getDecoder().decode(base64Payload);
            
            // 创建并编码MQTT PUBLISH消息
            byte[] encodedMessage = BinaryMessage.encodePublish(topicName, payload, messageId, qosLevel, dup, retain);
            
            // 发送二进制消息
            boolean sent = webSocketClientService.sendBinaryMessage(encodedMessage);

            response.put("success", sent);
            if (sent) {
                response.put("message", "MQTT PUBLISH消息发送成功");
                response.put("topic", topicName);
                response.put("qos", qosLevel);
                if (qosLevel > 0) {
                    response.put("messageId", messageId);
                }
                response.put("bytesSent", encodedMessage.length);
            } else {
                response.put("message", "MQTT PUBLISH消息发送失败，WebSocket可能未连接");
            }
        } catch (ClassCastException e) {
            log.error("参数类型错误: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "参数类型错误: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (IllegalArgumentException e) {
            log.error("Base64解码失败或参数无效: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "Base64解码失败或参数无效: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("发送MQTT PUBLISH消息失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "发送MQTT PUBLISH消息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }

        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送加密的MQTT PUBLISH消息
     * @param message 包含主题名称、QoS、消息ID、有效载荷和密钥的请求
     * @return 发送结果
     */
    @PostMapping("/send-encrypted-publish")
    public ResponseEntity<Map<String, Object>> sendEncryptedPublishMessage(@RequestBody Map<String, Object> message) {
        Map<String, Object> response = new HashMap<>();

        // 验证必要的参数
        if (!message.containsKey("topic") || !message.containsKey("payload")) {
            response.put("success", false);
            response.put("message", "主题名称和有效载荷不能为空");
            return ResponseEntity.badRequest().body(response);
        }

        try {
            // 解析参数
            String topicName = (String) message.get("topic");
            boolean dup = message.containsKey("dup") ? (Boolean) message.get("dup") : false;
            byte qosLevel = message.containsKey("qos") ? ((Number) message.get("qos")).byteValue() : 0;
            boolean retain = message.containsKey("retain") ? (Boolean) message.get("retain") : false;
            short messageId = message.containsKey("messageId") ? ((Number) message.get("messageId")).shortValue() : 0;
            String secret = message.containsKey("secret") ? (String) message.get("secret") : null;
            boolean useAes256 = message.containsKey("useAes256") ? (Boolean) message.get("useAes256") : false;
            
            // 解码Base64有效载荷
            String base64Payload = (String) message.get("payload");

            Base64.Encoder encoder = Base64.getEncoder();
            byte[] originalBytes = base64Payload.getBytes();
            String encodedString = encoder.encodeToString(originalBytes);

            byte[] payload = Base64.getDecoder().decode(encodedString);
            
            // 设置AES加密模式（AES-128或AES-256）
            AES.useAes256(useAes256);
            
            // 使用AES加密有效载荷
            String key = null;
            byte[] encryptedPayload = AES.AESEncrypt(payload, key);
            
            if (encryptedPayload == null) {
                response.put("success", false);
                response.put("message", "AES加密失败");
                return ResponseEntity.internalServerError().body(response);
            }
            
            // 创建并编码MQTT PUBLISH消息，使用加密后的有效载荷
            byte[] encodedMessage = BinaryMessage.encodePublish(topicName, encryptedPayload, messageId, qosLevel, dup, retain);
            
            // 发送二进制消息
            boolean sent = webSocketClientService.sendBinaryMessage(encodedMessage);

            response.put("success", sent);
            if (sent) {
                response.put("message", "加密的MQTT PUBLISH消息发送成功");
                response.put("topic", topicName);
                response.put("qos", qosLevel);
                if (qosLevel > 0) {
                    response.put("messageId", messageId);
                }
                response.put("bytesSent", encodedMessage.length);
                response.put("encrypted", true);
                response.put("encryptionMode", useAes256 ? "AES-256" : "AES-128");
            } else {
                response.put("message", "加密的MQTT PUBLISH消息发送失败，WebSocket可能未连接");
            }
        } catch (ClassCastException e) {
            log.error("参数类型错误: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "参数类型错误: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (IllegalArgumentException e) {
            log.error("Base64解码失败或参数无效: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "Base64解码失败或参数无效: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("发送加密的MQTT PUBLISH消息失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "发送加密的MQTT PUBLISH消息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }

        return ResponseEntity.ok(response);
    }
}
