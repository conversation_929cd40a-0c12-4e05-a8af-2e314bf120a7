// 后端天气 API
import store from "../store";
import Config from "../config";
import { getItem } from "../pages/util/storageHelper";

export default {
  /**
   * 获取当前位置天气信息
   * @returns {Promise<{temp: string, text: string, city: string}>} 温度、天气描述和城市名
   */
  async getWeatherInfo() {
    try {
      // 获取位置信息
      const location = await store._getLocation()
      console.log('获取地理位置详情', {
        latitude: location.latitude,
        longitude: location.longitude,
        完整位置信息: location
      })
      
      // 构建后端天气请求URL
      const weatherUrl = `${Config.APP_SERVER}/weather?longitude=${location.longitude}&latitude=${location.latitude}`
      console.log('请求后端天气API：', weatherUrl)
      console.log('请求参数详情：', {
        longitude: location.longitude,
        latitude: location.latitude,
        类型: typeof location.longitude,
        纬度类型: typeof location.latitude
      })
      
      // 请求天气信息
      const weatherResponse = await this.request(weatherUrl)
      console.log('天气API响应数据：', weatherResponse)
      
      if (weatherResponse.code === 200 && weatherResponse.data) {
        const weatherData = {
          temp: weatherResponse.data.temp, // 温度
          text: weatherResponse.data.text, // 天气描述
          city: weatherResponse.data.city // 城市名称
        }
        
        console.log('解析后的天气数据：', weatherData)
        console.log('城市名称：', weatherResponse.data.city)
        
        return weatherData
      } else {
        console.log('天气API响应格式不正确：', weatherResponse)
        throw new Error('获取天气信息失败')
      }
    } catch (error) {
      console.error('获取天气信息失败:', error)
      throw error
    }
  },

  /**
   * 发起请求
   * @param {string} url 请求地址
   * @returns {Promise<any>}
   */
  request(url) {
    const token = getItem('token')
    return new Promise((resolve, reject) => {
      uni.request({
        url,
        method: 'GET',
        header: {
          'content-type': 'application/json',
          'Authorization': 'Bearer ' + token,
        },
        success: res => {
          console.log('天气API原始响应：', res)
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(new Error(`请求失败: ${res.statusCode}`))
          }
        },
        fail: err => {
          console.error('请求失败:', err)
          reject(err)
        }
      })
    })
  }
} 