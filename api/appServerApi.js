import Config from "../config";
import FavItem from "../wfc/model/favItem";
import {
    stringValue
} from "../wfc/util/longUtil";
import AppServerError from "./appServerError";
import wfc from "../wfc/client/wfc";
import {
    getItem,
    setItem
} from "../pages/util/storageHelper";
import cacheManager from "../utils/CacheManager";

export class AppServerApi {
    constructor() {
    }
    requestAuthCode(mobile) {
        console.log(`requestAuthCode: mobile = ${mobile}`);
        return this.sendCode(mobile);
    }

    loginWithPassword(mobile, password) {
        console.log(`loginWithPassword: mobile = ${mobile}, password = ${password}`);
        return new Promise((resolve, reject) => {
            let responsePromise = this.loginWithPasswordPost('/auth/phone', {
                phone: mobile,
                password,
                platform: Config.getWFCPlatform(),
                clientId: wfc.getClientId()
            }, true);
            this._interceptLoginResponse(responsePromise, resolve, reject);
        });
    }

    loginWithAuthCode(mobile, authCode) {
        console.log(`loginWithAuthCode: mobile = ${mobile}, authCode = ${authCode}`);
        return new Promise((resolve, reject) => {
            console.log('loginWithAuthCode-=-------   ');
            let responsePromise = this.loginWithAuthCodePost('/auth/msg', {
                phone: mobile,
                code: authCode,
                platform: Config.getWFCPlatform(),
                clientId: wfc.getClientId()
            }, true);
            this._interceptLoginResponse(responsePromise, resolve, reject);
        });
    }
    //一键登录
    loginByaliSDK(authCode) {
        return new Promise((resolve, reject) => {
            console.log('loginByaliSDK-=-------   ');
            let responsePromise = this.loginWithAuthCodePost('/auth/code', {
                authenticationCode: authCode,
                platform: Config.getWFCPlatform(),
                clientId: wfc.getClientId()
            }, true);
            this._interceptLoginResponse(responsePromise, resolve, reject)
        })
    }

    /**
     * 修改密码-旧密码方式
     * @param {Object} oldPassword
     * @param {Object} newPassword
     */
    changePassword(oldPassword, newPassword) {
        console.log(`changePassword: oldPassword = ${oldPassword}, newPassword = ${newPassword}`);
        return this._post('/up/resetByOldPwd', {
            oldPassword,
            newPassword
        });
    }

    /**
     * 修改密码-短信验证码方式
     */
    requestResetPasswordAuthCode(mobile) {
        console.log(`requestResetPasswordAuthCode: mobile = ${mobile}`);
        return this.sendCode(mobile);
    }

    /**
     * 修改密码-短信验证码方式
     */
    resetPassword(mobile, resetPasswordAuthCode, newPassword) {
        console.log(`resetPassword: mobile = ${mobile}, resetPasswordAuthCode = ${resetPasswordAuthCode}, newPassword = ${newPassword}`);
        return this._post('/up/resetByCode', {
            mobile: mobile,
            resetCode: resetPasswordAuthCode,
            newPassword: newPassword,
        });
    }

    getGroupAnnouncement(groupId) {
        console.log(`getGroupAnnouncement: groupId = ${groupId}`);
        return this._post('/get_group_announcement', {
            groupId: groupId
        });
    }

    updateGroupAnnouncement(author, groupId, announcement) {
        console.log(`updateGroupAnnouncement: author = ${author}, groupId = ${groupId}, announcement = ${announcement}`);
        return this._post('/put_group_announcement', {
            author,
            groupId,
            text: announcement
        });
    }

    /**
     * 获取用户信息
     * 先从缓存获取，没有则调用接口并缓存
     */
    async getUserInfo() {
        try {
            // 先从缓存获取
            const cachedUserInfo = getItem('userInfo')
            if (cachedUserInfo && cachedUserInfo.earthId && cachedUserInfo.userId && cachedUserInfo.cover) {
                console.log('从缓存获取用户信息成功 ', cachedUserInfo)
                return cachedUserInfo
            }

            // 缓存没有，调用接口获取
            console.log('缓存中无用户信息，从接口获取')
            const result = await this._get('/user/info')
            if (result && result.data && result.data.data) {
                console.log('从接口获取用户信息成功 result.data.data', result.data.data)
                // 接口获取成功，存入缓存
                setItem('userInfo', result.data.data)
                console.log('result.data.data接口获取用户信息成功，已缓存')
                return result.data.data
            }
            if (result && result.data) {
                console.log('从接口获取用户信息成功 result.data:', result.data)
                // 接口获取成功，存入缓存
                setItem('userInfo', result.data)
                console.log('result.data接口获取用户信息成功，已缓存')
                return result.data
            }
            throw new Error('获取用户信息失败')
        } catch (error) {
            console.error('获取用户信息失败:', error)
            throw error
        }
    }
    /**
     * 注销用户信息
     */
    cancelAccount() {
        try {
            const result = this._get('/user/info/cancel')
            console.log('注销用户信息成功 result:', result)
            return result
        } catch (error) {
            console.error('注销用户信息失败:', error)
            throw error
        }
    }

    /**
     * 完善用户信息
     * @param {Object} userInfo 用户信息对象
     */
    async completeUserInfo(userInfo) {
        try {
            // 更新成功后更新缓存
            setItem('userInfo', userInfo)
            await this._post('/user/info/complete', userInfo)
            return true
        } catch (error) {
            console.error('更新用户信息失败:', error)
            throw error
        }
    }

    /**
     * 获取
     * 礼物信息
     */
    async getGiftList() {
        try {
            const result = await this._get('/gift/list')
            if (result && result.data) {
                setItem('gift_list', result.data)
                // console.log('gift_list 更新')
                return result.data
            }
        } catch (error) {
            console.error('获取礼物信息失败:', error)
            throw error
        }
    }
    /**
     * 查询某用户评分
     * @param {string} userId - 用户ID
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    getUserScore(userId) {
        console.log(`getUserCreatedBars: userId = ${userId}`);
        return this._get(`/user/comment/score?userId=${userId}`);
    }
    /**
     * 查询用户点评列表
     * @param {string} pageNo - 页码
     * @param {string} pageSize - 每页大小
     * @param {string} userId - 用户uid
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    getUserCommentList(params) {
        return this._get('/user/comment/user/' + params.userId, params);
    }
    /**
     * 查询所有点评回复
     * @param {string} pageNo - 页码
     * @param {string} pageSize - 每页大小
     * @param {string} commentId - 点评id
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    getAllCommentsSub(pageNo, pageSize, commentId) {
        console.log(`getAllCommentsSub: pageNo = ${pageNo}, pageSize = ${pageSize}, commentId = ${commentId}`);
        return this._get('/user/comment/sub', {
            pageNo,
            pageSize,
            commentId
        });
    }

    /**
     * 点评点赞请求函数
     * @param {string} topicId - 主题ID
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    likeComment(data) {
        console.log(`likeComment: topicId = ${data}`);
        return this._post('/user/comment/like',
            data, false, false, "form"
        );
    }

    /**
     * 点评回复
     * @param {string} parentId - 主题ID
     * @param {string} content - 内容
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    replyComment({parentId, content}) {
        console.log(`replyComment: parentId = ${parentId}, content = ${content}`);
        return this._post('/user/comment/sub', {
            parentId,
            content
        });
    }

    /**
     * 发表评价
     * @param {string} targetId - 被评人uid
     * @param {string} content - 内容
     * @param {number} baseScore - 基础分
     * @param {number} value1 - 感知层
     * @param {number} value2 - 角色层
     * @param {number} value3 - 资源层
     * @param {number} value4 - 能力层
     * @param {number} value5 - 内核层
     * @param {number} anonymous- 是否匿名
     * @param {Array} url - 网址数组
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    postUserComment(params) {
        console.log(`postUserComment: `, params);
        return this._post('/user/comment', params);
    }

    /**
     * 创建新群组
     * @param {Object} groupInfo 群组信息对象
     * @param {string} groupInfo.gid 群组ID（必填）
     * @param {string} groupInfo.name 群组名称（必填）
     * @returns {Promise} 返回创建群组的请求Promise
     */
    createGroup(groupInfo) {
        console.log(`createGroup: groupInfo = ${JSON.stringify(groupInfo)}`);
        return this._post('/group/new', groupInfo);
    }

    // 设置进群付费金额
    setGroupPrice(data) {
        return this._post('/group/price', data, false, false, "form");
    }

    // 付费进群
    joinPayGroup(data) {
        return this._get('/group/pay', data);
    }

    /**
     * 修改群组名称
     * @param {Object} groupInfo 群组信息对象
     * @param {string} groupInfo.gid 群组ID（必填）
     * @param {string} groupInfo.name 新的群组名称（必填）
     * @returns {Promise} 返回修改群组名称的请求Promise
     */
    updateGroupName(groupInfo) {
        console.log(`updateGroupName: groupInfo = ${JSON.stringify(groupInfo)}`);
        return this._post('/group/editName', groupInfo);
    }

    /**
     * 修改群组名称
     * @param {Object} groupInfo 群组信息对象
     * @param {string} groupInfo.gid 群组ID（必填）
     * @param {string} groupInfo.avatar 新的群组头像（必填）
     * @returns {Promise} 返回修改群组名称的请求Promise
     */
    updateGroupAvatar(groupInfo) {
        console.log(`updateGroupAvatar: groupInfo = ${JSON.stringify(groupInfo)}`);
        return this._post('/group/editAvatar', groupInfo);
    }

    /**
     * 修改群组介绍
     * @param {Object} groupInfo 群组信息对象
     * @param {string} groupInfo.gid 群组ID（必填）
     * @param {string} groupInfo.description 新的群组介绍（必填）
     * @returns {Promise} 返回修改群组介绍的请求Promise
     */
    updateGroupDescriotion(groupInfo) {
        console.log(`editDescriotion: groupInfo = ${JSON.stringify(groupInfo)}`);
        return this._post('/group/editDescriotion', groupInfo);
    }

    /**
     * 修改群组分类
     * @param {Object} groupInfo 群组信息对象
     * @param {string} groupInfo.gid 群组ID（必填）
     * @param {string} groupInfo.categoryId 新的群组分类ID（必填）
     * @returns {Promise} 返回修改群组分类的请求Promise
     */
    updateGroupCategory(groupInfo) {
        console.log(`updateGroupCategory: groupInfo = ${JSON.stringify(groupInfo)}`);
        return this._post('/group/editCategory', groupInfo);
    }

    /**
     * 解散群组
     * @param {Object} groupInfo 群组信息对象
     * @param {string} groupInfo.gid 需要解散的群组ID（必填）
     * @returns {Promise} 返回解散群组的请求Promise
     */
    disbandGroup(groupInfo) {
        console.log(`disbandGroup: groupInfo = ${JSON.stringify(groupInfo)}`);
        return this._post('/group/del', groupInfo);
    }

    /**
     * 查询所有群分类
     * @returns {Promise} 返回群分类列表的请求Promise
     */
    getAllGroupCategories() {
        console.log('getAllGroupCategories');
        return this._get('/group/category/list');
    }

    /**
     * 搜索群组
     * @param {Object} searchParams 搜索参数对象
     * @param {number} [searchParams.pageNo=1] 页码，默认为1
     * @param {number} [searchParams.pageSize=10] 每页数量，默认为10
     * @param {string} [searchParams.keyword=''] 搜索关键词，可选
     * @param {string} [searchParams.categoryId=''] 群组分类ID，可选
     * @returns {Promise} 返回搜索结果的请求Promise
     */
    searchGroups(searchParams = {}) {

        return this._get('/group/search', {
            ...searchParams
        });
    }

    /**
     * 获取群组详情
     * @param {Object} params 请求参数对象
     * @param {string} params.groupId 群组ID（必填）
     * @returns {Promise} 返回群组详情的请求Promise
     */
    getGroupDetail(id) {
        // 检查必填参数
        return this._get('/group/detail?groupId=' + id,);
    }

    /**
     * 查询所有吧分类
     * @returns {Promise} 返回吧分类列表的请求Promise
     */
    getAllBbsCategories() {
        console.log('getAllBbsCategories');
        return this._get('/bbs/category/list');
    }

    /**
     * 创建新吧
     * @param {Object} bbsInfo 吧信息对象
     * @param {string} bbsInfo.name 吧名称（必填）
     * @param {number} bbsInfo.categoryId 吧分类ID（必填）
     * @param {string} bbsInfo.avatar 吧头像URL（必填）
     * @returns {Promise} 返回创建吧的请求Promise
     */
    createBbs(bbsInfo) {
        // 参数校验
        if (!bbsInfo.name || !bbsInfo.categoryId || !bbsInfo.avatar) {
            return Promise.reject(new Error('name, categoryId, and avatar are required'));
        }
        console.log(`createBbs: bbsInfo = ${JSON.stringify(bbsInfo)}`);
        return this._post('/bbs/info/create', bbsInfo);
    }

    /**
     * 修改吧信息
     * @param {Object} bbsInfo 吧信息对象
     * @param {string} bbsInfo.name 吧名称
     * @param {number} bbsInfo.categoryId 吧分类ID
     * @param {string} bbsInfo.avatar 吧头像URL
     * @param {number} bbsInfo.bbsId 吧ID（必填）
     * @param {string} [bbsInfo.background] 吧背景
     * @param {string} [bbsInfo.rule] 吧规则
     * @param {string} [bbsInfo.description] 吧描述
     * @param {Array<string>} [bbsInfo.groups] 群组ID数组
     * @returns {Promise} 返回修改吧信息的请求Promise
     */
    updateBbs(bbsInfo) {
        // 参数校验
        if (!bbsInfo.bbsId) {
            return Promise.reject(new Error('bbsId is required'));
        }
        console.log(`updateBbs: bbsInfo = ${JSON.stringify(bbsInfo)}`);
        return this._post('/bbs/info/update', bbsInfo);
    }

    /**
     * 关注或已关注吧
     * @param {Object} followParams 关注参数对象
     * @param {string} followParams.id 吧ID（必填）
     * @param {number|string} followParams.follow 关注状态（必填）
     *        1 - 关注
     *        0 - 已关注
     * @returns {Promise} 返回关注/取关操作的请求Promise
     */
    toggleBbsFollow(followParams) {
        // 参数校验
        if (!followParams || !followParams.id ||
            (followParams.follow !== 1 && followParams.follow !== 0 &&
                followParams.follow !== '1' && followParams.follow !== '0')) {
            return Promise.reject(new Error('Invalid id or follow parameter'));
        }
        console.log(`toggleBbsFollow: followParams = ${JSON.stringify(followParams)}`);
        return this._post(`/bbs/user/follow?id=${followParams.id}&follow=${followParams.follow}`, {});
    }

    /**
     * 分页查询吧列表
     * @param {String} pageNo - 页码编号（必填）
     * @param {String} pageSize - 页面大小（必填）
     * @param {String} [categoryId] - 吧分类ID（选填）
     * @param {String} [keyword] - 关键词（选填）
     * @returns {Promise} - 返回一个 Promise，包含分页查询的结果
     * @example
     * queryBbsList({ pageNo: '1', pageSize: '10', categoryId: '123', keyword: 'example' })
     *   .then(response => console.log(response))
     *   .catch(error => console.error(error));
     */
    queryBbsList({
                     pageNo = 1,
                     pageSize = 10,
                     categoryId = '',
                     keyword = ''
                 }) {
        console.log(`queryBbsList: pageNo = ${pageNo} pageSize= ${pageSize} categoryId= ${categoryId} keyword=${keyword}`);
        return this._get(`/bbs/info/page?pageNo=${pageNo}&pageSize=${pageSize}&categoryId=${categoryId}&keyword=${keyword}`);
    }

    /**
     * 查询吧详情
     * @param {String} id - 吧ID（必填）
     * @returns {Promise} - 返回一个 Promise，包含吧详情的结果
     * @example
     * getBbsDetail('12345')
     *   .then(response => console.log(response))
     *   .catch(error => console.error(error));
     */
    getBbsDetail(id) {
        if (!id) {
            throw new Error('参数 "id" 为必填项');
        }
        console.log(`getBbsDetail: id = ${id}`);
        return this._get('/bbs/info/detail?id=' + id);
    }

    /**
     * 查询吧内帖子列表
     * @param {String} pageNo - 页码编号（必填）
     * @param {String} pageSize - 页面大小（必填）
     * @param {String} bbsId - 吧ID（必填）
     * @returns {Promise} - 返回一个 Promise，包含帖子列表的结果
     * @example
     * queryBbsTopicList({ pageNo: '1', pageSize: '10', bbsId: '2' })
     *   .then(response => console.log(response))
     *   .catch(error => console.error(error));
     */
    queryBbsTopicList({
                          pageNo,
                          pageSize,
                          bbsId
                      }) {
        if (!pageNo || !pageSize || !bbsId) {
            throw new Error('参数 "pageNo"、"pageSize" 和 "bbsId" 为必填项');
        }
        console.log(`queryBbsTopicList: pageNo = ${pageNo}, pageSize = ${pageSize}, bbsId = ${bbsId}`);
        return this._get('/bbs/topic/page?pageNo=' + pageNo + '&pageSize=' + pageSize + '&bbsId=' + bbsId);
    }

    /**
     * 帖子详情
     * @param {String} id - ID（必填）
     * @returns {Promise} - 返回一个 Promise，包含吧详情的结果
     * @example
     * getBbsDetail('12345')
     *   .then(response => console.log(response))
     *   .catch(error => console.error(error));
     */
    getPostsDetail(id) {
        return this._get('/bbs/topic/detail?topicId=' + id);
    }

    /**
     * 发帖请求函数
     * @param {string} title - 标题
     * @param {string} content - 内容
     * @param {string} moduleId - 模块ID
     * @param {string} bbsId - 吧ID
     * @param {Array} url - 网址数组
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    publishPost(title, content, moduleId, bbsId, url) {
        console.log(`publishPost: title = ${title}, content = ${content}, moduleId = ${moduleId}, bbsId = ${bbsId}, url = ${url}`);
        return this._post('/bbs/topic/publish', {
            title,
            content,
            moduleId,
            bbsId,
            url
        });
    }

    /**
     * 删除帖子
     * @param {string} topicId - 帖子ID
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    async deletePost(topicId) {
        try {
            console.log(`deletePost: topicId = ${topicId}`)
            const result = await this._post(`/bbs/topic/del`, {topicId: String(topicId)}, false, false, "form")
            return result
        } catch (error) {
            console.error('删除帖子失败:', error)
            throw error
        }
    }

    /**
     * 删除楼层
     * @param {string} floorId - 楼层ID
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    async deleteFloor(floorId) {
        try {
            console.log(`deleteFloor: floorId = ${floorId}`)
            const result = await this._post(`/bbs/floor/del`, {floorId: String(floorId)}, false, false, "form")
            return result
        } catch (error) {
            console.error('删除楼层失败:', error)
            throw error
        }
    }

    /**
     * 删除评论
     * @param {string} commentId - 回复ID
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    async deleteComment(commentId) {
        let path = '/moment/comment/del';
        let data = {
            commentId: String(commentId) // 确保将commentId转换为字符串类型
        };
        try {
            const result = await this._post(path, data, false, false, "form");
            
            // 检查是否有错误码
            if (result?.data?.code === 500) {
                const errorMsg = result?.data?.msg || '操作失败';
                console.error('删除评论失败:', errorMsg);
                
                // 抛出一个错误对象，包含服务器返回的错误消息
                throw new Error(errorMsg);
            }
            
            return result;
        } catch (e) {
            console.error('删除评论失败:', e);
            throw e;
        }
    }

    /**
     * 跟帖请求函数
     * @param {string} topicId - 主题ID
     * @param {string} content - 内容
     * @param {Array} url - URL地址数组
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    publishFollowUp({topicId, content, url}) {
        console.log(`publishFollowUp: topicId = ${topicId}, content = ${content}, url = ${url}`);
        return this._post('/bbs/floor/publish', {
            topicId,
            content,
            url
        });
    }

    /**
     * 发布评论请求函数
     * @param {string} content - 评论内容
     * @param {string} floorId - 楼层ID
     * @param {string} [toUserId] - 回复的用户ID（可选）
     * @param {Array} url - URL地址数组
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    publishComment({content, floorId, toUserId, url}) {
        console.log(`publishComment: content = ${content}, floorId = ${floorId}, toUserId = ${toUserId}, url = ${url}`);
        return this._post('/bbs/comment/publish', {
            content,
            floorId,
            toUserId,
            url
        });
    }

    /**
     * 分页查询楼中所有评论请求函数
     * @param {string} pageNo - 页码
     * @param {string} pageSize - 每页大小
     * @param {string} floorId - 楼层ID
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    getCommentsByPage(pageNo, pageSize, floorId) {
        console.log(`getCommentsByPage: pageNo = ${pageNo}, pageSize = ${pageSize}, floorId = ${floorId}`);
        return this._get('/bbs/comment/page', {
            pageNo,
            pageSize,
            floorId
        });
    }

    /**
     * 推荐话题请求函数
     * @param {string} pageNo - 页码
     * @param {string} pageSize - 每页大小
     * @param {string} categoryId - 分类ID
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    getRecommendedTopics(params) {
        return this._get('/bbs/topic/recommend', params);
    }

    /**
     * 查询所有跟帖请求函数
     * @param {string} pageNo - 页码
     * @param {string} pageSize - 每页大小
     * @param {string} topicId - 主题ID
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    getAllFollowUps(pageNo, pageSize, topicId) {
        console.log(`getAllFollowUps: pageNo = ${pageNo}, pageSize = ${pageSize}, topicId = ${topicId}`);
        return this._get('/bbs/floor/page', {
            pageNo,
            pageSize,
            topicId
        });
    }

    /**
     * 查询某用户的发帖请求函数
     * @param {string} pageNo - 页码
     * @param {string} pageSize - 每页大小
     * @param {string} userId - 用户ID
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    getUserPosts(userId, pageNo, pageSize,) {
        console.log(`getUserPosts: pageNo = ${pageNo}, pageSize = ${pageSize}, userId = ${userId}`);
        return this._get('/bbs/topic/user', {
            pageNo,
            pageSize,
            userId
        });
    }

    /**
     * 帖子点赞请求函数
     * @param {string} topicId - 主题ID
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    likeTopic(data) {
        console.log(`likeTopic: topicId = ${data}`);
        return this._post('/bbs/topic/like',
            data, true, true, "form"
        );
    }

    /**
     * 跟帖点赞请求函数
     * @param {string} floorId - 楼层ID
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    likeFloor(floorId) {
        console.log(`likeFloor: floorId = ${floorId}`);
        return this._post('/bbs/floor/like', {
            floorId
        }, false, false, "form");
    }

    /**
     * 查询某用户建的吧请求函数
     * @param {string} userId - 用户ID
     * @param {string} pageNo - 页码
     * @param {string} pageSize - 每页大小
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    getUserCreatedBars(userId, pageNo, pageSize) {
        console.log(`getUserCreatedBars: userId = ${userId}, pageNo = ${pageNo}, pageSize = ${pageSize}`);
        return this._get(`/bbs/info/created/${userId}`, {
            pageNo,
            pageSize
        });
    }

    /**
     * 查询某用户关注的吧请求函数
     * @param {string} userId - 用户ID
     * @param {string} pageNo - 页码
     * @param {string} pageSize - 每页大小
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    getUserFollowedBars(userId, pageNo, pageSize) {
        console.log(`getUserFollowedBars: userId = ${userId}, pageNo = ${pageNo}, pageSize = ${pageSize}`);
        return this._get(`/bbs/info/followed/${userId}`, {
            pageNo,
            pageSize
        });
    }

    /**
     * 查询所有吧请求函数
     * @param {string} pageNo - 页码
     * @param {string} pageSize - 每页大小
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    getAllBbsList(pageNo, pageSize) {
        console.log(`getAllBbsList: pageNo = ${pageNo}, pageSize = ${pageSize}`);
        return this._get('/bbs/info/page', {
            pageNo,
            pageSize
        });
    }

    /**
     * 创建子版块
     * @param {Object} moduleInfo - 子版块信息对象
     * @param {number} moduleInfo.bbsId - 吧ID（必填）
     * @param {string} moduleInfo.name - 子版块名称（必填）
     * @param {string} moduleInfo.icon - 子版块图标（必填）
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    createBbsModule(moduleInfo) {
        // 参数校验
        if (!moduleInfo || !moduleInfo.bbsId || !moduleInfo.name || !moduleInfo.icon) {
            return Promise.reject(new Error('bbsId, name, and icon are required'));
        }
        console.log(`createBbsModule: moduleInfo = ${JSON.stringify(moduleInfo)}`);
        return this._post('/bbs/module/create', moduleInfo);
    }

    /**
     * 更新子版块信息
     * @param {Object} moduleInfo - 子版块信息对象
     * @param {number} moduleInfo.bbsId - 吧ID（必填）
     * @param {string} moduleInfo.name - 子版块名称（必填）
     * @param {string} moduleInfo.icon - 子版块图标（必填）
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    updateBbsModule(moduleInfo) {
        // 参数校验
        if (!moduleInfo || !moduleInfo.bbsId || !moduleInfo.name || !moduleInfo.icon) {
            return Promise.reject(new Error('bbsId, name, and icon are required'));
        }
        console.log(`updateBbsModule: moduleInfo = ${JSON.stringify(moduleInfo)}`);
        
        // 使用正确的API路径 /bbs/module/edit
        return this._post('/bbs/module/edit', moduleInfo);
    }

    /**
     * 查询某个吧的所有子版块
     * @param {string|number} bbsId - 吧ID（必填）
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    getBbsModuleList(bbsId) {
        // 参数校验
        if (!bbsId) {
            return Promise.reject(new Error('bbsId is required'));
        }
        console.log(`getBbsModuleList: bbsId = ${bbsId}`);
        return this._get(`/bbs/module/list?bbsId=${bbsId}`);
    }

    /**
     * 文件上传请求函数
     * @param {File} file - 要上传的文件对象
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    uploadFile(file) {
        console.log(`uploadFile: file = ${file}`);
        const formData = new FormData();
        formData.append('file', file);
        return this._post('/ossfile/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    }

    uploadimgFile(res1, successCallback, errorCallback) {
        uni.showLoading({
            title: '文件上传中',
        });

        uni.uploadFile({
            url: Config.APP_SERVER + "/ossfile/upload",
            filePath: res1,
            name: "file",
            header: {
                'content-type': 'application/json', // 默认值
                'Authorization': 'Bearer ' + getItem('token'),
            },
            success: function (res) {
                uni.hideLoading();
                let data = res.data ? JSON.parse(res.data) : {};
                if (data.code == 200) {
                    successCallback(data);
                } else {
                    errorCallback && errorCallback(data);
                    uni.showToast({
                        title: data.msg,
                        icon: 'none',
                    });
                }
            },
            fail: function (res) {
                uni.hideLoading();
                throw new Error('request error: ' + res)
            },
        });
    }

    /**
     * 活动发布请求函数
     * @param {Object} activityData - 活动数据对象
     * @param {string} [activityData.cover] - 封面URL
     * @param {string} [activityData.title] - 标题
     * @param {string} [activityData.content] - 内容
     * @param {Array} [activityData.url] - 链接地址数组
     * @param {number} [activityData.categoryId] - 分类ID
     * @param {string} [activityData.startTime] - 开始时间
     * @param {string} [activityData.endTime] - 结束时间
     * @param {string} [activityData.address] - 地址
     * @param {Array} [activityData.priceList] - 价格列表
     * @param {string} [activityData.priceList.skuName] - SKU名称
     * @param {string} [activityData.priceList.sellCount] - 已报名人数
     * @param {string} [activityData.priceList.stock] - 库存数量
     * @param {string} [activityData.priceList.price] - 价格
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    createActivity(activityData) {
        console.log(`createActivity: activityData = ${JSON.stringify(activityData)}`);
        return this._post('/activity/create', activityData);
    }

    /**
     * 获取活动类型列表请求函数
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    getActivityCategories() {
        console.log('getActivityCategories');
        return this._get('/activity/category/list');
    }

    /**
     * 分页查询活动请求函数
     * @param {string} pageNo - 页码
     * @param {string} pageSize - 每页大小
     * @param {string} [categoryId] - 分类ID（可选）
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    getActivitiesByPage(pageNo, pageSize, categoryId) {
        console.log(`getActivitiesByPage: pageNo = ${pageNo}, pageSize = ${pageSize}, categoryId = ${categoryId}`);
        return this._get('/activity/page', {
            pageNo,
            pageSize,
            categoryId
        });
    }

    /**
     * 获取活动详情请求函数
     * @param {string} id - 活动ID
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    getActivityDetail(id) {
        console.log(`getActivityDetail: id = ${id}`);
        return this._get('/activity/detail', {
            id
        });
    }

    /**
     * 活动门票下单请求函数
     * @param {Array} items - 门票项目数组
     * @param {number} items.id - 票ID
     * @param {number} items.count - 数量
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    createActivityOrder(items) {
        console.log(`createActivityOrder: items = ${JSON.stringify(items)}`);
        return this._post('/activity/order/create', {
            items
        });
    }

    /**
     * 活动订单付款请求函数
     * @param {string} orderNo - 订单编号
     * @param {string} payType - 支付类型（1：支付宝，2：微信）
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    payActivityOrder(orderNo, payType) {
        console.log(`payActivityOrder: orderNo = ${orderNo}, payType = ${payType}`);
        return this._get('/activity/order/pay', {
            orderNo,
            payType
        });
    }

    /**
     * 获取资讯详情请求函数
     * @param {string} id - 资讯ID
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    getArticleDetail(id) {
        var detailId = id == null ? 1 : id;
        console.log("getArticleDetail: detailId = " + detailId);
        return this._get('/article/detail?id=' + detailId, {
            id: detailId
        });
    }

    favMessage(message) {
        let favItem = FavItem.fromMessage(message);
        console.log(`favMessage: message = ${JSON.stringify(message)}`);
        return this._post('/fav/add', {
            messageUid: stringValue(favItem.messageUid),
            type: favItem.favType,
            convType: favItem.conversation.type,
            convTarget: favItem.conversation.target,
            convLine: favItem.conversation.line,
            origin: favItem.origin,
            sender: favItem.sender,
            title: favItem.title,
            url: favItem.url,
            thumbUrl: favItem.thumbUrl,
            data: favItem.data,
        });
    }

    getFavList(startId, count = 20) {
        console.log(`getFavList: startId = ${startId}, count = ${count}`);
        return this._post('/fav/list', {
            id: startId,
            count: count
        }, false, true);
    }

    delFav(favItemId) {
        console.log(`delFav: favItemId = ${favItemId}`);
        return this._post('/fav/del/' + favItemId, '');
    }

    _interceptLoginResponse(responsePromise, resolve, reject) {
        responsePromise
            .then(response => {
                console.log('_interceptLoginResponse response---------', response);
                if (response.data.code === 200) {
                    // 获取返回数据
                    const responseData = response.data.data;

                    // 构造统一的返回格式
                    const result = {
                        userId: responseData.user_id,
                        access_token: responseData.access_token,
                        im_token: responseData.im_token
                    };

                    console.log('处理后的登录响应数据:', result);

                    // 检查必要字段是否存在
                    if (!result.userId || !result.access_token || !result.im_token) {
                        console.error('登录返回数据不完整:', responseData);
                        reject(new AppServerError(500, '登录返回数据不完整'));
                        return;
                    }

                    resolve(result);
                } else {
                    reject(new AppServerError(response.data.code, response.data.msg));
                }
            })
            .catch(err => {
                console.error('登录请求失败:', err);
                reject(err);
            });
    }

    // 附近的群
    fetchNearbyGroup(params = {}) {
        return this._get('/group/near?longitude=120&latitude=40&distance=10', params);
    }

    // 附近的人
    fetchNearbyPeople(params = {}) {
        return this._get('/user/info/near?longitude=120&latitude=40&distance=10', params);
    }

    /**
     *
     * @param path
     * @param data
     * @param rawResponse
     * @param rawResponseData
     * @return {Promise<string | Response<any>|*|T>}
     * @private
     */
    _get(path, params = {}, rawResponse = false, rawResponseData = false) {
        path = Config.APP_SERVER + path;
        const token = getItem('token')
        // console.log(`_get:path;token;params `, path, token, JSON.stringify(params));
        let p = new Promise((resolve, reject) => {
            uni.request({
                url: path,
                data: {
                    ...params,
                },
                header: {
                    'content-type': 'application/json', // 默认值
                    'Authorization': 'Bearer ' + token,
                },
                method: 'GET',

                success: (res) => {
                    console.log('_get result', path, JSON.stringify(params), res);
                    if (rawResponse) {
                        resolve(res);
                        return;
                    }
                    if (rawResponseData) {
                        resolve(res.data);
                        return
                    }
                    if (res.data.code === 200) {
                        resolve(res.data);
                    } else if (res.data.code === 401) {
                        uni.reLaunch({
                          url: '/pages/login/LoginPage'
                        })
                    } else {
                        throw new AppServerError(res.data.code, res.data.message)
                    }
                    // if (res.statusCode === 200) {
                    // } else {
                    //     throw new Error('request error, status code: ' + res.status)
                    // }
                },
                fail: (res) => {
                    console.log('fail', path, JSON.stringify(params), res);
                    throw new Error('request error: ' + res)
                }
            });
        });
        return p;
    }

    /**
     *
     * @param path
     * @param data
     * @param rawResponse
     * @param rawResponseData
     * @return {Promise<string | Response<any>|*|T>}
     * @private
     */
    _getX(path, params = {}, rawResponse = false, rawResponseData = false) {
        let response;
        path = Config.APP_SERVER + path;
        const token = getItem('token')
        console.log(`_get params`, params);
        let p = new Promise((resolve, reject) => {
            uni.request({
                url: path,
                header: {
                    'content-type': 'application/json', // 默认值
                    'Authorization': 'Bearer ' + token,
                },
                method: 'GET',
                data: {
                    ...params,
                },
                success: (res) => {
                    console.log('_get result', res);
                    if (rawResponse) {
                        resolve(res);
                        return;
                    }
                    if (rawResponseData) {
                        resolve(res.data);
                        return
                    }
                    if (res.data.code === 200) {
                        resolve(res.data);
                    } else {
                        throw new AppServerError(res.data.code, res.data.message)
                    }
                    // if (res.statusCode === 200) {
                    // } else {
                    //     throw new Error('request error, status code: ' + res.status)
                    // }
                },
                fail: (res) => {
                    console.log('fail', res);
                    throw new Error('request error: ' + res)
                }
            });
        });
        return p;
    }


    /**
     *
     * @param path
     * @param data
     * @param rawResponse
     * @param rawResponseData
     * @return {Promise<string | Response<any>|*|T>}
     * @private
     */
    _post(path, data = {}, rawResponse = false, rawResponseData = false, type = 'json') {
        path = Config.APP_SERVER + path;
        let p = new Promise((resolve, reject) => {
            console.log('_post', path, data);

            // 特殊处理 /group/member/identity/add 和 /group/member/identity/del 接口
            if (path.includes('/group/member/identity/add') || path.includes('/group/member/identity/del')) {
                console.log('处理成员身份API调用，检查参数:', JSON.stringify(data));

                // 确保 memberIds 是数组
                if (data.memberIds && !Array.isArray(data.memberIds)) {
                    console.error('memberIds 不是数组，正在转换:', data.memberIds);
                    // 尝试转换为数组
                    if (typeof data.memberIds === 'string') {
                        data.memberIds = [data.memberIds];
                    } else if (data.memberIds !== null && typeof data.memberIds === 'object') {
                        data.memberIds = Object.values(data.memberIds);
                    }
                    console.log('转换后的 memberIds:', data.memberIds);
                }

                // 检查转换后的结果
                if (!data.memberIds || !Array.isArray(data.memberIds) || data.memberIds.length === 0) {
                    console.error('memberIds 无效:', data.memberIds);
                    reject(new Error('memberIds参数无效，必须是非空数组'));
                    return p;
                }
            }

            uni.request({
                url: path,
                data: data,
                header: {
                    'Content-Type': type == 'json' ? 'application/json' : 'application/x-www-form-urlencoded', // 默认值
                    'Authorization': 'Bearer ' + getItem('token'),
                },
                method: 'POST',

                success: (res) => {
                    console.log('_post result', res);

                    // 针对成员身份接口的特殊处理
                    if (path.includes('/group/member/identity/add') || path.includes('/group/member/identity/del')) {
                        console.log('成员身份API响应详情:', JSON.stringify(res.data));
                    }

                    if (rawResponse) {
                        console.log('success rawResponse res.data  -------------', res
                            .data);
                        resolve(res);
                        return;
                    }
                    if (rawResponseData) {
                        console.log('success rawResponseData res.data  -------------', res
                            .data);
                        resolve(res.data);
                        return
                    }
                    if (res.data.code === 200) {
                        console.log('success res.data  -------------', res.data);
                        // 检查 res.data.data 是否存在且非 null
                        if (res.data.data !== undefined && res.data.data !== null) {
                            console.log('success res.data.data  -------------', res.data.data);
                            resolve(res.data.data);
                        } else {
                            console.log('success res.data (data is null/undefined)  -------------', res.data);
                            resolve(res.data); // 成功但 data 为空时，返回整个 res.data
                        }
                        return
                    } else if (res.data.code === 401) {
                        uni.reLaunch({
                          url: '/pages/login/LoginPage'
                        })
                    } else {
                        reject((res.data.msg))
                        throw new AppServerError(res.data.code, res.data.message)
                    }
                    // if (res.statusCode === 200) {
                    // } else {
                    //     throw new Error('request error, status code: ' + res.status)
                    // }
                },
                fail: (res) => {
                    console.log('fail', res);
                    reject(new Error('request error: ' + res));
                }
            });
        });
        return p;
    }

    /**
     * 发送短信验证码
     * @param {Object} phone
     */
    sendCode(phone) {
        let appServer = Config.APP_SERVER + '/auth/sms/send?phone=' + phone;
        let clientId = wfc.getClientId();
        console.log('sendCode', wfc.getClientId(), Config.getWFCPlatform());

        return new Promise((resolve, reject) => {
            uni.request({
                url: appServer,
                data: {},
                header: {
                    'content-type': 'application/json' // 默认值
                },
                method: 'GET',
                success: (res) => {
                    console.log('sendCode response:', res);
                    if (res.statusCode === 200) {
                        let codeResult = res.data;
                        if (codeResult.code === 200) {
                            console.log('sendCode success', codeResult);
                            resolve(codeResult);
                        } else {
                            console.log('sendCode failed', codeResult);
                            reject(new Error(codeResult.msg || '发送失败'));
                        }
                    } else {
                        reject(new Error('请求失败，状态码：' + res.statusCode));
                    }
                },
                fail: (err) => {
                    console.error('sendCode request failed:', err);
                    reject(new Error('网络请求失败'));
                }
            });
        });
    }

    /**
     * 使用验证码登录
     */
    loginWithAuthCodePost(path, data = {}, rawResponse = false, rawResponseData = false) {
        let response;
        path = Config.APP_SERVER + path;
        let p = new Promise((resolve, reject) => {
            console.log('login', path, data);
            uni.request({
                url: path,
                data: data,
                header: {
                    'content-type': 'application/json'
                },
                method: 'POST',

                success: (res) => {
                    console.log('login result', res);
                    console.log('success res.statusCode  -------------', res.statusCode);
                    console.log('success res.data  -------------', res.data);
                    if (res.statusCode === 200) {
                        if (rawResponse) {
                            console.log('success rawResponse res.data  -------------', res
                                .data);
                            console.log('success rawResponse res.data.data  -------------', res
                                .data.data);
                            resolve(res);
                            return;
                        }
                        if (rawResponseData) {
                            console.log('success rawResponseData res.data  -------------', res
                                .data);
                            console.log('success rawResponseData res.data.data  -------------',
                                res.data.data);
                            resolve(res.data);
                            return
                        }
                        if (res.data.code === 200) {
                            console.log('success res.data  -------------', res.data);
                            console.log('success res.data.data  -------------', res.data.data);
                            resolve(res.data.data);
                            return
                        } else {
                            throw new AppServerError(res.data.code, res.data.message)
                        }
                    } else {
                        throw new Error('request error, status code: ' + res.status)
                    }
                },
                fail: (res) => {
                    console.log('fail', res);
                    throw new Error('request error: ' + res)
                }
            });
        });
        return p;
    }


    /**
     *
     * 使用密码登录
     * @param path
     * @param data
     * @param rawResponse
     * @param rawResponseData
     * @return {Promise<string | Response<any>|*|T>}
     * @private
     */
    loinWithPasswordPost(path, data = {}, rawResponse = false, rawResponseData = false) {
        let response;
        path = Config.APP_SERVER + path;
        let p = new Promise((resolve, reject) => {
            console.log('_post', path, data);
            uni.request({
                url: path,
                data: data,
                header: {
                    'content-type': 'application/json',
                    'Authorization': 'Bearer ' + getItem('token')
                },
                method: 'POST',

                success: (res) => {
                    console.log('_post result', res);
                    console.log('success res.statusCode  -------------', res.statusCode);
                    console.log('success res.data  -------------', res.data);
                    if (res.statusCode === 200) {
                        if (rawResponse) {
                            console.log('success rawResponse res.data  -------------', res
                                .data);
                            console.log('success rawResponse res.data.data  -------------', res
                                .data.data);
                            resolve(res);
                            return;
                        }
                        if (rawResponseData) {
                            console.log('success rawResponseData res.data  -------------', res
                                .data);
                            console.log('success rawResponseData res.data.data  -------------',
                                res.data.data);
                            resolve(res.data);
                            return
                        }
                        if (res.data.code === 200) {
                            console.log('success res.data  -------------', res.data);
                            console.log('success res.data.data  -------------', res.data.data);
                            resolve(res.data.data);
                            return
                        } else {
                            throw new AppServerError(res.data.code, res.data.message)
                        }
                    } else {
                        throw new Error('request error, status code: ' + res.status)
                    }
                },
                fail: (res) => {
                    console.log('fail', res);
                    throw new Error('request error: ' + res)
                }
            });
        });
        return p;
    }

    /**
     * 登录-废弃了不用了，要用需要改造
     * @param {Object} phone
     * @param {Object} code
     */
    login(phone, code) {
        let appServer = Config.APP_SERVER + '/login';
        let clientId = wfc.getClientId();
        console.log('login', wfc.getClientId(), Config.getWFCPlatform());
        uni.request({
            url: appServer,
            data: {
                mobile: phone,
                code: code,
                clientId: clientId,
                platform: Config.getWFCPlatform(),
            },
            header: {
                'content-type': 'application/json' // 默认值
            },
            method: 'POST',

            success: (res) => {
                if (res.statusCode === 200) {
                    let loginResult = res.data;

                    if (loginResult.code === 0) {
                        let userId = loginResult.result.userId;
                        let token = loginResult.result.token;
                        wfc.connect(userId, token);
                        setItem('userId', userId);
                        setItem('token', token)

                        let authToken = res.header['authToken'];
                        if (!authToken) {
                            authToken = res.header['authtoken'];
                        }

                        console.log('setupAppserver', Config.APP_SERVER, authToken);
                        // wfcUIKit.setupAppServer(Config.APP_SERVER, authToken);


                        this.go2ConversationList();

                    } else {
                        console.log('login failed', loginResult);
                    }
                }
            }

        });
    }

    /**
     * 发布动态
     * @param {Object} momentData 动态数据
     * @param {string} [momentData.content] 动态内容
     * @param {Array} [momentData.url] 图片URL数组
     * @param {string} [momentData.address] 定位地址
     * @param {string} [momentData.longitude] 经度
     * @param {string} [momentData.latitude] 纬度
     * @returns {Promise} 返回发布结果的Promise
     */
    async publishMoment(momentData) {
        try {
            console.log('发布动态:', momentData)
            const result = await this._post('/moment/publish', {
                content: momentData.content || '',
                url: momentData.url || [],
                address: momentData.address || '',
                longitude: momentData.longitude || '',
                latitude: momentData.latitude || ''
            })
            return result
        } catch (error) {
            console.error('发布动态失败:', error)
            throw error
        }
    }

    /**
     * 分页查询动态列表
     * @param {{1, 100}} params 查询参数
     * @param {number} [params.pageNo=1] 页码
     * @param {number} [params.pageSize=10] 每页条数
     * @returns {Promise} 返回动态列表的Promise
     */
    async getMomentList(params = {}) {
        try {
            console.log('查询动态列表:', params)
            const defaultParams = {
                pageNo: 1,
                pageSize: 30,
                userId: params.userId || ''
            }
            const queryParams = {...defaultParams, ...params}
            console.log('查询动态列表参数:', queryParams)
            const result = await this._get(`/moment/page`, queryParams)
            console.log('查询动态列表结果:', result)
            return result
        } catch (error) {
            console.error('查询动态列表失败:', error)
            throw error
        }
    }

    /**
     * 分页查询他人动态列表
     * @param {Object} params 查询参数
     * @param {number} [params.pageNo=1] 页码
     * @param {number} [params.pageSize=10] 每页条数
     * @param {string} params.userId 用户ID(必填)
     * @returns {Promise} 返回他人动态列表的Promise
     */
    async getUserMomentList(params = {}) {
        try {
            if (!params.userId) {
                throw new Error('userId参数为必填项')
            }
            console.log('查询他人动态列表:', params)
            const defaultParams = {
                pageNo: 1,
                pageSize: 10
            }
            const queryParams = {...defaultParams, ...params}
            console.log('查询他人动态列表参数:', queryParams)
            const result = await this._get(`/moment/page/user`, queryParams)
            console.log('查询他人动态列表结果:', result)
            return result
        } catch (error) {
            console.error('查询他人动态列表失败:', error)
            throw error
        }
    }

    /**
     * 上传通讯录数据
     * @param {Array} contacts 通讯录联系人数组
     * @returns {Promise} 返回上传结果的Promise
     */
    async uploadContacts(contacts) {
        try {
            console.log('上传通讯录数据:', contacts)
            const result = await this._post('/contact/uploadContactData', {
                contacts: contacts
            })
            return result
        } catch (error) {
            console.error('上传通讯录失败:', error)
            throw error
        }
    }

    /**
     * 发布朋友圈评论
     * @param {Object} commentData 评论数据
     * @param {number} commentData.momentId 动态ID
     * @param {string} commentData.toUserId 回复用户ID（可选）
     * @param {string} commentData.content 评论内容
     * @returns {Promise} 返回发布结果的Promise
     */
    async publishMomentComment(commentData) {
        try {
            console.log('发布评论:', commentData)
            const result = await this._post('/moment/comment/publish', commentData)
            return result
        } catch (error) {
            console.error('发布评论失败:', error)
            throw error
        }
    }

    /**
     * 点赞/取消点赞动态
     * @param {number} momentId 动态ID
     * @returns {Promise} 返回点赞结果的Promise
     */
    async toggleMomentLike(momentId) {
        try {
            console.log('点赞/取消点赞动态:', momentId)
            const result = await this._post(`/moment/like?momentId=${momentId}`, {
                momentId: momentId
            })
            return result
        } catch (error) {
            console.error('点赞/取消点赞失败:', error)
            throw error
        }
    }

    /**
     * 删除动态
     * @param {number|string} momentId 动态ID
     * @returns {Promise} 返回删除结果的Promise
     */
    async deleteMoment(momentId) {
        try {
            console.log('删除动态:', momentId)
            const result = await this._post('/moment/del', {
                momentId: momentId
            }, false, false, "form")
            return result
        } catch (error) {
            console.error('删除动态失败:', error)
            throw error
        }
    }

    /**
     * 获取动态详情
     * @param {string} momentId - 动态ID
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    async getMomentDetail(momentId) {
        try {
            console.log('获取动态详情:', momentId);
            const result = await this._get(`/moment/detail?momentId=${momentId}`);
            return result;
        } catch (error) {
            console.error('获取动态详情失败:', error);
            throw error;
        }
    }

    /**
     * 分页查询动态评论
     * @param {Object} params 查询参数
     * @param {number} [params.pageNo=1] 页码
     * @param {number} [params.pageSize=10] 每页条数
     * @param {string} params.momentId 动态ID(必填)
     * @returns {Promise} 返回动态评论列表的Promise
     */
    async getMomentComments(params = {}) {
        try {
            if (!params.momentId) {
                throw new Error('momentId参数为必填项')
            }
            console.log('查询动态评论列表:', params)
            const defaultParams = {
                pageNo: 1,
                pageSize: 10
            }
            const queryParams = {...defaultParams, ...params}
            console.log('查询动态评论列表参数:', queryParams)
            const result = await this._get(`/moment/comment/page`, queryParams)
            console.log('查询动态评论列表结果:', result)
            return result
        } catch (error) {
            console.error('查询动态评论列表失败:', error)
            throw error
        }
    }

    /**
     * 获取当前应用版本号
     * @returns {string} 当前版本号
     */
    getCurrentVersion() {
        let version = '0';

        return version;
    }

    /**
     * 检查版本更新
     * @returns {Promise} 返回检查结果的Promise
     */
    async checkVersion() {
        try {
            let osName;
            uni.getSystemInfo({
                success: (res) => {
                    console.log('系统信息:', res.osName);
                    osName = res.osName;
                },
                fail: (err) => {
                    console.error('获取系统信息失败:', err);
                }
            });
            const currentVersion = this.getCurrentVersion();
            console.log('当前版本:', currentVersion);
            const path = Config.APP_SERVER + '/version/' + osName;

            return new Promise((resolve, reject) => {
                uni.request({
                    url: path,
                    data: {},
                    header: {
                        'content-type': 'application/json'
                    },
                    method: 'GET',
                    success: (res) => {
                        console.log('_get result', res);
                        if (res.data.code === 200 && res.data.data != null) {
                            const serverData = res.data.data;
                            console.log('serverData', serverData);
                            const result = {
                                needUpdate: Number(serverData.versionCode) > Number(currentVersion),
                                currentVersion,
                                serverVersion: serverData.vertion,
                                updateInfo: serverData
                            };
                            // console.log('版本查询结果:', result);
                            resolve(result);
                        } else {
                            resolve({
                                needUpdate: false,
                                currentVersion,
                                serverVersion: null,
                                updateInfo: null
                            });
                        }
                    },
                    fail: (res) => {
                        console.log('检查版本失败:', res);
                        reject(res);
                    }
                });
            });

        } catch (error) {
            console.error('检查版本更新失败:', error);
            return Promise.reject({
                needUpdate: false,
                currentVersion: this.getCurrentVersion(),
                serverVersion: null,
                updateInfo: null
            });
        }
    }

    /**
     * 设置朋友圈背景图片
     * @param {string} cover - 背景图片URL
     * @returns {Promise} - 返回设置结果的Promise
     */
    async setMomentCover(cover) {
        try {
            console.log('设置朋友圈背景:', cover)
            const result = await this._post(`/user/info/cover?cover=${encodeURIComponent(cover)}`, {}, false, false, "form")
            return result
        } catch (error) {
            console.error('设置朋友圈背景失败:', error)
            throw error
        }
    }

    /**
     * 查询朋友圈通知
     * @returns {Promise} - 返回朋友圈通知的Promise
     */
    async getMomentNotice() {
        try {
            const result = await this._get('/user/info/notice')
            // console.log('查询朋友圈通知结果:', result)
            return result
        } catch (error) {
            console.error('查询朋友圈通知失败:', error)
            throw error
        }
    }

    /**
     * 钱包充值
     * @param {string} amount - 背景图片URL
     * @returns {Promise} - 返回设置结果的Promise
     */
    async walletCharge(cover) {
        try {
            const result = await this._post(`/wallet/charge`, {}, false, false, "form")
            return result
        } catch (error) {
            throw error
        }
    }

    /**
     * 开通钱包
     */
    async walletActivate(cover) {
        try {
            const result = await this._post(`/wallet/new`, {}, false, false, "form")
            return result
        } catch (error) {
            throw error
        }
    }

    async walletCharge(data) {
        try {
            const result = await this._post(`/wallet/charge`, data, false, false, "form")
            return result
        } catch (error) {
            throw error
        }
    }

    async walletCharge(data) {
        try {
            const result = await this._post(`/wallet/charge`, data, false, false, "form")
            return result
        } catch (error) {
            throw error
        }
    }

    async mywallet(cover) {
        try {
            const result = await this._get(`/wallet`, {}, true, true)
            return result
        } catch (error) {
            throw error
        }
    }

    /**
     * 钱包记录
     * @param data
     * @returns {Promise<string|Response<*>|*|T>}
     */
    async walletRecordpage(data) {
        try {
            const result = await this._get(`/wallet/page`, data, true, true)
            return result
        } catch (error) {
            throw error
        }
    }

    /**
     * 提现记录
     * @param data
     * @returns {Promise<string|Response<*>|*|T>}
     */
    async withdrawRecords(data) {
        try {
            const result = await this._get(`/withdraw/list`, data, true, true)
            return result
        } catch (error) {
            throw error
        }
    }

    async setWalletpwd(data) {
        try {
            const result = await this._post(`/wallet/pwd`, data, false, false, "form")
            return result
        } catch (error) {
            throw error
        }
    }

    async sharePost(data) {
        try {
            const result = await this._post(`/bbs/topic/share`, data, false, false, "form")
            return result
        } catch (error) {
            throw error
        }
    }

    /**
     * 查询余额 、金币
     */
    async getWallet() {
        return this._get('/wallet');
    }

    /**
     * 金币充值
     * @param {string} amount - 背景图片URL
     * @returns {Promise} - 返回设置结果的Promise
     */
    async buyGold(params) {
        try {
            const result = await this._post(`/wallet/charge/gold`, params, false, false, "form")
            return result
        } catch (error) {
            console.log('buygold error')
            return {error}
        }
    }

    /**
     * 赞赏
     */
    sendGift(params) {
        return this._post(`/gift/send`, params, false, false, "form")
    }
    /**
     * 绑定支付宝
     * openId
     */
    bindAlipay(params) {
        return this._post(`/wallet/bind/alipay`, params, false, false, "form")
    }
    /**
     * 绑定微信
     * openId
     */
    bindWx(params) {
        return this._post(`/wallet/bindWx`, params, false, false, "form")
    }
    /**
     * 微信提现
     * openId
     */
    withDraw(params) {
        return this._post(`/wallet/withdraw`, params, false, false, "form")
    }
    /**
     * 微信提现
     * openId
     */
    getWxOpenId(code) {
        return this._get(`/weixin/decode?code=` + code, {}, false, false, "form")
    }

    //获取群成员身份
    async getGroupIdentitys(groupId) {
        try {
            const result = await this._get(`/group/member/identity?groupId=${groupId}`)
            return result?.data || []
        } catch (error) {
            throw error
        }
    }

    //查询已读人员
    async getReadUsers(messageId) {
        // 首先尝试从消息对象获取messageUid
        if (!messageId) {
            console.error('缺少messageId，无法获取已读用户');
            return [];
        }
        
        let messageUid = null;
        let messageUidCombined = null;

        // 检查传入的messageId是否已经是uid格式（包含-或–字符）
        const containsHyphen = messageId.includes('-') || messageId.includes('–');
        if (containsHyphen) {
            console.log('传入的messageId已经是uid格式:', messageId);
            messageUidCombined = messageId;
            
            // 保存映射关系到本地存储
            const uidMapKey = `msg_id_uid_map_${messageId}`;
            uni.setStorageSync(uidMapKey, messageId);
        } else {
            // 优先使用缓存的UID映射
            const uidMapKey = `msg_id_uid_map_${messageId}`;
            const cachedUid = uni.getStorageSync(uidMapKey);
            
            if (cachedUid) {
                messageUidCombined = cachedUid;
                console.log('从缓存获取messageUid:', messageUidCombined);
            } else {
                try {
                    // 尝试通过消息对象获取messageUid
                    const messageObj = wfc?.getMessageById(messageId);
                    if (messageObj?.messageUid) {
                        if (typeof messageObj.messageUid === 'object' && messageObj.messageUid.low !== undefined) {
                            const low = messageObj.messageUid.low;
                            const high = messageObj.messageUid.high;
                            messageUid = `${low}`;
                            messageUidCombined = `${high}-${low}`; // combined 格式
                        } else {
                            messageUid = messageObj.messageUid.toString();
                            messageUidCombined = messageUid;
                        }
                        
                        // 尝试获取会话ID
                        if (messageObj.conversation && messageObj.conversation.target) {
                            messageUidCombined = `${messageUidCombined}–${messageObj.conversation.target}`;
                        }
                        
                        // 保存映射关系到本地存储
                        uni.setStorageSync(uidMapKey, messageUidCombined);
                    }
                } catch (e) {
                    console.error('获取messageUid失败:', e);
                }
            }
        }
        
        // 检查是否有有效的messageUid
        if (!messageUidCombined) {
            console.error('无法获取有效的messageUid:', messageId);
            return this._getFallbackReadUsers(messageId, messageId);
        }
        
        // 使用处理后的messageUidCombined作为API请求参数
        console.log('使用处理后的messageUid请求API:', messageUidCombined);
        console.log('直接请求服务器获取最新已读人员列表，不使用缓存');
        
        try {
            // 确保messageUid中的特殊字符在URL中是安全的
            const encodedMessageUid = encodeURIComponent(messageUidCombined);
            
            // 直接请求API获取最新数据
            const apiUrl = `/msg/read/list?msgId=${encodedMessageUid}`;
            console.log('查询已读列表:', apiUrl);
            const result = await this._getWithDebug(apiUrl, {}, true); // isUidRequest = true
            const readUsers = result?.data || [];
            
            console.log(`获取到最新已读人员列表: ${readUsers.length}人`);
            
            // 直接返回最新数据
            return readUsers;
            
        } catch (error) {
            console.error('获取已读用户列表失败:', error);
            
            // 失败时使用备用方法
            return this._getFallbackReadUsers(messageId, messageUidCombined);
        }
    }

    // 获取备用的已读用户列表
    _getFallbackReadUsers(messageId, messageUidCombined) {
        console.log('使用备用方法获取已读用户，messageUid:', messageUidCombined);
        
        try {
            // 尝试从store获取会话信息和已读状态
            // 首先尝试直接使用import的store
            let storeInstance = null;
            
            // 尝试不同的方式获取store实例
            try {
                // 尝试从全局对象获取
                if (typeof window !== 'undefined' && window.userStore) {
                    storeInstance = window.userStore;
                } 
                // 尝试从uni对象获取
                else if (typeof uni !== 'undefined' && uni.store) {
                    storeInstance = uni.store;
                }
                // 尝试从本地存储获取序列化的store状态
                else {
                    const storeState = uni.getStorageSync('storeState');
                    if (storeState) {
                        console.log('从本地存储获取store状态');
                        // 仅用于判断，不能直接调用方法
                    }
                }
            } catch (e) {
                console.error('获取store实例失败:', e);
            }
            
            // 如果成功获取到store实例
            if (storeInstance && storeInstance.state) {
                // 尝试获取消息对象
                let messageObj = null;
                
                // 从store查找消息
                try {
                    // 先尝试通过getters.getMessageById获取消息
                    if (storeInstance.getters && storeInstance.getters.getMessageById) {
                        messageObj = storeInstance.getters.getMessageById(messageId);
                        if (messageObj) {
                            console.log('通过messageId找到消息:', messageId);
                        }
                    }
                    
                    // 如果没找到，尝试从会话列表查找
                    if (!messageObj && storeInstance.state.conversations) {
                        const conversation = storeInstance.state.conversations.find(conv => 
                            conv.lastMessage && conv.lastMessage.messageId === messageId);
                            
                        if (conversation) {
                            messageObj = conversation.lastMessage;
                            console.log('从会话中找到消息:', messageId);
                        }
                    }
                } catch (e) {
                    console.error('查找消息对象失败:', e);
                }
                
                // 如果找到了消息对象
                if (messageObj && messageObj.conversation) {
                    const conversation = messageObj.conversation;
                    // 如果是群聊，尝试获取群成员列表作为潜在已读用户
                    if (conversation.type === 2) { // 假设2是群聊类型
                        const targetId = conversation.target;
                        if (storeInstance.getters && storeInstance.getters.wfc) {
                            try {
                                const memberIds = storeInstance.getters.wfc.getGroupMemberIds(targetId, false);
                                if (memberIds && memberIds.length > 0) {
                                    console.log('从群成员列表估算已读用户:', memberIds.length, '人');
                                    
                                    // 尝试获取发送者ID
                                    const senderId = messageObj.from;
                                    let senderInfo = null;
                                    
                                    // 如果能获取到发送者，确保发送者在列表最前面
                                    if (senderId && storeInstance.getters.wfc.getUserInfo) {
                                        try {
                                            senderInfo = storeInstance.getters.wfc.getUserInfo(senderId);
                                        } catch (e) {
                                            console.error('获取发送者信息失败:', e);
                                        }
                                    }
                                    
                                    // 准备用户列表
                                    const result = [];
                                    
                                    // 首先添加发送者（如果有）
                                    if (senderInfo) {
                                        result.push({
                                            ...senderInfo,
                                            userId: senderInfo.userId || senderInfo.uid || senderId,
                                            isSender: true
                                        });
                                    }
                                    
                                    // 然后添加其他成员，但排除发送者
                                    for (const memberId of memberIds) {
                                        if (memberId !== senderId) {
                                            try {
                                                const memberInfo = storeInstance.getters.wfc.getUserInfo(memberId);
                                                if (memberInfo) {
                                                    result.push({
                                                        ...memberInfo,
                                                        userId: memberInfo.userId || memberInfo.uid || memberId
                                                    });
                                                } else {
                                                    result.push({ userId: memberId, displayName: '群成员' });
                                                }
                                            } catch (e) {
                                                console.error('获取成员信息失败:', e);
                                                result.push({ userId: memberId, displayName: '群成员' });
                                            }
                                        }
                                        
                                        // 最多返回前5个成员作为已读用户
                                        if (result.length >= 5) break;
                                    }
                                    
                                    return result;
                                }
                            } catch (e) {
                                console.error('获取群成员列表失败:', e);
                            }
                        }
                    } 
                    // 如果是单聊，对方ID就是潜在已读用户
                    else if (conversation.type === 1) {
                        const targetId = conversation.target;
                        if (targetId) {
                            console.log('单聊场景，对方ID作为潜在已读用户:', targetId);
                            return [{ userId: targetId }];
                        }
                    }
                }
            }
            
            // 尝试从全局变量获取已读信息
            if (typeof window !== 'undefined') {
                // 首先检查是否有专门针对此messageUid的缓存
                if (window._messageReadCountsByUid && window._messageReadCountsByUid[messageUidCombined]) {
                    const count = window._messageReadCountsByUid[messageUidCombined];
                    console.log('从全局变量获取messageUid的已读计数:', count);
                    
                    // 如果有计数但没有具体用户列表，创建一个只包含数量信息的占位用户列表
                    return [{ 
                        count: count,
                        isPlaceholder: true,
                        timestamp: Date.now()
                    }];
                }
                
                // 检查是否有针对messageId的缓存
                if (window._messageReadCounts && window._messageReadCounts[messageId]) {
                    const count = window._messageReadCounts[messageId];
                    console.log('从全局变量获取messageId的已读计数:', count);
                    
                    // 创建一个包含计数信息的占位用户列表
                    return [{ 
                        count: count,
                        isPlaceholder: true,
                        timestamp: Date.now()
                    }];
                }
            }
            
            // 尝试从本地存储获取已读计数
            try {
                const countCacheKey = `group_msg_read_count_uid_${messageUidCombined}`;
                const cachedCount = cacheManager.getItem(countCacheKey);
                if (cachedCount && cachedCount.count > 0) {
                    console.log('从本地存储获取已读计数:', cachedCount.count);
                    
                    // 返回缓存的已读计数，不再重置为0
                    return [{ 
                        count: cachedCount.count,
                        isPlaceholder: true,
                        timestamp: Date.now()
                    }];
                }
                
                // 如果没有找到messageUid的缓存，尝试找messageId的缓存
                const msgIdCacheKey = `group_msg_read_count_${cachedCount}`;
                const msgIdCachedCount = cacheManager.getItem(msgIdCacheKey);
                if (msgIdCachedCount && msgIdCachedCount.count > 0) {
                    console.log('从messageId缓存获取已读计数:', msgIdCachedCount.count);

                    // 返回缓存的已读计数
                    return [{
                        count: msgIdCachedCount.count,
                        isPlaceholder: true,
                        timestamp: Date.now()
                    }];
                }
            } catch (e) {
                console.error('从本地存储获取已读计数失败:', e);
            }
            
        } catch (e) {
            console.error('获取备用已读用户列表失败:', e);
        }
        
        // 最终无法获取数据，返回空数据
        console.log('无法获取已读数据，返回空数据');
        return []; // 返回空数组，表示没有已读用户
    }

    /**
     * 获取指定用户的信息
     * @param {string} userId - 用户ID
     * @returns {Promise} - 返回用户信息的Promise
     */
    async getOtherUserInfo(userId) {
        try {
            console.log('获取用户信息:', userId)
            const result = await this._get(`/user/info/${userId}`)
            if (result && result.data) {
                console.log('获取用户信息成功:', result.data)
                return result.data
            }
            throw new Error('获取用户信息失败')
        } catch (error) {
            console.error('获取用户信息失败:', error)
            throw error
        }
    }

    /**
     * 获取用户的获赞/关注/粉丝数统计
     * @param {string} userId - 用户ID
     * @returns {Promise} - 返回用户统计数据的Promise
     */
    async getUserStatistics(userId) {
        try {
            console.log('获取用户统计数据:', userId)
            const result = await this._get(`/bbs/statistic/user/relation?userId=${userId}`)
            if (result && result.data) {
                console.log('获取用户统计数据成功:', result.data)
                return result.data
            }
            throw new Error('获取用户统计数据失败')
        } catch (error) {
            console.error('获取用户统计数据失败:', error)
            throw error
        }
    }

    /**
     * 获取用户的吧贴数统计
     * @param {string} userId - 用户ID
     * @returns {Promise} - 返回用户吧贴数的Promise
     */
    async getUserPostCount(userId) {
        try {
            console.log('获取用户吧贴数:', userId)
            const result = await this._get(`/bbs/statistic/count/user?userId=${userId}`)
            if (result && result.data) {
                console.log('获取用户吧贴数成功:', result.data)
                return result.data
            }
            throw new Error('获取用户吧贴数失败')
        } catch (error) {
            console.error('获取用户吧贴数失败:', error)
            throw error
        }
    }

    /**
     * 关注/取关用户
     * @param {string} targetId - 对方用户的ID
     * @returns {Promise} - 返回操作结果的Promise
     */
    async toggleUserFollow(targetId) {
        try {
            console.log(`关注/取关用户: targetId = ${targetId}`)
            const result = await this._post('/bbs/user/relation/follow', { targetId }, false, false, "form")
            return result
        } catch (error) {
            console.error('关注/取关用户失败:', error)
            throw error
        }
    }

    /**
     * 检查是否关注用户
     * @param {string} targetId - 目标用户ID
     * @returns {Promise} - 返回是否关注的Promise
     */
    getCheckUserFollow(targetId) {
        console.log(`检查是否关注用户: targetId = ${targetId}`)
        const result = this._get(`/bbs/user/relation/check?targetId=${targetId}`)
        return result
    }


    // 检查单聊消息已读状态
    checkMessageReadStatus(messageId, successCB, failCB) {
        console.log('检查单聊消息已读状态:', messageId);
        
        // 先从本地缓存检查
        const readStatusKey = `msg_read_status_${messageId}`;
        const cachedStatus = uni.getStorageSync(readStatusKey);
        if (cachedStatus && cachedStatus.isRead) {
            console.log('从缓存获取消息已读状态:', messageId, cachedStatus);
            successCB && successCB(cachedStatus);
            return;
        }
        
        // 确保baseUrl存在，如果不存在，使用Config中的值
        const baseUrl = this.baseUrl || Config.APP_SERVER;
        
        // 获取token，确保token存在
        const token = getItem('token');
        if (!token) {
            console.warn('检查消息已读状态时token为空');
            // 使用备用逻辑判断消息读取状态
            this._useFallbackReadStatus(messageId, successCB);
            return;
        }
        
        // 调用服务器API获取消息已读状态
        const url = `${baseUrl}/message/readStatus`;
        console.log('请求单聊消息已读状态URL:', url);
        
        const params = {
            messageId: messageId
        };
        
        try {
            uni.request({
                url,
                data: params,
                method: 'GET',
                header: {
                    'Content-Type': 'application/json',
                    'token': token
                },
                success: (res) => {
                    console.log('获取消息已读状态成功:', res.data);
                    
                    if (res.data && res.data.code === 200) {
                        // 格式化结果
                        const result = {
                            isRead: res.data.data && res.data.data.isRead === true,
                            readTime: res.data.data && res.data.data.readTime || 0
                        };
                        
                        // 缓存结果
                        uni.setStorageSync(readStatusKey, result);
                        
                        successCB && successCB(result);
                    } else {
                        // 如果API报错，使用备用逻辑判断消息是否已读
                        console.log('API返回错误，使用备用逻辑判断');
                        this._useFallbackReadStatus(messageId, successCB);
                    }
                },
                fail: (err) => {
                    console.error('获取消息已读状态失败:', err);
                    
                    // 如果网络请求失败，使用备用逻辑判断消息是否已读
                    console.log('网络请求失败，使用备用逻辑判断');
                    this._useFallbackReadStatus(messageId, successCB);
                }
            });
        } catch (error) {
            console.error('检查消息已读状态异常:', error);
            
            // 如果发生异常，使用备用逻辑判断消息是否已读
            console.log('发生异常，使用备用逻辑判断');
            this._useFallbackReadStatus(messageId, successCB);
        }
    }
    
    // 使用备用逻辑判断消息已读状态
    _useFallbackReadStatus(messageId, callback) {
        try {
            // 避免使用require，直接引用store
            let storeInstance = null;
            
            // 尝试不同的方式获取store实例
            try {
                // 尝试从全局对象获取
                if (typeof window !== 'undefined' && window.userStore) {
                    storeInstance = window.userStore;
                } 
                // 尝试从uni对象获取
                else if (typeof uni !== 'undefined' && uni.store) {
                    storeInstance = uni.store;
                }
            } catch (e) {
                console.error('获取store实例失败:', e);
            }
            
            const readStatusKey = `msg_read_status_${messageId}`;
            let fallbackResult = {
                isRead: false,
                readTime: 0,
                serverConfirmed: false
            };
            
            // 先从本地存储中获取
            try {
                const cachedStatus = uni.getStorageSync(readStatusKey);
                if (cachedStatus && cachedStatus.isRead) {
                    console.log('从本地存储获取消息已读状态:', messageId);
                    fallbackResult = {
                        isRead: true,
                        readTime: cachedStatus.readTime || Date.now(),
                        serverConfirmed: cachedStatus.serverConfirmed || false
                    };
                }
            } catch (e) {
                console.error('读取本地缓存消息状态出错:', e);
            }
            
            // 尝试从全局变量中获取已读状态
            if (!fallbackResult.isRead && typeof window !== 'undefined') {
                if (window._serverConfirmedReadMessages && window._serverConfirmedReadMessages[messageId]) {
                    console.log('从全局变量获取消息已读状态:', messageId);
                    fallbackResult = {
                        isRead: true,
                        readTime: window._serverConfirmedReadMessages[messageId],
                        serverConfirmed: true
                    };
                } else if (window._lastReadMessages && window._lastReadMessages[messageId]) {
                    console.log('从全局变量获取消息本地已读状态:', messageId);
                    fallbackResult = {
                        isRead: true,
                        readTime: window._lastReadMessages[messageId],
                        serverConfirmed: false
                    };
                }
            }
            
            // 如果Store可用，尝试从messageReadStatus Map获取
            if (!fallbackResult.isRead && storeInstance && storeInstance.state && storeInstance.state.messageReadStatus) {
                const readStatus = storeInstance.state.messageReadStatus.get(messageId);
                if (readStatus && readStatus > 0) {
                    console.log('从Store的messageReadStatus获取消息已读状态:', messageId);
                    fallbackResult = {
                        isRead: true,
                        readTime: Date.now(),
                        serverConfirmed: false
                    };
                }
            }
            
            // 根据消息时间和对方阅读时间判断
            // 如果消息发送超过2天仍无回应，可能是已读但服务器未同步状态
            if (!fallbackResult.isRead) {
                try {
                    const msg = storeInstance?.getters?.getMessageById(messageId);
                    if (msg && msg.timestamp) {
                        const now = Date.now();
                        const msgAge = now - msg.timestamp;
                        // 如果消息超过2天，假设已读
                        if (msgAge > 2 * 24 * 60 * 60 * 1000) {
                            console.log('消息发送超过2天，可能已读:', messageId);
                            fallbackResult = {
                                isRead: true,
                                readTime: now,
                                serverConfirmed: false,
                                estimatedByAge: true
                            };
                        }
                    }
                } catch (e) {
                    console.error('无法获取消息对象:', e);
                }
            }
            
            // 返回结果
            callback && callback(fallbackResult);
            
            // 如果消息被判断为已读，更新本地缓存以加速后续判断
            if (fallbackResult.isRead) {
                try {
                    uni.setStorageSync(readStatusKey, fallbackResult);
                } catch (e) {
                    console.error('保存本地缓存消息状态出错:', e);
                }
            }
        } catch (error) {
            console.error('备用读取状态逻辑异常:', error);
            // 出错时返回默认状态（未读）
            callback && callback({
                isRead: false,
                readTime: 0,
                serverConfirmed: false,
                error: true
            });
        }
    }

    // 增强_get方法，添加专门的messageUid接口调试
    async _getWithDebug(path, params = {}, isUidRequest = false) {
        try {
            // 构建完整URL
            const fullUrl = `${this.baseUrl}${path}`;
            
            // 记录日志信息
            if (isUidRequest) {
                // 只在messageUid请求时输出一次日志
                console.log('发送messageUid请求:', path);
            }
            
            // 使用标准的_get方法
            const response = await this._get(path, params, true);
            
            // 请求结束后，对于messageUid相关的请求再次记录响应
            if (isUidRequest) {
                // 不再输出整个响应对象，避免日志过多
                // console.log('messageUid请求响应:', response);
            }
            
            // 返回标准的响应数据格式
            return response.data;
        } catch (e) {
            console.error('_getWithDebug请求失败:', e);
            throw e;
        }
    }
    
    // 记录消息详情的辅助方法
    _logMessageDetails(message) {
        if (!message) {
            console.log('消息对象为空，无法记录详情');
            return;
        }
        
        try {
            // 创建一个不包含循环引用的消息摘要对象
            const messageSummary = {
                messageId: message.messageId,
                messageUid: message.messageUid ? 
                    (typeof message.messageUid === 'object' ? 
                        `${message.messageUid.high || 0}-${message.messageUid.low || 0}` : 
                        message.messageUid.toString()) 
                    : '未定义',
                timestamp: message.timestamp,
                from: message.from,
                conversationType: message.conversation ? message.conversation.type : '未知',
                conversationTarget: message.conversation ? message.conversation.target : '未知',
                status: message.status,
                direction: message.direction
            };
            
            console.log('消息详情摘要:', messageSummary);
            console.log('消息完整UID信息:', 
                message.messageUid ? 
                (typeof message.messageUid === 'object' ? 
                    {high: message.messageUid.high, low: message.messageUid.low} : 
                    message.messageUid) 
                : '未定义');
        } catch (e) {
            console.error('记录消息详情时出错:', e);
        }
    }

    async submitFeedback(feedbackData) {
        return this._post('/feedback', feedbackData);
    }

    async getFeedbackList(pageNo = 1, pageSize = 10) {
        return this._get('/feedback/list', { pageNo, pageSize });
    }

    async getFeedbackReplyList(feedbackId, pageNo = 1, pageSize = 10) {
        return this._get('/feedback/reply/list', { feedbackId, pageNo, pageSize });
    }

    async submitFeedbackReply(feedbackId, content, url = '') {
        return this._post('/feedback/reply/save', { feedbackId, content, url });
    }

    /**
     * 保存/修改群名片
     * @param {Object} cardInfo 群名片信息
     * @param {string} cardInfo.groupId 群组ID（必填）
     * @param {string} cardInfo.userId 用户ID（必填）
     * @param {string} cardInfo.displayName 显示名称
     * @param {string} cardInfo.company 公司
     * @param {string} cardInfo.title 职位
     * @param {string} cardInfo.advantage 优势
     * @param {string} cardInfo.needResource 需求资源
     * @param {number} cardInfo.urgency 紧急程度
     * @returns {Promise} 返回保存群名片的请求Promise
     */
    saveGroupCard(cardInfo) {
        console.log(`saveGroupCard: cardInfo = ${JSON.stringify(cardInfo)}`);
        return this._post('/group/card/save', cardInfo);
    }

    /**
     * 分页查询群内所有名片
     * @param {Object} params 查询参数
     * @param {string} params.groupId 群组ID（必填）
     * @param {number} params.pageNum 页码（从1开始）
     * @param {number} params.pageSize 每页数量
     * @returns {Promise} 返回查询群名片的请求Promise
     */
    getGroupCards(params) {
        console.log(`getGroupCards: params = ${JSON.stringify(params)}`);
        return this._get('/group/card/page', params);
    }

    async addBankCard(data) {
        return await this._post('/user/bankcard/add', data);
    }
    
    async getBankCardList() {
        return await this._get('/user/bankcard/list');
    }

    /**
     * 分页查询图文朋友圈（视界）列表
     * @param {{pageNo: number, pageSize: number, userId: string}} params 查询参数
     * @returns {Promise} 返回视界列表的Promise
     */
    async getSightList(params = {}) {
        try {
            console.log('查询视界列表:', params)
            const defaultParams = {
                pageNo: 1,
                pageSize: 10,
                userId: params.userId || ''
            }
            const queryParams = { ...defaultParams, ...params }
            console.log('查询视界参数:', queryParams)
            const result = await this._get(`/moment/page/vision`, queryParams)
            console.log('查询视界结果:', result)
            return result
        } catch (error) {
            console.error('查询视界失败:', error)
            throw error
        }
    }

    async getSightDetail(id) {
        try {
            console.log('查询视界详情:', id)
            const result = await this._get(`/moment/detail/${id}`)
            console.log('查询视界详情结果:', result)
            return result
        } catch (error) {
            console.error('查询视界详情失败:', error)
            throw error
        }
    }

    /**
     * 获取推荐吧（随机5个）
     * @returns {Promise} - 返回推荐吧的Promise
     */
    async getRecommendBars() {
        try {
            const result = await this._get('/bbs/info/random5')
            console.log('获取推荐吧结果:', result)
            return result
        } catch (error) {
            console.error('获取推荐吧失败:', error)
            throw error
        }
    }

    /**
     * 保存/修改名片
     * @param {Object} cardInfo 名片信息对象
     * @param {string} cardInfo.advantage 优势
     * @param {string} cardInfo.company 公司
     * @param {string} cardInfo.defect 缺点
     * @param {string} cardInfo.name 姓名
     * @param {Array<string>} cardInfo.url 网址数组
     * @param {string} cardInfo.city 城市
     * @param {number} cardInfo.roleId 角色ID
     * @param {string} cardInfo.description 描述
     * @param {string} cardInfo.phone 电话
     * @param {string} cardInfo.wechatId 微信号
     * @param {string} [cardInfo.income] 年收入
     * @param {string} [cardInfo.introduction] 一句话介绍自己
     * @param {Object} [cardInfo.platformIndex] 其他社交媒体链接
     * @param {string} [cardInfo.platformIndex.douyin] 抖音主页链接
     * @param {string} [cardInfo.platformIndex.xiaohongshu] 小红书主页链接
     * @param {string} [cardInfo.platformIndex.kuaishou] 快手主页链接
     * @param {string} [cardInfo.platformIndex.weibo] 微博主页链接
     * @param {string} [cardInfo.platformIndex.toutiao] 今日头条主页链接
     * @param {string} [cardInfo.platformIndex.gongzhonghao] 公众号主页链接
     * @param {Array<Object>} [cardInfo.groups] 社群展示列表
     * @param {string} [cardInfo.groups.gid] 地球岛社群ID (当type为1时)
     * @param {string} [cardInfo.groups.groupId] 第三方社群ID (当type为2时)
     * @param {string} [cardInfo.groups.avatar] 社群头像
     * @param {string} [cardInfo.groups.groupName] 社群名称
     * @param {string} [cardInfo.groups.qrcode] 社群二维码URL
     * @param {string} [cardInfo.groups.description] 社群介绍
     * @param {number} [cardInfo.groups.groupType] 社群类型 (1: 地球岛社群, 2: 第三方社群)
     * @returns {Promise} 返回保存名片的请求Promise
     */
    saveCard(cardInfo) {
        console.log(`saveCard: cardInfo = ${JSON.stringify(cardInfo)}`);
        return this._post('/user/card/save', cardInfo);
    }

    getMyCard() {
        // 返回Promise，支持async/await
        return this._get('/user/card');
    }

    /**
     * 删除我的名片
     * @param {string} cardId - 要删除的名片ID
     * @returns {Promise} 返回删除结果的Promise
     */
    async deleteMyCard(cardId){
        try {
            console.log('删除名片:', cardId);
            const result = await this._post('/user/card/remove', { id: String(cardId) }, false, false, "form")
            console.log("接口调用之后返回的结果：",result)
            return result;
        } catch (error) {
            console.error('删除名片失败:', error);
            throw error;
        }
    }

    /**
     * 设置在群内展示名片
     * @param {Object} params
     * @param {string} params.cardId - 名片ID
     * @param {string} params.gid - 群组ID
     * @returns {Promise} - 返回请求结果的Promise
     */
    cardIntoGroup(params) {
        // 参数校验
        if (!params || !params.cardId || !params.gid) {
            return Promise.reject(new Error('cardId和gid为必填项'));
        }
        // 使用form表单方式提交
        return this._post('/group/user/card/set', {
            cardId: params.cardId,
            gid: params.gid
        }, false, false, 'form');
    }

    getAllRoles() {
        return this._get('/user/card/roles');
    }

    /**
     * 新增并发布名片到群
     * @param {Object} params
     * @param {string} params.gid - 群组ID
     * @param {Object} params.userCard - 名片对象，结构同saveCard
     * @returns {Promise} 返回接口请求Promise
     */
    publishCardIntoGroup(params) {
        if (!params || !params.gid || !params.userCard) {
            return Promise.reject(new Error('gid和userCard为必填项'));
        }
        // 以raw-json方式提交
        return this._post('/user/card/saveAndPublish', {
            gid: params.gid,
            userCard: params.userCard
        });
    }

    // 新版：查询群内所有名片
    getUserCard({ gid, pageNo = 1, pageSize = 20 }) {
        if (!gid) {
            return Promise.reject(new Error('gid为必填项'));
        }
        return this._get('/group/user/card/page', {
            gid,
            pageNo,
            pageSize
        });
    }

    /**
     * 修改我的名片
     * @param {Object} cardInfo - 要修改的名片信息
     * @param {string} cardInfo.advantage 优势
     * @param {string} cardInfo.company 公司
     * @param {string} cardInfo.defect 缺点
     * @param {string} cardInfo.name 姓名
     * @param {Array<string>} cardInfo.url 网址数组
     * @param {string} cardInfo.city 城市
     * @param {number} cardInfo.roleId 角色ID
     * @param {string} cardInfo.description 描述
     * @param {string} cardInfo.phone 电话
     * @param {string} cardInfo.wechatId 微信号
     * @param {string} [cardInfo.income] 年收入
     * @param {string} [cardInfo.introduction] 一句话介绍自己
     * @param {Object} [cardInfo.platformIndex] 其他社交媒体链接
     * @param {string} [cardInfo.platformIndex.douyin] 抖音主页链接
     * @param {string} [cardInfo.platformIndex.xiaohongshu] 小红书主页链接
     * @param {string} [cardInfo.platformIndex.kuaishou] 快手主页链接
     * @param {string} [cardInfo.platformIndex.weibo] 微博主页链接
     * @param {string} [cardInfo.platformIndex.toutiao] 今日头条主页链接
     * @param {string} [cardInfo.platformIndex.gongzhonghao] 公众号主页链接
     * @param {Array<Object>} [cardInfo.groups] 社群展示列表
     * @param {string} [cardInfo.groups.gid] 地球岛社群ID (当type为1时)
     * @param {string} [cardInfo.groups.groupId] 第三方社群ID (当type为2时)
     * @param {string} [cardInfo.groups.avatar] 社群头像
     * @param {string} [cardInfo.groups.groupName] 社群名称
     * @param {string} [cardInfo.groups.qrcode] 社群二维码URL
     * @param {string} [cardInfo.groups.description] 社群介绍
     * @param {number} [cardInfo.groups.type] 社群类型 (1: 地球岛社群, 2: 第三方社群)
     * @returns {Promise} 返回修改结果的Promise
     */
    async modifyCard(cardInfo){
        try {
            console.log('修改名片:', cardInfo);
            // 使用 raw-json 方式提交，_post 默认是 json
            const result = await this._post('/user/card/update', cardInfo);
            return result;
        } catch (error) {
            console.error('修改名片失败:', error);
            throw error;
        }
    }

    /**
     * 推荐话题请求函数
     * @param {string} keyword - 关键字
     * @param {string} pageNo - 页码
     * @param {string} pageSize - 每页大小
     * @returns {Promise} - 返回一个Promise对象，表示请求的结果
     */
    searchBars(params) {
        return this._get('/bbs/topic/search', params);
    }

    /**
     * 设置在群内展示名片 (新的方法，参考 deleteMyCard 风格)
     * @param {string} cardId - 名片ID
     * @param {string} groupId - 群组ID
     * @returns {Promise} - 返回请求结果的Promise
     */
    async setCardIntoGroup(cardId, groupId){
        try {
            console.log('设置名片到群组展示:', 'cardId:', cardId, 'groupId:', groupId);
            // API文档要求cardId和gid，使用form提交
            const result = await this._post('/group/user/card/set', { cardId: String(cardId), gid: String(groupId) }, false, false, "form");
            console.log("设置名片到群组展示接口调用结果：", result);
            
            // 根据Postman返回示例，成功时data为null，但code是200
            if (result && result.code === 200) {
                 return result; // 返回整个结果，包含code和msg
            } else {
                 // 接口返回非200状态码，抛出错误
                 const errorMsg = result?.msg || '设置名片到群组失败';
                 console.error('设置名片到群组失败:', errorMsg);
                 throw new Error(errorMsg);
            }
        } catch (error) {
            console.error('调用设置名片到群组接口失败:', error);
            throw error;
        }
    }
    /**
     * 取消名片在群内展示
     * @param {string} groupId - 群组ID
     * @returns {Promise} - 返回请求结果的Promise
     */
    async cancelCardIntoGroup(groupId) {
        try {
            console.log('取消名片在群内展示:', 'groupId:', groupId);
            // 使用form表单方式提交
            const result = await this._post('/group/user/card/disable', { gid: String(groupId) }, false, false, "form");
            console.log("取消名片在群内展示接口调用结果：", result);
            return result;
        } catch (error) {
            console.error('取消名片在群内展示失败:', error);
            throw error;
        }
    }
    

    /**
     * 综合搜索接口
     * @param {Object} params - { keyword: string, pageNo: number, pageSize: number }
     * @returns {Promise} - 返回Promise对象，包含综合搜索结果
     */
    searchAll(params) {
        console.log('调用综合搜索接口，参数:', params);
        return this._get('/bbs/search', params);
    }

    /**
     * 开启/关闭进群审核
     * @param {string} gid - 群ID
     * @param {string} audit - 1开启0关闭
     * @returns {Promise} - 返回请求结果的Promise
     */
    async setGroupAudit(gid, audit) {
        try {
            console.log('设置进群审核:', 'gid:', gid, 'audit:', audit);
            // 使用form表单方式提交
            const result = await this._post('/group/setAudit', { 
                gid: String(gid), 
                audit: String(audit) 
            }, false, false, "form");
            console.log("设置进群审核接口调用结果：", result);
            return result;
        } catch (error) {
            console.error('设置进群审核失败:', error);
            throw error;
        }
    }

    /**
     * 申请进群
     * @param {string} gid - 群ID
     * @param {string} reason - 申请说明
     * @returns {Promise} - 返回请求结果的Promise
     */
    async requestJoinGroup(gid, reason) {
        try {
            console.log('申请进群:', 'gid:', gid, 'reason:', reason);
            // 使用form表单方式提交
            const result = await this._post('/group/request', { 
                gid: String(gid), 
                reason: String(reason) 
            }, false, false, "form");
            console.log("申请进群接口调用结果：", result);
            return result;
        } catch (error) {
            console.error('申请进群失败:', error);
            throw error;
        }
    }

    /**
     * 审批进群
     * @param {string} id - 申请数据ID
     * @param {string} status - 1-通过 2-拒绝
     * @param {string} remark - 拒绝理由
     * @returns {Promise} - 返回请求结果的Promise
     */
    async auditJoinGroupRequest(id, status, remark) {
        try {
            console.log('审批进群:', 'id:', id, 'status:', status, 'remark:', remark);
            // 改为使用POST方式并通过form表单传递参数
            const result = await this._post('/group/request/audit', { 
                id: String(id), 
                status: String(status),
                remark: String(remark || '') 
            }, false, false, "form");
            console.log("审批进群接口调用结果：", result);
            return result;
        } catch (error) {
            console.error('审批进群失败:', error);
            throw error;
        }
    }

    /**
     * 查询待审批请求
     * @returns {Promise} - 返回待审批请求列表的Promise
     */
    async getGroupAuditRequestList() {
        try {
            console.log('查询待审批请求');
            const result = await this._get('/group/request/list');
            console.log("查询待审批请求接口调用结果：", result);
            return result;
        } catch (error) {
            console.error('查询待审批请求失败:', error);
            throw error;
        }
    }

    async getThreePic(userId) {
        return await this._get(`/moment/three`, {userId: userId})
    }

    /**
     * 获取卡片展示的群组列表
     * @param {string} id - 卡片ID
     * @returns {Promise} - 返回请求结果的Promise
     */
    getCardGroupsInner(cardId) {
        return this._get('/card/display/group/inner', {cardId: cardId})
    }

    /**
     * 获取卡片展示的群组列表
     * @param {string} id - 卡片ID
     * @returns {Promise} - 返回请求结果的Promise
     */
    getCardGroupsOuter(cardId) {
        return this._get('/card/display/group/outer', {cardId: cardId})
    }

    // 发布名片留言
    async publishCardComment(data) {
        return await this._post('/user/card/comment/publish', data);
    }

    // 分页查询名片留言
    async getCardCommentPage(params) {
        return await this._get('/user/card/comment/page', params);
    }

    async getCardCommentReply(params) {
        return await this._get('/user/card/comment/page', params);
    }

    // 发布名片留言回复
    async publishCardCommentReply(data) {
        return await this._post('/user/card/comment/reply/publish', data);
    }

    /**
     * 查询该名片展示的群
     * @param {string} cardId
     * @returns {Promise}
     */
    getCardDisplayGroups(cardId) {
        return this._get('/group/user/card/exist', { cardId });
    }

    /**
     * 批量设置名片展示到群
     * @param {Array<string>} gids
     * @param {string} cardId
     * @returns {Promise}
     */
    setCardDisplayGroupsBatch(gids, cardId) {
        return this._post('/group/user/card/set/batch', { gids, cardId });
    }
    
    /**
     * 通过 cardId 查询名片详情
     * @param {string|number} cardId
     * @returns {Promise} 返回名片详情
     */
    getCardDetailById(cardId) {
        return this._get('/share/user/card', { cardId });
    }
}

const appServerApi = new AppServerApi();
export default appServerApi;