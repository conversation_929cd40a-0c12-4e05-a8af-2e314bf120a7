import {
	AppServer<PERSON>pi
} from "./appServerApi";

class ActivityApi extends AppServerApi {
	constructor() {
		super();
	}


// 活动tab
	getActivityCategories() {
		return this._get('/activity/category/list')
	}
	
	// 活动list
	getActivityList(params) {
		return this._get('/activity/page', params)
	}

	// createConference(conferenceInfo) {
	//     return this._post('/conference/create', conferenceInfo);
	// }

}

const Activity = new ActivityApi();
export default Activity;