import store from "../store";

export default {

    async getWxOpenId(code) {
        const WX_APPID = 'wxebe732d88afb8986'
        const WX_SECRET = '640d08d3c2f503d3632a5901bb7a37b6'
        const url = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${WX_APPID}&secret=${WX_SECRET}&code=${code}&grant_type=authorization_code`

        try {
            const res = await uni.request({
                url,
                method: 'GET',
                header: {
                    'Content-Type': 'application/json'
                }
            })

            if (res.statusCode === 200 && res.data && !res.data.errcode) {
                return res.data.openid
            } else {
                throw new Error(res.data.errmsg || '获取 OpenID 失败')
            }
        } catch (error) {
            console.error('获取 OpenID 异常:', error)
            throw error
        }
    }
}